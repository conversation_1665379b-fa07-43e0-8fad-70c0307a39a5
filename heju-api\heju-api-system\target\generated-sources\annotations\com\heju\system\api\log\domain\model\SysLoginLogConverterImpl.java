package com.heju.system.api.log.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.api.log.domain.dto.SysLoginLogDto;
import com.heju.system.api.log.domain.po.SysLoginLogPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:51+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysLoginLogConverterImpl implements SysLoginLogConverter {

    @Override
    public SysLoginLogDto mapperDto(SysLoginLogPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysLoginLogDto sysLoginLogDto = new SysLoginLogDto();

        sysLoginLogDto.setId( arg0.getId() );
        sysLoginLogDto.setSourceName( arg0.getSourceName() );
        sysLoginLogDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysLoginLogDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysLoginLogDto.setName( arg0.getName() );
        sysLoginLogDto.setSort( arg0.getSort() );
        sysLoginLogDto.setRemark( arg0.getRemark() );
        sysLoginLogDto.setCreateBy( arg0.getCreateBy() );
        sysLoginLogDto.setCreateTime( arg0.getCreateTime() );
        sysLoginLogDto.setUpdateBy( arg0.getUpdateBy() );
        sysLoginLogDto.setUpdateTime( arg0.getUpdateTime() );
        sysLoginLogDto.setDelFlag( arg0.getDelFlag() );
        sysLoginLogDto.setCreateName( arg0.getCreateName() );
        sysLoginLogDto.setUpdateName( arg0.getUpdateName() );
        sysLoginLogDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysLoginLogDto.setEnterpriseName( arg0.getEnterpriseName() );
        sysLoginLogDto.setUserId( arg0.getUserId() );
        sysLoginLogDto.setUserName( arg0.getUserName() );
        sysLoginLogDto.setUserNick( arg0.getUserNick() );
        sysLoginLogDto.setStatus( arg0.getStatus() );
        sysLoginLogDto.setIpaddr( arg0.getIpaddr() );
        sysLoginLogDto.setMsg( arg0.getMsg() );
        sysLoginLogDto.setAccessTime( arg0.getAccessTime() );

        return sysLoginLogDto;
    }

    @Override
    public List<SysLoginLogDto> mapperDto(Collection<SysLoginLogPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysLoginLogDto> list = new ArrayList<SysLoginLogDto>( arg0.size() );
        for ( SysLoginLogPo sysLoginLogPo : arg0 ) {
            list.add( mapperDto( sysLoginLogPo ) );
        }

        return list;
    }

    @Override
    public Page<SysLoginLogDto> mapperPageDto(Collection<SysLoginLogPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysLoginLogDto> page = new Page<SysLoginLogDto>();
        for ( SysLoginLogPo sysLoginLogPo : arg0 ) {
            page.add( mapperDto( sysLoginLogPo ) );
        }

        return page;
    }

    @Override
    public SysLoginLogPo mapperPo(SysLoginLogDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysLoginLogPo sysLoginLogPo = new SysLoginLogPo();

        sysLoginLogPo.setId( arg0.getId() );
        sysLoginLogPo.setSourceName( arg0.getSourceName() );
        sysLoginLogPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysLoginLogPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysLoginLogPo.setName( arg0.getName() );
        sysLoginLogPo.setSort( arg0.getSort() );
        sysLoginLogPo.setRemark( arg0.getRemark() );
        sysLoginLogPo.setCreateBy( arg0.getCreateBy() );
        sysLoginLogPo.setCreateTime( arg0.getCreateTime() );
        sysLoginLogPo.setUpdateBy( arg0.getUpdateBy() );
        sysLoginLogPo.setUpdateTime( arg0.getUpdateTime() );
        sysLoginLogPo.setDelFlag( arg0.getDelFlag() );
        sysLoginLogPo.setCreateName( arg0.getCreateName() );
        sysLoginLogPo.setUpdateName( arg0.getUpdateName() );
        sysLoginLogPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysLoginLogPo.setEnterpriseName( arg0.getEnterpriseName() );
        sysLoginLogPo.setUserId( arg0.getUserId() );
        sysLoginLogPo.setUserName( arg0.getUserName() );
        sysLoginLogPo.setUserNick( arg0.getUserNick() );
        sysLoginLogPo.setStatus( arg0.getStatus() );
        sysLoginLogPo.setIpaddr( arg0.getIpaddr() );
        sysLoginLogPo.setMsg( arg0.getMsg() );
        sysLoginLogPo.setAccessTime( arg0.getAccessTime() );

        return sysLoginLogPo;
    }

    @Override
    public List<SysLoginLogPo> mapperPo(Collection<SysLoginLogDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysLoginLogPo> list = new ArrayList<SysLoginLogPo>( arg0.size() );
        for ( SysLoginLogDto sysLoginLogDto : arg0 ) {
            list.add( mapperPo( sysLoginLogDto ) );
        }

        return list;
    }

    @Override
    public Page<SysLoginLogPo> mapperPagePo(Collection<SysLoginLogDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysLoginLogPo> page = new Page<SysLoginLogPo>();
        for ( SysLoginLogDto sysLoginLogDto : arg0 ) {
            page.add( mapperPo( sysLoginLogDto ) );
        }

        return page;
    }
}
