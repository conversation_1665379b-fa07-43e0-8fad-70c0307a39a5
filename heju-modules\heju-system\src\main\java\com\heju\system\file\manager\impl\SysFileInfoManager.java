package com.heju.system.file.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.file.domain.dto.SysFileInfoDto;
import com.heju.system.file.domain.dto.SysFileRoleMergeDto;
import com.heju.system.file.domain.model.SysFileInfoConverter;
import com.heju.system.file.domain.po.SysFileInfoPo;
import com.heju.system.file.domain.query.SysFileInfoQuery;
import com.heju.system.file.manager.ISysFileInfoManager;
import com.heju.system.file.manager.ISysFileRoleMergeManager;
import com.heju.system.file.mapper.SysFileInfoMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件信息管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysFileInfoManager extends BaseManagerImpl<SysFileInfoQuery, SysFileInfoDto, SysFileInfoPo, SysFileInfoMapper, SysFileInfoConverter> implements ISysFileInfoManager {

    @Resource
    private SysFileInfoMapper sysFileInfoMapper;

    @Resource
    private ISysFileRoleMergeManager fileRoleMergeManager;

    /**
     * 查列表
     * @param query
     * @return
     */
    @Override
    public List<SysFileInfoDto> selectByQuery(SysFileInfoQuery query) {
        return sysFileInfoMapper.selectByQuery(query);
    }

    /**
     * 查admin列表
     * @param query
     * @return
     */
    @Override
    public List<SysFileInfoDto> selectAdminList(SysFileInfoQuery query) {
        return sysFileInfoMapper.selectAdminList(query);
    }

    /**
     * 查详情
     * @param id
     * @return
     */
    @Override
    public SysFileInfoDto selectInfoById(Serializable id) {
        return sysFileInfoMapper.selectInfoById(id);
    }

    @Override
    public void insertFileRole(String[] viewRole, String[] downloadRole, Long fileId) {
        List<Long> roleList = Arrays.stream(viewRole)
                .map(Long::valueOf)
                .toList();
        List<Long> downloadList = Arrays.stream(downloadRole)
                .map(Long::valueOf)
                .toList();
        List<SysFileRoleMergeDto> fileRoleMergeDtos=new ArrayList<>();
        //两个list的交集
        List<Long> intersection = roleList.stream()
                .filter(downloadList::contains)
                .toList();
        //只能查看
        List<Long> viewOnly = roleList.stream()
                .filter(id -> !intersection.contains(id))
                .toList();
        // 只能下载
        List<Long> downloadOnly = downloadList.stream()
                .filter(id -> !intersection.contains(id))
                .toList();
        if(!intersection.isEmpty()) {
            List<SysFileRoleMergeDto> intersectionList = intersection.stream()
                    .map(roleId -> {
                        SysFileRoleMergeDto dto = new SysFileRoleMergeDto();
                        dto.setFileId(fileId);
                        dto.setRoleId(roleId);
                        dto.setOperateType(NumberUtil.Three);
                        return dto;
                    }).toList();
            fileRoleMergeDtos.addAll(intersectionList);
        }
        if(!viewOnly.isEmpty()) {
            List<SysFileRoleMergeDto> viewOnlyList = viewOnly.stream()
                    .map(roleId -> {
                        SysFileRoleMergeDto dto = new SysFileRoleMergeDto();
                        dto.setFileId(fileId);
                        dto.setRoleId(roleId);
                        dto.setOperateType(NumberUtil.One);
                        return dto;
                    }).toList();
            fileRoleMergeDtos.addAll(viewOnlyList);
        }
        if(!downloadOnly.isEmpty()) {
            List<SysFileRoleMergeDto> downloadOnlyList = downloadOnly.stream()
                    .map(roleId -> {
                        SysFileRoleMergeDto dto = new SysFileRoleMergeDto();
                        dto.setFileId(fileId);
                        dto.setRoleId(roleId);
                        dto.setOperateType(NumberUtil.Two);
                        return dto;
                    }).toList();
            fileRoleMergeDtos.addAll(downloadOnlyList);
        }
        fileRoleMergeManager.insertBatch(fileRoleMergeDtos);
    }

    /**
     * 暂存管理查询
     * @param query
     * @return
     */
    @Override
    public List<SysFileInfoDto> tempStorageList(SysFileInfoQuery query) {
        List<SysFileInfoPo> sysFileInfoPos = baseMapper.selectList(new LambdaQueryWrapper<SysFileInfoPo>(query)
                        .isNull(SysFileInfoPo::getPositionId)
                        .eq(query.getClassifyId() !=null, SysFileInfoPo::getClassifyId, query.getClassifyId())
                        .like(StringUtils.isNoneBlank(query.getName()), SysFileInfoPo::getName, query.getName()));
        return mapperDto(sysFileInfoPos);
    }

    /**
     * 回收站查询
     * @param query
     * @return
     */
    @Override
    public List<SysFileInfoDto> selectRecycList(SysFileInfoQuery query) {
        List<SysFileInfoDto> list = sysFileInfoMapper.selectByDelFlag(query);
        return list;
    }

    /**
     * 回收站批量恢复
     * @param fileIds
     * @return
     */
    @Override
    public int updateByIds(List<Long> fileIds) {
        LambdaQueryWrapper<SysFileInfoPo> wrapper=new LambdaQueryWrapper<>();
        wrapper.in(SysFileInfoPo::getId,fileIds);
        SysFileInfoPo sysFileInfoPo=new SysFileInfoPo();
        sysFileInfoPo.setDelFlag(0L);
        return sysFileInfoMapper.update(sysFileInfoPo,wrapper);
    }

}
