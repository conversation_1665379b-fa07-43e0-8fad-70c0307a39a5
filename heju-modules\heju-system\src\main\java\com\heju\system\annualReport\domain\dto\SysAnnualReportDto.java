package com.heju.system.annualReport.domain.dto;

import com.heju.system.annualReport.domain.po.SysAnnualReportPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 工商年报 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysAnnualReportDto extends SysAnnualReportPo {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 实体名称 */
    protected String entityName;

    /**
     * 申报人
     */
    private String createName;

}