09:27:10.274 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:27:11.051 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0
09:27:11.147 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 41 ms to scan 1 urls, producing 3 keys and 6 values 
09:27:11.189 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:27:11.203 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:27:11.211 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:27:11.222 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 7 values 
09:27:11.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:27:11.234 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:27:11.234 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000021ebd3b8b08
09:27:11.234 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000021ebd3b8d28
09:27:11.234 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:27:11.238 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:27:11.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:27:13.515 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754443632592_127.0.0.1_2899
09:27:13.517 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Notify connected event to listeners.
09:27:13.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:27:13.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000021ebd4f0ad8
09:27:15.956 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:27:21.142 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:27:21.143 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:27:21.143 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:27:21.432 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:27:23.600 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:27:25.717 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b9d37b3a-9297-4bcb-9723-de2404383ee1
09:27:25.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] RpcClient init label, labels = {module=naming, source=sdk}
09:27:25.722 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:27:25.722 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:27:25.722 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:27:25.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:27:25.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Success to connect to server [localhost:8848] on start up, connectionId = 1754443645737_127.0.0.1_3079
09:27:25.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:27:25.868 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Notify connected event to listeners.
09:27:25.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000021ebd4f0ad8
09:27:25.962 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:27:26.014 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:27:26.235 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 16.81 seconds (JVM running for 26.702)
09:27:26.254 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:27:26.256 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:27:26.260 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:27:27.050 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:27:27.050 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:30:22.477 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:30:30.405 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:30:30.405 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:30:33.823 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:30:33.825 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 13
10:25:03.237 [nacos-grpc-client-executor-739] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 21
10:25:03.276 [nacos-grpc-client-executor-739] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 21
10:25:30.635 [nacos-grpc-client-executor-745] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 24
10:25:30.656 [nacos-grpc-client-executor-745] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 24
11:20:56.512 [nacos-grpc-client-executor-1454] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 28
11:20:56.531 [nacos-grpc-client-executor-1454] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 28
11:21:27.419 [nacos-grpc-client-executor-1460] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 31
11:21:27.436 [nacos-grpc-client-executor-1460] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 31
13:17:10.223 [lettuce-nioEventLoop-4-1] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
13:17:10.369 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /192.168.1.200:6379
13:17:19.459 [lettuce-eventExecutorLoop-1-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:17:27.664 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:17:27.697 [lettuce-nioEventLoop-4-15] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to 192.168.1.200/<unresolved>:6379
14:25:35.377 [nacos-grpc-client-executor-3801] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 35
14:25:35.396 [nacos-grpc-client-executor-3801] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 35
14:25:38.201 [nacos-grpc-client-executor-3802] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 38
14:25:38.215 [nacos-grpc-client-executor-3802] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 38
14:27:09.944 [nacos-grpc-client-executor-3821] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 43
14:27:09.965 [nacos-grpc-client-executor-3821] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 43
14:27:37.874 [nacos-grpc-client-executor-3827] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 47
14:27:37.907 [nacos-grpc-client-executor-3827] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 47
14:55:05.041 [nacos-grpc-client-executor-4177] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 51
14:55:05.051 [nacos-grpc-client-executor-4177] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 51
14:55:24.583 [nacos-grpc-client-executor-4181] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 56
14:55:24.596 [nacos-grpc-client-executor-4181] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 56
16:14:05.024 [nacos-grpc-client-executor-5152] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 60
16:14:05.045 [nacos-grpc-client-executor-5152] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 60
16:14:07.959 [nacos-grpc-client-executor-5153] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Receive server push request, request = NotifySubscriberRequest, requestId = 64
16:14:07.990 [nacos-grpc-client-executor-5153] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Ack server push request, request = NotifySubscriberRequest, requestId = 64
20:49:40.243 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:49:40.243 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:49:40.385 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.386 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.601 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.630 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.917 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.938 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:41.350 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:41.363 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:41.868 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:41.875 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:42.486 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:42.486 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:42.796 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:49:43.131 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:49:43.198 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d5b3330-c538-4bf7-8326-01dffa0e7cff_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:43.201 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:43.467 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:49:43.467 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@76efbb7c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:49:43.469 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9d37b3a-9297-4bcb-9723-de2404383ee1] Client is shutdown, stop reconnect to server
20:49:43.469 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754443645737_127.0.0.1_3079
20:49:43.470 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5dd7bac7[Running, pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 8570]
