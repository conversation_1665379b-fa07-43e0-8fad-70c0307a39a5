09:13:43.215 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:13:43.916 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9c945317-76a3-4ddf-ab3d-90d61a0bf0a7_config-0
09:13:44.002 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 42 ms to scan 1 urls, producing 3 keys and 6 values 
09:13:44.043 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:13:44.052 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 3 keys and 10 values 
09:13:44.064 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:13:44.075 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:13:44.086 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:13:44.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c945317-76a3-4ddf-ab3d-90d61a0bf0a7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:13:44.091 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c945317-76a3-4ddf-ab3d-90d61a0bf0a7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000029b603b8d60
09:13:44.091 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c945317-76a3-4ddf-ab3d-90d61a0bf0a7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000029b603b8f80
09:13:44.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c945317-76a3-4ddf-ab3d-90d61a0bf0a7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:13:44.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c945317-76a3-4ddf-ab3d-90d61a0bf0a7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:13:44.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c945317-76a3-4ddf-ab3d-90d61a0bf0a7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:13:45.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c945317-76a3-4ddf-ab3d-90d61a0bf0a7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753751624946_127.0.0.1_10288
09:13:45.221 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c945317-76a3-4ddf-ab3d-90d61a0bf0a7_config-0] Notify connected event to listeners.
09:13:45.222 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c945317-76a3-4ddf-ab3d-90d61a0bf0a7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:13:45.222 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c945317-76a3-4ddf-ab3d-90d61a0bf0a7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000029b604f0668
09:13:45.394 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:13:50.993 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:13:55.962 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:13:58.178 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7cdbefc8-bd9e-4aaf-84ea-9285e629f88a_config-0
09:13:58.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cdbefc8-bd9e-4aaf-84ea-9285e629f88a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:13:58.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cdbefc8-bd9e-4aaf-84ea-9285e629f88a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000029b603b8d60
09:13:58.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cdbefc8-bd9e-4aaf-84ea-9285e629f88a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000029b603b8f80
09:13:58.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cdbefc8-bd9e-4aaf-84ea-9285e629f88a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:13:58.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cdbefc8-bd9e-4aaf-84ea-9285e629f88a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:13:58.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cdbefc8-bd9e-4aaf-84ea-9285e629f88a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:13:58.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cdbefc8-bd9e-4aaf-84ea-9285e629f88a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753751638211_127.0.0.1_10488
09:13:58.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cdbefc8-bd9e-4aaf-84ea-9285e629f88a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:13:58.365 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cdbefc8-bd9e-4aaf-84ea-9285e629f88a_config-0] Notify connected event to listeners.
09:13:58.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cdbefc8-bd9e-4aaf-84ea-9285e629f88a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000029b604f0668
09:13:58.774 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 203fd20c-d098-404e-8e67-12be05e7b19c
09:13:58.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] RpcClient init label, labels = {module=naming, source=sdk}
09:13:58.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:13:58.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:13:58.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:13:58.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:13:58.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Success to connect to server [localhost:8848] on start up, connectionId = 1753751638811_127.0.0.1_10490
09:13:58.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:13:58.962 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Notify connected event to listeners.
09:13:58.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000029b604f0668
09:14:00.697 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:14:00.699 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:14:01.050 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
09:14:01.181 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 18.704 seconds (JVM running for 26.055)
09:14:01.201 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:14:01.205 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:14:01.209 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:14:01.564 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:14:01.565 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:14:30.076 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:14:30.076 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:14:30.086 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:14:30.098 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:14:30.106 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:14:30.107 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:14:55.321 [nacos-grpc-client-executor-68] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:14:55.322 [nacos-grpc-client-executor-68] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Ack server push request, request = NotifySubscriberRequest, requestId = 10
10:38:33.592 [nacos-grpc-client-executor-1620] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Receive server push request, request = NotifySubscriberRequest, requestId = 15
10:38:33.610 [nacos-grpc-client-executor-1620] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Ack server push request, request = NotifySubscriberRequest, requestId = 15
10:39:05.621 [nacos-grpc-client-executor-1631] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Receive server push request, request = NotifySubscriberRequest, requestId = 19
10:39:05.641 [nacos-grpc-client-executor-1631] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Ack server push request, request = NotifySubscriberRequest, requestId = 19
10:46:18.273 [nacos-grpc-client-executor-1766] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Receive server push request, request = NotifySubscriberRequest, requestId = 22
10:46:18.274 [nacos-grpc-client-executor-1766] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Ack server push request, request = NotifySubscriberRequest, requestId = 22
10:49:54.893 [nacos-grpc-client-executor-1834] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Receive server push request, request = NotifySubscriberRequest, requestId = 26
10:49:54.910 [nacos-grpc-client-executor-1834] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Ack server push request, request = NotifySubscriberRequest, requestId = 26
11:08:50.068 [nacos-grpc-client-executor-2179] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Receive server push request, request = NotifySubscriberRequest, requestId = 29
11:08:50.080 [nacos-grpc-client-executor-2179] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Ack server push request, request = NotifySubscriberRequest, requestId = 29
11:09:11.869 [nacos-grpc-client-executor-2185] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Receive server push request, request = NotifySubscriberRequest, requestId = 33
11:09:11.888 [nacos-grpc-client-executor-2185] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [203fd20c-d098-404e-8e67-12be05e7b19c] Ack server push request, request = NotifySubscriberRequest, requestId = 33
11:41:03.851 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:41:03.855 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:41:04.166 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:41:04.166 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2a9a7ca1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:41:04.166 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753751638811_127.0.0.1_10490
11:41:04.168 [nacos-grpc-client-executor-2783] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753751638811_127.0.0.1_10490]Ignore complete event,isRunning:false,isAbandon=false
11:41:04.172 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4ee5aa23[Running, pool size = 8, active threads = 0, queued tasks = 0, completed tasks = 2784]
11:44:53.933 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:44:54.474 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e99e49c5-5eca-47c9-a53b-57ad6e952ba1_config-0
11:44:54.532 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 3 keys and 6 values 
11:44:54.564 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
11:44:54.575 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
11:44:54.583 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:44:54.591 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
11:44:54.600 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
11:44:54.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e99e49c5-5eca-47c9-a53b-57ad6e952ba1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:44:54.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e99e49c5-5eca-47c9-a53b-57ad6e952ba1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001dcc93bc2b8
11:44:54.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e99e49c5-5eca-47c9-a53b-57ad6e952ba1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001dcc93bc4d8
11:44:54.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e99e49c5-5eca-47c9-a53b-57ad6e952ba1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:44:54.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e99e49c5-5eca-47c9-a53b-57ad6e952ba1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:44:54.615 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e99e49c5-5eca-47c9-a53b-57ad6e952ba1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:44:55.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e99e49c5-5eca-47c9-a53b-57ad6e952ba1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753760695293_127.0.0.1_7225
11:44:55.488 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e99e49c5-5eca-47c9-a53b-57ad6e952ba1_config-0] Notify connected event to listeners.
11:44:55.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e99e49c5-5eca-47c9-a53b-57ad6e952ba1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:44:55.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e99e49c5-5eca-47c9-a53b-57ad6e952ba1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001dcc94f6780
11:44:55.575 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:44:58.987 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
11:45:00.163 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
11:45:00.797 [com.alibaba.nacos.client.Worker] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a6968f27-30fd-4651-933f-0072e3249cf7_config-0
11:45:00.797 [com.alibaba.nacos.client.Worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6968f27-30fd-4651-933f-0072e3249cf7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:45:00.797 [com.alibaba.nacos.client.Worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6968f27-30fd-4651-933f-0072e3249cf7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001dcc93bc2b8
11:45:00.797 [com.alibaba.nacos.client.Worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6968f27-30fd-4651-933f-0072e3249cf7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001dcc93bc4d8
11:45:00.798 [com.alibaba.nacos.client.Worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6968f27-30fd-4651-933f-0072e3249cf7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:45:00.798 [com.alibaba.nacos.client.Worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6968f27-30fd-4651-933f-0072e3249cf7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:45:00.799 [com.alibaba.nacos.client.Worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6968f27-30fd-4651-933f-0072e3249cf7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:45:00.937 [com.alibaba.nacos.client.Worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6968f27-30fd-4651-933f-0072e3249cf7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753760700819_127.0.0.1_7243
11:45:00.937 [com.alibaba.nacos.client.Worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6968f27-30fd-4651-933f-0072e3249cf7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:45:00.937 [com.alibaba.nacos.client.Worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6968f27-30fd-4651-933f-0072e3249cf7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001dcc94f6780
11:45:00.937 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6968f27-30fd-4651-933f-0072e3249cf7_config-0] Notify connected event to listeners.
11:45:01.103 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of aa29ad7a-bb40-4148-9707-1850486ff82b
11:45:01.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] RpcClient init label, labels = {module=naming, source=sdk}
11:45:01.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:45:01.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:45:01.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:45:01.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:45:01.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Success to connect to server [localhost:8848] on start up, connectionId = 1753760701123_127.0.0.1_7252
11:45:01.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:45:01.244 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Notify connected event to listeners.
11:45:01.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001dcc94f6780
11:45:01.666 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
11:45:01.697 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 8.219 seconds (JVM running for 9.014)
11:45:01.717 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
11:45:01.718 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
11:45:01.718 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
11:45:01.804 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 36
11:45:01.804 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 36
11:45:30.702 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 41
11:45:30.703 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 41
11:45:30.711 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 43
11:45:30.712 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 43
11:45:30.723 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 42
11:45:30.723 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 42
11:45:30.804 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 44
11:45:30.804 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 44
13:15:29.896 [nacos-grpc-client-executor-1821] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 45
13:15:29.913 [nacos-grpc-client-executor-1821] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 45
13:15:32.879 [nacos-grpc-client-executor-1825] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 47
13:15:32.896 [nacos-grpc-client-executor-1825] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 47
13:46:06.311 [nacos-grpc-client-executor-2425] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 48
13:46:06.328 [nacos-grpc-client-executor-2425] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 48
13:46:29.557 [nacos-grpc-client-executor-2430] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 50
13:46:29.572 [nacos-grpc-client-executor-2430] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 50
13:46:52.825 [nacos-grpc-client-executor-2439] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 51
13:46:52.828 [nacos-grpc-client-executor-2439] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 51
13:47:02.566 [nacos-grpc-client-executor-2441] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 52
13:47:02.576 [nacos-grpc-client-executor-2441] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 52
13:47:24.528 [nacos-grpc-client-executor-2449] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 55
13:47:24.543 [nacos-grpc-client-executor-2449] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 55
13:47:49.794 [nacos-grpc-client-executor-2460] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 56
13:47:49.811 [nacos-grpc-client-executor-2460] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 56
13:58:07.907 [nacos-grpc-client-executor-2659] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 58
13:58:07.925 [nacos-grpc-client-executor-2659] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 58
13:58:25.025 [nacos-grpc-client-executor-2663] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 60
13:58:25.037 [nacos-grpc-client-executor-2663] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 60
13:59:10.547 [nacos-grpc-client-executor-2677] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 61
13:59:10.559 [nacos-grpc-client-executor-2677] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 61
13:59:39.592 [nacos-grpc-client-executor-2687] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 62
13:59:39.609 [nacos-grpc-client-executor-2687] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 62
15:18:42.938 [nacos-grpc-client-executor-4232] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 64
15:18:42.954 [nacos-grpc-client-executor-4232] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 64
15:19:00.941 [nacos-grpc-client-executor-4236] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 65
15:19:00.957 [nacos-grpc-client-executor-4236] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 65
16:11:09.396 [nacos-grpc-client-executor-5239] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 67
16:11:09.416 [nacos-grpc-client-executor-5239] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 67
16:11:37.662 [nacos-grpc-client-executor-5249] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 68
16:11:37.706 [nacos-grpc-client-executor-5249] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 68
16:16:50.002 [nacos-grpc-client-executor-5350] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 70
16:16:50.025 [nacos-grpc-client-executor-5350] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 70
16:17:15.953 [nacos-grpc-client-executor-5359] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Receive server push request, request = NotifySubscriberRequest, requestId = 71
16:17:15.977 [nacos-grpc-client-executor-5359] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa29ad7a-bb40-4148-9707-1850486ff82b] Ack server push request, request = NotifySubscriberRequest, requestId = 71
20:27:07.901 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:27:07.901 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:27:08.235 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:27:08.235 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4e00370a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:27:08.235 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753760701123_127.0.0.1_7252
20:27:08.235 [nacos-grpc-client-executor-9874] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753760701123_127.0.0.1_7252]Ignore complete event,isRunning:false,isAbandon=false
20:27:08.251 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@23098228[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 9875]
