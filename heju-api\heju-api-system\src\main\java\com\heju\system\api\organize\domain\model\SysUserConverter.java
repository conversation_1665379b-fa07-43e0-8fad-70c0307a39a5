package com.heju.system.api.organize.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.domain.po.SysUserPo;
import com.heju.system.api.organize.domain.query.SysUserQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 用户 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysUserConverter extends BaseConverter<SysUserQuery, SysUserDto, SysUserPo> {
}
