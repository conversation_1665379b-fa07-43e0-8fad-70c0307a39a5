09:40:18.388 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:40:19.311 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0
09:40:19.437 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 52 ms to scan 1 urls, producing 3 keys and 6 values 
09:40:19.492 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 4 keys and 9 values 
09:40:19.505 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:40:19.519 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:40:19.536 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 7 values 
09:40:19.550 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:40:19.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:40:19.555 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001a7013b4fb8
09:40:19.555 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001a7013b51d8
09:40:19.556 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:40:19.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:40:19.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:20.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:20.797 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:20.805 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:40:20.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:40:20.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001a7014c5770
09:40:20.936 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:21.154 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:21.466 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:21.892 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:22.405 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:22.631 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:40:23.029 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:23.744 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:24.553 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:25.474 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:25.882 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:40:25.882 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:40:25.883 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:40:26.080 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:40:26.584 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:27.769 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:28.257 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:40:29.102 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:30.024 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9
09:40:30.024 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] RpcClient init label, labels = {module=naming, source=sdk}
09:40:30.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:40:30.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:40:30.029 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:40:30.029 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:30.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:30.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:30.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:40:30.062 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:40:30.063 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001a7014c5770
09:40:30.197 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:30.409 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:40:30.415 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:30.416 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:30.740 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:31.157 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:31.413 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:40:31.414 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@ccea4be[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:40:31.415 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cc8c089-0c0d-4bbd-aeee-d94929e4a4d9] Client is shutdown, stop reconnect to server
09:40:31.415 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@40f931ec[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:40:31.419 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2cf97652-73cb-4678-a133-29fffcefac43
09:40:31.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cf97652-73cb-4678-a133-29fffcefac43] RpcClient init label, labels = {module=naming, source=sdk}
09:40:31.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cf97652-73cb-4678-a133-29fffcefac43] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:40:31.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cf97652-73cb-4678-a133-29fffcefac43] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:40:31.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cf97652-73cb-4678-a133-29fffcefac43] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:40:31.421 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cf97652-73cb-4678-a133-29fffcefac43] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:31.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cf97652-73cb-4678-a133-29fffcefac43] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:31.445 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cf97652-73cb-4678-a133-29fffcefac43] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:31.452 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cf97652-73cb-4678-a133-29fffcefac43] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:40:31.452 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cf97652-73cb-4678-a133-29fffcefac43] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:40:31.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cf97652-73cb-4678-a133-29fffcefac43] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001a7014c5770
09:40:31.589 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cf97652-73cb-4678-a133-29fffcefac43] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:31.807 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cf97652-73cb-4678-a133-29fffcefac43] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:31.824 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
09:40:31.824 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:40:31.834 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
09:40:31.838 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
09:40:31.839 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e557c3c8-085f-4109-895f-350d2b5bd4cf_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:32.116 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cf97652-73cb-4678-a133-29fffcefac43] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:32.524 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cf97652-73cb-4678-a133-29fffcefac43] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:53.707 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:40:54.591 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 093318bc-2031-438c-b870-10c43971978f_config-0
09:40:54.677 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
09:40:54.705 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:40:54.715 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:40:54.729 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:40:54.739 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:40:54.755 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:40:54.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [093318bc-2031-438c-b870-10c43971978f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:40:54.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [093318bc-2031-438c-b870-10c43971978f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001970139a328
09:40:54.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [093318bc-2031-438c-b870-10c43971978f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001970139a548
09:40:54.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [093318bc-2031-438c-b870-10c43971978f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:40:54.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [093318bc-2031-438c-b870-10c43971978f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:40:54.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [093318bc-2031-438c-b870-10c43971978f_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:56.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [093318bc-2031-438c-b870-10c43971978f_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:56.151 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [093318bc-2031-438c-b870-10c43971978f_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:40:56.176 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [093318bc-2031-438c-b870-10c43971978f_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:40:56.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [093318bc-2031-438c-b870-10c43971978f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:40:56.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [093318bc-2031-438c-b870-10c43971978f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000019701514ea8
09:40:56.330 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [093318bc-2031-438c-b870-10c43971978f_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:40:56.695 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [093318bc-2031-438c-b870-10c43971978f_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1751506856551_127.0.0.1_14856
09:40:56.696 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [093318bc-2031-438c-b870-10c43971978f_config-0] Notify connected event to listeners.
09:40:56.931 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:40:59.909 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:40:59.910 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:40:59.910 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:41:00.118 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:41:02.387 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:41:04.243 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1
09:41:04.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] RpcClient init label, labels = {module=naming, source=sdk}
09:41:04.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:41:04.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:41:04.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:41:04.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:41:04.386 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751506864260_127.0.0.1_14944
09:41:04.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:41:04.387 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Notify connected event to listeners.
09:41:04.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000019701514ea8
09:41:04.454 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:41:04.497 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:41:04.688 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 11.801 seconds (JVM running for 13.141)
09:41:04.697 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:41:04.697 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:41:04.710 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:41:05.019 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:41:05.045 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:41:05.097 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:43:03.828 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:43:03.829 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:43:05.897 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:43:05.897 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 14
09:55:01.102 [nacos-grpc-client-executor-196] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 16
09:55:01.128 [nacos-grpc-client-executor-196] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 16
09:55:32.019 [nacos-grpc-client-executor-204] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 25
09:55:32.045 [nacos-grpc-client-executor-204] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 25
10:12:59.180 [nacos-grpc-client-executor-429] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 44
10:12:59.205 [nacos-grpc-client-executor-429] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 44
10:13:31.727 [nacos-grpc-client-executor-437] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 48
10:13:31.749 [nacos-grpc-client-executor-437] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 48
10:15:15.104 [nacos-grpc-client-executor-461] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 57
10:15:15.129 [nacos-grpc-client-executor-461] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 57
10:15:55.422 [nacos-grpc-client-executor-469] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 61
10:15:55.447 [nacos-grpc-client-executor-469] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 61
10:33:00.421 [nacos-grpc-client-executor-690] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 72
10:33:00.445 [nacos-grpc-client-executor-690] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 72
10:33:37.588 [nacos-grpc-client-executor-699] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 76
10:33:37.610 [nacos-grpc-client-executor-699] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 76
10:40:02.479 [nacos-grpc-client-executor-782] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 79
10:40:02.501 [nacos-grpc-client-executor-782] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 79
10:40:33.295 [nacos-grpc-client-executor-789] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 83
10:40:33.346 [nacos-grpc-client-executor-789] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 83
10:42:59.138 [nacos-grpc-client-executor-820] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 86
10:42:59.199 [nacos-grpc-client-executor-820] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 86
10:43:19.251 [nacos-grpc-client-executor-825] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 90
10:43:19.311 [nacos-grpc-client-executor-825] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 90
11:23:38.615 [nacos-grpc-client-executor-1341] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 93
11:23:38.642 [nacos-grpc-client-executor-1341] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 93
11:24:07.539 [nacos-grpc-client-executor-1349] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 97
11:24:07.640 [nacos-grpc-client-executor-1349] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 97
11:45:55.843 [nacos-grpc-client-executor-1636] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 101
11:45:55.873 [nacos-grpc-client-executor-1636] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 101
11:46:44.354 [nacos-grpc-client-executor-1647] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 104
11:46:44.400 [nacos-grpc-client-executor-1647] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 104
12:00:23.751 [nacos-grpc-client-executor-1824] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 106
12:00:23.774 [nacos-grpc-client-executor-1824] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 106
12:01:00.142 [nacos-grpc-client-executor-1831] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 109
12:01:00.178 [nacos-grpc-client-executor-1831] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 109
12:15:51.444 [nacos-grpc-client-executor-2018] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 128
12:15:51.464 [nacos-grpc-client-executor-2018] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 128
12:16:23.187 [nacos-grpc-client-executor-2026] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 136
12:16:23.193 [nacos-grpc-client-executor-2026] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 136
12:25:09.753 [nacos-grpc-client-executor-2142] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 141
12:25:09.781 [nacos-grpc-client-executor-2142] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 141
12:25:41.657 [nacos-grpc-client-executor-2149] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Receive server push request, request = NotifySubscriberRequest, requestId = 146
12:25:41.681 [nacos-grpc-client-executor-2149] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf63de42-9fc1-4e73-9bf8-30be1ff5fbc1] Ack server push request, request = NotifySubscriberRequest, requestId = 146
12:32:13.980 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:32:13.989 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:32:14.326 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:32:14.326 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@c76c240[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:32:14.326 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751506864260_127.0.0.1_14944
12:32:14.327 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3d3a98b9[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2236]
12:33:04.785 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:33:05.594 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7784a19c-5354-4009-a7a3-cff24775502d_config-0
12:33:05.672 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
12:33:05.717 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
12:33:05.728 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
12:33:05.741 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
12:33:05.751 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
12:33:05.763 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
12:33:05.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7784a19c-5354-4009-a7a3-cff24775502d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:33:05.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7784a19c-5354-4009-a7a3-cff24775502d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000015e6739af18
12:33:05.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7784a19c-5354-4009-a7a3-cff24775502d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000015e6739b138
12:33:05.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7784a19c-5354-4009-a7a3-cff24775502d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:33:05.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7784a19c-5354-4009-a7a3-cff24775502d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:33:05.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7784a19c-5354-4009-a7a3-cff24775502d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:33:07.164 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7784a19c-5354-4009-a7a3-cff24775502d_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751517186939_127.0.0.1_11167
12:33:07.166 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7784a19c-5354-4009-a7a3-cff24775502d_config-0] Notify connected event to listeners.
12:33:07.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7784a19c-5354-4009-a7a3-cff24775502d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:33:07.168 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7784a19c-5354-4009-a7a3-cff24775502d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000015e67514d90
12:33:07.300 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:33:10.214 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
12:33:10.215 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:33:10.215 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:33:10.402 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:33:12.619 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:33:14.254 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 921b38c1-208a-48d6-b2da-b1ea02f69c4b
12:33:14.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921b38c1-208a-48d6-b2da-b1ea02f69c4b] RpcClient init label, labels = {module=naming, source=sdk}
12:33:14.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921b38c1-208a-48d6-b2da-b1ea02f69c4b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:33:14.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921b38c1-208a-48d6-b2da-b1ea02f69c4b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:33:14.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921b38c1-208a-48d6-b2da-b1ea02f69c4b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:33:14.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921b38c1-208a-48d6-b2da-b1ea02f69c4b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:33:14.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921b38c1-208a-48d6-b2da-b1ea02f69c4b] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751517194273_127.0.0.1_11235
12:33:14.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921b38c1-208a-48d6-b2da-b1ea02f69c4b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:33:14.393 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921b38c1-208a-48d6-b2da-b1ea02f69c4b] Notify connected event to listeners.
12:33:14.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921b38c1-208a-48d6-b2da-b1ea02f69c4b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000015e67514d90
12:33:14.455 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
12:33:14.490 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
12:33:14.656 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 10.711 seconds (JVM running for 12.022)
12:33:14.669 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
12:33:14.669 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
12:33:14.689 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
12:33:14.779 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:33:14.993 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921b38c1-208a-48d6-b2da-b1ea02f69c4b] Receive server push request, request = NotifySubscriberRequest, requestId = 156
12:33:15.015 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921b38c1-208a-48d6-b2da-b1ea02f69c4b] Ack server push request, request = NotifySubscriberRequest, requestId = 156
12:40:32.331 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:40:32.339 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:40:32.673 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:40:32.674 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4b5dfe62[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:40:32.674 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751517194273_127.0.0.1_11235
12:40:32.676 [nacos-grpc-client-executor-97] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751517194273_127.0.0.1_11235]Ignore complete event,isRunning:false,isAbandon=false
12:40:32.679 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5db968b1[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 98]
12:40:36.905 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:40:37.691 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 711bebaf-5ba4-4ab5-9faa-2ac034df3f7c_config-0
12:40:37.766 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
12:40:37.813 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 4 keys and 9 values 
12:40:37.813 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
12:40:37.831 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 5 values 
12:40:37.845 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
12:40:37.860 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
12:40:37.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [711bebaf-5ba4-4ab5-9faa-2ac034df3f7c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:40:37.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [711bebaf-5ba4-4ab5-9faa-2ac034df3f7c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000025f9539b188
12:40:37.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [711bebaf-5ba4-4ab5-9faa-2ac034df3f7c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000025f9539b3a8
12:40:37.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [711bebaf-5ba4-4ab5-9faa-2ac034df3f7c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:40:37.869 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [711bebaf-5ba4-4ab5-9faa-2ac034df3f7c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:40:37.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [711bebaf-5ba4-4ab5-9faa-2ac034df3f7c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:40:39.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [711bebaf-5ba4-4ab5-9faa-2ac034df3f7c_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751517638886_127.0.0.1_13287
12:40:39.128 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [711bebaf-5ba4-4ab5-9faa-2ac034df3f7c_config-0] Notify connected event to listeners.
12:40:39.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [711bebaf-5ba4-4ab5-9faa-2ac034df3f7c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:40:39.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [711bebaf-5ba4-4ab5-9faa-2ac034df3f7c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025f95514d90
12:40:39.285 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:40:42.044 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
12:40:42.044 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:40:42.044 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:40:42.220 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:40:44.104 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:40:45.729 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bc208ab1-4db1-42f7-ae4e-c2cd454c3a07
12:40:45.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc208ab1-4db1-42f7-ae4e-c2cd454c3a07] RpcClient init label, labels = {module=naming, source=sdk}
12:40:45.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc208ab1-4db1-42f7-ae4e-c2cd454c3a07] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:40:45.733 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc208ab1-4db1-42f7-ae4e-c2cd454c3a07] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:40:45.734 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc208ab1-4db1-42f7-ae4e-c2cd454c3a07] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:40:45.734 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc208ab1-4db1-42f7-ae4e-c2cd454c3a07] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:40:45.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc208ab1-4db1-42f7-ae4e-c2cd454c3a07] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751517645771_127.0.0.1_13331
12:40:45.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc208ab1-4db1-42f7-ae4e-c2cd454c3a07] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:40:45.886 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc208ab1-4db1-42f7-ae4e-c2cd454c3a07] Notify connected event to listeners.
12:40:45.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc208ab1-4db1-42f7-ae4e-c2cd454c3a07] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025f95514d90
12:40:45.947 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
12:40:45.987 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
12:40:46.180 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 10.099 seconds (JVM running for 11.555)
12:40:46.200 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
12:40:46.203 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
12:40:46.209 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
12:40:46.422 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:40:46.450 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc208ab1-4db1-42f7-ae4e-c2cd454c3a07] Receive server push request, request = NotifySubscriberRequest, requestId = 170
12:40:46.472 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc208ab1-4db1-42f7-ae4e-c2cd454c3a07] Ack server push request, request = NotifySubscriberRequest, requestId = 170
13:33:05.827 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:33:05.832 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:33:06.168 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:33:06.168 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4a26b252[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:33:06.168 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751517645771_127.0.0.1_13331
13:33:06.171 [nacos-grpc-client-executor-636] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751517645771_127.0.0.1_13331]Ignore complete event,isRunning:false,isAbandon=false
13:33:06.174 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7d1e19d9[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 637]
14:04:00.920 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:04:01.821 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0
14:04:01.903 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
14:04:01.935 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:04:01.948 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:04:01.962 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:04:01.975 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:04:01.987 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:04:01.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:04:01.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000281da3ba7e8
14:04:01.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000281da3baa08
14:04:01.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:04:01.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:04:02.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:05.795 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:08.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:11.827 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:04:11.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:04:11.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000281da4c97e0
14:04:13.568 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:04:16.334 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
14:04:16.335 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:04:16.335 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:04:16.525 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:04:17.975 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:18.810 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:04:20.457 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d06060b0-1f6b-49e3-aaf5-e4d895b97a68
14:04:20.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d06060b0-1f6b-49e3-aaf5-e4d895b97a68] RpcClient init label, labels = {module=naming, source=sdk}
14:04:20.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d06060b0-1f6b-49e3-aaf5-e4d895b97a68] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:04:20.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d06060b0-1f6b-49e3-aaf5-e4d895b97a68] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:04:20.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d06060b0-1f6b-49e3-aaf5-e4d895b97a68] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:04:20.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d06060b0-1f6b-49e3-aaf5-e4d895b97a68] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:21.194 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:23.480 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d06060b0-1f6b-49e3-aaf5-e4d895b97a68] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:24.507 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:26.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d06060b0-1f6b-49e3-aaf5-e4d895b97a68] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:27.923 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:29.505 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d06060b0-1f6b-49e3-aaf5-e4d895b97a68] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:04:29.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d06060b0-1f6b-49e3-aaf5-e4d895b97a68] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:04:29.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d06060b0-1f6b-49e3-aaf5-e4d895b97a68] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000281da4c97e0
14:04:29.854 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
14:04:30.768 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:04:30.769 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5c8d685e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:04:30.769 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4b1ad751[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3]
14:04:30.773 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f1105312-7963-4a2f-b597-e409812aa64d
14:04:30.774 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1105312-7963-4a2f-b597-e409812aa64d] RpcClient init label, labels = {module=naming, source=sdk}
14:04:30.774 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1105312-7963-4a2f-b597-e409812aa64d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:04:30.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1105312-7963-4a2f-b597-e409812aa64d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:04:30.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1105312-7963-4a2f-b597-e409812aa64d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:04:30.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1105312-7963-4a2f-b597-e409812aa64d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:30.872 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d06060b0-1f6b-49e3-aaf5-e4d895b97a68] Client is shutdown, stop reconnect to server
14:04:31.449 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:33.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1105312-7963-4a2f-b597-e409812aa64d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:35.064 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:36.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1105312-7963-4a2f-b597-e409812aa64d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:38.783 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f102aa1-942c-4a23-88e4-0344d1d2bcd5_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:39.827 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1105312-7963-4a2f-b597-e409812aa64d] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:04:39.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1105312-7963-4a2f-b597-e409812aa64d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:04:39.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1105312-7963-4a2f-b597-e409812aa64d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000281da4c97e0
14:04:40.228 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
14:04:40.229 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:04:40.240 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
14:04:40.246 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
14:10:01.515 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:10:02.598 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0
14:10:02.700 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 51 ms to scan 1 urls, producing 3 keys and 6 values 
14:10:02.752 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 4 keys and 9 values 
14:10:02.770 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
14:10:02.788 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
14:10:02.808 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
14:10:02.827 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
14:10:02.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:10:02.833 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000190d83b2328
14:10:02.834 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000190d83b2548
14:10:02.835 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:10:02.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:10:02.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:06.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:09.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:12.936 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:10:12.937 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:12.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000190d84c1800
14:10:14.705 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:10:17.483 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
14:10:17.485 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:10:17.485 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:10:17.680 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:10:19.069 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:19.892 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:10:21.771 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2006b74f-5d5f-4771-98e6-d48ae0ce147c
14:10:21.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2006b74f-5d5f-4771-98e6-d48ae0ce147c] RpcClient init label, labels = {module=naming, source=sdk}
14:10:21.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2006b74f-5d5f-4771-98e6-d48ae0ce147c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:10:21.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2006b74f-5d5f-4771-98e6-d48ae0ce147c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:10:21.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2006b74f-5d5f-4771-98e6-d48ae0ce147c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:10:21.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2006b74f-5d5f-4771-98e6-d48ae0ce147c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:22.295 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:24.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2006b74f-5d5f-4771-98e6-d48ae0ce147c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:25.610 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:27.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2006b74f-5d5f-4771-98e6-d48ae0ce147c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:29.021 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:30.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2006b74f-5d5f-4771-98e6-d48ae0ce147c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:30.842 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2006b74f-5d5f-4771-98e6-d48ae0ce147c] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:10:30.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2006b74f-5d5f-4771-98e6-d48ae0ce147c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000190d84c1800
14:10:31.187 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
14:10:32.077 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:10:32.077 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@570ede3b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:10:32.080 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@c22cf4d[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3]
14:10:32.088 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b5b577d8-a85f-41f6-b985-4627fb68b736
14:10:32.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b577d8-a85f-41f6-b985-4627fb68b736] RpcClient init label, labels = {module=naming, source=sdk}
14:10:32.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b577d8-a85f-41f6-b985-4627fb68b736] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:10:32.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b577d8-a85f-41f6-b985-4627fb68b736] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:10:32.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b577d8-a85f-41f6-b985-4627fb68b736] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:10:32.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b577d8-a85f-41f6-b985-4627fb68b736] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:32.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2006b74f-5d5f-4771-98e6-d48ae0ce147c] Client is shutdown, stop reconnect to server
14:10:32.551 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:35.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b577d8-a85f-41f6-b985-4627fb68b736] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:36.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:38.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b577d8-a85f-41f6-b985-4627fb68b736] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:39.907 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24155c7b-d5c2-486a-aaf9-69f9e8d9d720_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:41.137 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b577d8-a85f-41f6-b985-4627fb68b736] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:41.138 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b577d8-a85f-41f6-b985-4627fb68b736] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000190d84c1800
14:10:41.138 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5b577d8-a85f-41f6-b985-4627fb68b736] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:10:41.526 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
14:10:41.526 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:10:41.534 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
14:10:41.539 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
14:27:43.994 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:27:44.774 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0
14:27:44.857 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
14:27:44.891 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:27:44.902 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
14:27:44.916 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:27:44.928 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:27:44.942 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
14:27:44.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:27:44.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000231ce3baa48
14:27:44.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000231ce3bac68
14:27:44.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:27:44.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:27:44.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:27:48.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:27:51.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:27:54.851 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:27:54.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:54.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000231ce4c97e0
14:27:56.575 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:27:59.140 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
14:27:59.141 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:27:59.141 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:27:59.314 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:28:00.979 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:01.333 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:28:02.964 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3a35d3dd-ad03-42ff-b7d7-e27d2311e552
14:28:02.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a35d3dd-ad03-42ff-b7d7-e27d2311e552] RpcClient init label, labels = {module=naming, source=sdk}
14:28:02.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a35d3dd-ad03-42ff-b7d7-e27d2311e552] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:28:02.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a35d3dd-ad03-42ff-b7d7-e27d2311e552] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:28:02.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a35d3dd-ad03-42ff-b7d7-e27d2311e552] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:28:02.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a35d3dd-ad03-42ff-b7d7-e27d2311e552] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:04.209 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:05.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a35d3dd-ad03-42ff-b7d7-e27d2311e552] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:07.529 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:08.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a35d3dd-ad03-42ff-b7d7-e27d2311e552] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:10.951 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:12.008 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a35d3dd-ad03-42ff-b7d7-e27d2311e552] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:28:12.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a35d3dd-ad03-42ff-b7d7-e27d2311e552] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:28:12.009 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a35d3dd-ad03-42ff-b7d7-e27d2311e552] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000231ce4c97e0
14:28:12.352 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
14:28:13.285 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:28:13.285 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@562ec518[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:28:13.285 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5c8d685e[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3]
14:28:13.289 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6b095f1c-3c35-4ddc-b125-faead08c1e71
14:28:13.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b095f1c-3c35-4ddc-b125-faead08c1e71] RpcClient init label, labels = {module=naming, source=sdk}
14:28:13.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b095f1c-3c35-4ddc-b125-faead08c1e71] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:28:13.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b095f1c-3c35-4ddc-b125-faead08c1e71] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:28:13.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b095f1c-3c35-4ddc-b125-faead08c1e71] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:28:13.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b095f1c-3c35-4ddc-b125-faead08c1e71] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:13.393 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a35d3dd-ad03-42ff-b7d7-e27d2311e552] Client is shutdown, stop reconnect to server
14:28:14.468 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:16.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b095f1c-3c35-4ddc-b125-faead08c1e71] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:18.084 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:19.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b095f1c-3c35-4ddc-b125-faead08c1e71] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:21.814 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de7cc3be-5abe-4fa5-b818-b2049170b86b_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:22.328 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b095f1c-3c35-4ddc-b125-faead08c1e71] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:28:22.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b095f1c-3c35-4ddc-b125-faead08c1e71] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:28:22.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b095f1c-3c35-4ddc-b125-faead08c1e71] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000231ce4c97e0
14:28:22.716 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
14:28:22.717 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:28:22.727 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
14:28:22.733 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
14:33:47.932 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:33:48.765 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1884aefb-ed2d-4b12-99c3-2d9906927da5_config-0
14:33:48.846 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
14:33:48.888 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
14:33:48.903 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
14:33:48.919 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
14:33:48.936 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
14:33:48.952 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
14:33:48.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1884aefb-ed2d-4b12-99c3-2d9906927da5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:33:48.987 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1884aefb-ed2d-4b12-99c3-2d9906927da5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001ff353b27e8
14:33:48.987 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1884aefb-ed2d-4b12-99c3-2d9906927da5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001ff353b2a08
14:33:48.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1884aefb-ed2d-4b12-99c3-2d9906927da5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:33:48.989 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1884aefb-ed2d-4b12-99c3-2d9906927da5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:33:49.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1884aefb-ed2d-4b12-99c3-2d9906927da5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:33:50.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1884aefb-ed2d-4b12-99c3-2d9906927da5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751524430093_127.0.0.1_6442
14:33:50.340 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1884aefb-ed2d-4b12-99c3-2d9906927da5_config-0] Notify connected event to listeners.
14:33:50.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1884aefb-ed2d-4b12-99c3-2d9906927da5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:33:50.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1884aefb-ed2d-4b12-99c3-2d9906927da5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001ff354ec200
14:33:50.487 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:33:53.872 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
14:33:53.874 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:33:53.875 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:33:54.084 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:33:56.021 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:33:57.570 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f26bb462-b444-4288-a151-ee3e9f29f112
14:33:57.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f26bb462-b444-4288-a151-ee3e9f29f112] RpcClient init label, labels = {module=naming, source=sdk}
14:33:57.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f26bb462-b444-4288-a151-ee3e9f29f112] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:33:57.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f26bb462-b444-4288-a151-ee3e9f29f112] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:33:57.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f26bb462-b444-4288-a151-ee3e9f29f112] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:33:57.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f26bb462-b444-4288-a151-ee3e9f29f112] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:33:57.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f26bb462-b444-4288-a151-ee3e9f29f112] Success to connect to server [localhost:8848] on start up, connectionId = 1751524437588_127.0.0.1_6523
14:33:57.709 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f26bb462-b444-4288-a151-ee3e9f29f112] Notify connected event to listeners.
14:33:57.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f26bb462-b444-4288-a151-ee3e9f29f112] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:33:57.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f26bb462-b444-4288-a151-ee3e9f29f112] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001ff354ec200
14:33:57.772 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
14:33:57.812 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
14:33:57.983 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 10.953 seconds (JVM running for 12.408)
14:33:58.000 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
14:33:58.001 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
14:33:58.027 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
14:33:58.308 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f26bb462-b444-4288-a151-ee3e9f29f112] Receive server push request, request = NotifySubscriberRequest, requestId = 174
14:33:58.332 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f26bb462-b444-4288-a151-ee3e9f29f112] Ack server push request, request = NotifySubscriberRequest, requestId = 174
16:14:21.878 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:14:21.881 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:14:22.207 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:14:22.207 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4418badb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:14:22.207 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751524437588_127.0.0.1_6523
16:14:22.210 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4686902a[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 1215]
16:50:43.399 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:50:44.090 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4e073ec9-de51-4e32-8c20-5f60be497820_config-0
16:50:44.161 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
16:50:44.179 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
16:50:44.188 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
16:50:44.200 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
16:50:44.216 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
16:50:44.226 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
16:50:44.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:50:44.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001f581399ed0
16:50:44.231 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f58139a0f0
16:50:44.231 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:50:44.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:50:44.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:50:45.231 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751532645030_127.0.0.1_3661
16:50:45.235 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] Notify connected event to listeners.
16:50:45.239 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:50:45.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001f581513ff8
16:50:45.347 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:50:47.894 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
16:50:47.895 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:50:47.895 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:50:48.206 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:50:59.635 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:51:05.653 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 65f2590e-9df0-48b0-9b6f-840fe85947eb
16:51:05.654 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] RpcClient init label, labels = {module=naming, source=sdk}
16:51:05.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:51:05.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:51:05.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:51:05.666 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:51:05.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Success to connect to server [localhost:8848] on start up, connectionId = 1751532665698_127.0.0.1_3761
16:51:05.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:51:05.843 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Notify connected event to listeners.
16:51:05.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001f581513ff8
16:51:06.015 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
16:51:06.100 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
16:51:06.455 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Receive server push request, request = NotifySubscriberRequest, requestId = 280
16:51:06.496 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Ack server push request, request = NotifySubscriberRequest, requestId = 280
16:51:06.713 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 23.946 seconds (JVM running for 24.919)
16:51:06.751 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
16:51:06.756 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
16:51:06.767 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
16:51:07.071 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:21:48.440 [nacos-grpc-client-executor-2535] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Receive server push request, request = NotifySubscriberRequest, requestId = 314
20:21:48.441 [nacos-grpc-client-executor-2535] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Ack server push request, request = NotifySubscriberRequest, requestId = 314
20:22:05.112 [nacos-grpc-client-executor-2541] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Receive server push request, request = NotifySubscriberRequest, requestId = 315
20:22:05.151 [nacos-grpc-client-executor-2541] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Ack server push request, request = NotifySubscriberRequest, requestId = 315
20:24:57.093 [nacos-grpc-client-executor-2577] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Receive server push request, request = NotifySubscriberRequest, requestId = 318
20:24:57.095 [nacos-grpc-client-executor-2577] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Ack server push request, request = NotifySubscriberRequest, requestId = 318
20:26:53.202 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] Server healthy check fail, currentConnection = 1751532645030_127.0.0.1_3661
20:26:53.202 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:26:53.772 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] Success to connect a server [localhost:8848], connectionId = 1751545613594_127.0.0.1_13437
20:26:53.822 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751532645030_127.0.0.1_3661
20:26:53.831 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751532645030_127.0.0.1_3661
20:26:53.868 [nacos-grpc-client-executor-2598] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751532645030_127.0.0.1_3661]Ignore complete event,isRunning:false,isAbandon=true
20:26:53.870 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] Notify disconnected event to listeners
20:26:53.872 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e073ec9-de51-4e32-8c20-5f60be497820_config-0] Notify connected event to listeners.
20:26:54.418 [nacos-grpc-client-executor-2601] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Receive server push request, request = NotifySubscriberRequest, requestId = 320
20:26:54.434 [nacos-grpc-client-executor-2601] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Ack server push request, request = NotifySubscriberRequest, requestId = 320
20:26:56.956 [nacos-grpc-client-executor-2602] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Receive server push request, request = NotifySubscriberRequest, requestId = 322
20:26:56.984 [nacos-grpc-client-executor-2602] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65f2590e-9df0-48b0-9b6f-840fe85947eb] Ack server push request, request = NotifySubscriberRequest, requestId = 322
20:46:23.725 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:46:23.736 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:46:24.068 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:46:24.069 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4ba12c9b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:46:24.069 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751532665698_127.0.0.1_3761
20:46:24.069 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@56a35fbc[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 2837]
