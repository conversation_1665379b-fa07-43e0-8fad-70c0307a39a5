package com.heju.job.api.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.job.api.domain.dto.SysJobDto;
import com.heju.job.api.domain.po.SysJobPo;
import com.heju.job.api.domain.query.SysJobQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 调度任务 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysJobConverter extends BaseConverter<SysJobQuery, SysJobDto, SysJobPo> {
}
