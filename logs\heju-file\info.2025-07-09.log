09:49:34.564 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:49:35.233 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1f3629cc-618e-41d1-a6ad-46a79c32eb7f_config-0
09:49:35.297 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 33 ms to scan 1 urls, producing 3 keys and 6 values 
09:49:35.323 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:49:35.331 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 3 keys and 10 values 
09:49:35.343 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:49:35.353 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:49:35.367 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:49:35.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3629cc-618e-41d1-a6ad-46a79c32eb7f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:49:35.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3629cc-618e-41d1-a6ad-46a79c32eb7f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000021d4c396d88
09:49:35.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3629cc-618e-41d1-a6ad-46a79c32eb7f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000021d4c396fa8
09:49:35.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3629cc-618e-41d1-a6ad-46a79c32eb7f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:49:35.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3629cc-618e-41d1-a6ad-46a79c32eb7f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:49:35.383 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3629cc-618e-41d1-a6ad-46a79c32eb7f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:49:36.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3629cc-618e-41d1-a6ad-46a79c32eb7f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752025776144_127.0.0.1_8027
09:49:36.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3629cc-618e-41d1-a6ad-46a79c32eb7f_config-0] Notify connected event to listeners.
09:49:36.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3629cc-618e-41d1-a6ad-46a79c32eb7f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:49:36.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3629cc-618e-41d1-a6ad-46a79c32eb7f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000021d4c510ad8
09:49:36.497 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:49:38.745 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:49:38.746 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:49:38.746 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:49:38.887 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:49:40.683 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:49:43.645 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 25c5a5fc-1ad2-4426-8ab2-e2d80a950336
09:49:43.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25c5a5fc-1ad2-4426-8ab2-e2d80a950336] RpcClient init label, labels = {module=naming, source=sdk}
09:49:43.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25c5a5fc-1ad2-4426-8ab2-e2d80a950336] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:49:43.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25c5a5fc-1ad2-4426-8ab2-e2d80a950336] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:49:43.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25c5a5fc-1ad2-4426-8ab2-e2d80a950336] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:49:43.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25c5a5fc-1ad2-4426-8ab2-e2d80a950336] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:49:43.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25c5a5fc-1ad2-4426-8ab2-e2d80a950336] Success to connect to server [localhost:8848] on start up, connectionId = 1752025783668_127.0.0.1_8049
09:49:43.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25c5a5fc-1ad2-4426-8ab2-e2d80a950336] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:49:43.793 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25c5a5fc-1ad2-4426-8ab2-e2d80a950336] Notify connected event to listeners.
09:49:43.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25c5a5fc-1ad2-4426-8ab2-e2d80a950336] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000021d4c510ad8
09:49:43.859 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:49:43.899 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ***********:9300 register finished
09:49:44.100 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 10.179 seconds (JVM running for 11.216)
09:49:44.118 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:49:44.120 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:49:44.125 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:49:44.412 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25c5a5fc-1ad2-4426-8ab2-e2d80a950336] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:49:44.433 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25c5a5fc-1ad2-4426-8ab2-e2d80a950336] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:49:44.649 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:55:10.290 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:55:10.821 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9127fc11-3cbe-4cf7-bc80-10d09ca4ff9c_config-0
09:55:10.887 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
09:55:10.912 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:55:10.923 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:55:10.934 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:55:10.943 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
09:55:10.958 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:55:10.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9127fc11-3cbe-4cf7-bc80-10d09ca4ff9c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:55:10.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9127fc11-3cbe-4cf7-bc80-10d09ca4ff9c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001fde03af470
09:55:10.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9127fc11-3cbe-4cf7-bc80-10d09ca4ff9c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001fde03af690
09:55:10.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9127fc11-3cbe-4cf7-bc80-10d09ca4ff9c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:55:10.966 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9127fc11-3cbe-4cf7-bc80-10d09ca4ff9c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:55:10.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9127fc11-3cbe-4cf7-bc80-10d09ca4ff9c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:55:11.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9127fc11-3cbe-4cf7-bc80-10d09ca4ff9c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752026111690_127.0.0.1_8464
09:55:11.913 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9127fc11-3cbe-4cf7-bc80-10d09ca4ff9c_config-0] Notify connected event to listeners.
09:55:11.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9127fc11-3cbe-4cf7-bc80-10d09ca4ff9c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:55:11.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9127fc11-3cbe-4cf7-bc80-10d09ca4ff9c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001fde04e8fb0
09:55:12.072 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:55:14.274 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:55:14.276 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:55:14.276 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:55:14.427 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:55:15.757 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:55:18.045 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 99d2a153-5a7b-4470-b930-8d6b6ac263de
09:55:18.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99d2a153-5a7b-4470-b930-8d6b6ac263de] RpcClient init label, labels = {module=naming, source=sdk}
09:55:18.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99d2a153-5a7b-4470-b930-8d6b6ac263de] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:55:18.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99d2a153-5a7b-4470-b930-8d6b6ac263de] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:55:18.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99d2a153-5a7b-4470-b930-8d6b6ac263de] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:55:18.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99d2a153-5a7b-4470-b930-8d6b6ac263de] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:55:18.188 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99d2a153-5a7b-4470-b930-8d6b6ac263de] Success to connect to server [localhost:8848] on start up, connectionId = 1752026118061_127.0.0.1_8475
09:55:18.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99d2a153-5a7b-4470-b930-8d6b6ac263de] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:55:18.188 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99d2a153-5a7b-4470-b930-8d6b6ac263de] Notify connected event to listeners.
09:55:18.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99d2a153-5a7b-4470-b930-8d6b6ac263de] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001fde04e8fb0
09:55:18.249 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:55:18.278 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ***********:9300 register finished
09:55:18.429 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 8.742 seconds (JVM running for 9.631)
09:55:18.442 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:55:18.443 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:55:18.448 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:55:18.746 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99d2a153-5a7b-4470-b930-8d6b6ac263de] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:55:18.765 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99d2a153-5a7b-4470-b930-8d6b6ac263de] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:55:18.995 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:56:53.867 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:56:53.877 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:56:54.197 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:56:54.197 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@693002e7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:56:54.197 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752026118061_127.0.0.1_8475
09:56:54.199 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752026118061_127.0.0.1_8475]Ignore complete event,isRunning:false,isAbandon=false
09:56:54.201 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5a654b47[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 32]
09:57:20.906 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:57:21.551 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e135f6f0-d09e-4aca-848e-8ec38851e583_config-0
09:57:21.618 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 3 keys and 6 values 
09:57:21.644 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:57:21.654 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:57:21.665 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:57:21.683 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
09:57:21.695 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:57:21.700 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e135f6f0-d09e-4aca-848e-8ec38851e583_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:57:21.700 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e135f6f0-d09e-4aca-848e-8ec38851e583_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001ebda3aed88
09:57:21.701 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e135f6f0-d09e-4aca-848e-8ec38851e583_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001ebda3aefa8
09:57:21.701 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e135f6f0-d09e-4aca-848e-8ec38851e583_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:57:21.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e135f6f0-d09e-4aca-848e-8ec38851e583_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:57:21.714 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e135f6f0-d09e-4aca-848e-8ec38851e583_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:57:22.733 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e135f6f0-d09e-4aca-848e-8ec38851e583_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752026242515_127.0.0.1_8900
09:57:22.734 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e135f6f0-d09e-4aca-848e-8ec38851e583_config-0] Notify connected event to listeners.
09:57:22.734 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e135f6f0-d09e-4aca-848e-8ec38851e583_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:57:22.735 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e135f6f0-d09e-4aca-848e-8ec38851e583_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001ebda4e8668
09:57:22.832 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:57:26.233 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:57:26.233 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:57:26.234 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:57:26.451 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:57:28.611 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:57:33.225 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7fe093f6-8976-412c-827d-ed9fe6eebda9
09:57:33.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fe093f6-8976-412c-827d-ed9fe6eebda9] RpcClient init label, labels = {module=naming, source=sdk}
09:57:33.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fe093f6-8976-412c-827d-ed9fe6eebda9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:57:33.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fe093f6-8976-412c-827d-ed9fe6eebda9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:57:33.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fe093f6-8976-412c-827d-ed9fe6eebda9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:57:33.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fe093f6-8976-412c-827d-ed9fe6eebda9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:57:33.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fe093f6-8976-412c-827d-ed9fe6eebda9] Success to connect to server [localhost:8848] on start up, connectionId = 1752026253244_127.0.0.1_8924
09:57:33.367 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fe093f6-8976-412c-827d-ed9fe6eebda9] Notify connected event to listeners.
09:57:33.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fe093f6-8976-412c-827d-ed9fe6eebda9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:57:33.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fe093f6-8976-412c-827d-ed9fe6eebda9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001ebda4e8668
09:57:33.482 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:57:33.536 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ***********:9300 register finished
09:57:33.845 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 13.56 seconds (JVM running for 14.576)
09:57:33.867 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:57:33.868 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:57:33.892 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:57:33.962 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fe093f6-8976-412c-827d-ed9fe6eebda9] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:57:33.985 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fe093f6-8976-412c-827d-ed9fe6eebda9] Ack server push request, request = NotifySubscriberRequest, requestId = 14
09:57:34.192 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:14:48.191 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:14:48.195 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:14:48.550 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:14:48.550 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@486e59aa[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:14:48.550 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752026253244_127.0.0.1_8924
15:14:48.556 [nacos-grpc-client-executor-3813] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752026253244_127.0.0.1_8924]Ignore complete event,isRunning:false,isAbandon=false
15:14:48.572 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@75f3cfb9[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 3814]
15:32:36.333 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:32:36.941 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f62a950f-8f6a-4013-ad96-36087b4d8ab7_config-0
15:32:37.029 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
15:32:37.057 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
15:32:37.067 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
15:32:37.078 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
15:32:37.088 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
15:32:37.101 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
15:32:37.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f62a950f-8f6a-4013-ad96-36087b4d8ab7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:32:37.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f62a950f-8f6a-4013-ad96-36087b4d8ab7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001a581397220
15:32:37.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f62a950f-8f6a-4013-ad96-36087b4d8ab7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001a581397440
15:32:37.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f62a950f-8f6a-4013-ad96-36087b4d8ab7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:32:37.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f62a950f-8f6a-4013-ad96-36087b4d8ab7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:32:37.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f62a950f-8f6a-4013-ad96-36087b4d8ab7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:32:38.040 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f62a950f-8f6a-4013-ad96-36087b4d8ab7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752046357813_127.0.0.1_8709
15:32:38.040 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f62a950f-8f6a-4013-ad96-36087b4d8ab7_config-0] Notify connected event to listeners.
15:32:38.041 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f62a950f-8f6a-4013-ad96-36087b4d8ab7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:32:38.041 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f62a950f-8f6a-4013-ad96-36087b4d8ab7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001a581510fb0
15:32:38.153 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:32:40.455 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
15:32:40.457 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:32:40.458 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:32:40.723 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:32:42.277 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:32:44.670 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 854c5756-37a2-4b45-8172-50cde633e0b2
15:32:44.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [854c5756-37a2-4b45-8172-50cde633e0b2] RpcClient init label, labels = {module=naming, source=sdk}
15:32:44.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [854c5756-37a2-4b45-8172-50cde633e0b2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:32:44.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [854c5756-37a2-4b45-8172-50cde633e0b2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:32:44.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [854c5756-37a2-4b45-8172-50cde633e0b2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:32:44.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [854c5756-37a2-4b45-8172-50cde633e0b2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:32:44.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [854c5756-37a2-4b45-8172-50cde633e0b2] Success to connect to server [localhost:8848] on start up, connectionId = 1752046364689_127.0.0.1_8744
15:32:44.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [854c5756-37a2-4b45-8172-50cde633e0b2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:32:44.811 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [854c5756-37a2-4b45-8172-50cde633e0b2] Notify connected event to listeners.
15:32:44.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [854c5756-37a2-4b45-8172-50cde633e0b2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001a581510fb0
15:32:44.863 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
15:32:44.889 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ***********:9300 register finished
15:32:45.009 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 9.314 seconds (JVM running for 10.474)
15:32:45.024 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
15:32:45.025 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
15:32:45.032 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
15:32:45.225 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:32:45.435 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [854c5756-37a2-4b45-8172-50cde633e0b2] Receive server push request, request = NotifySubscriberRequest, requestId = 61
15:32:45.463 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [854c5756-37a2-4b45-8172-50cde633e0b2] Ack server push request, request = NotifySubscriberRequest, requestId = 61
16:28:58.709 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:28:58.749 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:28:59.089 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:28:59.090 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@48ce6f44[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:28:59.091 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752046364689_127.0.0.1_8744
16:28:59.106 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5baf055[Running, pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 687]
16:28:59.188 [nacos-grpc-client-executor-687] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752046364689_127.0.0.1_8744]Ignore complete event,isRunning:false,isAbandon=false
17:36:47.437 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:36:49.075 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4233b139-a8b3-4f39-8c1d-83aac0294242_config-0
17:36:49.292 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 103 ms to scan 1 urls, producing 3 keys and 6 values 
17:36:49.386 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 4 keys and 9 values 
17:36:49.412 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 10 values 
17:36:49.439 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 5 values 
17:36:49.466 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 7 values 
17:36:49.509 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 2 keys and 8 values 
17:36:49.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4233b139-a8b3-4f39-8c1d-83aac0294242_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:36:49.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4233b139-a8b3-4f39-8c1d-83aac0294242_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000023f113b7220
17:36:49.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4233b139-a8b3-4f39-8c1d-83aac0294242_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023f113b7440
17:36:49.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4233b139-a8b3-4f39-8c1d-83aac0294242_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:36:49.523 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4233b139-a8b3-4f39-8c1d-83aac0294242_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:36:49.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4233b139-a8b3-4f39-8c1d-83aac0294242_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:36:52.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4233b139-a8b3-4f39-8c1d-83aac0294242_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752053811712_127.0.0.1_11253
17:36:52.092 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4233b139-a8b3-4f39-8c1d-83aac0294242_config-0] Notify connected event to listeners.
17:36:52.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4233b139-a8b3-4f39-8c1d-83aac0294242_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:36:52.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4233b139-a8b3-4f39-8c1d-83aac0294242_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000023f114f0fb0
17:36:52.314 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:36:56.595 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
17:36:56.596 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:36:56.597 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:36:56.804 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:36:58.766 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:37:01.700 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c1271089-fc7a-4136-8d24-3e9ccc0ad35e
17:37:01.701 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1271089-fc7a-4136-8d24-3e9ccc0ad35e] RpcClient init label, labels = {module=naming, source=sdk}
17:37:01.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1271089-fc7a-4136-8d24-3e9ccc0ad35e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:37:01.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1271089-fc7a-4136-8d24-3e9ccc0ad35e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:37:01.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1271089-fc7a-4136-8d24-3e9ccc0ad35e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:37:01.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1271089-fc7a-4136-8d24-3e9ccc0ad35e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:37:01.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1271089-fc7a-4136-8d24-3e9ccc0ad35e] Success to connect to server [localhost:8848] on start up, connectionId = 1752053821719_127.0.0.1_11281
17:37:01.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1271089-fc7a-4136-8d24-3e9ccc0ad35e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:37:01.851 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1271089-fc7a-4136-8d24-3e9ccc0ad35e] Notify connected event to listeners.
17:37:01.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1271089-fc7a-4136-8d24-3e9ccc0ad35e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000023f114f0fb0
17:37:01.910 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
17:37:01.961 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ***********:9300 register finished
17:37:02.211 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 16.041 seconds (JVM running for 17.807)
17:37:02.228 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
17:37:02.229 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
17:37:02.232 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
17:37:02.368 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:37:02.486 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1271089-fc7a-4136-8d24-3e9ccc0ad35e] Receive server push request, request = NotifySubscriberRequest, requestId = 78
17:37:02.516 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1271089-fc7a-4136-8d24-3e9ccc0ad35e] Ack server push request, request = NotifySubscriberRequest, requestId = 78
19:28:57.800 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:28:57.806 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:28:58.144 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:28:58.145 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2b47f6c1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:28:58.145 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752053821719_127.0.0.1_11281
19:28:58.148 [nacos-grpc-client-executor-1353] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752053821719_127.0.0.1_11281]Ignore complete event,isRunning:false,isAbandon=false
19:28:58.155 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@115f8af0[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1354]
