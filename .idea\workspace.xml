<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="55d6bb72-a71d-423e-91e3-edddbe028dc2" name="Changes" comment="fix:通用新增/修改字段，标头字段，详情接口修正">
      <change beforePath="$PROJECT_DIR$/heju-auth/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/heju-auth/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/heju-common/heju-common-flowable/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/heju-common/heju-common-flowable/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/heju-gateway/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/heju-gateway/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/heju-modules/heju-file/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/heju-modules/heju-file/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/heju-modules/heju-gen/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/heju-modules/heju-gen/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/heju-modules/heju-job/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/heju-modules/heju-job/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/heju-modules/heju-system/src/main/java/com/heju/system/forms/universal/controller/UniversalController.java" beforeDir="false" afterPath="$PROJECT_DIR$/heju-modules/heju-system/src/main/java/com/heju/system/forms/universal/controller/UniversalController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/heju-modules/heju-system/src/main/java/com/heju/system/forms/universal/mapper/UniversalMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/heju-modules/heju-system/src/main/java/com/heju/system/forms/universal/mapper/UniversalMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/heju-modules/heju-system/src/main/java/com/heju/system/forms/universal/service/UniversalService.java" beforeDir="false" afterPath="$PROJECT_DIR$/heju-modules/heju-system/src/main/java/com/heju/system/forms/universal/service/UniversalService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/heju-modules/heju-system/src/main/java/com/heju/system/forms/universal/service/impl/UniversalServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/heju-modules/heju-system/src/main/java/com/heju/system/forms/universal/service/impl/UniversalServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/heju-modules/heju-system/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/heju-modules/heju-system/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/heju-modules/heju-tenant/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/heju-modules/heju-tenant/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/heju-visual/heju-monitor/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/heju-visual/heju-monitor/src/main/resources/bootstrap.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <branch-grouping />
    <option name="PREVIOUS_COMMIT_AUTHORS">
      <list>
        <option value="王东升(wangdsh) &lt;<EMAIL>&gt;" />
      </list>
    </option>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="develop" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;history&quot;: [
    {
      &quot;assignee&quot;: {
        &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
        &quot;username&quot;: &quot;dongxing&quot;,
        &quot;fullname&quot;: &quot;董星&quot;
      }
    }
  ],
  &quot;lastFilter&quot;: {}
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;http://192.168.1.200/joylink/HeJu-Multisaas.git&quot;,
    &quot;second&quot;: &quot;7d0e72ca-f489-42e9-b2a4-a3f85f48edd0&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/heju-modules/heju-system/src/main/java/com/heju/system/approval/mapper/SysApprovalCustomerinfoMapper.java" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\devapps\Java-repo\apache-maven-3.9.9" />
        <option name="localRepository" value="D:\devapps\Java-repo\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\devapps\Java-repo\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2yhyPmse9v1FjbX7ODPHhDH1sw3" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.SysFileBorrowRecordServiceImpl.executor&quot;: &quot;Run&quot;,
    &quot;Maven.heju [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.heju [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.heju-common-security [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.heju-common-security [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.heju-common-swagger [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.heju-kkfileview [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.heju-kkfileview [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.heju-modules-system [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.heju-modules-system [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.heju-modules-system [package].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.HeJuAuthApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.HeJuFileApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.HeJuFileviewApplication (1).executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.HeJuFileviewApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.HeJuGatewayApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.HeJuGenApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.HeJuJobApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.HeJuSystemApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.HeJuTenantApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.ServerMain.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;develop&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/devapps/Projects/heju/HeJu-Multisaas/heju-modules/heju-system/src/main/java/com/heju/system/annualReport&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;editor.preferences.fonts.default&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;,
      &quot;mariadb&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-system\src\main\java\com\heju\system\annualReport" />
      <recent name="D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-kkfileview\src\main\resources" />
      <recent name="D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-kkfileview\src\main\resources\web" />
      <recent name="D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-kkfileview\src" />
      <recent name="D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-kkfileview\src\main" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-file\src\main\java\com\heju\file" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.heju.system.api.fileInfo.feign.factory" />
      <recent name="com.heju.system.api.fileInfo.feign" />
      <recent name="com.heju.system.file.service" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.HeJuSystemApplication">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="HeJu-Multisaas" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="HeJu-Multisaas" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="HeJuAuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="heju-auth" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.heju.auth.HeJuAuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HeJuFileApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="heju-modules-file" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.heju.file.HeJuFileApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HeJuFlowableApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="heju-modules-flowable" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.heju.flowable.HeJuFlowableApplication" />
      <option name="VM_PARAMETERS" value="--add-opens java.base/java.lang=ALL-UNNAMED" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HeJuGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="heju-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.heju.gateway.HeJuGatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HeJuGenApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="heju-modules-gen" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.heju.gen.HeJuGenApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HeJuJobApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="heju-modules-job" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.heju.job.HeJuJobApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HeJuMonitorApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="heju-visual-monitor" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.heju.modules.monitor.HeJuMonitorApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HeJuSystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="heju-modules-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.heju.system.HeJuSystemApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HeJuTenantApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="heju-modules-tenant" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.heju.tenant.HeJuTenantApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.HeJuAuthApplication" />
      <item itemvalue="Spring Boot.HeJuFileApplication" />
      <item itemvalue="Spring Boot.HeJuFlowableApplication" />
      <item itemvalue="Spring Boot.HeJuGatewayApplication" />
      <item itemvalue="Spring Boot.HeJuGenApplication" />
      <item itemvalue="Spring Boot.HeJuJobApplication" />
      <item itemvalue="Spring Boot.HeJuMonitorApplication" />
      <item itemvalue="Spring Boot.HeJuSystemApplication" />
      <item itemvalue="Spring Boot.HeJuTenantApplication" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="StructureViewState">
    <option name="selectedTab" value="Logical" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="55d6bb72-a71d-423e-91e3-edddbe028dc2" name="Changes" comment="" />
      <created>1750301077603</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750301077603</updated>
      <workItem from="1750301078988" duration="540000" />
      <workItem from="1750302022787" duration="9928000" />
      <workItem from="1750321998312" duration="4465000" />
      <workItem from="1750328978085" duration="2871000" />
      <workItem from="1750335538380" duration="8000" />
      <workItem from="1750335558143" duration="218000" />
      <workItem from="1750393911367" duration="2503000" />
      <workItem from="1750404911113" duration="2759000" />
      <workItem from="1750422990106" duration="5578000" />
      <workItem from="1750494968232" duration="25000" />
      <workItem from="1750558054113" duration="21124000" />
      <workItem from="1750600572538" duration="611000" />
      <workItem from="1750653828060" duration="1153000" />
      <workItem from="1750656317169" duration="17620000" />
      <workItem from="1750729776392" duration="29471000" />
      <workItem from="1750813543368" duration="23782000" />
      <workItem from="1750929403302" duration="716000" />
      <workItem from="1750936896980" duration="2232000" />
      <workItem from="1750991355698" duration="8000" />
      <workItem from="1751332628421" duration="41291000" />
      <workItem from="1751418626150" duration="11454000" />
      <workItem from="1751435042390" duration="5266000" />
      <workItem from="1751440825655" duration="17919000" />
      <workItem from="1751504631141" duration="16550000" />
      <workItem from="1751522311850" duration="21449000" />
      <workItem from="1751591116171" duration="20129000" />
      <workItem from="1751850842848" duration="9652000" />
      <workItem from="1751866286696" duration="661000" />
      <workItem from="1751872789587" duration="1447000" />
      <workItem from="1751874284374" duration="10224000" />
      <workItem from="1751936938906" duration="6588000" />
      <workItem from="1751958132177" duration="19631000" />
      <workItem from="1752023308738" duration="25209000" />
      <workItem from="1752053686307" duration="5756000" />
      <workItem from="1752110083615" duration="7984000" />
      <workItem from="1752131680151" duration="16730000" />
      <workItem from="1752196076694" duration="30988000" />
      <workItem from="1752250636407" duration="2064000" />
      <workItem from="1752371599337" duration="20552000" />
      <workItem from="1752405649823" duration="800000" />
      <workItem from="1752456715838" duration="731000" />
      <workItem from="1752457513795" duration="23683000" />
      <workItem from="1752541358854" duration="17543000" />
      <workItem from="1752564526148" duration="5505000" />
      <workItem from="1752627679912" duration="27123000" />
      <workItem from="1752714612861" duration="1275000" />
      <workItem from="1752715992388" duration="33089000" />
      <workItem from="1752800722606" duration="27029000" />
      <workItem from="1752934236762" duration="3025000" />
      <workItem from="1752976860232" duration="18234000" />
      <workItem from="1753059590080" duration="16155000" />
      <workItem from="1753146982821" duration="17411000" />
      <workItem from="1753233006868" duration="33715000" />
      <workItem from="1753319417349" duration="34787000" />
      <workItem from="1753405498495" duration="27964000" />
      <workItem from="1753456802983" duration="601000" />
      <workItem from="1753537973344" duration="2286000" />
      <workItem from="1753581874523" duration="22000" />
      <workItem from="1753581924501" duration="12000" />
      <workItem from="1753581964529" duration="19967000" />
      <workItem from="1753664494450" duration="22925000" />
      <workItem from="1753751364809" duration="31684000" />
      <workItem from="1753837116326" duration="27276000" />
      <workItem from="1753874546173" duration="622000" />
      <workItem from="1753923818199" duration="34591000" />
      <workItem from="1754010369435" duration="32027000" />
      <workItem from="1754057700682" duration="90000" />
      <workItem from="1754057920024" duration="237000" />
      <workItem from="1754063169666" duration="13000" />
      <workItem from="1754186842548" duration="23493000" />
      <workItem from="1754269226663" duration="27049000" />
      <workItem from="1754356459138" duration="32309000" />
      <workItem from="1754443547463" duration="25129000" />
      <workItem from="1754528668684" duration="18142000" />
    </task>
    <task id="LOCAL-00001" summary="feat: websoocket服务 回收站过期时间 定时任务删除过期文件 文件单/批量下载">
      <option name="closed" value="true" />
      <created>1751613829701</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751613829701</updated>
    </task>
    <task id="LOCAL-00002" summary="feat:文件编号生成更新">
      <option name="closed" value="true" />
      <created>1752128527010</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752128527010</updated>
    </task>
    <task id="LOCAL-00003" summary="feat:客户信息审批，动态表单-业务选项接口，工商业务中心：年度申报和年度申报管理">
      <option name="closed" value="true" />
      <created>1753080930212</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753080930212</updated>
    </task>
    <task id="LOCAL-00004" summary="feat:修改单选字段类型为bigint；新增时间，日期区间；通用接口删除接口与选择框接口完善">
      <option name="closed" value="true" />
      <created>1753752891895</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753752891895</updated>
    </task>
    <task id="LOCAL-00005" summary="feat:通用选择框查询与修改接口调整">
      <option name="closed" value="true" />
      <created>1753839708893</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753839708893</updated>
    </task>
    <task id="LOCAL-00006" summary="feat:工商申请管理字段与列表接口，年度日报管理接口，通用新增 credit_no 检查接口">
      <option name="closed" value="true" />
      <created>1754273270754</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1754273270754</updated>
    </task>
    <task id="LOCAL-00007" summary="fix:删除 seal 文件夹">
      <option name="closed" value="true" />
      <created>1754273530060</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1754273530060</updated>
    </task>
    <task id="LOCAL-00008" summary="fix:通用管理列表接口修正">
      <option name="closed" value="true" />
      <created>1754275361627</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1754275361627</updated>
    </task>
    <task id="LOCAL-00009" summary="fix:工商年报管理查询条件接口">
      <option name="closed" value="true" />
      <created>1754303842724</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1754303842724</updated>
    </task>
    <task id="LOCAL-00010" summary="fix:通用新增/修改字段，标头字段，详情接口修正">
      <option name="closed" value="true" />
      <created>1754446318982</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1754446318982</updated>
    </task>
    <option name="localTasksCounter" value="11" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="develop" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="main" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="HEAD" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/develop" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="CUSTOM_BOOLEAN_PROPERTIES">
                <map>
                  <entry key="Show.Git.Branches" value="true" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/develop" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat:文件管理接口" />
    <MESSAGE value="feat: websoocket服务 回收站过期时间 定时任务删除过期文件 文件单/批量下载" />
    <MESSAGE value="feat:文件编号生成更新" />
    <MESSAGE value="feat:客户信息审批，动态表单-业务选项接口，工商业务中心：年度申报和年度申报管理" />
    <MESSAGE value="feat:修改单选字段类型为bigint；新增时间，日期区间；通用接口删除接口与选择框接口完善" />
    <MESSAGE value="feat:通用选择框查询与修改接口调整" />
    <MESSAGE value="feat:工商申请管理字段与列表接口，年度日报管理接口，通用新增 credit_no 检查接口" />
    <MESSAGE value="fix:删除 seal 文件夹" />
    <MESSAGE value="fix:通用管理列表接口修正" />
    <MESSAGE value="fix:工商年报管理查询条件接口" />
    <MESSAGE value="fix:通用新增/修改字段，标头字段，详情接口修正" />
    <option name="LAST_COMMIT_MESSAGE" value="fix:通用新增/修改字段，标头字段，详情接口修正" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/heju-modules/heju-kkfileview/src/main/java/com/heju/keking/service/impl/PictureFilePreviewImpl.java</url>
          <line>28</line>
          <option name="timeStamp" value="50" />
        </line-breakpoint>
        <line-breakpoint type="java-method">
          <url>file://$PROJECT_DIR$/heju-modules/heju-kkfileview/src/main/java/com/heju/keking/service/FilePreview.java</url>
          <line>36</line>
          <properties class="com.heju.keking.service.FilePreview">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="46" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="((ErrorResponseException)((ErrorResponseException)e).cause).errorResponse" custom="io.minio.errors.ErrorResponseException" />
        <watch expression="key" />
        <watch expression="key" language="JAVA" />
        <watch expression="//        List&lt;SysApprovalCustomerinfoDto&gt; list = baseManager.selectList(approvalCustomerinfo);&#10;//        List&lt;Long&gt; sheetIds = list.stream().map(SysApprovalCustomerinfoDto::getSheetId).toList();&#10;//        List&lt;Long&gt; fieldIds = list.stream().map(SysApprovalCustomerinfoDto::getFieldId).toList();&#10;//        List&lt;Long&gt; createByIds = list.stream().map(SysApprovalCustomerinfoDto::getCreateBy).toList();&#10;//        List&lt;SysSheetDto&gt; sysSheetDtos = sheetManager.selectListByIds(sheetIds);&#10;//        List&lt;SysFieldDto&gt; sysFieldDtos = fieldManager.selectListByIds(fieldIds);&#10;//        List&lt;SysUserDto&gt; sysUserDtos = sysUserManager.selectListByIds(createByIds);&#10;//        Map&lt;Long, String&gt; sheetMap = sysSheetDtos.stream().collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getName, (v1, v2) -&gt; v1));&#10;//        Map&lt;Long, String&gt; fieldMap = sysFieldDtos.stream().collect(Collectors.toMap(SysFieldDto::getId, SysFieldDto::getName, (v1, v2) -&gt; v1));&#10;//        Map&lt;Long, String&gt; userMap = sysUserDtos.stream().collect(Collectors.toMap(SysUserDto::getId, SysUserDto::getNickName, (v1, v2) -&gt; v1));&#10;//        sysApprovalCustomerinfoDtos.forEach(sysApprovalCustomerinfoDto -&gt; {&#10;//            sysApprovalCustomerinfoDto.setSheetName(sheetMap.get(sysApprovalCustomerinfoDto.getSheetId()));&#10;//            sysApprovalCustomerinfoDto.setFieldName(fieldMap.get(sysApprovalCustomerinfoDto.getFieldId()));&#10;//            sysApprovalCustomerinfoDto.setCreateName(userMap.get(sysApprovalCustomerinfoDto.getCreateBy()));&#10;//        });&#10;//        List&lt;String&gt; apiNameList = sysSheetDtos.stream().map(SysSheetDto::getApiName).toList();&#10;        SysApprovalCustomerinfoDto s=new SysApprovalCustomerinfoDto();&#10;        s.getBusinessId();" />
        <watch expression="// 1.将审核状态修改为 1,&#10;    // 2.在 sys_field 中 通过 field_name 对应 name 查出 id， 将 id 写入 sys_approval_customerinfo 的 field_id 中" language="JAVA" />
      </configuration>
    </watches-manager>
  </component>
</project>