09:21:22.882 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:21:23.429 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 212917ba-080b-4c66-a9b8-5c6320d7012b_config-0
09:21:23.481 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 3 keys and 6 values 
09:21:23.513 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 4 keys and 9 values 
09:21:23.521 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:21:23.528 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:21:23.539 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:21:23.547 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
09:21:23.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [212917ba-080b-4c66-a9b8-5c6320d7012b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:21:23.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [212917ba-080b-4c66-a9b8-5c6320d7012b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002883b3ceaf8
09:21:23.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [212917ba-080b-4c66-a9b8-5c6320d7012b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002883b3ced18
09:21:23.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [212917ba-080b-4c66-a9b8-5c6320d7012b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:21:23.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [212917ba-080b-4c66-a9b8-5c6320d7012b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:21:23.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [212917ba-080b-4c66-a9b8-5c6320d7012b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:24.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [212917ba-080b-4c66-a9b8-5c6320d7012b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752196884031_127.0.0.1_14312
09:21:24.228 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [212917ba-080b-4c66-a9b8-5c6320d7012b_config-0] Notify connected event to listeners.
09:21:24.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [212917ba-080b-4c66-a9b8-5c6320d7012b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:24.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [212917ba-080b-4c66-a9b8-5c6320d7012b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002883b508fd0
09:21:24.346 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:21:27.189 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:21:27.189 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:21:27.189 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:21:27.331 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:21:27.969 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:21:27.969 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:21:27.969 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:21:34.768 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:21:38.128 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2afcf139-5b14-4f63-a148-7ed3bf76329a
09:21:38.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2afcf139-5b14-4f63-a148-7ed3bf76329a] RpcClient init label, labels = {module=naming, source=sdk}
09:21:38.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2afcf139-5b14-4f63-a148-7ed3bf76329a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:38.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2afcf139-5b14-4f63-a148-7ed3bf76329a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:38.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2afcf139-5b14-4f63-a148-7ed3bf76329a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:38.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2afcf139-5b14-4f63-a148-7ed3bf76329a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:38.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2afcf139-5b14-4f63-a148-7ed3bf76329a] Success to connect to server [localhost:8848] on start up, connectionId = 1752196898140_127.0.0.1_14370
09:21:38.255 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2afcf139-5b14-4f63-a148-7ed3bf76329a] Notify connected event to listeners.
09:21:38.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2afcf139-5b14-4f63-a148-7ed3bf76329a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:38.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2afcf139-5b14-4f63-a148-7ed3bf76329a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002883b508fd0
09:21:38.306 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:21:38.330 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
09:21:38.446 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.051 seconds (JVM running for 16.873)
09:21:38.458 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:21:38.460 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:21:38.460 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:21:38.842 [RMI TCP Connection(10)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:21:38.883 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2afcf139-5b14-4f63-a148-7ed3bf76329a] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:21:38.899 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2afcf139-5b14-4f63-a148-7ed3bf76329a] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:22:25.137 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2afcf139-5b14-4f63-a148-7ed3bf76329a] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:22:25.137 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2afcf139-5b14-4f63-a148-7ed3bf76329a] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:22:25.469 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:22:25.469 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:22:25.589 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:22:25.589 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
11:30:16.862 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:30:16.868 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:30:17.220 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:30:17.220 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@731e0aa0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:30:17.220 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752196898140_127.0.0.1_14370
11:30:17.223 [nacos-grpc-client-executor-1584] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752196898140_127.0.0.1_14370]Ignore complete event,isRunning:false,isAbandon=false
11:30:17.225 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@715c5c98[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1585]
11:30:17.393 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:30:17.398 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
11:30:17.405 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
11:30:17.405 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:30:17.413 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:30:17.413 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:30:17.413 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:30:17.413 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:31:23.662 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:31:24.656 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9eb6b24a-1d1d-4987-987c-4629c677a574_config-0
11:31:24.725 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
11:31:24.788 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
11:31:24.798 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
11:31:24.809 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
11:31:24.822 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
11:31:24.838 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
11:31:24.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eb6b24a-1d1d-4987-987c-4629c677a574_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:31:24.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eb6b24a-1d1d-4987-987c-4629c677a574_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001f7813b7b00
11:31:24.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eb6b24a-1d1d-4987-987c-4629c677a574_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001f7813b7d20
11:31:24.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eb6b24a-1d1d-4987-987c-4629c677a574_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:31:24.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eb6b24a-1d1d-4987-987c-4629c677a574_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:31:24.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eb6b24a-1d1d-4987-987c-4629c677a574_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:31:25.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eb6b24a-1d1d-4987-987c-4629c677a574_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752204685716_127.0.0.1_12478
11:31:25.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eb6b24a-1d1d-4987-987c-4629c677a574_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:31:25.940 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eb6b24a-1d1d-4987-987c-4629c677a574_config-0] Notify connected event to listeners.
11:31:25.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eb6b24a-1d1d-4987-987c-4629c677a574_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f7814efb88
11:31:26.161 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:31:32.221 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:31:32.221 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:31:32.221 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:31:32.598 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:31:34.010 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:31:34.015 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:31:34.015 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:31:50.111 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:31:53.334 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d1ef2275-4627-4ec2-a7a0-d6f74a96f547
11:31:53.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1ef2275-4627-4ec2-a7a0-d6f74a96f547] RpcClient init label, labels = {module=naming, source=sdk}
11:31:53.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1ef2275-4627-4ec2-a7a0-d6f74a96f547] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:31:53.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1ef2275-4627-4ec2-a7a0-d6f74a96f547] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:31:53.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1ef2275-4627-4ec2-a7a0-d6f74a96f547] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:31:53.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1ef2275-4627-4ec2-a7a0-d6f74a96f547] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:31:53.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1ef2275-4627-4ec2-a7a0-d6f74a96f547] Success to connect to server [localhost:8848] on start up, connectionId = 1752204713351_127.0.0.1_12524
11:31:53.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1ef2275-4627-4ec2-a7a0-d6f74a96f547] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:31:53.475 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1ef2275-4627-4ec2-a7a0-d6f74a96f547] Notify connected event to listeners.
11:31:53.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1ef2275-4627-4ec2-a7a0-d6f74a96f547] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f7814efb88
11:31:53.528 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:31:53.581 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
11:31:53.746 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 30.784 seconds (JVM running for 33.114)
11:31:53.765 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:31:53.765 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:31:53.765 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:31:54.065 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1ef2275-4627-4ec2-a7a0-d6f74a96f547] Receive server push request, request = NotifySubscriberRequest, requestId = 18
11:31:54.090 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1ef2275-4627-4ec2-a7a0-d6f74a96f547] Ack server push request, request = NotifySubscriberRequest, requestId = 18
11:32:16.234 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:32:19.235 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:32:19.235 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:33:40.661 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:33:40.676 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:33:41.032 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:33:41.032 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4082901b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:33:41.034 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752204713351_127.0.0.1_12524
13:33:41.036 [nacos-grpc-client-executor-1378] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752204713351_127.0.0.1_12524]Ignore complete event,isRunning:false,isAbandon=false
13:33:41.043 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@112d9542[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 1379]
13:33:41.226 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:33:41.226 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:33:41.238 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:33:41.238 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:33:41.245 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:33:41.246 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:33:47.519 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:33:48.327 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6cf2b3f3-6184-43f6-addb-4ce44df20a59_config-0
13:33:48.401 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
13:33:48.442 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
13:33:48.453 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
13:33:48.470 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
13:33:48.481 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
13:33:48.489 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
13:33:48.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cf2b3f3-6184-43f6-addb-4ce44df20a59_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:33:48.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cf2b3f3-6184-43f6-addb-4ce44df20a59_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000272a839eaf8
13:33:48.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cf2b3f3-6184-43f6-addb-4ce44df20a59_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000272a839ed18
13:33:48.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cf2b3f3-6184-43f6-addb-4ce44df20a59_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:33:48.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cf2b3f3-6184-43f6-addb-4ce44df20a59_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:33:48.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cf2b3f3-6184-43f6-addb-4ce44df20a59_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:33:49.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cf2b3f3-6184-43f6-addb-4ce44df20a59_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752212029273_127.0.0.1_7079
13:33:49.503 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cf2b3f3-6184-43f6-addb-4ce44df20a59_config-0] Notify connected event to listeners.
13:33:49.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cf2b3f3-6184-43f6-addb-4ce44df20a59_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:33:49.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6cf2b3f3-6184-43f6-addb-4ce44df20a59_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000272a8518ad8
13:33:49.710 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:33:53.899 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:33:53.899 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:33:53.899 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:33:54.091 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:33:54.880 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:33:54.881 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:33:54.881 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:34:02.898 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:34:06.021 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0c1eb486-7947-4f62-9a4b-1ba3fbf5f522
13:34:06.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1eb486-7947-4f62-9a4b-1ba3fbf5f522] RpcClient init label, labels = {module=naming, source=sdk}
13:34:06.024 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1eb486-7947-4f62-9a4b-1ba3fbf5f522] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:34:06.024 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1eb486-7947-4f62-9a4b-1ba3fbf5f522] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:34:06.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1eb486-7947-4f62-9a4b-1ba3fbf5f522] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:34:06.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1eb486-7947-4f62-9a4b-1ba3fbf5f522] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:34:06.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1eb486-7947-4f62-9a4b-1ba3fbf5f522] Success to connect to server [localhost:8848] on start up, connectionId = 1752212046038_127.0.0.1_7104
13:34:06.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1eb486-7947-4f62-9a4b-1ba3fbf5f522] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:34:06.191 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1eb486-7947-4f62-9a4b-1ba3fbf5f522] Notify connected event to listeners.
13:34:06.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1eb486-7947-4f62-9a4b-1ba3fbf5f522] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000272a8518ad8
13:34:06.276 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:34:06.319 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:34:06.495 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.623 seconds (JVM running for 20.698)
13:34:06.510 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:34:06.511 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:34:06.512 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:34:06.591 [RMI TCP Connection(6)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:34:06.788 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1eb486-7947-4f62-9a4b-1ba3fbf5f522] Receive server push request, request = NotifySubscriberRequest, requestId = 28
13:34:06.801 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1eb486-7947-4f62-9a4b-1ba3fbf5f522] Ack server push request, request = NotifySubscriberRequest, requestId = 28
13:34:34.482 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:34:34.482 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:44:25.336 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:44:25.344 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:44:25.679 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:44:25.681 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6637f7c7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:44:25.681 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752212046038_127.0.0.1_7104
13:44:25.684 [nacos-grpc-client-executor-133] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752212046038_127.0.0.1_7104]Ignore complete event,isRunning:false,isAbandon=false
13:44:25.690 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@e7ed558[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 134]
13:44:25.898 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:44:25.904 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:44:25.920 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:44:25.920 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:44:25.922 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:44:25.922 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:44:31.654 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:44:32.413 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 79b45c15-34c6-4178-a16a-da24117482c4_config-0
13:44:32.483 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
13:44:32.519 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
13:44:32.527 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
13:44:32.536 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
13:44:32.546 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
13:44:32.558 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
13:44:32.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79b45c15-34c6-4178-a16a-da24117482c4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:44:32.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79b45c15-34c6-4178-a16a-da24117482c4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002cc813be8d8
13:44:32.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79b45c15-34c6-4178-a16a-da24117482c4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002cc813beaf8
13:44:32.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79b45c15-34c6-4178-a16a-da24117482c4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:44:32.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79b45c15-34c6-4178-a16a-da24117482c4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:44:32.571 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79b45c15-34c6-4178-a16a-da24117482c4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:44:33.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79b45c15-34c6-4178-a16a-da24117482c4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752212673438_127.0.0.1_8143
13:44:33.672 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79b45c15-34c6-4178-a16a-da24117482c4_config-0] Notify connected event to listeners.
13:44:33.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79b45c15-34c6-4178-a16a-da24117482c4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:44:33.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79b45c15-34c6-4178-a16a-da24117482c4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002cc814f8fb0
13:44:33.835 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:44:37.517 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:44:37.518 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:44:37.518 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:44:37.702 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:44:38.637 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:44:38.639 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:44:38.640 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:44:46.328 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:44:50.492 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-4f70-4b02-b0a6-442728f01756
13:44:50.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f70-4b02-b0a6-442728f01756] RpcClient init label, labels = {module=naming, source=sdk}
13:44:50.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f70-4b02-b0a6-442728f01756] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:44:50.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f70-4b02-b0a6-442728f01756] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:44:50.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f70-4b02-b0a6-442728f01756] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:44:50.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f70-4b02-b0a6-442728f01756] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:44:50.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f70-4b02-b0a6-442728f01756] Success to connect to server [localhost:8848] on start up, connectionId = 1752212690516_127.0.0.1_8161
13:44:50.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f70-4b02-b0a6-442728f01756] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:44:50.644 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f70-4b02-b0a6-442728f01756] Notify connected event to listeners.
13:44:50.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f70-4b02-b0a6-442728f01756] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002cc814f8fb0
13:44:50.745 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:44:50.817 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:44:51.165 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.097 seconds (JVM running for 21.179)
13:44:51.196 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:44:51.196 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:44:51.196 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:44:51.248 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f70-4b02-b0a6-442728f01756] Receive server push request, request = NotifySubscriberRequest, requestId = 35
13:44:51.269 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f70-4b02-b0a6-442728f01756] Ack server push request, request = NotifySubscriberRequest, requestId = 35
13:44:51.803 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:45:14.338 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:45:14.338 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:45:59.935 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:45:59.943 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:46:00.282 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:46:00.282 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3c7a3bcc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:46:00.282 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752212690516_127.0.0.1_8161
13:46:00.282 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752212690516_127.0.0.1_8161]Ignore complete event,isRunning:false,isAbandon=false
13:46:00.282 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3222d7f8[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 24]
13:46:00.458 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:46:00.462 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:46:00.469 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:46:00.469 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:46:00.471 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:46:00.471 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:46:05.442 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:46:06.298 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7a0a5194-50d6-409a-a7a2-0ad3dfd1497a_config-0
13:46:06.388 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
13:46:06.428 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
13:46:06.444 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
13:46:06.444 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
13:46:06.461 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
13:46:06.476 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
13:46:06.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a0a5194-50d6-409a-a7a2-0ad3dfd1497a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:46:06.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a0a5194-50d6-409a-a7a2-0ad3dfd1497a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000269e239eaf8
13:46:06.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a0a5194-50d6-409a-a7a2-0ad3dfd1497a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000269e239ed18
13:46:06.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a0a5194-50d6-409a-a7a2-0ad3dfd1497a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:46:06.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a0a5194-50d6-409a-a7a2-0ad3dfd1497a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:46:06.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a0a5194-50d6-409a-a7a2-0ad3dfd1497a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:46:07.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a0a5194-50d6-409a-a7a2-0ad3dfd1497a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752212767437_127.0.0.1_8283
13:46:07.677 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a0a5194-50d6-409a-a7a2-0ad3dfd1497a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:46:07.676 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a0a5194-50d6-409a-a7a2-0ad3dfd1497a_config-0] Notify connected event to listeners.
13:46:07.678 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7a0a5194-50d6-409a-a7a2-0ad3dfd1497a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000269e2518668
13:46:07.838 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:46:11.755 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:46:11.756 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:46:11.756 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:46:11.961 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:46:13.846 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:46:13.848 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:46:13.849 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:46:23.155 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:46:26.564 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of efd386c5-70a3-427c-b27f-c415297780bb
13:46:26.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd386c5-70a3-427c-b27f-c415297780bb] RpcClient init label, labels = {module=naming, source=sdk}
13:46:26.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd386c5-70a3-427c-b27f-c415297780bb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:46:26.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd386c5-70a3-427c-b27f-c415297780bb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:46:26.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd386c5-70a3-427c-b27f-c415297780bb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:46:26.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd386c5-70a3-427c-b27f-c415297780bb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:46:26.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd386c5-70a3-427c-b27f-c415297780bb] Success to connect to server [localhost:8848] on start up, connectionId = 1752212786580_127.0.0.1_8335
13:46:26.733 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd386c5-70a3-427c-b27f-c415297780bb] Notify connected event to listeners.
13:46:26.733 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd386c5-70a3-427c-b27f-c415297780bb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:46:26.734 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd386c5-70a3-427c-b27f-c415297780bb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000269e2518668
13:46:26.821 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:46:26.868 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:46:27.050 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 22.225 seconds (JVM running for 23.238)
13:46:27.071 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:46:27.072 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:46:27.073 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:46:27.391 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd386c5-70a3-427c-b27f-c415297780bb] Receive server push request, request = NotifySubscriberRequest, requestId = 40
13:46:27.400 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd386c5-70a3-427c-b27f-c415297780bb] Ack server push request, request = NotifySubscriberRequest, requestId = 40
13:46:27.618 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:46:34.787 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:46:34.787 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:05:13.496 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:05:13.500 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:05:13.842 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:05:13.843 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2e275719[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:05:13.843 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752212786580_127.0.0.1_8335
14:05:13.843 [nacos-grpc-client-executor-236] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752212786580_127.0.0.1_8335]Ignore complete event,isRunning:false,isAbandon=false
14:05:13.852 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@22bb645f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 237]
14:05:14.012 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:05:14.012 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:05:14.031 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:05:14.031 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:05:14.034 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:05:14.034 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:05:19.832 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:05:20.692 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b9a436e2-bbaa-46d6-a5c8-0c54fe6c9917_config-0
14:05:20.805 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
14:05:20.850 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
14:05:20.863 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
14:05:20.872 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
14:05:20.883 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
14:05:20.895 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
14:05:20.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9a436e2-bbaa-46d6-a5c8-0c54fe6c9917_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:05:20.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9a436e2-bbaa-46d6-a5c8-0c54fe6c9917_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002aa6539eaf8
14:05:20.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9a436e2-bbaa-46d6-a5c8-0c54fe6c9917_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002aa6539ed18
14:05:20.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9a436e2-bbaa-46d6-a5c8-0c54fe6c9917_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:05:20.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9a436e2-bbaa-46d6-a5c8-0c54fe6c9917_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:05:20.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9a436e2-bbaa-46d6-a5c8-0c54fe6c9917_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:05:21.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9a436e2-bbaa-46d6-a5c8-0c54fe6c9917_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752213921765_127.0.0.1_9989
14:05:21.967 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9a436e2-bbaa-46d6-a5c8-0c54fe6c9917_config-0] Notify connected event to listeners.
14:05:21.966 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9a436e2-bbaa-46d6-a5c8-0c54fe6c9917_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:05:21.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9a436e2-bbaa-46d6-a5c8-0c54fe6c9917_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002aa65518ad8
14:05:22.130 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:05:25.884 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:05:25.885 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:05:25.885 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:05:26.065 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:05:27.109 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:05:27.111 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:05:27.112 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:05:34.559 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:05:37.808 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1b92ec48-15db-46bf-ab58-926cee6d6ba4
14:05:37.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b92ec48-15db-46bf-ab58-926cee6d6ba4] RpcClient init label, labels = {module=naming, source=sdk}
14:05:37.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b92ec48-15db-46bf-ab58-926cee6d6ba4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:05:37.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b92ec48-15db-46bf-ab58-926cee6d6ba4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:05:37.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b92ec48-15db-46bf-ab58-926cee6d6ba4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:05:37.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b92ec48-15db-46bf-ab58-926cee6d6ba4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:05:37.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b92ec48-15db-46bf-ab58-926cee6d6ba4] Success to connect to server [localhost:8848] on start up, connectionId = 1752213937821_127.0.0.1_10009
14:05:37.945 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b92ec48-15db-46bf-ab58-926cee6d6ba4] Notify connected event to listeners.
14:05:37.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b92ec48-15db-46bf-ab58-926cee6d6ba4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:05:37.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b92ec48-15db-46bf-ab58-926cee6d6ba4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002aa65518ad8
14:05:38.024 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:05:38.074 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:05:38.241 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.011 seconds (JVM running for 20.142)
14:05:38.259 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:05:38.259 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:05:38.260 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:05:38.473 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:05:38.582 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b92ec48-15db-46bf-ab58-926cee6d6ba4] Receive server push request, request = NotifySubscriberRequest, requestId = 47
14:05:38.603 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b92ec48-15db-46bf-ab58-926cee6d6ba4] Ack server push request, request = NotifySubscriberRequest, requestId = 47
14:06:32.948 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:06:32.948 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:19:41.360 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:19:41.370 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:19:41.712 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:19:41.712 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@78381ece[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:19:41.712 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752213937821_127.0.0.1_10009
14:19:41.717 [nacos-grpc-client-executor-181] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752213937821_127.0.0.1_10009]Ignore complete event,isRunning:false,isAbandon=false
14:19:41.721 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2c41fa5c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 182]
14:19:41.861 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:19:41.861 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:19:41.875 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:19:41.875 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:19:41.875 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:19:41.875 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:19:45.609 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:19:46.432 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 503c4f5a-4c91-46af-b638-7de76b412db1_config-0
14:19:46.506 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
14:19:46.542 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
14:19:46.552 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
14:19:46.560 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
14:19:46.569 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:19:46.580 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:19:46.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [503c4f5a-4c91-46af-b638-7de76b412db1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:19:46.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [503c4f5a-4c91-46af-b638-7de76b412db1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002a6b03bed38
14:19:46.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [503c4f5a-4c91-46af-b638-7de76b412db1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002a6b03bef58
14:19:46.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [503c4f5a-4c91-46af-b638-7de76b412db1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:19:46.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [503c4f5a-4c91-46af-b638-7de76b412db1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:19:46.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [503c4f5a-4c91-46af-b638-7de76b412db1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:19:47.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [503c4f5a-4c91-46af-b638-7de76b412db1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752214787374_127.0.0.1_11012
14:19:47.599 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [503c4f5a-4c91-46af-b638-7de76b412db1_config-0] Notify connected event to listeners.
14:19:47.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [503c4f5a-4c91-46af-b638-7de76b412db1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:19:47.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [503c4f5a-4c91-46af-b638-7de76b412db1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002a6b04f8ad8
14:19:47.776 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:19:51.461 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:19:51.461 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:19:51.461 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:19:51.623 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:19:52.502 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:19:52.503 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:19:52.503 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:19:59.821 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:20:02.753 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 75f88fe5-a20b-4584-bfcc-08f4e69fe019
14:20:02.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75f88fe5-a20b-4584-bfcc-08f4e69fe019] RpcClient init label, labels = {module=naming, source=sdk}
14:20:02.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75f88fe5-a20b-4584-bfcc-08f4e69fe019] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:20:02.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75f88fe5-a20b-4584-bfcc-08f4e69fe019] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:20:02.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75f88fe5-a20b-4584-bfcc-08f4e69fe019] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:20:02.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75f88fe5-a20b-4584-bfcc-08f4e69fe019] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:20:02.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75f88fe5-a20b-4584-bfcc-08f4e69fe019] Success to connect to server [localhost:8848] on start up, connectionId = 1752214802764_127.0.0.1_11028
14:20:02.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75f88fe5-a20b-4584-bfcc-08f4e69fe019] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:20:02.885 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75f88fe5-a20b-4584-bfcc-08f4e69fe019] Notify connected event to listeners.
14:20:02.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75f88fe5-a20b-4584-bfcc-08f4e69fe019] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002a6b04f8ad8
14:20:02.958 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:20:02.997 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:20:03.143 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.188 seconds (JVM running for 19.367)
14:20:03.159 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:20:03.159 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:20:03.159 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:20:03.467 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75f88fe5-a20b-4584-bfcc-08f4e69fe019] Receive server push request, request = NotifySubscriberRequest, requestId = 53
14:20:03.490 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75f88fe5-a20b-4584-bfcc-08f4e69fe019] Ack server push request, request = NotifySubscriberRequest, requestId = 53
14:20:03.562 [RMI TCP Connection(1)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:20:16.178 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:20:16.179 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:30:35.067 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:30:35.067 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:30:35.411 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:30:35.411 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@a2394a4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:30:35.411 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752214802764_127.0.0.1_11028
14:30:35.414 [nacos-grpc-client-executor-142] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752214802764_127.0.0.1_11028]Ignore complete event,isRunning:false,isAbandon=false
14:30:35.419 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@265ffd38[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 143]
14:30:35.618 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:30:35.624 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:30:35.634 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:30:35.636 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:30:35.638 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:30:35.639 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:30:40.621 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:30:41.422 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 18590c07-fd0e-4e17-9fe5-e090d24c8227_config-0
14:30:41.492 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
14:30:41.534 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
14:30:41.544 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
14:30:41.553 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
14:30:41.563 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:30:41.576 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
14:30:41.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18590c07-fd0e-4e17-9fe5-e090d24c8227_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:30:41.580 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18590c07-fd0e-4e17-9fe5-e090d24c8227_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000018f8139eaf8
14:30:41.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18590c07-fd0e-4e17-9fe5-e090d24c8227_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000018f8139ed18
14:30:41.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18590c07-fd0e-4e17-9fe5-e090d24c8227_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:30:41.583 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18590c07-fd0e-4e17-9fe5-e090d24c8227_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:30:41.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18590c07-fd0e-4e17-9fe5-e090d24c8227_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:30:42.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18590c07-fd0e-4e17-9fe5-e090d24c8227_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752215442394_127.0.0.1_11761
14:30:42.614 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18590c07-fd0e-4e17-9fe5-e090d24c8227_config-0] Notify connected event to listeners.
14:30:42.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18590c07-fd0e-4e17-9fe5-e090d24c8227_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:30:42.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18590c07-fd0e-4e17-9fe5-e090d24c8227_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018f81518668
14:30:42.775 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:30:46.656 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:30:46.657 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:30:46.657 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:30:46.848 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:30:47.639 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:30:47.641 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:30:47.642 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:30:56.314 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:31:01.931 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a222e4fd-b74a-4fec-bdb7-73b26b9b83fa
14:31:01.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a222e4fd-b74a-4fec-bdb7-73b26b9b83fa] RpcClient init label, labels = {module=naming, source=sdk}
14:31:01.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a222e4fd-b74a-4fec-bdb7-73b26b9b83fa] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:31:01.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a222e4fd-b74a-4fec-bdb7-73b26b9b83fa] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:31:01.937 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a222e4fd-b74a-4fec-bdb7-73b26b9b83fa] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:31:01.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a222e4fd-b74a-4fec-bdb7-73b26b9b83fa] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:02.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a222e4fd-b74a-4fec-bdb7-73b26b9b83fa] Success to connect to server [localhost:8848] on start up, connectionId = 1752215461951_127.0.0.1_11807
14:31:02.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a222e4fd-b74a-4fec-bdb7-73b26b9b83fa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:02.090 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a222e4fd-b74a-4fec-bdb7-73b26b9b83fa] Notify connected event to listeners.
14:31:02.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a222e4fd-b74a-4fec-bdb7-73b26b9b83fa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018f81518668
14:31:02.171 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:31:02.213 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:31:02.350 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 22.336 seconds (JVM running for 23.308)
14:31:02.370 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:31:02.371 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:31:02.371 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:31:02.734 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a222e4fd-b74a-4fec-bdb7-73b26b9b83fa] Receive server push request, request = NotifySubscriberRequest, requestId = 60
14:31:02.754 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a222e4fd-b74a-4fec-bdb7-73b26b9b83fa] Ack server push request, request = NotifySubscriberRequest, requestId = 60
14:31:02.875 [RMI TCP Connection(1)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:31:31.412 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:31:31.414 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:34:05.143 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:34:05.151 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:34:05.484 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:34:05.484 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@507f2e08[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:34:05.484 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752215461951_127.0.0.1_11807
14:34:05.490 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752215461951_127.0.0.1_11807]Ignore complete event,isRunning:false,isAbandon=false
14:34:05.493 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@66c5af1[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 48]
14:34:05.647 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:34:05.656 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:34:05.665 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:34:05.665 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:34:05.669 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:34:05.671 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:34:10.623 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:34:11.539 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b90345eb-8272-47bb-8023-e255c886fd07_config-0
14:34:11.613 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
14:34:11.652 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
14:34:11.662 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
14:34:11.671 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
14:34:11.679 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
14:34:11.689 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:34:11.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b90345eb-8272-47bb-8023-e255c886fd07_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:34:11.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b90345eb-8272-47bb-8023-e255c886fd07_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002b94b39dd00
14:34:11.693 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b90345eb-8272-47bb-8023-e255c886fd07_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002b94b39df20
14:34:11.694 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b90345eb-8272-47bb-8023-e255c886fd07_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:34:11.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b90345eb-8272-47bb-8023-e255c886fd07_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:34:11.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b90345eb-8272-47bb-8023-e255c886fd07_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:34:12.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b90345eb-8272-47bb-8023-e255c886fd07_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752215652491_127.0.0.1_12033
14:34:12.733 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b90345eb-8272-47bb-8023-e255c886fd07_config-0] Notify connected event to listeners.
14:34:12.733 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b90345eb-8272-47bb-8023-e255c886fd07_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:12.735 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b90345eb-8272-47bb-8023-e255c886fd07_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002b94b517da0
14:34:12.901 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:34:17.290 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:34:17.290 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:34:17.294 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:34:17.687 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:34:19.053 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:34:19.053 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:34:19.053 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:34:28.458 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:34:31.439 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 453b4b56-5bef-4bb4-96a9-0bc519b8c117
14:34:31.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453b4b56-5bef-4bb4-96a9-0bc519b8c117] RpcClient init label, labels = {module=naming, source=sdk}
14:34:31.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453b4b56-5bef-4bb4-96a9-0bc519b8c117] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:34:31.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453b4b56-5bef-4bb4-96a9-0bc519b8c117] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:34:31.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453b4b56-5bef-4bb4-96a9-0bc519b8c117] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:34:31.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453b4b56-5bef-4bb4-96a9-0bc519b8c117] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:34:31.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453b4b56-5bef-4bb4-96a9-0bc519b8c117] Success to connect to server [localhost:8848] on start up, connectionId = 1752215671451_127.0.0.1_12056
14:34:31.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453b4b56-5bef-4bb4-96a9-0bc519b8c117] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:31.578 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453b4b56-5bef-4bb4-96a9-0bc519b8c117] Notify connected event to listeners.
14:34:31.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453b4b56-5bef-4bb4-96a9-0bc519b8c117] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002b94b517da0
14:34:31.661 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:34:31.699 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:34:31.856 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.881 seconds (JVM running for 22.968)
14:34:31.875 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:34:31.876 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:34:31.877 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:34:32.211 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453b4b56-5bef-4bb4-96a9-0bc519b8c117] Receive server push request, request = NotifySubscriberRequest, requestId = 68
14:34:32.231 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453b4b56-5bef-4bb4-96a9-0bc519b8c117] Ack server push request, request = NotifySubscriberRequest, requestId = 68
14:34:32.404 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:34:38.181 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:34:38.181 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:09:05.865 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:09:05.865 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:09:06.205 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:09:06.205 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@53e3fce0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:09:06.205 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752215671451_127.0.0.1_12056
15:09:06.205 [nacos-grpc-client-executor-434] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752215671451_127.0.0.1_12056]Ignore complete event,isRunning:false,isAbandon=false
15:09:06.215 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@49f4ef48[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 435]
15:09:06.350 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:09:06.350 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:09:06.366 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:09:06.366 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:09:06.368 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:09:06.368 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:09:11.094 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:09:11.669 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 22b03657-5ae5-4568-afd1-a053a6879585_config-0
15:09:11.714 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 6 values 
15:09:11.746 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
15:09:11.746 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
15:09:11.761 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
15:09:11.769 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
15:09:11.777 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
15:09:11.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:09:11.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000236893ce8d8
15:09:11.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000236893ceaf8
15:09:11.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:09:11.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:09:11.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:09:12.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752217752313_127.0.0.1_1231
15:09:12.492 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] Notify connected event to listeners.
15:09:12.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:09:12.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023689508ad8
15:09:12.581 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:09:15.197 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:09:15.198 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:09:15.198 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:09:15.318 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:09:15.929 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:09:15.930 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:09:15.930 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:09:21.033 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:09:23.284 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 64822bff-48a2-4bd8-9d94-0b02e539f425
15:09:23.284 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64822bff-48a2-4bd8-9d94-0b02e539f425] RpcClient init label, labels = {module=naming, source=sdk}
15:09:23.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64822bff-48a2-4bd8-9d94-0b02e539f425] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:09:23.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64822bff-48a2-4bd8-9d94-0b02e539f425] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:09:23.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64822bff-48a2-4bd8-9d94-0b02e539f425] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:09:23.287 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64822bff-48a2-4bd8-9d94-0b02e539f425] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:09:23.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64822bff-48a2-4bd8-9d94-0b02e539f425] Success to connect to server [localhost:8848] on start up, connectionId = 1752217763294_127.0.0.1_1244
15:09:23.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64822bff-48a2-4bd8-9d94-0b02e539f425] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:09:23.419 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64822bff-48a2-4bd8-9d94-0b02e539f425] Notify connected event to listeners.
15:09:23.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64822bff-48a2-4bd8-9d94-0b02e539f425] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023689508ad8
15:09:23.472 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:09:23.508 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:09:23.652 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.072 seconds (JVM running for 13.998)
15:09:23.666 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:09:23.667 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:09:23.668 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:09:24.007 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64822bff-48a2-4bd8-9d94-0b02e539f425] Receive server push request, request = NotifySubscriberRequest, requestId = 75
15:09:24.024 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64822bff-48a2-4bd8-9d94-0b02e539f425] Ack server push request, request = NotifySubscriberRequest, requestId = 75
15:09:24.253 [RMI TCP Connection(6)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:09:58.569 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:09:58.569 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:14:28.266 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] Server healthy check fail, currentConnection = 1752217752313_127.0.0.1_1231
15:14:28.274 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:14:28.279 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64822bff-48a2-4bd8-9d94-0b02e539f425] Server check success, currentServer is localhost:8848 
15:14:37.982 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] Success to connect a server [localhost:8848], connectionId = 1752218077856_127.0.0.1_1604
15:14:37.982 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1752217752313_127.0.0.1_1231
15:14:37.982 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752217752313_127.0.0.1_1231
15:14:37.982 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] Notify disconnected event to listeners
15:14:37.982 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b03657-5ae5-4568-afd1-a053a6879585_config-0] Notify connected event to listeners.
15:18:32.155 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:18:32.171 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:18:32.492 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:18:32.492 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1add340a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:18:32.492 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752217763294_127.0.0.1_1244
15:18:32.492 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@15d8a6dd[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 90]
15:18:32.628 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:18:32.628 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:18:32.637 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:18:32.637 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:18:32.637 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:18:32.637 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:18:35.272 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:18:35.806 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3392ff7d-eaa9-416a-ad5d-80259369d466_config-0
15:18:35.858 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
15:18:35.882 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
15:18:35.888 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
15:18:35.894 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
15:18:35.900 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
15:18:35.907 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
15:18:35.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3392ff7d-eaa9-416a-ad5d-80259369d466_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:18:35.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3392ff7d-eaa9-416a-ad5d-80259369d466_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023e603bdd00
15:18:35.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3392ff7d-eaa9-416a-ad5d-80259369d466_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000023e603bdf20
15:18:35.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3392ff7d-eaa9-416a-ad5d-80259369d466_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:18:35.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3392ff7d-eaa9-416a-ad5d-80259369d466_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:18:35.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3392ff7d-eaa9-416a-ad5d-80259369d466_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:18:36.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3392ff7d-eaa9-416a-ad5d-80259369d466_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752218316400_127.0.0.1_1888
15:18:36.579 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3392ff7d-eaa9-416a-ad5d-80259369d466_config-0] Notify connected event to listeners.
15:18:36.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3392ff7d-eaa9-416a-ad5d-80259369d466_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:18:36.580 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3392ff7d-eaa9-416a-ad5d-80259369d466_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023e604f8228
15:18:36.650 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:18:38.954 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:18:38.954 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:18:38.954 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:18:39.057 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:18:39.513 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:18:39.514 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:18:39.515 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:18:44.299 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:18:46.280 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ec379ace-027b-4fae-81db-70b6d7a28350
15:18:46.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec379ace-027b-4fae-81db-70b6d7a28350] RpcClient init label, labels = {module=naming, source=sdk}
15:18:46.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec379ace-027b-4fae-81db-70b6d7a28350] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:18:46.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec379ace-027b-4fae-81db-70b6d7a28350] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:18:46.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec379ace-027b-4fae-81db-70b6d7a28350] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:18:46.283 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec379ace-027b-4fae-81db-70b6d7a28350] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:18:46.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec379ace-027b-4fae-81db-70b6d7a28350] Success to connect to server [localhost:8848] on start up, connectionId = 1752218326290_127.0.0.1_1909
15:18:46.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec379ace-027b-4fae-81db-70b6d7a28350] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:18:46.399 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec379ace-027b-4fae-81db-70b6d7a28350] Notify connected event to listeners.
15:18:46.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec379ace-027b-4fae-81db-70b6d7a28350] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023e604f8228
15:18:46.437 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:18:46.462 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:18:46.557 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 11.792 seconds (JVM running for 12.778)
15:18:46.568 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:18:46.569 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:18:46.570 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:18:46.746 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:18:46.977 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec379ace-027b-4fae-81db-70b6d7a28350] Receive server push request, request = NotifySubscriberRequest, requestId = 82
15:18:46.991 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec379ace-027b-4fae-81db-70b6d7a28350] Ack server push request, request = NotifySubscriberRequest, requestId = 82
15:18:53.702 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:18:53.702 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:20:11.079 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:20:11.083 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:20:11.416 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:20:11.416 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@62604e7d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:20:11.416 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752218326290_127.0.0.1_1909
15:20:11.416 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752218326290_127.0.0.1_1909]Ignore complete event,isRunning:false,isAbandon=false
15:20:11.416 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@fccad0b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 29]
15:20:11.552 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:20:11.552 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:20:11.552 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:20:11.552 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:20:11.568 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:20:11.570 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:20:14.149 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:20:14.699 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-758d-414d-8e2a-88a290609c57_config-0
15:20:14.746 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 6 values 
15:20:14.773 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
15:20:14.778 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
15:20:14.784 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
15:20:14.790 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
15:20:14.798 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
15:20:14.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-758d-414d-8e2a-88a290609c57_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:20:14.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-758d-414d-8e2a-88a290609c57_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000017b8139daf0
15:20:14.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-758d-414d-8e2a-88a290609c57_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000017b8139dd10
15:20:14.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-758d-414d-8e2a-88a290609c57_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:20:14.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-758d-414d-8e2a-88a290609c57_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:20:14.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-758d-414d-8e2a-88a290609c57_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:20:15.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-758d-414d-8e2a-88a290609c57_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752218415297_127.0.0.1_2030
15:20:15.480 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-758d-414d-8e2a-88a290609c57_config-0] Notify connected event to listeners.
15:20:15.480 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-758d-414d-8e2a-88a290609c57_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:20:15.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-758d-414d-8e2a-88a290609c57_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017b81517b88
15:20:15.555 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:20:17.908 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:20:17.908 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:20:17.908 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:20:18.016 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:20:18.532 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:20:18.533 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:20:18.533 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:20:26.591 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:20:31.138 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f0a1787f-a36c-4237-863a-a4cee5601025
15:20:31.138 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0a1787f-a36c-4237-863a-a4cee5601025] RpcClient init label, labels = {module=naming, source=sdk}
15:20:31.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0a1787f-a36c-4237-863a-a4cee5601025] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:20:31.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0a1787f-a36c-4237-863a-a4cee5601025] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:20:31.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0a1787f-a36c-4237-863a-a4cee5601025] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:20:31.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0a1787f-a36c-4237-863a-a4cee5601025] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:20:31.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0a1787f-a36c-4237-863a-a4cee5601025] Success to connect to server [localhost:8848] on start up, connectionId = 1752218431155_127.0.0.1_2053
15:20:31.277 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0a1787f-a36c-4237-863a-a4cee5601025] Notify connected event to listeners.
15:20:31.277 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0a1787f-a36c-4237-863a-a4cee5601025] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:20:31.277 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0a1787f-a36c-4237-863a-a4cee5601025] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017b81517b88
15:20:31.342 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:20:31.388 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:20:31.619 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 17.938 seconds (JVM running for 18.895)
15:20:31.643 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:20:31.644 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:20:31.645 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:20:31.902 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0a1787f-a36c-4237-863a-a4cee5601025] Receive server push request, request = NotifySubscriberRequest, requestId = 89
15:20:31.917 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0a1787f-a36c-4237-863a-a4cee5601025] Ack server push request, request = NotifySubscriberRequest, requestId = 89
15:20:32.254 [RMI TCP Connection(6)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:20:46.544 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:20:46.544 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:22:07.767 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:22:07.767 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:22:08.113 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:22:08.114 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@59e4e135[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:22:08.114 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752218431155_127.0.0.1_2053
15:22:08.117 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752218431155_127.0.0.1_2053]Ignore complete event,isRunning:false,isAbandon=false
15:22:08.119 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@8149c5c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 19]
15:22:08.269 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:22:08.279 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:22:08.281 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:22:08.281 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:22:08.281 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:22:08.281 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:22:11.065 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:22:11.610 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e80b857a-347a-4f44-aad0-f8d3b1317755_config-0
15:22:11.662 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
15:22:11.690 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
15:22:11.694 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
15:22:11.700 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
15:22:11.702 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
15:22:11.711 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
15:22:11.714 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e80b857a-347a-4f44-aad0-f8d3b1317755_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:22:11.714 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e80b857a-347a-4f44-aad0-f8d3b1317755_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000268cc3bdd00
15:22:11.714 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e80b857a-347a-4f44-aad0-f8d3b1317755_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000268cc3bdf20
15:22:11.714 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e80b857a-347a-4f44-aad0-f8d3b1317755_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:22:11.714 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e80b857a-347a-4f44-aad0-f8d3b1317755_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:22:11.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e80b857a-347a-4f44-aad0-f8d3b1317755_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:22:12.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e80b857a-347a-4f44-aad0-f8d3b1317755_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752218532195_127.0.0.1_2176
15:22:12.369 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e80b857a-347a-4f44-aad0-f8d3b1317755_config-0] Notify connected event to listeners.
15:22:12.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e80b857a-347a-4f44-aad0-f8d3b1317755_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:22:12.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e80b857a-347a-4f44-aad0-f8d3b1317755_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000268cc4f8228
15:22:12.434 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:22:14.844 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:22:14.844 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:22:14.844 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:22:14.957 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:22:15.624 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:22:15.625 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:22:15.625 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:22:20.780 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:22:22.795 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 736561a0-4fea-4a05-b42c-d121e6c7466a
15:22:22.795 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [736561a0-4fea-4a05-b42c-d121e6c7466a] RpcClient init label, labels = {module=naming, source=sdk}
15:22:22.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [736561a0-4fea-4a05-b42c-d121e6c7466a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:22:22.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [736561a0-4fea-4a05-b42c-d121e6c7466a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:22:22.797 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [736561a0-4fea-4a05-b42c-d121e6c7466a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:22:22.797 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [736561a0-4fea-4a05-b42c-d121e6c7466a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:22:22.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [736561a0-4fea-4a05-b42c-d121e6c7466a] Success to connect to server [localhost:8848] on start up, connectionId = 1752218542805_127.0.0.1_2199
15:22:22.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [736561a0-4fea-4a05-b42c-d121e6c7466a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:22:22.914 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [736561a0-4fea-4a05-b42c-d121e6c7466a] Notify connected event to listeners.
15:22:22.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [736561a0-4fea-4a05-b42c-d121e6c7466a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000268cc4f8228
15:22:22.951 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:22:22.972 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:22:23.063 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.473 seconds (JVM running for 13.431)
15:22:23.076 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:22:23.076 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:22:23.077 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:22:23.187 [RMI TCP Connection(1)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:22:23.493 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [736561a0-4fea-4a05-b42c-d121e6c7466a] Receive server push request, request = NotifySubscriberRequest, requestId = 95
15:22:23.509 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [736561a0-4fea-4a05-b42c-d121e6c7466a] Ack server push request, request = NotifySubscriberRequest, requestId = 95
15:22:33.948 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:22:33.948 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:25:32.682 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:25:32.685 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:25:33.022 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:25:33.022 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@542b1196[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:25:33.022 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752218542805_127.0.0.1_2199
15:25:33.025 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752218542805_127.0.0.1_2199]Ignore complete event,isRunning:false,isAbandon=false
15:25:33.026 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@39b8a74e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 42]
15:25:33.181 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:25:33.183 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:25:33.188 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:25:33.188 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:25:33.188 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:25:33.188 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:25:37.140 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:25:37.692 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e3f2e7f6-b8f7-41f4-a451-0869db3ed647_config-0
15:25:37.743 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
15:25:37.770 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
15:25:37.777 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
15:25:37.783 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
15:25:37.791 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
15:25:37.797 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
15:25:37.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3f2e7f6-b8f7-41f4-a451-0869db3ed647_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:25:37.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3f2e7f6-b8f7-41f4-a451-0869db3ed647_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002dba83beaf8
15:25:37.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3f2e7f6-b8f7-41f4-a451-0869db3ed647_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002dba83bed18
15:25:37.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3f2e7f6-b8f7-41f4-a451-0869db3ed647_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:25:37.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3f2e7f6-b8f7-41f4-a451-0869db3ed647_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:25:37.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3f2e7f6-b8f7-41f4-a451-0869db3ed647_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:25:38.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3f2e7f6-b8f7-41f4-a451-0869db3ed647_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752218738304_127.0.0.1_2471
15:25:38.481 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3f2e7f6-b8f7-41f4-a451-0869db3ed647_config-0] Notify connected event to listeners.
15:25:38.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3f2e7f6-b8f7-41f4-a451-0869db3ed647_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:25:38.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3f2e7f6-b8f7-41f4-a451-0869db3ed647_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002dba84f8668
15:25:38.561 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:25:40.940 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:25:40.940 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:25:40.940 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:25:41.048 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:25:41.515 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:25:41.516 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:25:41.516 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:25:46.348 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:25:48.294 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c8382098-3fae-4ad3-9430-7d432360f992
15:25:48.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8382098-3fae-4ad3-9430-7d432360f992] RpcClient init label, labels = {module=naming, source=sdk}
15:25:48.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8382098-3fae-4ad3-9430-7d432360f992] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:25:48.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8382098-3fae-4ad3-9430-7d432360f992] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:25:48.296 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8382098-3fae-4ad3-9430-7d432360f992] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:25:48.296 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8382098-3fae-4ad3-9430-7d432360f992] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:25:48.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8382098-3fae-4ad3-9430-7d432360f992] Success to connect to server [localhost:8848] on start up, connectionId = 1752218748304_127.0.0.1_2483
15:25:48.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8382098-3fae-4ad3-9430-7d432360f992] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:25:48.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8382098-3fae-4ad3-9430-7d432360f992] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002dba84f8668
15:25:48.418 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8382098-3fae-4ad3-9430-7d432360f992] Notify connected event to listeners.
15:25:48.454 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:25:48.473 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:25:48.560 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 11.883 seconds (JVM running for 12.668)
15:25:48.570 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:25:48.571 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:25:48.571 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:25:48.986 [RMI TCP Connection(1)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:25:48.993 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8382098-3fae-4ad3-9430-7d432360f992] Receive server push request, request = NotifySubscriberRequest, requestId = 103
15:25:49.007 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8382098-3fae-4ad3-9430-7d432360f992] Ack server push request, request = NotifySubscriberRequest, requestId = 103
15:25:59.993 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:25:59.993 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:51:29.652 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:51:29.655 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:51:29.987 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:51:29.987 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@69f58144[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:51:29.987 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752218748304_127.0.0.1_2483
15:51:29.990 [nacos-grpc-client-executor-274] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752218748304_127.0.0.1_2483]Ignore complete event,isRunning:false,isAbandon=false
15:51:29.994 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7bed8c88[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 275]
15:51:30.149 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:51:30.152 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:51:30.160 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:51:30.161 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:51:30.162 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:51:30.162 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:51:54.450 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:51:55.121 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of faeb7f1b-aadc-4989-889a-e050fbf9521d_config-0
15:51:55.178 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
15:51:55.206 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
15:51:55.212 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
15:51:55.219 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
15:51:55.226 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
15:51:55.233 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
15:51:55.235 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [faeb7f1b-aadc-4989-889a-e050fbf9521d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:51:55.235 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [faeb7f1b-aadc-4989-889a-e050fbf9521d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001db223b8200
15:51:55.235 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [faeb7f1b-aadc-4989-889a-e050fbf9521d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001db223b8420
15:51:55.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [faeb7f1b-aadc-4989-889a-e050fbf9521d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:51:55.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [faeb7f1b-aadc-4989-889a-e050fbf9521d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:51:55.242 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [faeb7f1b-aadc-4989-889a-e050fbf9521d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:51:55.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [faeb7f1b-aadc-4989-889a-e050fbf9521d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752220315741_127.0.0.1_4152
15:51:55.931 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [faeb7f1b-aadc-4989-889a-e050fbf9521d_config-0] Notify connected event to listeners.
15:51:55.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [faeb7f1b-aadc-4989-889a-e050fbf9521d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:51:55.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [faeb7f1b-aadc-4989-889a-e050fbf9521d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001db224f0228
15:51:56.039 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:51:58.584 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:51:58.585 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:51:58.585 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:51:58.697 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:51:59.334 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:51:59.336 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:51:59.336 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:52:04.236 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:52:06.257 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 84e5938b-6e53-4b66-9c65-0cc00ab668ef
15:52:06.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84e5938b-6e53-4b66-9c65-0cc00ab668ef] RpcClient init label, labels = {module=naming, source=sdk}
15:52:06.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84e5938b-6e53-4b66-9c65-0cc00ab668ef] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:52:06.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84e5938b-6e53-4b66-9c65-0cc00ab668ef] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:52:06.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84e5938b-6e53-4b66-9c65-0cc00ab668ef] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:52:06.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84e5938b-6e53-4b66-9c65-0cc00ab668ef] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:06.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84e5938b-6e53-4b66-9c65-0cc00ab668ef] Success to connect to server [localhost:8848] on start up, connectionId = 1752220326265_127.0.0.1_4169
15:52:06.380 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84e5938b-6e53-4b66-9c65-0cc00ab668ef] Notify connected event to listeners.
15:52:06.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84e5938b-6e53-4b66-9c65-0cc00ab668ef] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:52:06.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84e5938b-6e53-4b66-9c65-0cc00ab668ef] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001db224f0228
15:52:06.418 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:52:06.439 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:52:06.534 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.569 seconds (JVM running for 14.532)
15:52:06.546 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:52:06.546 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:52:06.547 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:52:06.994 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84e5938b-6e53-4b66-9c65-0cc00ab668ef] Receive server push request, request = NotifySubscriberRequest, requestId = 110
15:52:07.011 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84e5938b-6e53-4b66-9c65-0cc00ab668ef] Ack server push request, request = NotifySubscriberRequest, requestId = 110
15:52:15.517 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:52:17.300 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:52:17.300 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:24:07.265 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:24:07.267 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:24:07.603 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:24:07.604 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@68ae3cb6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:24:07.604 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752220326265_127.0.0.1_4169
16:24:07.606 [nacos-grpc-client-executor-398] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752220326265_127.0.0.1_4169]Ignore complete event,isRunning:false,isAbandon=false
16:24:07.609 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@690b5a6a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 399]
16:24:07.752 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:24:07.754 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:24:07.760 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:24:07.760 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:24:07.762 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:24:07.762 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:24:12.523 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:24:13.074 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b1928077-0496-436d-802b-3458157614ce_config-0
16:24:13.125 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
16:24:13.153 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
16:24:13.161 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
16:24:13.168 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
16:24:13.175 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
16:24:13.183 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
16:24:13.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1928077-0496-436d-802b-3458157614ce_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:24:13.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1928077-0496-436d-802b-3458157614ce_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000027a3b3be690
16:24:13.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1928077-0496-436d-802b-3458157614ce_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000027a3b3be8b0
16:24:13.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1928077-0496-436d-802b-3458157614ce_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:24:13.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1928077-0496-436d-802b-3458157614ce_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:24:13.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1928077-0496-436d-802b-3458157614ce_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:24:13.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1928077-0496-436d-802b-3458157614ce_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752222253690_127.0.0.1_7060
16:24:13.872 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1928077-0496-436d-802b-3458157614ce_config-0] Notify connected event to listeners.
16:24:13.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1928077-0496-436d-802b-3458157614ce_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:24:13.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1928077-0496-436d-802b-3458157614ce_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000027a3b4f88c8
16:24:13.959 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:24:16.402 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:24:16.402 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:24:16.403 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:24:16.517 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:24:17.081 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:24:17.082 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:24:17.082 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:24:25.622 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:24:30.783 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f284ab20-ff32-43ba-b6eb-b7f348767a4e
16:24:30.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f284ab20-ff32-43ba-b6eb-b7f348767a4e] RpcClient init label, labels = {module=naming, source=sdk}
16:24:30.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f284ab20-ff32-43ba-b6eb-b7f348767a4e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:24:30.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f284ab20-ff32-43ba-b6eb-b7f348767a4e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:24:30.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f284ab20-ff32-43ba-b6eb-b7f348767a4e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:24:30.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f284ab20-ff32-43ba-b6eb-b7f348767a4e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:24:30.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f284ab20-ff32-43ba-b6eb-b7f348767a4e] Success to connect to server [localhost:8848] on start up, connectionId = 1752222270799_127.0.0.1_7082
16:24:30.917 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f284ab20-ff32-43ba-b6eb-b7f348767a4e] Notify connected event to listeners.
16:24:30.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f284ab20-ff32-43ba-b6eb-b7f348767a4e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:24:30.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f284ab20-ff32-43ba-b6eb-b7f348767a4e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000027a3b4f88c8
16:24:30.999 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:24:31.055 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:24:31.267 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.225 seconds (JVM running for 20.071)
16:24:31.295 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:24:31.295 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:24:31.296 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:24:31.504 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f284ab20-ff32-43ba-b6eb-b7f348767a4e] Receive server push request, request = NotifySubscriberRequest, requestId = 119
16:24:31.520 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f284ab20-ff32-43ba-b6eb-b7f348767a4e] Ack server push request, request = NotifySubscriberRequest, requestId = 119
16:24:31.795 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:24:55.373 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:24:55.374 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:39:00.555 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:39:00.558 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:39:00.881 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:39:00.882 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@25c54dc0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:39:00.882 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752222270799_127.0.0.1_7082
16:39:00.883 [nacos-grpc-client-executor-182] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752222270799_127.0.0.1_7082]Ignore complete event,isRunning:false,isAbandon=false
16:39:00.884 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@39a04004[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 183]
16:39:01.011 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:39:01.013 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:39:01.018 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:39:01.018 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:39:01.018 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:39:01.019 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:39:05.417 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:39:05.940 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 726cecc4-2097-4db1-ac5c-e2f2e94da718_config-0
16:39:05.990 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
16:39:06.020 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
16:39:06.058 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
16:39:06.067 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
16:39:06.075 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
16:39:06.087 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
16:39:06.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [726cecc4-2097-4db1-ac5c-e2f2e94da718_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:39:06.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [726cecc4-2097-4db1-ac5c-e2f2e94da718_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000025a5c3ce480
16:39:06.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [726cecc4-2097-4db1-ac5c-e2f2e94da718_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000025a5c3ce6a0
16:39:06.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [726cecc4-2097-4db1-ac5c-e2f2e94da718_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:39:06.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [726cecc4-2097-4db1-ac5c-e2f2e94da718_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:39:06.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [726cecc4-2097-4db1-ac5c-e2f2e94da718_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:39:06.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [726cecc4-2097-4db1-ac5c-e2f2e94da718_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752223146599_127.0.0.1_8218
16:39:06.782 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [726cecc4-2097-4db1-ac5c-e2f2e94da718_config-0] Notify connected event to listeners.
16:39:06.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [726cecc4-2097-4db1-ac5c-e2f2e94da718_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:06.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [726cecc4-2097-4db1-ac5c-e2f2e94da718_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025a5c508228
16:39:06.858 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:39:09.275 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:39:09.275 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:39:09.276 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:39:09.395 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:39:09.914 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:39:09.916 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:39:09.916 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:39:14.844 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:39:16.970 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1fde1518-019f-4665-8738-f88d174237f8
16:39:16.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fde1518-019f-4665-8738-f88d174237f8] RpcClient init label, labels = {module=naming, source=sdk}
16:39:16.971 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fde1518-019f-4665-8738-f88d174237f8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:39:16.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fde1518-019f-4665-8738-f88d174237f8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:39:16.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fde1518-019f-4665-8738-f88d174237f8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:39:16.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fde1518-019f-4665-8738-f88d174237f8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:39:17.091 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fde1518-019f-4665-8738-f88d174237f8] Success to connect to server [localhost:8848] on start up, connectionId = 1752223156979_127.0.0.1_8230
16:39:17.091 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fde1518-019f-4665-8738-f88d174237f8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:17.091 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fde1518-019f-4665-8738-f88d174237f8] Notify connected event to listeners.
16:39:17.091 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fde1518-019f-4665-8738-f88d174237f8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025a5c508228
16:39:17.128 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:39:17.149 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:39:17.233 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.313 seconds (JVM running for 13.199)
16:39:17.243 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:39:17.243 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:39:17.244 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:39:17.577 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:39:17.680 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fde1518-019f-4665-8738-f88d174237f8] Receive server push request, request = NotifySubscriberRequest, requestId = 123
16:39:17.695 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fde1518-019f-4665-8738-f88d174237f8] Ack server push request, request = NotifySubscriberRequest, requestId = 123
16:39:24.955 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:39:24.956 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:40:27.404 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:40:27.407 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:40:27.742 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:40:27.742 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2755434a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:40:27.743 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752223156979_127.0.0.1_8230
16:40:27.745 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752223156979_127.0.0.1_8230]Ignore complete event,isRunning:false,isAbandon=false
16:40:27.746 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3f04a8fb[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 25]
16:40:27.886 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:40:27.887 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:40:27.892 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:40:27.892 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:40:27.893 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:40:27.893 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:40:31.754 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:40:32.260 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 46008a4e-c76a-4778-bb92-d99908260bae_config-0
16:40:32.312 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
16:40:32.339 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
16:40:32.345 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
16:40:32.353 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
16:40:32.359 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
16:40:32.366 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
16:40:32.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46008a4e-c76a-4778-bb92-d99908260bae_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:40:32.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46008a4e-c76a-4778-bb92-d99908260bae_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000202d43bdd70
16:40:32.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46008a4e-c76a-4778-bb92-d99908260bae_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000202d43bdf90
16:40:32.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46008a4e-c76a-4778-bb92-d99908260bae_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:40:32.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46008a4e-c76a-4778-bb92-d99908260bae_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:40:32.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46008a4e-c76a-4778-bb92-d99908260bae_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:40:33.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46008a4e-c76a-4778-bb92-d99908260bae_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752223232867_127.0.0.1_8350
16:40:33.049 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46008a4e-c76a-4778-bb92-d99908260bae_config-0] Notify connected event to listeners.
16:40:33.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46008a4e-c76a-4778-bb92-d99908260bae_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:40:33.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46008a4e-c76a-4778-bb92-d99908260bae_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000202d44f7cb0
16:40:33.133 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:40:35.475 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:40:35.476 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:40:35.476 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:40:35.585 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:40:36.072 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:40:36.073 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:40:36.073 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:40:40.838 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:40:42.884 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of abbe7b6c-2deb-42ee-8d4f-e686734800c9
16:40:42.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbe7b6c-2deb-42ee-8d4f-e686734800c9] RpcClient init label, labels = {module=naming, source=sdk}
16:40:42.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbe7b6c-2deb-42ee-8d4f-e686734800c9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:40:42.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbe7b6c-2deb-42ee-8d4f-e686734800c9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:40:42.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbe7b6c-2deb-42ee-8d4f-e686734800c9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:40:42.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbe7b6c-2deb-42ee-8d4f-e686734800c9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:40:43.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbe7b6c-2deb-42ee-8d4f-e686734800c9] Success to connect to server [localhost:8848] on start up, connectionId = 1752223242894_127.0.0.1_8363
16:40:43.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbe7b6c-2deb-42ee-8d4f-e686734800c9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:40:43.016 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbe7b6c-2deb-42ee-8d4f-e686734800c9] Notify connected event to listeners.
16:40:43.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbe7b6c-2deb-42ee-8d4f-e686734800c9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000202d44f7cb0
16:40:43.055 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:40:43.077 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:40:43.162 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 11.872 seconds (JVM running for 12.695)
16:40:43.172 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:40:43.173 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:40:43.174 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:40:43.434 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:40:43.622 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbe7b6c-2deb-42ee-8d4f-e686734800c9] Receive server push request, request = NotifySubscriberRequest, requestId = 131
16:40:43.635 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbe7b6c-2deb-42ee-8d4f-e686734800c9] Ack server push request, request = NotifySubscriberRequest, requestId = 131
16:40:47.014 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:40:47.014 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:08:51.940 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:08:51.942 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:08:52.286 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:08:52.286 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@128f0ad0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:08:52.286 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752223242894_127.0.0.1_8363
17:08:52.292 [nacos-grpc-client-executor-348] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752223242894_127.0.0.1_8363]Ignore complete event,isRunning:false,isAbandon=false
17:08:52.295 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@49c543b3[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 349]
17:08:52.465 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:08:52.469 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:08:52.480 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:08:52.480 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:08:52.482 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:08:52.483 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:08:58.419 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:08:59.296 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 173c497e-1883-47e0-b9f5-62b3dee9635b_config-0
17:08:59.376 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
17:08:59.416 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
17:08:59.425 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
17:08:59.435 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
17:08:59.444 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
17:08:59.455 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
17:08:59.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:08:59.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000022f0139e480
17:08:59.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000022f0139e6a0
17:08:59.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:08:59.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:08:59.469 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:09:00.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752224940202_127.0.0.1_11252
17:09:00.416 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] Notify connected event to listeners.
17:09:00.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:09:00.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022f01518228
17:09:00.610 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:09:05.040 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:09:05.040 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:09:05.040 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:09:05.397 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:09:07.010 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:09:07.012 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:09:07.012 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:09:16.369 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:09:19.270 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 70b190f2-8bc8-4d10-8261-a47a30b11766
17:09:19.270 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] RpcClient init label, labels = {module=naming, source=sdk}
17:09:19.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:09:19.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:09:19.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:09:19.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:09:19.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Success to connect to server [localhost:8848] on start up, connectionId = 1752224959284_127.0.0.1_11294
17:09:19.421 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:09:19.421 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Notify connected event to listeners.
17:09:19.421 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022f01518228
17:09:19.503 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:09:19.544 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
17:09:19.709 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.936 seconds (JVM running for 23.086)
17:09:19.730 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:09:19.730 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:09:19.731 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:09:19.945 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:09:20.021 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Receive server push request, request = NotifySubscriberRequest, requestId = 138
17:09:20.038 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Ack server push request, request = NotifySubscriberRequest, requestId = 138
17:09:26.277 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:09:26.278 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:14:18.966 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Server healthy check fail, currentConnection = 1752224959284_127.0.0.1_11294
17:14:18.966 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] Server healthy check fail, currentConnection = 1752224940202_127.0.0.1_11252
17:14:18.968 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:14:18.968 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:14:19.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] Success to connect a server [localhost:8848], connectionId = 1752225258986_127.0.0.1_11628
17:14:19.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Success to connect a server [localhost:8848], connectionId = 1752225258986_127.0.0.1_11627
17:14:19.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1752224940202_127.0.0.1_11252
17:14:19.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Abandon prev connection, server is localhost:8848, connectionId is 1752224959284_127.0.0.1_11294
17:14:19.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752224940202_127.0.0.1_11252
17:14:19.115 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752224959284_127.0.0.1_11294
17:14:19.118 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752224959284_127.0.0.1_11294]Ignore complete event,isRunning:false,isAbandon=true
17:14:19.119 [nacos-grpc-client-executor-63] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752224940202_127.0.0.1_11252]Ignore complete event,isRunning:false,isAbandon=true
17:14:19.129 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] Notify disconnected event to listeners
17:14:19.129 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Notify disconnected event to listeners
17:14:19.129 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [173c497e-1883-47e0-b9f5-62b3dee9635b_config-0] Notify connected event to listeners.
17:14:19.136 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Notify connected event to listeners.
17:14:50.471 [nacos-grpc-client-executor-60] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Receive server push request, request = NotifySubscriberRequest, requestId = 145
17:14:50.490 [nacos-grpc-client-executor-60] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [70b190f2-8bc8-4d10-8261-a47a30b11766] Ack server push request, request = NotifySubscriberRequest, requestId = 145
17:28:18.045 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:28:18.050 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:28:18.393 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:28:18.394 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5aae83a1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:28:18.394 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752225258986_127.0.0.1_11627
17:28:18.396 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@37fcc26d[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 226]
17:28:18.604 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:28:18.612 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:28:18.617 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:28:18.619 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:28:18.622 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:28:18.622 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:28:24.691 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:28:25.501 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7882b59f-a114-42e2-ad36-78ba1d3b4500_config-0
17:28:25.608 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 51 ms to scan 1 urls, producing 3 keys and 6 values 
17:28:25.656 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
17:28:25.667 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
17:28:25.679 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
17:28:25.690 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
17:28:25.707 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
17:28:25.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7882b59f-a114-42e2-ad36-78ba1d3b4500_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:28:25.711 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7882b59f-a114-42e2-ad36-78ba1d3b4500_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002629239ef80
17:28:25.711 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7882b59f-a114-42e2-ad36-78ba1d3b4500_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002629239f1a0
17:28:25.711 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7882b59f-a114-42e2-ad36-78ba1d3b4500_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:28:25.711 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7882b59f-a114-42e2-ad36-78ba1d3b4500_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:28:25.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7882b59f-a114-42e2-ad36-78ba1d3b4500_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:28:26.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7882b59f-a114-42e2-ad36-78ba1d3b4500_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752226106536_127.0.0.1_12725
17:28:26.740 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7882b59f-a114-42e2-ad36-78ba1d3b4500_config-0] Notify connected event to listeners.
17:28:26.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7882b59f-a114-42e2-ad36-78ba1d3b4500_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:28:26.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7882b59f-a114-42e2-ad36-78ba1d3b4500_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026292519450
17:28:26.913 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:28:31.386 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:28:31.388 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:28:31.388 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:28:31.785 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:28:33.467 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:28:33.470 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:28:33.471 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:28:49.843 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:28:58.137 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 67b1cb7a-9606-4f2c-89e4-7f5552447530
17:28:58.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67b1cb7a-9606-4f2c-89e4-7f5552447530] RpcClient init label, labels = {module=naming, source=sdk}
17:28:58.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67b1cb7a-9606-4f2c-89e4-7f5552447530] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:28:58.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67b1cb7a-9606-4f2c-89e4-7f5552447530] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:28:58.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67b1cb7a-9606-4f2c-89e4-7f5552447530] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:28:58.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67b1cb7a-9606-4f2c-89e4-7f5552447530] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:28:58.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67b1cb7a-9606-4f2c-89e4-7f5552447530] Success to connect to server [localhost:8848] on start up, connectionId = 1752226138166_127.0.0.1_12800
17:28:58.301 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67b1cb7a-9606-4f2c-89e4-7f5552447530] Notify connected event to listeners.
17:28:58.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67b1cb7a-9606-4f2c-89e4-7f5552447530] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:28:58.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67b1cb7a-9606-4f2c-89e4-7f5552447530] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026292519450
17:28:58.402 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:28:58.476 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
17:28:58.827 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 34.771 seconds (JVM running for 35.819)
17:28:58.865 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:28:58.866 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:28:58.867 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:28:58.950 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67b1cb7a-9606-4f2c-89e4-7f5552447530] Receive server push request, request = NotifySubscriberRequest, requestId = 154
17:28:58.976 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67b1cb7a-9606-4f2c-89e4-7f5552447530] Ack server push request, request = NotifySubscriberRequest, requestId = 154
17:28:59.124 [RMI TCP Connection(8)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:29:46.665 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:29:46.667 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:29:47.516 [http-nio-9600-exec-1] INFO  c.h.s.a.s.i.SysApprovalCustomerinfoServiceImpl - [selectById,118] - sys_field Object: null
17:30:35.708 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:30:35.716 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:30:36.059 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:30:36.060 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2a269006[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:30:36.060 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752226138166_127.0.0.1_12800
17:30:36.062 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752226138166_127.0.0.1_12800]Ignore complete event,isRunning:false,isAbandon=false
17:30:36.067 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@527f98bb[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 29]
17:30:36.248 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:30:36.255 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:30:36.267 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:30:36.267 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:30:36.269 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:30:36.270 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:30:41.832 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:30:42.829 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 44021a1f-743e-48f4-81fb-593a74936960_config-0
17:30:42.911 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
17:30:42.958 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
17:30:42.967 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
17:30:42.977 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
17:30:42.987 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
17:30:42.998 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
17:30:43.001 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44021a1f-743e-48f4-81fb-593a74936960_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:30:43.001 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44021a1f-743e-48f4-81fb-593a74936960_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d3d439f1c0
17:30:43.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44021a1f-743e-48f4-81fb-593a74936960_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001d3d439f3e0
17:30:43.003 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44021a1f-743e-48f4-81fb-593a74936960_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:30:43.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44021a1f-743e-48f4-81fb-593a74936960_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:30:43.019 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44021a1f-743e-48f4-81fb-593a74936960_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:30:44.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44021a1f-743e-48f4-81fb-593a74936960_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752226243815_127.0.0.1_12966
17:30:44.034 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44021a1f-743e-48f4-81fb-593a74936960_config-0] Notify connected event to listeners.
17:30:44.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44021a1f-743e-48f4-81fb-593a74936960_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:30:44.036 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44021a1f-743e-48f4-81fb-593a74936960_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d3d4518fb0
17:30:44.220 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:30:48.415 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:30:48.417 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:30:48.417 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:30:48.803 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:30:50.285 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:30:50.288 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:30:50.289 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:31:07.510 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:31:14.924 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6afc7eef-b1d6-47df-b2d0-7e8db623f704
17:31:14.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6afc7eef-b1d6-47df-b2d0-7e8db623f704] RpcClient init label, labels = {module=naming, source=sdk}
17:31:14.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6afc7eef-b1d6-47df-b2d0-7e8db623f704] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:31:14.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6afc7eef-b1d6-47df-b2d0-7e8db623f704] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:31:14.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6afc7eef-b1d6-47df-b2d0-7e8db623f704] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:31:14.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6afc7eef-b1d6-47df-b2d0-7e8db623f704] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:31:15.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6afc7eef-b1d6-47df-b2d0-7e8db623f704] Success to connect to server [localhost:8848] on start up, connectionId = 1752226274947_127.0.0.1_13023
17:31:15.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6afc7eef-b1d6-47df-b2d0-7e8db623f704] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:31:15.076 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6afc7eef-b1d6-47df-b2d0-7e8db623f704] Notify connected event to listeners.
17:31:15.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6afc7eef-b1d6-47df-b2d0-7e8db623f704] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d3d4518fb0
17:31:15.169 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:31:15.225 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
17:31:15.517 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 34.325 seconds (JVM running for 35.369)
17:31:15.557 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:31:15.559 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:31:15.561 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:31:15.696 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:31:15.737 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6afc7eef-b1d6-47df-b2d0-7e8db623f704] Receive server push request, request = NotifySubscriberRequest, requestId = 159
17:31:15.781 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6afc7eef-b1d6-47df-b2d0-7e8db623f704] Ack server push request, request = NotifySubscriberRequest, requestId = 159
17:33:50.256 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:33:50.256 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:38:48.805 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:38:48.812 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:38:49.146 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:38:49.148 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@115acd3d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:38:49.149 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752226274947_127.0.0.1_13023
17:38:49.152 [nacos-grpc-client-executor-101] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752226274947_127.0.0.1_13023]Ignore complete event,isRunning:false,isAbandon=false
17:38:49.156 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5d4dc864[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 102]
17:38:49.330 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:38:49.335 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:38:49.351 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:38:49.353 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:38:49.358 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:38:49.358 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:38:54.874 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:38:55.585 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 16c4e43a-9691-4b99-a793-43bc09d8e20e_config-0
17:38:55.670 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
17:38:55.718 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
17:38:55.728 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
17:38:55.737 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
17:38:55.747 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
17:38:55.756 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
17:38:55.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c4e43a-9691-4b99-a793-43bc09d8e20e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:38:55.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c4e43a-9691-4b99-a793-43bc09d8e20e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002000139ed38
17:38:55.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c4e43a-9691-4b99-a793-43bc09d8e20e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002000139ef58
17:38:55.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c4e43a-9691-4b99-a793-43bc09d8e20e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:38:55.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c4e43a-9691-4b99-a793-43bc09d8e20e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:38:55.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c4e43a-9691-4b99-a793-43bc09d8e20e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:38:56.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c4e43a-9691-4b99-a793-43bc09d8e20e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752226736581_127.0.0.1_13457
17:38:56.790 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c4e43a-9691-4b99-a793-43bc09d8e20e_config-0] Notify connected event to listeners.
17:38:56.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c4e43a-9691-4b99-a793-43bc09d8e20e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:38:56.792 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c4e43a-9691-4b99-a793-43bc09d8e20e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020001518ad8
17:38:56.964 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:39:01.117 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:39:01.118 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:39:01.118 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:39:01.287 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:39:02.037 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:39:02.040 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:39:02.040 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:39:10.975 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:39:16.949 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c3f1f117-27f0-4016-bf55-ef512b44894e
17:39:16.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f1f117-27f0-4016-bf55-ef512b44894e] RpcClient init label, labels = {module=naming, source=sdk}
17:39:16.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f1f117-27f0-4016-bf55-ef512b44894e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:39:16.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f1f117-27f0-4016-bf55-ef512b44894e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:39:16.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f1f117-27f0-4016-bf55-ef512b44894e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:39:16.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f1f117-27f0-4016-bf55-ef512b44894e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:39:17.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f1f117-27f0-4016-bf55-ef512b44894e] Success to connect to server [localhost:8848] on start up, connectionId = 1752226756965_127.0.0.1_13512
17:39:17.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f1f117-27f0-4016-bf55-ef512b44894e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:39:17.079 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f1f117-27f0-4016-bf55-ef512b44894e] Notify connected event to listeners.
17:39:17.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f1f117-27f0-4016-bf55-ef512b44894e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020001518ad8
17:39:17.141 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:39:17.178 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
17:39:17.326 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.049 seconds (JVM running for 24.05)
17:39:17.342 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:39:17.342 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:39:17.343 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:39:17.713 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f1f117-27f0-4016-bf55-ef512b44894e] Receive server push request, request = NotifySubscriberRequest, requestId = 166
17:39:17.738 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3f1f117-27f0-4016-bf55-ef512b44894e] Ack server push request, request = NotifySubscriberRequest, requestId = 166
17:39:17.884 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:40:13.167 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:40:13.167 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:42:31.601 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:42:31.605 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:42:31.939 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:42:31.940 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4e7199c5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:42:31.940 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752226756965_127.0.0.1_13512
17:42:31.945 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752226756965_127.0.0.1_13512]Ignore complete event,isRunning:false,isAbandon=false
17:42:31.947 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@11c313e3[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 51]
17:42:32.120 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:42:32.125 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:42:32.132 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:42:32.133 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:42:32.134 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:42:32.135 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:42:37.671 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:42:38.438 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2de1e0bf-e8e0-4889-aef7-e26688f93e43_config-0
17:42:38.514 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
17:42:38.562 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
17:42:38.568 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
17:42:38.580 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
17:42:38.594 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
17:42:38.594 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
17:42:38.594 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2de1e0bf-e8e0-4889-aef7-e26688f93e43_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:42:38.594 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2de1e0bf-e8e0-4889-aef7-e26688f93e43_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000020d8939eaf8
17:42:38.594 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2de1e0bf-e8e0-4889-aef7-e26688f93e43_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000020d8939ed18
17:42:38.594 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2de1e0bf-e8e0-4889-aef7-e26688f93e43_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:42:38.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2de1e0bf-e8e0-4889-aef7-e26688f93e43_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:42:38.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2de1e0bf-e8e0-4889-aef7-e26688f93e43_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:42:39.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2de1e0bf-e8e0-4889-aef7-e26688f93e43_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752226959500_127.0.0.1_13810
17:42:39.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2de1e0bf-e8e0-4889-aef7-e26688f93e43_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:42:39.754 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2de1e0bf-e8e0-4889-aef7-e26688f93e43_config-0] Notify connected event to listeners.
17:42:39.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2de1e0bf-e8e0-4889-aef7-e26688f93e43_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020d89518ad8
17:42:39.934 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:42:44.109 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:42:44.110 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:42:44.110 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:42:44.320 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:42:45.182 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:42:45.184 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:42:45.185 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:42:53.959 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:42:57.656 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6f5a10d8-55da-4353-9ffc-2083da22936b
17:42:57.656 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5a10d8-55da-4353-9ffc-2083da22936b] RpcClient init label, labels = {module=naming, source=sdk}
17:42:57.658 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5a10d8-55da-4353-9ffc-2083da22936b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:42:57.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5a10d8-55da-4353-9ffc-2083da22936b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:42:57.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5a10d8-55da-4353-9ffc-2083da22936b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:42:57.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5a10d8-55da-4353-9ffc-2083da22936b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:42:57.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5a10d8-55da-4353-9ffc-2083da22936b] Success to connect to server [localhost:8848] on start up, connectionId = 1752226977669_127.0.0.1_13850
17:42:57.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5a10d8-55da-4353-9ffc-2083da22936b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:42:57.785 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5a10d8-55da-4353-9ffc-2083da22936b] Notify connected event to listeners.
17:42:57.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5a10d8-55da-4353-9ffc-2083da22936b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020d89518ad8
17:42:57.869 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:42:57.919 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
17:42:58.087 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.055 seconds (JVM running for 22.075)
17:42:58.106 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:42:58.107 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:42:58.108 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:42:58.420 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5a10d8-55da-4353-9ffc-2083da22936b] Receive server push request, request = NotifySubscriberRequest, requestId = 175
17:42:58.442 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f5a10d8-55da-4353-9ffc-2083da22936b] Ack server push request, request = NotifySubscriberRequest, requestId = 175
17:42:58.542 [RMI TCP Connection(1)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:43:06.790 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:43:06.791 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:52:06.833 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:52:06.838 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:52:07.170 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:52:07.171 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3e71886[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:52:07.171 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752226977669_127.0.0.1_13850
17:52:07.176 [nacos-grpc-client-executor-79] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752226977669_127.0.0.1_13850]Ignore complete event,isRunning:false,isAbandon=false
17:52:07.179 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2715abae[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 80]
17:52:07.351 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:52:07.356 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:52:07.364 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:52:07.364 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:52:07.365 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:52:07.365 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:52:13.192 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:52:14.018 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f879f2a4-d378-46f6-bb43-d0f0b8ff6937_config-0
17:52:14.109 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
17:52:14.159 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
17:52:14.175 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
17:52:14.184 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
17:52:14.203 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
17:52:14.215 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
17:52:14.231 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f879f2a4-d378-46f6-bb43-d0f0b8ff6937_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:52:14.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f879f2a4-d378-46f6-bb43-d0f0b8ff6937_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000020d0c39dd70
17:52:14.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f879f2a4-d378-46f6-bb43-d0f0b8ff6937_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000020d0c39df90
17:52:14.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f879f2a4-d378-46f6-bb43-d0f0b8ff6937_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:52:14.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f879f2a4-d378-46f6-bb43-d0f0b8ff6937_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:52:14.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f879f2a4-d378-46f6-bb43-d0f0b8ff6937_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:52:15.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f879f2a4-d378-46f6-bb43-d0f0b8ff6937_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752227535113_127.0.0.1_14592
17:52:15.352 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f879f2a4-d378-46f6-bb43-d0f0b8ff6937_config-0] Notify connected event to listeners.
17:52:15.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f879f2a4-d378-46f6-bb43-d0f0b8ff6937_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:52:15.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f879f2a4-d378-46f6-bb43-d0f0b8ff6937_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020d0c517b88
17:52:15.518 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:52:19.635 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:52:19.635 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:52:19.635 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:52:20.025 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:52:21.457 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:52:21.464 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:52:21.464 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:52:35.118 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:52:38.119 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7bf12088-7a59-4427-a484-0fd671f8dd83
17:52:38.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf12088-7a59-4427-a484-0fd671f8dd83] RpcClient init label, labels = {module=naming, source=sdk}
17:52:38.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf12088-7a59-4427-a484-0fd671f8dd83] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:52:38.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf12088-7a59-4427-a484-0fd671f8dd83] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:52:38.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf12088-7a59-4427-a484-0fd671f8dd83] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:52:38.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf12088-7a59-4427-a484-0fd671f8dd83] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:52:38.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf12088-7a59-4427-a484-0fd671f8dd83] Success to connect to server [localhost:8848] on start up, connectionId = 1752227558131_127.0.0.1_14626
17:52:38.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf12088-7a59-4427-a484-0fd671f8dd83] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:52:38.246 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf12088-7a59-4427-a484-0fd671f8dd83] Notify connected event to listeners.
17:52:38.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf12088-7a59-4427-a484-0fd671f8dd83] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020d0c517b88
17:52:38.316 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:52:38.356 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
17:52:38.500 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.984 seconds (JVM running for 27.022)
17:52:38.516 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:52:38.517 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:52:38.517 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:52:38.831 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf12088-7a59-4427-a484-0fd671f8dd83] Receive server push request, request = NotifySubscriberRequest, requestId = 179
17:52:38.853 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bf12088-7a59-4427-a484-0fd671f8dd83] Ack server push request, request = NotifySubscriberRequest, requestId = 179
17:52:39.102 [RMI TCP Connection(7)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:52:44.827 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:52:44.828 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:54:54.149 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:54:54.158 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:54:54.498 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:54:54.499 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@********[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:54:54.499 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752227558131_127.0.0.1_14626
17:54:54.504 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752227558131_127.0.0.1_14626]Ignore complete event,isRunning:false,isAbandon=false
17:54:54.508 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@12bbb016[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 24]
17:54:54.699 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:54:54.708 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:54:54.724 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:54:54.725 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:54:54.727 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:54:54.729 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:54:58.700 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:54:59.643 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 30ceafcc-70f3-4fce-baa2-1313e39bd0e9_config-0
17:54:59.740 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
17:54:59.786 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
17:54:59.797 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
17:54:59.808 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
17:54:59.819 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
17:54:59.831 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
17:54:59.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30ceafcc-70f3-4fce-baa2-1313e39bd0e9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:54:59.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30ceafcc-70f3-4fce-baa2-1313e39bd0e9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000243d539e8d8
17:54:59.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30ceafcc-70f3-4fce-baa2-1313e39bd0e9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000243d539eaf8
17:54:59.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30ceafcc-70f3-4fce-baa2-1313e39bd0e9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:54:59.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30ceafcc-70f3-4fce-baa2-1313e39bd0e9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:54:59.855 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30ceafcc-70f3-4fce-baa2-1313e39bd0e9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:55:01.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30ceafcc-70f3-4fce-baa2-1313e39bd0e9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752227700851_127.0.0.1_14790
17:55:01.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30ceafcc-70f3-4fce-baa2-1313e39bd0e9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:55:01.103 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30ceafcc-70f3-4fce-baa2-1313e39bd0e9_config-0] Notify connected event to listeners.
17:55:01.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30ceafcc-70f3-4fce-baa2-1313e39bd0e9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000243d5518440
17:55:01.280 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:55:05.619 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:55:05.621 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:55:05.621 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:55:05.841 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:55:06.837 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:55:06.840 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:55:06.841 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:55:15.458 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:55:18.740 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c72248c6-22e4-4ce1-966b-faa385875a11
17:55:18.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c72248c6-22e4-4ce1-966b-faa385875a11] RpcClient init label, labels = {module=naming, source=sdk}
17:55:18.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c72248c6-22e4-4ce1-966b-faa385875a11] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:55:18.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c72248c6-22e4-4ce1-966b-faa385875a11] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:55:18.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c72248c6-22e4-4ce1-966b-faa385875a11] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:55:18.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c72248c6-22e4-4ce1-966b-faa385875a11] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:55:18.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c72248c6-22e4-4ce1-966b-faa385875a11] Success to connect to server [localhost:8848] on start up, connectionId = 1752227718775_127.0.0.1_14822
17:55:18.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c72248c6-22e4-4ce1-966b-faa385875a11] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:55:18.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c72248c6-22e4-4ce1-966b-faa385875a11] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000243d5518440
17:55:18.901 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c72248c6-22e4-4ce1-966b-faa385875a11] Notify connected event to listeners.
17:55:18.961 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:55:18.995 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
17:55:19.154 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.035 seconds (JVM running for 22.03)
17:55:19.175 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:55:19.176 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:55:19.177 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:55:19.265 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:55:19.441 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c72248c6-22e4-4ce1-966b-faa385875a11] Receive server push request, request = NotifySubscriberRequest, requestId = 187
17:55:19.472 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c72248c6-22e4-4ce1-966b-faa385875a11] Ack server push request, request = NotifySubscriberRequest, requestId = 187
17:55:24.956 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:55:24.957 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:55:50.117 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:55:50.122 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:55:50.452 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:55:50.452 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4ce21d5a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:55:50.452 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752227718775_127.0.0.1_14822
17:55:50.454 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752227718775_127.0.0.1_14822]Ignore complete event,isRunning:false,isAbandon=false
17:55:50.456 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7cb767ce[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 17]
17:55:50.596 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:55:50.596 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:55:50.602 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:55:50.602 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:55:50.608 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:55:50.608 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
