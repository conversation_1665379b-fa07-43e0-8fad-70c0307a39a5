package com.heju.system.api.dict.feign.factory;

import com.heju.common.core.web.result.R;
import com.heju.system.api.dict.feign.RemoteDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


/**
 * 字典服务 降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteDictFallbackFactory implements FallbackFactory<RemoteDictService> {

    @Override
    public RemoteDictService create(Throwable throwable) {
        log.error("字典服务调用失败:{}", throwable.getMessage());
        return new RemoteDictService() {

            @Override
            public R<Boolean> refreshCache(String source) {
                return R.fail("刷新字典缓存失败:" + throwable.getMessage());
            }
        };
    }
}