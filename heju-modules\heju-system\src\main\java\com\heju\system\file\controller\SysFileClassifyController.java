package com.heju.system.file.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.TreeController;
import com.heju.system.file.domain.dto.SysFileClassifyDto;
import com.heju.system.file.domain.query.SysFileClassifyQuery;
import com.heju.system.file.service.ISysFileClassifyService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 文件分类管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/classify")
public class SysFileClassifyController extends TreeController<SysFileClassifyQuery, SysFileClassifyDto, ISysFileClassifyService> {

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "文件分类" ;
    }

    /**
     * 查询文件分类列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_FILE_CLASSIFY_LIST)
    public AjaxResult list(SysFileClassifyQuery fileClassify) {
        return super.list(fileClassify);
    }

    /**
     * 查询文件分类列表（排除节点）
     */
    @GetMapping("/list/exclude")
    public AjaxResult listExNodes(SysFileClassifyQuery fileClassify) {
        return super.listExNodes(fileClassify);
    }

    /**
     * 查询文件分类详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_FILE_CLASSIFY_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 文件分类新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_FILE_CLASSIFY_ADD)
    @Log(title = "文件分类管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysFileClassifyDto fileClassify) {
        return super.add(fileClassify);
    }

    /**
     * 文件分类修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_FILE_CLASSIFY_EDIT)
    @Log(title = "文件分类管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysFileClassifyDto fileClassify) {
        return super.edit(fileClassify);
    }

    /**
     * 文件分类修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_FILE_CLASSIFY_EDIT, Auth.SYS_FILE_CLASSIFY_ES}, logical = Logical.OR)
    @Log(title = "文件分类管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysFileClassifyDto fileClassify) {
        return super.editStatus(fileClassify);
    }

    /**
     * 文件分类批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_FILE_CLASSIFY_DEL)
    @Log(title = "文件分类管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取文件分类选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    @Override
    protected void AEHandle(BaseConstants.Operate operate, SysFileClassifyDto fileClassify) {
        if (baseService.checkNameUnique(fileClassify.getId(), fileClassify.getName()))
            warn(StrUtil.format("{}{}{}失败，文件分类名称已存在", operate.getInfo(), getNodeName(), fileClassify.getName()));
    }
}
