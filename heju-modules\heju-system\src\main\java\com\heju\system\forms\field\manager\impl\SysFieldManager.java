package com.heju.system.forms.field.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.constant.basic.SqlConstants;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.model.SysFieldConverter;
import com.heju.system.forms.field.domain.po.SysFieldPo;
import com.heju.system.forms.field.domain.query.SysFieldQuery;
import com.heju.system.forms.field.manager.ISysFieldManager;
import com.heju.system.forms.field.mapper.SysFieldMapper;
import com.heju.system.forms.sheet.mapper.SysSheetMapper;
import com.heju.system.utils.FieldTypeConstants;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 字段管理管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysFieldManager extends BaseManagerImpl<SysFieldQuery, SysFieldDto, SysFieldPo, SysFieldMapper, SysFieldConverter> implements ISysFieldManager {


    @Resource
    private SysSheetMapper sysSheetMapper;

    @Override
    public SysFieldPo checkConfigCodeUnique(Long Id, String apiName,Long sheetId) {
        SysFieldPo sheet = baseMapper.selectOne(
                Wrappers.<SysFieldPo>query().lambda()
                        .ne(SysFieldPo::getId, Id)
                        .eq(SysFieldPo::getApiName, apiName)
                        .eq(SysFieldPo::getSheetId, sheetId)
                        .last(SqlConstants.LIMIT_ONE));
        return baseConverter.mapperDto(sheet);
    }

    /**
     *
     * @param dto 1:文本;2:单选;3:多选;4:文本域;5:整数;6:货币;7:日期;8:关联关系;
     *       9:引用类型;10:电话;11:邮箱;12:网站;13:图片;14:文件;15:级联类型;16:判断类型
     */
    @Override
    public void insertFieldToSql(SysFieldDto dto) {
        switch (dto.getFieldType()){
            case FieldTypeConstants.VARCHAR -> baseMapper.createSheetByVarchar(dto);
            case FieldTypeConstants.SELECT_SINGLE -> baseMapper.createSheetByBigintNotLength(dto);
            case FieldTypeConstants.SELECT_MULTI ,FieldTypeConstants.TELEPHONE ,
                    FieldTypeConstants.E_MAIL,FieldTypeConstants.WEBSITE ,FieldTypeConstants.CASCADE -> baseMapper.createSheetByVarcharNotLength(dto);
            case FieldTypeConstants.TEXT -> baseMapper.createSheetByText(dto);
            case FieldTypeConstants.INTEGER -> baseMapper.createSheetByInt(dto);
            case FieldTypeConstants.CURRENCY -> baseMapper.createSheetByDecimal(dto);
            case FieldTypeConstants.DATE ->{
                if(Objects.equals(dto.getDateType(), FieldTypeConstants.DATE_NO_TIME)){
                    baseMapper.createSheetByDate(dto);
                }
                if(Objects.equals(dto.getDateType(), FieldTypeConstants.DATE_TIME)) {
                    baseMapper.createSheetByDatetime(dto);
                }
                if(Objects.equals(dto.getDateType(), FieldTypeConstants.DATE_TIME_INTERVAL) ||
                        Objects.equals(dto.getDateType(), FieldTypeConstants.DATE_INTERVAL)){
                    baseMapper.createSheetByVarcharNotLength(dto);
                }
            }
            case FieldTypeConstants.RELATION -> baseMapper.createSheetByBigint(dto);
            case FieldTypeConstants.QUOTE ->{
                SysFieldDto sysFieldDto = mapperDto(baseMapper.selectById(dto.getQuoteSheetFieldId()));
                //查询出引用字段 字段类型, 设置新增数据列信息
                dto.setFieldType(sysFieldDto.getFieldType());
                if(sysFieldDto.getFieldLength()!=null){
                    dto.setFieldLength(sysFieldDto.getFieldLength());
                }
                if(sysFieldDto.getDecimalLength()!=null){
                    dto.setDecimalLength(sysFieldDto.getDecimalLength());
                }
                if(sysFieldDto.getQuoteSheetFieldId()!=null){
                    dto.setQuoteSheetFieldId(sysFieldDto.getQuoteSheetFieldId());
                }
                insertFieldToSql(dto);
            }
            case FieldTypeConstants.PICTURE ,FieldTypeConstants.FILE ->{
                if(Objects.equals(dto.getFileType(), FieldTypeConstants.SINGLE_FILE)){
                    baseMapper.createSheetByTextNotLength(dto);
                }
                if(Objects.equals(dto.getFileType(), FieldTypeConstants.MULTI_FILE)){
                    baseMapper.createSheetByTextNotLength(dto);
                }
            }
            case FieldTypeConstants.JUDGMENT -> baseMapper.createSheetByJudgment(dto);
        }
    }

    @Override
    public void updateSheetColumn(SysFieldDto dto) {
        switch (dto.getFieldType()){
            case FieldTypeConstants.VARCHAR -> baseMapper.updateSheetByVarchar(dto);
            case FieldTypeConstants.SELECT_SINGLE ,FieldTypeConstants.SELECT_MULTI ,FieldTypeConstants.TELEPHONE ,
                    FieldTypeConstants.E_MAIL,FieldTypeConstants.WEBSITE ,FieldTypeConstants.CASCADE -> baseMapper.updateSheetByVarcharNotLength(dto);
            case FieldTypeConstants.TEXT -> baseMapper.updateSheetByText(dto);
            case FieldTypeConstants.INTEGER -> baseMapper.updateSheetByInt(dto);
            case FieldTypeConstants.CURRENCY -> baseMapper.updateSheetByDecimal(dto);
            case FieldTypeConstants.DATE ->{
                if(Objects.equals(dto.getDateType(), FieldTypeConstants.DATE_NO_TIME)){
                    baseMapper.updateSheetByDate(dto);
                }
                if(Objects.equals(dto.getDateType(), FieldTypeConstants.DATE_TIME)) {
                    baseMapper.updateSheetByDatetime(dto);
                }
            }
            case FieldTypeConstants.RELATION -> baseMapper.updateSheetByBigint(dto);
            case FieldTypeConstants.QUOTE ->{

            }
            case FieldTypeConstants.PICTURE ,FieldTypeConstants.FILE ->{
                if(Objects.equals(dto.getFileType(), FieldTypeConstants.SINGLE_FILE)){
                    baseMapper.updateSheetByTextNotLength(dto);
                }
                if(Objects.equals(dto.getFileType(), FieldTypeConstants.MULTI_FILE)){
                    baseMapper.updateSheetByTextNotLength(dto);
                }
            }
            case FieldTypeConstants.JUDGMENT -> baseMapper.updateSheetByJudgment(dto);
        }
    }

    @Override
    public List<SysFieldPo> selectQuote(List<Long> ids) {
        return baseMapper.selectList(Wrappers.<SysFieldPo>query().lambda()
                .in(SysFieldPo::getQuoteSheetFieldId, ids));
    }

    @Override
    public List<SysFieldPo> selectRelation(List<Long> ids) {
        return baseMapper.selectList(Wrappers.<SysFieldPo>query().lambda()
                .in(SysFieldPo::getRelationSheetFieldId, ids));
    }

    @Override
    public void deleteSheetField(List<SysFieldDto> fieldDtoList,String sheetApiName) {
        fieldDtoList.forEach(sysField -> baseMapper.deleteSheetField(sheetApiName,sysField.getApiName()));
    }

    @Override
    public SysFieldDto selectPrimary(Long sheetId) {
        SysFieldPo sheet = baseMapper.selectOne(
                Wrappers.<SysFieldPo>query().lambda()
                        .eq(SysFieldPo::getFieldType, FieldTypeConstants.PRIMARY)
                        .eq(SysFieldPo::getSheetId, sheetId)
                        .last(SqlConstants.LIMIT_ONE));
        return baseConverter.mapperDto(sheet);
    }

    // 客户信息审核 查出 fieldName 对应 fieldId
    @Override
    public List<Long> selectIdsByFieldName(String fieldName) {
        return baseMapper.selectList(Wrappers.<SysFieldPo>query().lambda()
                .like(SysFieldPo::getName, fieldName))
                .stream().map(SysFieldPo::getId).toList();
    }

    @Override
    public List<SysFieldDto> selectBySheetIds(List<Long> sheetIds) {
        return mapperDto(baseMapper.selectList(Wrappers.<SysFieldPo>query().lambda()
                .in(SysFieldPo::getSheetId,sheetIds)));
    }

    @Override
    public List<SysFieldDto> selectByApiNames(List<String> apiName,Long sheetId) {
        return mapperDto(baseMapper.selectList(Wrappers.<SysFieldPo>query().lambda()
                .eq(SysFieldPo::getSheetId,sheetId)
                .in(SysFieldPo::getApiName,apiName)));
    }

    @Override
    public List<SysFieldDto> selectQuoteByApiNames(List<Long> ids) {
        return mapperDto(baseMapper.selectList(Wrappers.<SysFieldPo>query().lambda()
                .in(SysFieldPo::getQuoteSheetFieldId,ids)));
    }

    @Override
    public List<SysFieldDto> selectReferencingByIds(List<Long> ids) {
        return mapperDto(baseMapper.selectList(Wrappers.<SysFieldPo>query().lambda()
                .in(SysFieldPo::getReferencingFieldId,ids)));
    }

    @Override
    public List<SysFieldDto> selectReferencedByIds(List<Long> ids) {
        return mapperDto(baseMapper.selectList(Wrappers.<SysFieldPo>query().lambda()
                .in(SysFieldPo::getReferencedFieldId,ids)));
    }
}
