package com.heju.system.phoneinfo.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.phoneinfo.domain.dto.SysTelephoneCodeDto;
import com.heju.system.phoneinfo.domain.model.SysTelephoneCodeConverter;
import com.heju.system.phoneinfo.domain.po.SysTelephoneCodePo;
import com.heju.system.phoneinfo.domain.query.SysTelephoneCodeQuery;
import com.heju.system.phoneinfo.manager.ISysTelephoneCodeManager;
import com.heju.system.phoneinfo.mapper.SysTelephoneCodeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 转发短信管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysTelephoneCodeManager extends BaseManagerImpl<SysTelephoneCodeQuery, SysTelephoneCodeDto, SysTelephoneCodePo, SysTelephoneCodeMapper, SysTelephoneCodeConverter> implements ISysTelephoneCodeManager {

    @Autowired
    private SysTelephoneCodeMapper mapper;

    /**
     * 条件查询短信信息
     * @param query
     * @return
     */
    public List<SysTelephoneCodeDto> selectPhoMsgList(SysTelephoneCodeQuery query) {
        LambdaQueryWrapper<SysTelephoneCodePo> wrapper = new LambdaQueryWrapper<>();
        if (query.getStartTime() != null && query.getEndTime() != null) {
            // 有起始和结束时间，查区间内的数据
            wrapper.between(SysTelephoneCodePo::getCreateTime, query.getStartTime(), query.getEndTime());
        } else {
            // 没有起始或结束时间，查创建时间大于等于当前时间的数据（即未来时间的数据）
            wrapper.ge(SysTelephoneCodePo::getCreateTime, query.getPermissionTime());
        }
        if (query.getReceive() != null) {
            wrapper.eq(SysTelephoneCodePo::getReceive, query.getReceive());
        }
        wrapper.orderByAsc(SysTelephoneCodePo::getCreateTime);
        List<SysTelephoneCodePo> poList = baseMapper.selectList(wrapper);
        return subMerge(mapperDto(poList));
    }
}
