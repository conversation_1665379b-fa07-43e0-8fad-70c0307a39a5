package com.heju.system.declaration.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.declaration.domain.dto.SysTaxFilingsDto;
import com.heju.system.declaration.domain.po.SysTaxFilingsPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:35+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysTaxFilingsConverterImpl implements SysTaxFilingsConverter {

    @Override
    public SysTaxFilingsDto mapperDto(SysTaxFilingsPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysTaxFilingsDto sysTaxFilingsDto = new SysTaxFilingsDto();

        sysTaxFilingsDto.setId( arg0.getId() );
        sysTaxFilingsDto.setSourceName( arg0.getSourceName() );
        sysTaxFilingsDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysTaxFilingsDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysTaxFilingsDto.setName( arg0.getName() );
        sysTaxFilingsDto.setStatus( arg0.getStatus() );
        sysTaxFilingsDto.setSort( arg0.getSort() );
        sysTaxFilingsDto.setCreateBy( arg0.getCreateBy() );
        sysTaxFilingsDto.setCreateTime( arg0.getCreateTime() );
        sysTaxFilingsDto.setUpdateBy( arg0.getUpdateBy() );
        sysTaxFilingsDto.setUpdateTime( arg0.getUpdateTime() );
        sysTaxFilingsDto.setDelFlag( arg0.getDelFlag() );
        sysTaxFilingsDto.setCreateName( arg0.getCreateName() );
        sysTaxFilingsDto.setUpdateName( arg0.getUpdateName() );
        sysTaxFilingsDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysTaxFilingsDto.setEntityId( arg0.getEntityId() );
        sysTaxFilingsDto.setCode( arg0.getCode() );
        sysTaxFilingsDto.setTaxType( arg0.getTaxType() );
        sysTaxFilingsDto.setReporttimeType( arg0.getReporttimeType() );
        sysTaxFilingsDto.setYear( arg0.getYear() );
        sysTaxFilingsDto.setMonth( arg0.getMonth() );
        sysTaxFilingsDto.setSeason( arg0.getSeason() );
        sysTaxFilingsDto.setReportAddress( arg0.getReportAddress() );
        sysTaxFilingsDto.setRemark( arg0.getRemark() );
        sysTaxFilingsDto.setReason( arg0.getReason() );
        sysTaxFilingsDto.setDeclareStatus( arg0.getDeclareStatus() );
        sysTaxFilingsDto.setDeclareRemark( arg0.getDeclareRemark() );

        return sysTaxFilingsDto;
    }

    @Override
    public List<SysTaxFilingsDto> mapperDto(Collection<SysTaxFilingsPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysTaxFilingsDto> list = new ArrayList<SysTaxFilingsDto>( arg0.size() );
        for ( SysTaxFilingsPo sysTaxFilingsPo : arg0 ) {
            list.add( mapperDto( sysTaxFilingsPo ) );
        }

        return list;
    }

    @Override
    public Page<SysTaxFilingsDto> mapperPageDto(Collection<SysTaxFilingsPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysTaxFilingsDto> page = new Page<SysTaxFilingsDto>();
        for ( SysTaxFilingsPo sysTaxFilingsPo : arg0 ) {
            page.add( mapperDto( sysTaxFilingsPo ) );
        }

        return page;
    }

    @Override
    public SysTaxFilingsPo mapperPo(SysTaxFilingsDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysTaxFilingsPo sysTaxFilingsPo = new SysTaxFilingsPo();

        sysTaxFilingsPo.setId( arg0.getId() );
        sysTaxFilingsPo.setSourceName( arg0.getSourceName() );
        sysTaxFilingsPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysTaxFilingsPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysTaxFilingsPo.setName( arg0.getName() );
        sysTaxFilingsPo.setStatus( arg0.getStatus() );
        sysTaxFilingsPo.setSort( arg0.getSort() );
        sysTaxFilingsPo.setCreateBy( arg0.getCreateBy() );
        sysTaxFilingsPo.setCreateTime( arg0.getCreateTime() );
        sysTaxFilingsPo.setUpdateBy( arg0.getUpdateBy() );
        sysTaxFilingsPo.setUpdateTime( arg0.getUpdateTime() );
        sysTaxFilingsPo.setDelFlag( arg0.getDelFlag() );
        sysTaxFilingsPo.setCreateName( arg0.getCreateName() );
        sysTaxFilingsPo.setUpdateName( arg0.getUpdateName() );
        sysTaxFilingsPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysTaxFilingsPo.setEntityId( arg0.getEntityId() );
        sysTaxFilingsPo.setCode( arg0.getCode() );
        sysTaxFilingsPo.setTaxType( arg0.getTaxType() );
        sysTaxFilingsPo.setReporttimeType( arg0.getReporttimeType() );
        sysTaxFilingsPo.setYear( arg0.getYear() );
        sysTaxFilingsPo.setMonth( arg0.getMonth() );
        sysTaxFilingsPo.setSeason( arg0.getSeason() );
        sysTaxFilingsPo.setReportAddress( arg0.getReportAddress() );
        sysTaxFilingsPo.setRemark( arg0.getRemark() );
        sysTaxFilingsPo.setReason( arg0.getReason() );
        sysTaxFilingsPo.setDeclareStatus( arg0.getDeclareStatus() );
        sysTaxFilingsPo.setDeclareRemark( arg0.getDeclareRemark() );

        return sysTaxFilingsPo;
    }

    @Override
    public List<SysTaxFilingsPo> mapperPo(Collection<SysTaxFilingsDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysTaxFilingsPo> list = new ArrayList<SysTaxFilingsPo>( arg0.size() );
        for ( SysTaxFilingsDto sysTaxFilingsDto : arg0 ) {
            list.add( mapperPo( sysTaxFilingsDto ) );
        }

        return list;
    }

    @Override
    public Page<SysTaxFilingsPo> mapperPagePo(Collection<SysTaxFilingsDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysTaxFilingsPo> page = new Page<SysTaxFilingsPo>();
        for ( SysTaxFilingsDto sysTaxFilingsDto : arg0 ) {
            page.add( mapperPo( sysTaxFilingsDto ) );
        }

        return page;
    }
}
