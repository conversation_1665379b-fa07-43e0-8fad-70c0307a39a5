package com.heju.flowable.config;

import com.heju.common.security.utils.SecurityUtils;
import org.flowable.engine.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class FlowableServiceResolver {


    public static RepositoryService getRepositoryService() {
        SecurityUtils.getSourceName();
        return TenantProcessEngineRegistry.getEngine(SecurityUtils.getSourceName()).getRepositoryService();
    }

    public static RuntimeService getRuntimeService() {
        return TenantProcessEngineRegistry.getEngine(SecurityUtils.getSourceName()).getRuntimeService();
    }

    public static TaskService getTaskService() {
        return TenantProcessEngineRegistry.getEngine(SecurityUtils.getSourceName()).getTaskService();
    }

    public static IdentityService getIdentityService() {
        return TenantProcessEngineRegistry.getEngine(SecurityUtils.getSourceName()).getIdentityService();
    }

    public static FormService getFormService() {
        return TenantProcessEngineRegistry.getEngine(SecurityUtils.getSourceName()).getFormService();
    }

    public static HistoryService getHistoryService() {
        return TenantProcessEngineRegistry.getEngine(SecurityUtils.getSourceName()).getHistoryService();
    }

    public static ManagementService getManagementService() {
        return TenantProcessEngineRegistry.getEngine(SecurityUtils.getSourceName()).getManagementService();
    }
}
