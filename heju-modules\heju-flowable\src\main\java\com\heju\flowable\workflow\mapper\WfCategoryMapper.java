package com.heju.flowable.workflow.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.flowable.core.mapper.BaseMapperPlus;
import com.heju.flowable.workflow.domain.WfCategory;
import com.heju.flowable.workflow.domain.WfForm;
import com.heju.flowable.workflow.domain.vo.WfCategoryVo;
import org.apache.ibatis.annotations.Select;

/**
 * 流程分类Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-15
 */
@Isolate
public interface WfCategoryMapper extends BaseMapperPlus<WfCategoryMapper, WfCategory, WfCategoryVo> {


    @Select("select  * from wf_form where form_id=#{formId}")
    WfForm selectFormByFormId(Long formId);
}
