package com.heju.system.api.authority.domain.dto;

import com.heju.system.api.authority.domain.po.SysRoleGroupPo;
import com.heju.system.api.authority.domain.po.SysRolePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 角色组 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysRoleGroupDto extends SysRoleGroupPo {

    @Serial
    private static final long serialVersionUID = 1L;

    private List<SysRolePo> sysRolePoList;
}