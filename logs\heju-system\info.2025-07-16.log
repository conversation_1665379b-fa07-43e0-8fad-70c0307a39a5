09:19:01.550 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:19:05.544 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0
09:19:05.866 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 137 ms to scan 1 urls, producing 3 keys and 6 values 
09:19:06.052 [main] INFO  o.r.Reflections - [scan,232] - <PERSON>flections took 42 ms to scan 1 urls, producing 4 keys and 9 values 
09:19:06.079 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 24 ms to scan 1 urls, producing 3 keys and 10 values 
09:19:06.116 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 1 keys and 5 values 
09:19:06.142 [main] INFO  o.r.Reflections - [scan,232] - <PERSON>flections took 22 ms to scan 1 urls, producing 1 keys and 7 values 
09:19:06.179 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 2 keys and 8 values 
09:19:06.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:19:06.188 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002295e39c730
09:19:06.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002295e39c950
09:19:06.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:19:06.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:19:06.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:09.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:09.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:09.758 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:19:09.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:09.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002295e4aa620
09:19:09.917 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:10.165 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:10.523 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:10.947 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:11.571 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:12.089 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:19:12.223 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:13.004 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:14.029 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:14.967 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:16.647 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:18.263 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:20.278 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:22.417 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:24.191 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:26.189 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:28.155 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:30.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:32.313 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:34.725 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:35.144 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:19:35.144 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:19:35.144 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:19:35.553 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:19:36.894 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:37.495 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:19:37.495 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:19:37.495 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:19:39.185 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:41.556 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:43.956 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:46.452 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:49.308 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:52.008 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:52.468 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:19:54.806 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:57.337 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d5f676cb-33de-4c8b-93f1-ac4708770900
09:19:57.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] RpcClient init label, labels = {module=naming, source=sdk}
09:19:57.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:19:57.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:19:57.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:19:57.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:57.447 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:57.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:57.474 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:19:57.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:57.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002295e4aa620
09:19:57.596 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:57.626 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7804b50-a91c-4d51-b1aa-0d4b23fe18a4_config-0] Fail to connect server, after trying 28 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:57.813 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:19:57.813 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:58.143 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:58.554 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:58.826 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:19:58.826 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@54539319[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:19:58.826 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5312c883[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:19:58.826 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5f676cb-33de-4c8b-93f1-ac4708770900] Client is shutdown, stop reconnect to server
09:19:58.826 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d0500bbb-1f9b-429d-a99a-156eb2d4f195
09:19:58.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0500bbb-1f9b-429d-a99a-156eb2d4f195] RpcClient init label, labels = {module=naming, source=sdk}
09:19:58.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0500bbb-1f9b-429d-a99a-156eb2d4f195] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:19:58.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0500bbb-1f9b-429d-a99a-156eb2d4f195] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:19:58.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0500bbb-1f9b-429d-a99a-156eb2d4f195] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:19:58.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0500bbb-1f9b-429d-a99a-156eb2d4f195] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:58.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0500bbb-1f9b-429d-a99a-156eb2d4f195] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:58.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0500bbb-1f9b-429d-a99a-156eb2d4f195] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:58.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0500bbb-1f9b-429d-a99a-156eb2d4f195] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:58.877 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0500bbb-1f9b-429d-a99a-156eb2d4f195] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:19:58.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0500bbb-1f9b-429d-a99a-156eb2d4f195] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002295e4aa620
09:19:58.997 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0500bbb-1f9b-429d-a99a-156eb2d4f195] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:59.221 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0500bbb-1f9b-429d-a99a-156eb2d4f195] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:59.266 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:19:59.278 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:19:59.282 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:19:59.282 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:19:59.305 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9600"]
09:19:59.305 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:19:59.333 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9600"]
09:19:59.342 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9600"]
09:19:59.535 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0500bbb-1f9b-429d-a99a-156eb2d4f195] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:59.948 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0500bbb-1f9b-429d-a99a-156eb2d4f195] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:26:49.121 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:26:49.926 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 349aee17-d8d6-4347-befc-a81c794c18db_config-0
09:26:50.012 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
09:26:50.054 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:26:50.060 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
09:26:50.075 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:26:50.083 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:26:50.097 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:26:50.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:26:50.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000292813cdd70
09:26:50.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000292813cdf90
09:26:50.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:26:50.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:26:50.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:26:51.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752629210888_127.0.0.1_12411
09:26:51.099 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] Notify connected event to listeners.
09:26:51.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:26:51.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000029281508228
09:26:51.284 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:26:55.731 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:26:55.732 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:26:55.732 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:26:55.912 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:26:56.749 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:26:56.750 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:26:56.750 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:27:09.703 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:27:17.922 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ea0aac1d-a1f5-42e1-bdc8-04696453db4f
09:27:17.922 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea0aac1d-a1f5-42e1-bdc8-04696453db4f] RpcClient init label, labels = {module=naming, source=sdk}
09:27:17.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea0aac1d-a1f5-42e1-bdc8-04696453db4f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:27:17.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea0aac1d-a1f5-42e1-bdc8-04696453db4f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:27:17.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea0aac1d-a1f5-42e1-bdc8-04696453db4f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:27:17.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea0aac1d-a1f5-42e1-bdc8-04696453db4f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:27:18.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea0aac1d-a1f5-42e1-bdc8-04696453db4f] Success to connect to server [localhost:8848] on start up, connectionId = 1752629237936_127.0.0.1_12522
09:27:18.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea0aac1d-a1f5-42e1-bdc8-04696453db4f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:27:18.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea0aac1d-a1f5-42e1-bdc8-04696453db4f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000029281508228
09:27:18.077 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea0aac1d-a1f5-42e1-bdc8-04696453db4f] Notify connected event to listeners.
09:27:18.139 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:27:18.185 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:27:18.432 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 29.83 seconds (JVM running for 30.755)
09:27:18.463 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:27:18.465 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:27:18.466 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:27:18.630 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea0aac1d-a1f5-42e1-bdc8-04696453db4f] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:27:18.649 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea0aac1d-a1f5-42e1-bdc8-04696453db4f] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:27:18.885 [RMI TCP Connection(13)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:33:22.544 [nacos-grpc-client-executor-88] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea0aac1d-a1f5-42e1-bdc8-04696453db4f] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:33:22.553 [nacos-grpc-client-executor-88] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea0aac1d-a1f5-42e1-bdc8-04696453db4f] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:33:23.371 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:33:23.371 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
09:33:23.451 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:33:23.457 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
09:33:23.470 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
09:33:23.470 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
09:36:39.164 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
09:36:39.166 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:18:19.612 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] Server healthy check fail, currentConnection = 1752629210888_127.0.0.1_12411
10:18:19.614 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:18:23.882 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] Success to connect a server [localhost:8848], connectionId = 1752632303727_127.0.0.1_3110
10:18:23.882 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1752629210888_127.0.0.1_12411
10:18:23.882 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752629210888_127.0.0.1_12411
10:18:23.883 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] Notify disconnected event to listeners
10:18:23.884 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [349aee17-d8d6-4347-befc-a81c794c18db_config-0] Notify connected event to listeners.
14:00:59.473 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:00:59.486 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:00:59.833 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:00:59.833 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3c8edfa9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:00:59.834 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752629237936_127.0.0.1_12522
14:00:59.836 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@409ff8b4[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3537]
14:01:00.119 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:01:00.120 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
14:01:00.123 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
14:01:00.125 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
14:01:00.128 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
14:01:00.128 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:01:00.132 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:01:00.134 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:01:33.560 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:01:34.388 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 36c75219-7d46-48e5-a360-d135f1b73b29_config-0
14:01:34.460 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
14:01:34.497 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
14:01:34.507 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
14:01:34.517 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
14:01:34.526 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
14:01:34.537 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
14:01:34.541 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:01:34.542 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001eb9c3b7410
14:01:34.542 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001eb9c3b7630
14:01:34.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:01:34.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:01:34.550 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:01:35.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752645695332_127.0.0.1_10687
14:01:35.589 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] Notify connected event to listeners.
14:01:35.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:01:35.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001eb9c4ef520
14:01:35.857 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:01:39.986 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:01:39.987 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:01:39.987 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:01:40.179 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:01:41.088 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:01:41.090 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:01:41.091 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:01:48.794 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:01:51.814 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 76e2f89a-4b4a-4225-b00a-2eb3b07c9dbb
14:01:51.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76e2f89a-4b4a-4225-b00a-2eb3b07c9dbb] RpcClient init label, labels = {module=naming, source=sdk}
14:01:51.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76e2f89a-4b4a-4225-b00a-2eb3b07c9dbb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:01:51.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76e2f89a-4b4a-4225-b00a-2eb3b07c9dbb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:01:51.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76e2f89a-4b4a-4225-b00a-2eb3b07c9dbb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:01:51.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76e2f89a-4b4a-4225-b00a-2eb3b07c9dbb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:01:51.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76e2f89a-4b4a-4225-b00a-2eb3b07c9dbb] Success to connect to server [localhost:8848] on start up, connectionId = 1752645711829_127.0.0.1_10717
14:01:51.956 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76e2f89a-4b4a-4225-b00a-2eb3b07c9dbb] Notify connected event to listeners.
14:01:51.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76e2f89a-4b4a-4225-b00a-2eb3b07c9dbb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:01:51.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76e2f89a-4b4a-4225-b00a-2eb3b07c9dbb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001eb9c4ef520
14:01:52.043 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:01:52.084 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:01:52.242 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.373 seconds (JVM running for 21.932)
14:01:52.259 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:01:52.261 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:01:52.262 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:01:52.517 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76e2f89a-4b4a-4225-b00a-2eb3b07c9dbb] Receive server push request, request = NotifySubscriberRequest, requestId = 22
14:01:52.541 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76e2f89a-4b4a-4225-b00a-2eb3b07c9dbb] Ack server push request, request = NotifySubscriberRequest, requestId = 22
14:03:30.670 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:03:33.930 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:03:33.931 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:09:23.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] Server healthy check fail, currentConnection = 1752645695332_127.0.0.1_10687
15:09:23.197 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:09:23.348 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] Success to connect a server [localhost:8848], connectionId = 1752649763223_127.0.0.1_4977
15:09:23.348 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1752645695332_127.0.0.1_10687
15:09:23.348 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752645695332_127.0.0.1_10687
15:09:23.352 [nacos-grpc-client-executor-816] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752645695332_127.0.0.1_10687]Ignore complete event,isRunning:false,isAbandon=true
15:09:23.356 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] Notify disconnected event to listeners
15:09:23.357 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36c75219-7d46-48e5-a360-d135f1b73b29_config-0] Notify connected event to listeners.
15:31:22.524 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:31:22.535 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:31:22.876 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:31:22.876 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@20755a40[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:31:22.876 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752645711829_127.0.0.1_10717
15:31:22.876 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@635503f8[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 1107]
15:31:22.878 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76e2f89a-4b4a-4225-b00a-2eb3b07c9dbb] Notify disconnected event to listeners
15:31:23.045 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:31:23.050 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:31:23.066 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:31:23.066 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:31:23.071 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:31:23.071 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:31:30.162 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:31:30.988 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1f603be1-0119-4692-b2e3-2c18b684725a_config-0
15:31:31.068 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
15:31:31.107 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
15:31:31.117 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
15:31:31.126 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
15:31:31.138 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
15:31:31.154 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
15:31:31.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f603be1-0119-4692-b2e3-2c18b684725a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:31:31.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f603be1-0119-4692-b2e3-2c18b684725a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000027d3e39eaf8
15:31:31.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f603be1-0119-4692-b2e3-2c18b684725a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000027d3e39ed18
15:31:31.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f603be1-0119-4692-b2e3-2c18b684725a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:31:31.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f603be1-0119-4692-b2e3-2c18b684725a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:31:31.168 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f603be1-0119-4692-b2e3-2c18b684725a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:31:32.172 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f603be1-0119-4692-b2e3-2c18b684725a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752651091936_127.0.0.1_8476
15:31:32.174 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f603be1-0119-4692-b2e3-2c18b684725a_config-0] Notify connected event to listeners.
15:31:32.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f603be1-0119-4692-b2e3-2c18b684725a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:31:32.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f603be1-0119-4692-b2e3-2c18b684725a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000027d3e518ad8
15:31:32.328 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:31:36.386 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:31:36.387 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:31:36.388 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:31:36.584 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:31:37.512 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:31:37.514 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:31:37.514 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:31:45.956 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:31:49.027 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c7ae3456-f608-4a22-8e34-c52cebf6fc6b
15:31:49.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7ae3456-f608-4a22-8e34-c52cebf6fc6b] RpcClient init label, labels = {module=naming, source=sdk}
15:31:49.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7ae3456-f608-4a22-8e34-c52cebf6fc6b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:31:49.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7ae3456-f608-4a22-8e34-c52cebf6fc6b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:31:49.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7ae3456-f608-4a22-8e34-c52cebf6fc6b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:31:49.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7ae3456-f608-4a22-8e34-c52cebf6fc6b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:31:49.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7ae3456-f608-4a22-8e34-c52cebf6fc6b] Success to connect to server [localhost:8848] on start up, connectionId = 1752651109041_127.0.0.1_8548
15:31:49.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7ae3456-f608-4a22-8e34-c52cebf6fc6b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:31:49.158 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7ae3456-f608-4a22-8e34-c52cebf6fc6b] Notify connected event to listeners.
15:31:49.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7ae3456-f608-4a22-8e34-c52cebf6fc6b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000027d3e518ad8
15:31:49.255 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:31:49.292 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:31:49.446 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.896 seconds (JVM running for 20.856)
15:31:49.464 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:31:49.465 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:31:49.466 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:31:49.758 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7ae3456-f608-4a22-8e34-c52cebf6fc6b] Receive server push request, request = NotifySubscriberRequest, requestId = 29
15:31:49.783 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7ae3456-f608-4a22-8e34-c52cebf6fc6b] Ack server push request, request = NotifySubscriberRequest, requestId = 29
15:31:49.878 [RMI TCP Connection(8)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:32:04.074 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:32:04.079 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:40:36.640 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:40:36.651 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:40:36.989 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:40:36.989 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3eca2a6b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:40:36.989 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752651109041_127.0.0.1_8548
15:40:36.992 [nacos-grpc-client-executor-117] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752651109041_127.0.0.1_8548]Ignore complete event,isRunning:false,isAbandon=false
15:40:36.997 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5fd8f399[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 118]
15:40:37.175 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:40:37.181 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:40:37.185 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:40:37.185 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:40:37.185 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:40:37.185 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:40:42.460 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:40:43.301 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of de13a7d1-d033-4067-a560-bd89fd6b60ba_config-0
15:40:43.401 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
15:40:43.437 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 4 keys and 9 values 
15:40:43.450 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
15:40:43.450 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
15:40:43.472 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
15:40:43.482 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
15:40:43.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de13a7d1-d033-4067-a560-bd89fd6b60ba_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:40:43.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de13a7d1-d033-4067-a560-bd89fd6b60ba_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000019a8139e8d8
15:40:43.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de13a7d1-d033-4067-a560-bd89fd6b60ba_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000019a8139eaf8
15:40:43.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de13a7d1-d033-4067-a560-bd89fd6b60ba_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:40:43.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de13a7d1-d033-4067-a560-bd89fd6b60ba_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:40:43.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de13a7d1-d033-4067-a560-bd89fd6b60ba_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:40:44.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de13a7d1-d033-4067-a560-bd89fd6b60ba_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752651644370_127.0.0.1_9894
15:40:44.668 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de13a7d1-d033-4067-a560-bd89fd6b60ba_config-0] Notify connected event to listeners.
15:40:44.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de13a7d1-d033-4067-a560-bd89fd6b60ba_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:40:44.672 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de13a7d1-d033-4067-a560-bd89fd6b60ba_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019a81518fb0
15:40:44.837 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:40:49.138 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:40:49.139 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:40:49.140 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:40:49.368 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:40:50.312 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:40:50.315 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:40:50.316 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:40:59.096 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:41:02.402 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5253969e-ab75-4bee-bbbb-10d06952ba2c
15:41:02.403 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5253969e-ab75-4bee-bbbb-10d06952ba2c] RpcClient init label, labels = {module=naming, source=sdk}
15:41:02.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5253969e-ab75-4bee-bbbb-10d06952ba2c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:41:02.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5253969e-ab75-4bee-bbbb-10d06952ba2c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:41:02.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5253969e-ab75-4bee-bbbb-10d06952ba2c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:41:02.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5253969e-ab75-4bee-bbbb-10d06952ba2c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:41:02.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5253969e-ab75-4bee-bbbb-10d06952ba2c] Success to connect to server [localhost:8848] on start up, connectionId = 1752651662412_127.0.0.1_9974
15:41:02.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5253969e-ab75-4bee-bbbb-10d06952ba2c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:41:02.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5253969e-ab75-4bee-bbbb-10d06952ba2c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019a81518fb0
15:41:02.527 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5253969e-ab75-4bee-bbbb-10d06952ba2c] Notify connected event to listeners.
15:41:02.619 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:41:02.662 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:41:02.783 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.983 seconds (JVM running for 21.956)
15:41:02.797 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:41:02.797 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:41:02.797 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:41:03.154 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5253969e-ab75-4bee-bbbb-10d06952ba2c] Receive server push request, request = NotifySubscriberRequest, requestId = 37
15:41:03.181 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5253969e-ab75-4bee-bbbb-10d06952ba2c] Ack server push request, request = NotifySubscriberRequest, requestId = 37
15:41:03.192 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:41:13.553 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:41:13.553 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:02:03.403 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:02:03.403 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:02:03.746 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:02:03.746 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@39d31fb1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:02:03.746 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752651662412_127.0.0.1_9974
17:02:03.746 [nacos-grpc-client-executor-982] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752651662412_127.0.0.1_9974]Ignore complete event,isRunning:false,isAbandon=false
17:02:03.746 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@59a3a478[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 983]
17:02:03.919 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:02:03.919 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:02:03.930 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:02:03.930 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:02:03.930 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:02:03.930 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:02:08.468 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:02:09.002 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 23241cda-f322-4438-abd3-78f10257a979_config-0
17:02:09.052 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
17:02:09.079 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
17:02:09.085 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
17:02:09.091 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
17:02:09.097 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
17:02:09.105 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
17:02:09.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23241cda-f322-4438-abd3-78f10257a979_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:02:09.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23241cda-f322-4438-abd3-78f10257a979_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000244033cdb10
17:02:09.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23241cda-f322-4438-abd3-78f10257a979_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000244033cdd30
17:02:09.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23241cda-f322-4438-abd3-78f10257a979_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:02:09.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23241cda-f322-4438-abd3-78f10257a979_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:02:09.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23241cda-f322-4438-abd3-78f10257a979_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:02:09.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23241cda-f322-4438-abd3-78f10257a979_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752656529688_127.0.0.1_7764
17:02:09.873 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23241cda-f322-4438-abd3-78f10257a979_config-0] Notify connected event to listeners.
17:02:09.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23241cda-f322-4438-abd3-78f10257a979_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:02:09.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23241cda-f322-4438-abd3-78f10257a979_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024403507b88
17:02:09.997 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:02:12.385 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:02:12.385 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:02:12.385 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:02:12.492 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:02:13.161 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:02:13.162 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:02:13.163 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:02:18.134 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:02:20.322 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of de569d3f-d130-4b7d-ad34-788fbf8a8613
17:02:20.323 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de569d3f-d130-4b7d-ad34-788fbf8a8613] RpcClient init label, labels = {module=naming, source=sdk}
17:02:20.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de569d3f-d130-4b7d-ad34-788fbf8a8613] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:02:20.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de569d3f-d130-4b7d-ad34-788fbf8a8613] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:02:20.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de569d3f-d130-4b7d-ad34-788fbf8a8613] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:02:20.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de569d3f-d130-4b7d-ad34-788fbf8a8613] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:02:20.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de569d3f-d130-4b7d-ad34-788fbf8a8613] Success to connect to server [localhost:8848] on start up, connectionId = 1752656540334_127.0.0.1_7805
17:02:20.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de569d3f-d130-4b7d-ad34-788fbf8a8613] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:02:20.455 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de569d3f-d130-4b7d-ad34-788fbf8a8613] Notify connected event to listeners.
17:02:20.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de569d3f-d130-4b7d-ad34-788fbf8a8613] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024403507b88
17:02:20.495 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:02:20.517 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:02:20.610 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.669 seconds (JVM running for 13.561)
17:02:20.621 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:02:20.621 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:02:20.621 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:02:21.065 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de569d3f-d130-4b7d-ad34-788fbf8a8613] Receive server push request, request = NotifySubscriberRequest, requestId = 41
17:02:21.080 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de569d3f-d130-4b7d-ad34-788fbf8a8613] Ack server push request, request = NotifySubscriberRequest, requestId = 41
17:02:21.130 [RMI TCP Connection(10)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:06:19.494 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:06:19.494 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:06:19.494 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
17:06:19.511 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:06:19.517 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:06:19.522 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:33:44.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23241cda-f322-4438-abd3-78f10257a979_config-0] Server check success, currentServer is localhost:8848 
17:43:20.751 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:43:20.756 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:43:21.089 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:43:21.089 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@77e9b39f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:43:21.089 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752656540334_127.0.0.1_7805
17:43:21.092 [nacos-grpc-client-executor-485] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752656540334_127.0.0.1_7805]Ignore complete event,isRunning:false,isAbandon=false
17:43:21.094 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2ff20733[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 486]
17:43:21.222 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:43:21.222 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
17:43:21.222 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
17:43:21.222 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:43:21.222 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:43:21.222 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:44:16.501 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:44:17.038 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6b3be1f9-14d4-4dab-bc3f-15c130278c0b_config-0
17:44:17.091 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 3 keys and 6 values 
17:44:17.119 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
17:44:17.124 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
17:44:17.130 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
17:44:17.137 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
17:44:17.143 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
17:44:17.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b3be1f9-14d4-4dab-bc3f-15c130278c0b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:44:17.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b3be1f9-14d4-4dab-bc3f-15c130278c0b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002a75339eaf8
17:44:17.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b3be1f9-14d4-4dab-bc3f-15c130278c0b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002a75339ed18
17:44:17.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b3be1f9-14d4-4dab-bc3f-15c130278c0b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:44:17.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b3be1f9-14d4-4dab-bc3f-15c130278c0b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:44:17.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b3be1f9-14d4-4dab-bc3f-15c130278c0b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:44:17.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b3be1f9-14d4-4dab-bc3f-15c130278c0b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752659057686_127.0.0.1_13079
17:44:17.900 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b3be1f9-14d4-4dab-bc3f-15c130278c0b_config-0] Notify connected event to listeners.
17:44:17.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b3be1f9-14d4-4dab-bc3f-15c130278c0b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:44:17.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6b3be1f9-14d4-4dab-bc3f-15c130278c0b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002a753518668
17:44:18.008 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:44:20.535 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:44:20.536 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:44:20.536 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:44:20.667 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:44:21.148 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:44:21.149 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:44:21.149 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:44:26.361 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:44:28.452 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a1601e83-4880-437b-8544-b4b0af1b6245
17:44:28.452 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1601e83-4880-437b-8544-b4b0af1b6245] RpcClient init label, labels = {module=naming, source=sdk}
17:44:28.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1601e83-4880-437b-8544-b4b0af1b6245] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:44:28.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1601e83-4880-437b-8544-b4b0af1b6245] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:44:28.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1601e83-4880-437b-8544-b4b0af1b6245] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:44:28.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1601e83-4880-437b-8544-b4b0af1b6245] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:44:28.596 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1601e83-4880-437b-8544-b4b0af1b6245] Success to connect to server [localhost:8848] on start up, connectionId = 1752659068464_127.0.0.1_13098
17:44:28.596 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1601e83-4880-437b-8544-b4b0af1b6245] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:44:28.596 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1601e83-4880-437b-8544-b4b0af1b6245] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002a753518668
17:44:28.596 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1601e83-4880-437b-8544-b4b0af1b6245] Notify connected event to listeners.
17:44:28.635 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:44:28.657 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:44:28.749 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.817 seconds (JVM running for 13.687)
17:44:28.760 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:44:28.761 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:44:28.761 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:44:29.052 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:44:29.228 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1601e83-4880-437b-8544-b4b0af1b6245] Receive server push request, request = NotifySubscriberRequest, requestId = 50
17:44:29.246 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1601e83-4880-437b-8544-b4b0af1b6245] Ack server push request, request = NotifySubscriberRequest, requestId = 50
17:44:47.789 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:44:47.789 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:01:10.253 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:01:10.264 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:01:10.584 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:01:10.584 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2fd5ee8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:01:10.584 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752659068464_127.0.0.1_13098
20:01:10.587 [nacos-grpc-client-executor-1649] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752659068464_127.0.0.1_13098]Ignore complete event,isRunning:false,isAbandon=false
20:01:10.591 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5a65b0bd[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 1650]
20:01:10.750 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:01:10.772 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:01:10.784 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:01:10.786 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:01:10.788 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:01:10.788 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:01:17.638 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:01:18.511 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-ac57-45da-8ebd-6ce1ea7c4fcc_config-0
20:01:18.608 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
20:01:18.656 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
20:01:18.666 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
20:01:18.675 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
20:01:18.684 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
20:01:18.693 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
20:01:18.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ac57-45da-8ebd-6ce1ea7c4fcc_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:01:18.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ac57-45da-8ebd-6ce1ea7c4fcc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001af2639e480
20:01:18.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ac57-45da-8ebd-6ce1ea7c4fcc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001af2639e6a0
20:01:18.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ac57-45da-8ebd-6ce1ea7c4fcc_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:01:18.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ac57-45da-8ebd-6ce1ea7c4fcc_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:01:18.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ac57-45da-8ebd-6ce1ea7c4fcc_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:01:19.795 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ac57-45da-8ebd-6ce1ea7c4fcc_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752667279549_127.0.0.1_4875
20:01:19.797 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ac57-45da-8ebd-6ce1ea7c4fcc_config-0] Notify connected event to listeners.
20:01:19.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ac57-45da-8ebd-6ce1ea7c4fcc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:01:19.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ac57-45da-8ebd-6ce1ea7c4fcc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001af26518228
20:01:19.978 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:01:24.308 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:01:24.308 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:01:24.308 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:01:24.481 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:01:25.235 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:01:25.239 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:01:25.239 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:01:35.910 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:01:43.138 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1fec1894-afab-41c1-a43b-0e99a2029f0e
20:01:43.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fec1894-afab-41c1-a43b-0e99a2029f0e] RpcClient init label, labels = {module=naming, source=sdk}
20:01:43.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fec1894-afab-41c1-a43b-0e99a2029f0e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:01:43.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fec1894-afab-41c1-a43b-0e99a2029f0e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:01:43.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fec1894-afab-41c1-a43b-0e99a2029f0e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:01:43.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fec1894-afab-41c1-a43b-0e99a2029f0e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:01:43.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fec1894-afab-41c1-a43b-0e99a2029f0e] Success to connect to server [localhost:8848] on start up, connectionId = 1752667303165_127.0.0.1_4973
20:01:43.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fec1894-afab-41c1-a43b-0e99a2029f0e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:01:43.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fec1894-afab-41c1-a43b-0e99a2029f0e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001af26518228
20:01:43.291 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fec1894-afab-41c1-a43b-0e99a2029f0e] Notify connected event to listeners.
20:01:43.381 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:01:43.436 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:01:43.773 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.754 seconds (JVM running for 27.833)
20:01:43.811 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:01:43.813 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:01:43.813 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:01:43.846 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fec1894-afab-41c1-a43b-0e99a2029f0e] Receive server push request, request = NotifySubscriberRequest, requestId = 58
20:01:43.871 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fec1894-afab-41c1-a43b-0e99a2029f0e] Ack server push request, request = NotifySubscriberRequest, requestId = 58
20:01:44.346 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:04:57.963 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fec1894-afab-41c1-a43b-0e99a2029f0e] Receive server push request, request = NotifySubscriberRequest, requestId = 59
20:04:57.963 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fec1894-afab-41c1-a43b-0e99a2029f0e] Ack server push request, request = NotifySubscriberRequest, requestId = 59
20:04:59.405 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
20:04:59.413 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:04:59.417 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
20:04:59.434 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:04:59.452 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:04:59.456 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:13:13.403 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:13:13.407 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:13:13.721 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:13:13.721 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@18b85f83[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:13:13.721 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752667303165_127.0.0.1_4973
20:13:13.721 [nacos-grpc-client-executor-150] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752667303165_127.0.0.1_4973]Ignore complete event,isRunning:false,isAbandon=false
20:13:13.721 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@d0918ad[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 151]
20:13:13.867 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:13:13.867 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
20:13:13.867 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
20:13:13.867 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:13:13.874 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:13:13.874 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
