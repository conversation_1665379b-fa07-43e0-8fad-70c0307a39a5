14:32:29.025 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:32:29.583 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 93c18af8-24cd-4d32-b8a0-8534120b52c9_config-0
14:32:29.642 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 3 keys and 6 values 
14:32:29.674 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 4 keys and 9 values 
14:32:29.680 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 5 ms to scan 1 urls, producing 3 keys and 10 values 
14:32:29.686 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 4 ms to scan 1 urls, producing 1 keys and 5 values 
14:32:29.692 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 4 ms to scan 1 urls, producing 1 keys and 7 values 
14:32:29.700 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
14:32:29.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c18af8-24cd-4d32-b8a0-8534120b52c9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:32:29.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c18af8-24cd-4d32-b8a0-8534120b52c9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000247013ceaf8
14:32:29.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c18af8-24cd-4d32-b8a0-8534120b52c9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000247013ced18
14:32:29.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c18af8-24cd-4d32-b8a0-8534120b52c9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:32:29.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c18af8-24cd-4d32-b8a0-8534120b52c9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:32:29.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c18af8-24cd-4d32-b8a0-8534120b52c9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:32:30.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c18af8-24cd-4d32-b8a0-8534120b52c9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753597950194_127.0.0.1_5273
14:32:30.371 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c18af8-24cd-4d32-b8a0-8534120b52c9_config-0] Notify connected event to listeners.
14:32:30.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c18af8-24cd-4d32-b8a0-8534120b52c9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:32:30.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93c18af8-24cd-4d32-b8a0-8534120b52c9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024701508ad8
14:32:30.460 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:32:33.164 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:32:33.165 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:32:33.165 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:32:33.287 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:32:34.032 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:32:34.033 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:32:34.033 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:32:39.249 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:32:42.018 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 20c4dfe0-73fa-467b-9e02-3d828ebba2e8
14:32:42.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] RpcClient init label, labels = {module=naming, source=sdk}
14:32:42.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:32:42.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:32:42.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:32:42.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:32:42.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Success to connect to server [localhost:8848] on start up, connectionId = 1753597962032_127.0.0.1_5283
14:32:42.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:32:42.153 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Notify connected event to listeners.
14:32:42.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024701508ad8
14:32:42.200 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:32:42.227 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:32:42.356 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.828 seconds (JVM running for 14.703)
14:32:42.369 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:32:42.370 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:32:42.370 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:32:42.712 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Receive server push request, request = NotifySubscriberRequest, requestId = 8
14:32:42.732 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Ack server push request, request = NotifySubscriberRequest, requestId = 8
14:32:42.801 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:36:56.331 [nacos-grpc-client-executor-60] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Receive server push request, request = NotifySubscriberRequest, requestId = 12
14:36:56.331 [nacos-grpc-client-executor-60] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Ack server push request, request = NotifySubscriberRequest, requestId = 12
14:36:57.016 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:36:57.016 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:36:57.220 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
14:36:57.220 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
16:24:16.593 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:28:02.051 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
16:28:34.150 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
16:28:34.563 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Success to connect a server [localhost:8848], connectionId = 1753604914457_127.0.0.1_14097
16:28:34.563 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Abandon prev connection, server is localhost:8848, connectionId is 1753597962032_127.0.0.1_5283
16:28:34.563 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753597962032_127.0.0.1_5283
16:28:34.564 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Notify disconnected event to listeners
16:28:34.566 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Notify connected event to listeners.
16:28:37.745 [nacos-grpc-client-executor-1345] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Receive server push request, request = NotifySubscriberRequest, requestId = 18
16:28:37.745 [nacos-grpc-client-executor-1345] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Ack server push request, request = NotifySubscriberRequest, requestId = 18
16:28:37.745 [nacos-grpc-client-executor-1346] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Receive server push request, request = NotifySubscriberRequest, requestId = 19
16:28:37.745 [nacos-grpc-client-executor-1346] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20c4dfe0-73fa-467b-9e02-3d828ebba2e8] Ack server push request, request = NotifySubscriberRequest, requestId = 19
17:03:24.249 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:03:24.266 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:03:24.596 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:03:24.596 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3773ebd4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:03:24.596 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753604914457_127.0.0.1_14097
17:03:24.596 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@25e2bc8[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 1605]
17:03:24.770 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:03:24.770 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
17:03:24.786 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
17:03:24.786 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:03:24.786 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:03:24.786 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:03:24.794 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:03:24.794 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:03:30.033 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:03:30.559 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b68f4665-1c60-472c-82ff-9195dde4c617_config-0
17:03:30.611 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
17:03:30.639 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
17:03:30.645 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
17:03:30.651 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
17:03:30.657 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
17:03:30.663 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
17:03:30.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b68f4665-1c60-472c-82ff-9195dde4c617_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:03:30.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b68f4665-1c60-472c-82ff-9195dde4c617_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001a5613ceaf8
17:03:30.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b68f4665-1c60-472c-82ff-9195dde4c617_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001a5613ced18
17:03:30.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b68f4665-1c60-472c-82ff-9195dde4c617_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:03:30.666 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b68f4665-1c60-472c-82ff-9195dde4c617_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:03:30.672 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b68f4665-1c60-472c-82ff-9195dde4c617_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:03:31.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b68f4665-1c60-472c-82ff-9195dde4c617_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753607011181_127.0.0.1_2323
17:03:31.364 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b68f4665-1c60-472c-82ff-9195dde4c617_config-0] Notify connected event to listeners.
17:03:31.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b68f4665-1c60-472c-82ff-9195dde4c617_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:03:31.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b68f4665-1c60-472c-82ff-9195dde4c617_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a561508ad8
17:03:31.439 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:03:33.825 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:03:33.826 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:03:33.826 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:03:33.941 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:03:34.446 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:03:34.448 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:03:34.448 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:03:39.403 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:03:41.424 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 417ebea3-f431-4b20-9713-d1d0e7884f03
17:03:41.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [417ebea3-f431-4b20-9713-d1d0e7884f03] RpcClient init label, labels = {module=naming, source=sdk}
17:03:41.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [417ebea3-f431-4b20-9713-d1d0e7884f03] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:03:41.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [417ebea3-f431-4b20-9713-d1d0e7884f03] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:03:41.427 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [417ebea3-f431-4b20-9713-d1d0e7884f03] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:03:41.427 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [417ebea3-f431-4b20-9713-d1d0e7884f03] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:03:41.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [417ebea3-f431-4b20-9713-d1d0e7884f03] Success to connect to server [localhost:8848] on start up, connectionId = 1753607021434_127.0.0.1_2336
17:03:41.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [417ebea3-f431-4b20-9713-d1d0e7884f03] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:03:41.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [417ebea3-f431-4b20-9713-d1d0e7884f03] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a561508ad8
17:03:41.545 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [417ebea3-f431-4b20-9713-d1d0e7884f03] Notify connected event to listeners.
17:03:41.585 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:03:41.608 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:03:41.698 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.127 seconds (JVM running for 12.97)
17:03:41.725 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:03:41.725 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:03:41.725 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:03:42.139 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [417ebea3-f431-4b20-9713-d1d0e7884f03] Receive server push request, request = NotifySubscriberRequest, requestId = 28
17:03:42.155 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [417ebea3-f431-4b20-9713-d1d0e7884f03] Ack server push request, request = NotifySubscriberRequest, requestId = 28
17:03:42.191 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:07:30.734 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:07:30.734 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:10:15.230 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:10:15.230 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:10:15.578 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:10:15.578 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@64fbb66b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:10:15.578 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753607021434_127.0.0.1_2336
17:10:15.578 [nacos-grpc-client-executor-90] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753607021434_127.0.0.1_2336]Ignore complete event,isRunning:false,isAbandon=false
17:10:15.583 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@64fd607b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 91]
17:10:15.713 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:10:15.715 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:10:15.720 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:10:15.720 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:10:15.720 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:10:15.720 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:10:19.802 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:10:20.327 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e2169b03-af7e-45ce-be61-32e2d60f8d16_config-0
17:10:20.377 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
17:10:20.401 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
17:10:20.406 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
17:10:20.412 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
17:10:20.418 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
17:10:20.425 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
17:10:20.427 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2169b03-af7e-45ce-be61-32e2d60f8d16_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:10:20.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2169b03-af7e-45ce-be61-32e2d60f8d16_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001feb33ce480
17:10:20.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2169b03-af7e-45ce-be61-32e2d60f8d16_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001feb33ce6a0
17:10:20.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2169b03-af7e-45ce-be61-32e2d60f8d16_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:10:20.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2169b03-af7e-45ce-be61-32e2d60f8d16_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:10:20.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2169b03-af7e-45ce-be61-32e2d60f8d16_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:10:21.085 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2169b03-af7e-45ce-be61-32e2d60f8d16_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753607420904_127.0.0.1_2935
17:10:21.089 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2169b03-af7e-45ce-be61-32e2d60f8d16_config-0] Notify connected event to listeners.
17:10:21.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2169b03-af7e-45ce-be61-32e2d60f8d16_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:10:21.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2169b03-af7e-45ce-be61-32e2d60f8d16_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001feb3508228
17:10:21.170 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:10:23.481 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:10:23.481 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:10:23.481 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:10:23.595 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:10:24.021 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:10:24.022 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:10:24.023 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:10:28.851 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:10:30.829 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5ecb83f1-f45e-4c3e-a9de-5ca8fc9af67a
17:10:30.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ecb83f1-f45e-4c3e-a9de-5ca8fc9af67a] RpcClient init label, labels = {module=naming, source=sdk}
17:10:30.831 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ecb83f1-f45e-4c3e-a9de-5ca8fc9af67a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:10:30.831 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ecb83f1-f45e-4c3e-a9de-5ca8fc9af67a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:10:30.831 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ecb83f1-f45e-4c3e-a9de-5ca8fc9af67a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:10:30.831 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ecb83f1-f45e-4c3e-a9de-5ca8fc9af67a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:10:30.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ecb83f1-f45e-4c3e-a9de-5ca8fc9af67a] Success to connect to server [localhost:8848] on start up, connectionId = 1753607430838_127.0.0.1_2954
17:10:30.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ecb83f1-f45e-4c3e-a9de-5ca8fc9af67a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:10:30.951 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ecb83f1-f45e-4c3e-a9de-5ca8fc9af67a] Notify connected event to listeners.
17:10:30.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ecb83f1-f45e-4c3e-a9de-5ca8fc9af67a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001feb3508228
17:10:30.989 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:10:31.008 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:10:31.093 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 11.77 seconds (JVM running for 12.632)
17:10:31.103 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:10:31.103 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:10:31.103 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:10:31.453 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:10:31.569 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ecb83f1-f45e-4c3e-a9de-5ca8fc9af67a] Receive server push request, request = NotifySubscriberRequest, requestId = 32
17:10:31.584 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ecb83f1-f45e-4c3e-a9de-5ca8fc9af67a] Ack server push request, request = NotifySubscriberRequest, requestId = 32
17:11:14.435 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:11:14.435 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:12:06.623 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:12:06.623 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:12:06.962 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:12:06.962 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3dec1e39[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:12:06.962 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753607430838_127.0.0.1_2954
17:12:06.962 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753607430838_127.0.0.1_2954]Ignore complete event,isRunning:false,isAbandon=false
17:12:06.970 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@34298e4a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 32]
17:12:07.100 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:12:07.111 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:12:07.116 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:12:07.119 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:12:07.119 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:12:07.119 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:12:11.606 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:12:12.278 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f28c0fe3-ab74-4538-9121-987409c2aac2_config-0
17:12:12.362 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
17:12:12.405 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
17:12:12.406 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
17:12:12.420 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
17:12:12.422 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 2 ms to scan 1 urls, producing 1 keys and 7 values 
17:12:12.434 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
17:12:12.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28c0fe3-ab74-4538-9121-987409c2aac2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:12:12.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28c0fe3-ab74-4538-9121-987409c2aac2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002429139e480
17:12:12.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28c0fe3-ab74-4538-9121-987409c2aac2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002429139e6a0
17:12:12.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28c0fe3-ab74-4538-9121-987409c2aac2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:12:12.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28c0fe3-ab74-4538-9121-987409c2aac2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:12:12.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28c0fe3-ab74-4538-9121-987409c2aac2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:12:13.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28c0fe3-ab74-4538-9121-987409c2aac2_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753607533029_127.0.0.1_3115
17:12:13.213 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28c0fe3-ab74-4538-9121-987409c2aac2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:12:13.213 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28c0fe3-ab74-4538-9121-987409c2aac2_config-0] Notify connected event to listeners.
17:12:13.214 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28c0fe3-ab74-4538-9121-987409c2aac2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024291518668
17:12:13.302 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:12:15.764 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:12:15.764 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:12:15.764 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:12:15.873 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:12:16.421 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:12:16.422 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:12:16.423 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:12:24.658 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:12:26.635 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d08eacf0-f8ad-48a9-9776-1c705a0fc030
17:12:26.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d08eacf0-f8ad-48a9-9776-1c705a0fc030] RpcClient init label, labels = {module=naming, source=sdk}
17:12:26.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d08eacf0-f8ad-48a9-9776-1c705a0fc030] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:12:26.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d08eacf0-f8ad-48a9-9776-1c705a0fc030] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:12:26.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d08eacf0-f8ad-48a9-9776-1c705a0fc030] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:12:26.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d08eacf0-f8ad-48a9-9776-1c705a0fc030] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:12:26.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d08eacf0-f8ad-48a9-9776-1c705a0fc030] Success to connect to server [localhost:8848] on start up, connectionId = 1753607546644_127.0.0.1_3132
17:12:26.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d08eacf0-f8ad-48a9-9776-1c705a0fc030] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:12:26.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d08eacf0-f8ad-48a9-9776-1c705a0fc030] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024291518668
17:12:26.768 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d08eacf0-f8ad-48a9-9776-1c705a0fc030] Notify connected event to listeners.
17:12:26.805 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:12:26.826 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:12:26.915 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.93 seconds (JVM running for 17.151)
17:12:26.927 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:12:26.928 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:12:26.928 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:12:27.373 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d08eacf0-f8ad-48a9-9776-1c705a0fc030] Receive server push request, request = NotifySubscriberRequest, requestId = 42
17:12:27.390 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d08eacf0-f8ad-48a9-9776-1c705a0fc030] Ack server push request, request = NotifySubscriberRequest, requestId = 42
17:12:27.424 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:12:32.496 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:12:32.496 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:13:30.795 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:13:30.795 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:13:31.121 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:13:31.121 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@53d859be[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:13:31.121 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753607546644_127.0.0.1_3132
17:13:31.129 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753607546644_127.0.0.1_3132]Ignore complete event,isRunning:false,isAbandon=false
17:13:31.129 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3837251b[Running, pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 25]
17:13:31.247 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:13:31.247 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:13:31.263 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:13:31.263 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:13:31.263 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:13:31.263 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:13:35.442 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:13:36.004 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ec1cd7de-8db6-4f25-bce0-af6e214a085f_config-0
17:13:36.053 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
17:13:36.087 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
17:13:36.095 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
17:13:36.095 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
17:13:36.104 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
17:13:36.117 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
17:13:36.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec1cd7de-8db6-4f25-bce0-af6e214a085f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:13:36.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec1cd7de-8db6-4f25-bce0-af6e214a085f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d0a939e8d8
17:13:36.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec1cd7de-8db6-4f25-bce0-af6e214a085f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001d0a939eaf8
17:13:36.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec1cd7de-8db6-4f25-bce0-af6e214a085f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:13:36.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec1cd7de-8db6-4f25-bce0-af6e214a085f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:13:36.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec1cd7de-8db6-4f25-bce0-af6e214a085f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:13:36.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec1cd7de-8db6-4f25-bce0-af6e214a085f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753607616622_127.0.0.1_3255
17:13:36.825 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec1cd7de-8db6-4f25-bce0-af6e214a085f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:13:36.825 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec1cd7de-8db6-4f25-bce0-af6e214a085f_config-0] Notify connected event to listeners.
17:13:36.825 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec1cd7de-8db6-4f25-bce0-af6e214a085f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d0a9518ad8
17:13:36.914 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:13:39.539 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:13:39.539 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:13:39.539 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:13:39.656 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:13:40.194 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:13:40.199 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:13:40.199 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:13:45.897 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:13:48.007 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of eab4f63c-219f-4717-9f04-3d8e5b80f71f
17:13:48.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab4f63c-219f-4717-9f04-3d8e5b80f71f] RpcClient init label, labels = {module=naming, source=sdk}
17:13:48.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab4f63c-219f-4717-9f04-3d8e5b80f71f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:13:48.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab4f63c-219f-4717-9f04-3d8e5b80f71f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:13:48.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab4f63c-219f-4717-9f04-3d8e5b80f71f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:13:48.009 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab4f63c-219f-4717-9f04-3d8e5b80f71f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:13:48.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab4f63c-219f-4717-9f04-3d8e5b80f71f] Success to connect to server [localhost:8848] on start up, connectionId = 1753607628016_127.0.0.1_3284
17:13:48.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab4f63c-219f-4717-9f04-3d8e5b80f71f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:13:48.139 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab4f63c-219f-4717-9f04-3d8e5b80f71f] Notify connected event to listeners.
17:13:48.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab4f63c-219f-4717-9f04-3d8e5b80f71f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d0a9518ad8
17:13:48.190 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:13:48.212 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:13:48.308 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.414 seconds (JVM running for 14.36)
17:13:48.319 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:13:48.319 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:13:48.320 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:13:48.511 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:13:48.733 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab4f63c-219f-4717-9f04-3d8e5b80f71f] Receive server push request, request = NotifySubscriberRequest, requestId = 48
17:13:48.747 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab4f63c-219f-4717-9f04-3d8e5b80f71f] Ack server push request, request = NotifySubscriberRequest, requestId = 48
17:13:52.946 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:13:52.946 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:15:51.962 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:15:51.962 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:15:52.292 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:15:52.292 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3a69414f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:15:52.292 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753607628016_127.0.0.1_3284
17:15:52.292 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753607628016_127.0.0.1_3284]Ignore complete event,isRunning:false,isAbandon=false
17:15:52.297 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 39]
17:15:52.430 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:15:52.430 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:15:52.442 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:15:52.442 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:15:52.442 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:15:52.442 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:15:56.583 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:15:57.142 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 09bdeb93-90cd-4234-8f77-0c43050ff906_config-0
17:15:57.191 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
17:15:57.217 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
17:15:57.223 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
17:15:57.230 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
17:15:57.236 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
17:15:57.243 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
17:15:57.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09bdeb93-90cd-4234-8f77-0c43050ff906_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:15:57.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09bdeb93-90cd-4234-8f77-0c43050ff906_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001994539dd70
17:15:57.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09bdeb93-90cd-4234-8f77-0c43050ff906_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001994539df90
17:15:57.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09bdeb93-90cd-4234-8f77-0c43050ff906_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:15:57.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09bdeb93-90cd-4234-8f77-0c43050ff906_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:15:57.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09bdeb93-90cd-4234-8f77-0c43050ff906_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:15:57.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09bdeb93-90cd-4234-8f77-0c43050ff906_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753607757749_127.0.0.1_3495
17:15:57.925 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09bdeb93-90cd-4234-8f77-0c43050ff906_config-0] Notify connected event to listeners.
17:15:57.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09bdeb93-90cd-4234-8f77-0c43050ff906_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:15:57.926 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09bdeb93-90cd-4234-8f77-0c43050ff906_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019945518228
17:15:58.008 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:16:00.821 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:16:00.821 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:16:00.822 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:16:00.957 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:16:01.518 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:16:01.520 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:16:01.520 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:16:07.061 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:16:09.512 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 28e24a4d-6f93-4c85-9249-b97a0e7ba68e
17:16:09.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e24a4d-6f93-4c85-9249-b97a0e7ba68e] RpcClient init label, labels = {module=naming, source=sdk}
17:16:09.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e24a4d-6f93-4c85-9249-b97a0e7ba68e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:16:09.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e24a4d-6f93-4c85-9249-b97a0e7ba68e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:16:09.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e24a4d-6f93-4c85-9249-b97a0e7ba68e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:16:09.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e24a4d-6f93-4c85-9249-b97a0e7ba68e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:16:09.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e24a4d-6f93-4c85-9249-b97a0e7ba68e] Success to connect to server [localhost:8848] on start up, connectionId = 1753607769522_127.0.0.1_3532
17:16:09.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e24a4d-6f93-4c85-9249-b97a0e7ba68e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:16:09.637 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e24a4d-6f93-4c85-9249-b97a0e7ba68e] Notify connected event to listeners.
17:16:09.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e24a4d-6f93-4c85-9249-b97a0e7ba68e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019945518228
17:16:09.680 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:16:09.706 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:16:09.802 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.735 seconds (JVM running for 14.717)
17:16:09.818 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:16:09.820 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:16:09.820 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:16:10.059 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:16:10.198 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e24a4d-6f93-4c85-9249-b97a0e7ba68e] Receive server push request, request = NotifySubscriberRequest, requestId = 55
17:16:10.214 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e24a4d-6f93-4c85-9249-b97a0e7ba68e] Ack server push request, request = NotifySubscriberRequest, requestId = 55
17:16:19.172 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:16:19.172 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:31:51.386 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09bdeb93-90cd-4234-8f77-0c43050ff906_config-0] Server check success, currentServer is localhost:8848 
19:48:19.078 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:48:19.083 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:48:19.411 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:48:19.411 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@283ba031[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:48:19.411 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753607769522_127.0.0.1_3532
19:48:19.412 [nacos-grpc-client-executor-1824] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753607769522_127.0.0.1_3532]Ignore complete event,isRunning:false,isAbandon=false
19:48:19.416 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6f130ce9[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 1825]
19:48:19.587 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:48:19.590 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:48:19.597 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:48:19.597 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:48:19.599 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:48:19.600 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:48:25.626 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:48:26.191 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e16a1499-2824-41b5-bc5f-686d9d614ef3_config-0
19:48:26.242 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 
19:48:26.270 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
19:48:26.276 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
19:48:26.283 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
19:48:26.290 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
19:48:26.299 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
19:48:26.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e16a1499-2824-41b5-bc5f-686d9d614ef3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:48:26.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e16a1499-2824-41b5-bc5f-686d9d614ef3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002040139ed38
19:48:26.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e16a1499-2824-41b5-bc5f-686d9d614ef3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002040139ef58
19:48:26.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e16a1499-2824-41b5-bc5f-686d9d614ef3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:48:26.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e16a1499-2824-41b5-bc5f-686d9d614ef3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:48:26.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e16a1499-2824-41b5-bc5f-686d9d614ef3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:48:27.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e16a1499-2824-41b5-bc5f-686d9d614ef3_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753616906826_127.0.0.1_14849
19:48:27.010 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e16a1499-2824-41b5-bc5f-686d9d614ef3_config-0] Notify connected event to listeners.
19:48:27.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e16a1499-2824-41b5-bc5f-686d9d614ef3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:48:27.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e16a1499-2824-41b5-bc5f-686d9d614ef3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020401518fd0
19:48:27.098 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:48:29.592 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:48:29.593 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:48:29.593 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:48:29.708 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:48:30.191 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:48:30.192 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:48:30.192 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:48:35.377 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:48:37.551 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d71799a6-30d1-46bc-b1cc-663c215f4f6a
19:48:37.551 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d71799a6-30d1-46bc-b1cc-663c215f4f6a] RpcClient init label, labels = {module=naming, source=sdk}
19:48:37.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d71799a6-30d1-46bc-b1cc-663c215f4f6a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:48:37.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d71799a6-30d1-46bc-b1cc-663c215f4f6a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:48:37.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d71799a6-30d1-46bc-b1cc-663c215f4f6a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:48:37.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d71799a6-30d1-46bc-b1cc-663c215f4f6a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:48:37.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d71799a6-30d1-46bc-b1cc-663c215f4f6a] Success to connect to server [localhost:8848] on start up, connectionId = 1753616917561_127.0.0.1_14874
19:48:37.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d71799a6-30d1-46bc-b1cc-663c215f4f6a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:48:37.675 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d71799a6-30d1-46bc-b1cc-663c215f4f6a] Notify connected event to listeners.
19:48:37.676 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d71799a6-30d1-46bc-b1cc-663c215f4f6a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020401518fd0
19:48:37.718 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:48:37.743 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:48:37.834 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.739 seconds (JVM running for 13.689)
19:48:37.863 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:48:37.863 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:48:37.864 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:48:38.180 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:48:38.299 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d71799a6-30d1-46bc-b1cc-663c215f4f6a] Receive server push request, request = NotifySubscriberRequest, requestId = 63
19:48:38.315 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d71799a6-30d1-46bc-b1cc-663c215f4f6a] Ack server push request, request = NotifySubscriberRequest, requestId = 63
19:48:44.778 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:48:44.778 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:51:57.078 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:51:57.081 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:51:57.400 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:51:57.400 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@77a7d635[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:51:57.400 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753616917561_127.0.0.1_14874
19:51:57.402 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753616917561_127.0.0.1_14874]Ignore complete event,isRunning:false,isAbandon=false
19:51:57.404 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 54]
19:51:57.532 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:51:57.534 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:51:57.538 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:51:57.539 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:51:57.540 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:51:57.540 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:52:01.653 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:52:02.213 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0b14f53e-206c-4c14-8d1a-6acc3e0ae8ad_config-0
19:52:02.266 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
19:52:02.293 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
19:52:02.300 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
19:52:02.307 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
19:52:02.313 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
19:52:02.318 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
19:52:02.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b14f53e-206c-4c14-8d1a-6acc3e0ae8ad_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:52:02.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b14f53e-206c-4c14-8d1a-6acc3e0ae8ad_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000230613ce8d8
19:52:02.321 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b14f53e-206c-4c14-8d1a-6acc3e0ae8ad_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000230613ceaf8
19:52:02.321 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b14f53e-206c-4c14-8d1a-6acc3e0ae8ad_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:52:02.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b14f53e-206c-4c14-8d1a-6acc3e0ae8ad_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:52:02.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b14f53e-206c-4c14-8d1a-6acc3e0ae8ad_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:52:03.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b14f53e-206c-4c14-8d1a-6acc3e0ae8ad_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753617122817_127.0.0.1_1197
19:52:03.005 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b14f53e-206c-4c14-8d1a-6acc3e0ae8ad_config-0] Notify connected event to listeners.
19:52:03.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b14f53e-206c-4c14-8d1a-6acc3e0ae8ad_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:52:03.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b14f53e-206c-4c14-8d1a-6acc3e0ae8ad_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023061508668
19:52:03.084 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:52:06.123 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:52:06.124 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:52:06.124 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:52:06.244 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:52:06.732 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:52:06.732 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:52:06.733 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:52:13.232 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:52:16.237 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7294fd87-936f-404d-b680-b81938d6ad28
19:52:16.237 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7294fd87-936f-404d-b680-b81938d6ad28] RpcClient init label, labels = {module=naming, source=sdk}
19:52:16.238 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7294fd87-936f-404d-b680-b81938d6ad28] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:52:16.239 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7294fd87-936f-404d-b680-b81938d6ad28] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:52:16.239 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7294fd87-936f-404d-b680-b81938d6ad28] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:52:16.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7294fd87-936f-404d-b680-b81938d6ad28] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:52:16.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7294fd87-936f-404d-b680-b81938d6ad28] Success to connect to server [localhost:8848] on start up, connectionId = 1753617136247_127.0.0.1_1215
19:52:16.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7294fd87-936f-404d-b680-b81938d6ad28] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:52:16.369 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7294fd87-936f-404d-b680-b81938d6ad28] Notify connected event to listeners.
19:52:16.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7294fd87-936f-404d-b680-b81938d6ad28] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023061508668
19:52:16.415 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:52:16.442 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:52:16.552 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.377 seconds (JVM running for 16.27)
19:52:16.565 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:52:16.566 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:52:16.566 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:52:16.888 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:52:16.992 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7294fd87-936f-404d-b680-b81938d6ad28] Receive server push request, request = NotifySubscriberRequest, requestId = 69
19:52:17.006 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7294fd87-936f-404d-b680-b81938d6ad28] Ack server push request, request = NotifySubscriberRequest, requestId = 69
19:52:22.193 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:52:22.194 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:53:45.555 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:53:45.565 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:53:45.900 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:53:45.900 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@525094a2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:53:45.900 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753617136247_127.0.0.1_1215
19:53:45.900 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753617136247_127.0.0.1_1215]Ignore complete event,isRunning:false,isAbandon=false
19:53:45.916 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 30]
19:53:46.089 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:53:46.089 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:53:46.105 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:53:46.105 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:53:46.105 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:53:46.105 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:53:50.408 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:53:50.946 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f9de9d4d-5c4f-4df6-bbff-94cc6d855868_config-0
19:53:50.995 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
19:53:51.026 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
19:53:51.032 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
19:53:51.039 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
19:53:51.046 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
19:53:51.052 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
19:53:51.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9de9d4d-5c4f-4df6-bbff-94cc6d855868_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:53:51.055 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9de9d4d-5c4f-4df6-bbff-94cc6d855868_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000014cbc3ceaf8
19:53:51.055 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9de9d4d-5c4f-4df6-bbff-94cc6d855868_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000014cbc3ced18
19:53:51.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9de9d4d-5c4f-4df6-bbff-94cc6d855868_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:53:51.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9de9d4d-5c4f-4df6-bbff-94cc6d855868_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:53:51.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9de9d4d-5c4f-4df6-bbff-94cc6d855868_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:53:51.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9de9d4d-5c4f-4df6-bbff-94cc6d855868_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753617231577_127.0.0.1_1389
19:53:51.762 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9de9d4d-5c4f-4df6-bbff-94cc6d855868_config-0] Notify connected event to listeners.
19:53:51.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9de9d4d-5c4f-4df6-bbff-94cc6d855868_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:53:51.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9de9d4d-5c4f-4df6-bbff-94cc6d855868_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000014cbc508d48
19:53:51.836 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:53:54.229 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:53:54.229 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:53:54.230 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:53:54.339 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:53:55.063 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:53:55.064 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:53:55.064 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:54:00.182 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:54:02.246 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5c78e98f-0b6b-4749-9ef3-e1e35bb4bb0d
19:54:02.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c78e98f-0b6b-4749-9ef3-e1e35bb4bb0d] RpcClient init label, labels = {module=naming, source=sdk}
19:54:02.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c78e98f-0b6b-4749-9ef3-e1e35bb4bb0d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:54:02.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c78e98f-0b6b-4749-9ef3-e1e35bb4bb0d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:54:02.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c78e98f-0b6b-4749-9ef3-e1e35bb4bb0d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:54:02.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c78e98f-0b6b-4749-9ef3-e1e35bb4bb0d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:54:02.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c78e98f-0b6b-4749-9ef3-e1e35bb4bb0d] Success to connect to server [localhost:8848] on start up, connectionId = 1753617242255_127.0.0.1_1406
19:54:02.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c78e98f-0b6b-4749-9ef3-e1e35bb4bb0d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:54:02.372 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c78e98f-0b6b-4749-9ef3-e1e35bb4bb0d] Notify connected event to listeners.
19:54:02.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c78e98f-0b6b-4749-9ef3-e1e35bb4bb0d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000014cbc508d48
19:54:02.411 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:54:02.433 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:54:02.519 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.594 seconds (JVM running for 13.416)
19:54:02.532 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:54:02.533 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:54:02.533 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:54:02.917 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c78e98f-0b6b-4749-9ef3-e1e35bb4bb0d] Receive server push request, request = NotifySubscriberRequest, requestId = 77
19:54:02.929 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c78e98f-0b6b-4749-9ef3-e1e35bb4bb0d] Ack server push request, request = NotifySubscriberRequest, requestId = 77
19:54:03.052 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:54:06.746 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:54:06.747 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:54:27.046 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:54:27.049 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:54:27.373 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:54:27.373 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6d47369[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:54:27.373 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753617242255_127.0.0.1_1406
19:54:27.376 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753617242255_127.0.0.1_1406]Ignore complete event,isRunning:false,isAbandon=false
19:54:27.376 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4cf8639a[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 15]
19:54:27.520 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:54:27.522 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:54:27.526 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:54:27.526 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:54:27.527 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:54:27.527 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:54:32.895 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:54:33.949 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2014d686-adb3-4b91-8157-52ee49279694_config-0
19:54:34.031 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
19:54:34.081 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
19:54:34.092 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
19:54:34.106 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
19:54:34.119 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
19:54:34.133 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
19:54:34.137 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2014d686-adb3-4b91-8157-52ee49279694_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:54:34.137 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2014d686-adb3-4b91-8157-52ee49279694_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001a7d939e8d8
19:54:34.138 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2014d686-adb3-4b91-8157-52ee49279694_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001a7d939eaf8
19:54:34.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2014d686-adb3-4b91-8157-52ee49279694_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:54:34.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2014d686-adb3-4b91-8157-52ee49279694_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:54:34.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2014d686-adb3-4b91-8157-52ee49279694_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:54:35.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2014d686-adb3-4b91-8157-52ee49279694_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753617274932_127.0.0.1_1516
19:54:35.146 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2014d686-adb3-4b91-8157-52ee49279694_config-0] Notify connected event to listeners.
19:54:35.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2014d686-adb3-4b91-8157-52ee49279694_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:54:35.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2014d686-adb3-4b91-8157-52ee49279694_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a7d9518440
19:54:35.253 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:54:38.145 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:54:38.146 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:54:38.146 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:54:38.277 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:54:38.796 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:54:38.798 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:54:38.798 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:54:44.486 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:54:47.024 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-2677-4c5b-abd7-91f7160e1271
19:54:47.024 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2677-4c5b-abd7-91f7160e1271] RpcClient init label, labels = {module=naming, source=sdk}
19:54:47.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2677-4c5b-abd7-91f7160e1271] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:54:47.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2677-4c5b-abd7-91f7160e1271] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:54:47.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2677-4c5b-abd7-91f7160e1271] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:54:47.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2677-4c5b-abd7-91f7160e1271] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:54:47.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2677-4c5b-abd7-91f7160e1271] Success to connect to server [localhost:8848] on start up, connectionId = 1753617287034_127.0.0.1_1534
19:54:47.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2677-4c5b-abd7-91f7160e1271] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:54:47.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2677-4c5b-abd7-91f7160e1271] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a7d9518440
19:54:47.155 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2677-4c5b-abd7-91f7160e1271] Notify connected event to listeners.
19:54:47.212 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:54:47.247 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:54:47.371 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.355 seconds (JVM running for 16.698)
19:54:47.384 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:54:47.385 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:54:47.386 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:54:47.507 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:54:47.682 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2677-4c5b-abd7-91f7160e1271] Receive server push request, request = NotifySubscriberRequest, requestId = 81
19:54:47.696 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2677-4c5b-abd7-91f7160e1271] Ack server push request, request = NotifySubscriberRequest, requestId = 81
19:54:51.823 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:54:51.823 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:59:06.658 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:59:06.663 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:59:06.999 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:59:06.999 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1e6fe971[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:59:06.999 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753617287034_127.0.0.1_1534
19:59:07.001 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753617287034_127.0.0.1_1534]Ignore complete event,isRunning:false,isAbandon=false
19:59:07.003 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@285a5901[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 48]
19:59:07.142 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:59:07.145 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:59:07.150 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:59:07.150 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:59:07.151 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:59:07.151 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
