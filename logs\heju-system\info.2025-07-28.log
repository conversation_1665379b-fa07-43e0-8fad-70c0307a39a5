09:05:51.051 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:05:52.137 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 199a3965-cd4b-4d46-a454-05b644be0847_config-0
09:05:52.267 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 52 ms to scan 1 urls, producing 3 keys and 6 values 
09:05:52.340 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:05:52.352 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:05:52.367 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:05:52.381 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:05:52.392 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:05:52.396 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [199a3965-cd4b-4d46-a454-05b644be0847_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:05:52.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [199a3965-cd4b-4d46-a454-05b644be0847_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001a50139bda8
09:05:52.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [199a3965-cd4b-4d46-a454-05b644be0847_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001a50139bfc8
09:05:52.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [199a3965-cd4b-4d46-a454-05b644be0847_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:05:52.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [199a3965-cd4b-4d46-a454-05b644be0847_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:05:52.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [199a3965-cd4b-4d46-a454-05b644be0847_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:05:53.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [199a3965-cd4b-4d46-a454-05b644be0847_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753664753387_127.0.0.1_11189
09:05:53.684 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [199a3965-cd4b-4d46-a454-05b644be0847_config-0] Notify connected event to listeners.
09:05:53.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [199a3965-cd4b-4d46-a454-05b644be0847_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:05:53.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [199a3965-cd4b-4d46-a454-05b644be0847_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001a501513db0
09:05:53.893 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:06:06.776 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:06:06.780 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:06:06.785 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:06:07.468 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:06:09.288 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:06:09.290 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:06:09.290 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:06:22.318 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:06:25.708 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1805a690-a0ea-4036-ad29-93e374936133
09:06:25.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1805a690-a0ea-4036-ad29-93e374936133] RpcClient init label, labels = {module=naming, source=sdk}
09:06:25.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1805a690-a0ea-4036-ad29-93e374936133] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:06:25.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1805a690-a0ea-4036-ad29-93e374936133] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:06:25.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1805a690-a0ea-4036-ad29-93e374936133] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:06:25.711 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1805a690-a0ea-4036-ad29-93e374936133] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:25.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1805a690-a0ea-4036-ad29-93e374936133] Success to connect to server [localhost:8848] on start up, connectionId = 1753664785718_127.0.0.1_11403
09:06:25.844 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1805a690-a0ea-4036-ad29-93e374936133] Notify connected event to listeners.
09:06:25.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1805a690-a0ea-4036-ad29-93e374936133] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:25.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1805a690-a0ea-4036-ad29-93e374936133] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001a501513db0
09:06:25.896 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:06:25.936 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:06:26.096 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 36.113 seconds (JVM running for 41.025)
09:06:26.131 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:06:26.132 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:06:26.132 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:06:26.400 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1805a690-a0ea-4036-ad29-93e374936133] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:06:26.420 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1805a690-a0ea-4036-ad29-93e374936133] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:11:45.623 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:11:49.099 [nacos-grpc-client-executor-74] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1805a690-a0ea-4036-ad29-93e374936133] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:11:49.099 [nacos-grpc-client-executor-74] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1805a690-a0ea-4036-ad29-93e374936133] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:11:50.475 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:11:50.476 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:11:50.780 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:11:50.783 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
11:57:31.414 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:57:31.430 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:57:31.789 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:57:31.792 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@425178ac[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:57:31.792 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753664785718_127.0.0.1_11403
11:57:31.796 [nacos-grpc-client-executor-2070] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753664785718_127.0.0.1_11403]Ignore complete event,isRunning:false,isAbandon=false
11:57:31.800 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5267b6ef[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2071]
11:57:32.006 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:57:32.048 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
11:57:32.079 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
11:57:32.081 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:57:32.084 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:57:32.084 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:57:32.086 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:57:32.086 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:59:06.073 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:59:06.893 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bbb6ff25-b3b4-4038-a18a-8a587d5845df_config-0
11:59:06.967 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
11:59:07.005 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
11:59:07.005 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
11:59:07.022 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
11:59:07.036 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
11:59:07.036 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
11:59:07.036 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbb6ff25-b3b4-4038-a18a-8a587d5845df_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:59:07.052 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbb6ff25-b3b4-4038-a18a-8a587d5845df_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000250123b6480
11:59:07.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbb6ff25-b3b4-4038-a18a-8a587d5845df_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000250123b66a0
11:59:07.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbb6ff25-b3b4-4038-a18a-8a587d5845df_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:59:07.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbb6ff25-b3b4-4038-a18a-8a587d5845df_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:59:07.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbb6ff25-b3b4-4038-a18a-8a587d5845df_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:59:08.085 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbb6ff25-b3b4-4038-a18a-8a587d5845df_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753675147837_127.0.0.1_4183
11:59:08.088 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbb6ff25-b3b4-4038-a18a-8a587d5845df_config-0] Notify connected event to listeners.
11:59:08.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbb6ff25-b3b4-4038-a18a-8a587d5845df_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:59:08.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbb6ff25-b3b4-4038-a18a-8a587d5845df_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000250124f0228
11:59:08.316 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:59:12.492 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:59:12.492 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:59:12.499 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:59:12.690 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:59:13.436 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:59:13.436 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:59:13.436 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:59:21.623 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:59:24.926 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a0b2224e-b4fe-4835-a4f0-82890632b5d1
11:59:24.926 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0b2224e-b4fe-4835-a4f0-82890632b5d1] RpcClient init label, labels = {module=naming, source=sdk}
11:59:24.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0b2224e-b4fe-4835-a4f0-82890632b5d1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:59:24.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0b2224e-b4fe-4835-a4f0-82890632b5d1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:59:24.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0b2224e-b4fe-4835-a4f0-82890632b5d1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:59:24.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0b2224e-b4fe-4835-a4f0-82890632b5d1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:59:25.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0b2224e-b4fe-4835-a4f0-82890632b5d1] Success to connect to server [localhost:8848] on start up, connectionId = 1753675164957_127.0.0.1_4227
11:59:25.105 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0b2224e-b4fe-4835-a4f0-82890632b5d1] Notify connected event to listeners.
11:59:25.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0b2224e-b4fe-4835-a4f0-82890632b5d1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:59:25.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0b2224e-b4fe-4835-a4f0-82890632b5d1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000250124f0228
11:59:25.169 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:59:25.207 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:59:25.367 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.951 seconds (JVM running for 21.549)
11:59:25.385 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:59:25.388 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:59:25.388 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:59:25.730 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0b2224e-b4fe-4835-a4f0-82890632b5d1] Receive server push request, request = NotifySubscriberRequest, requestId = 20
11:59:25.742 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0b2224e-b4fe-4835-a4f0-82890632b5d1] Ack server push request, request = NotifySubscriberRequest, requestId = 20
11:59:52.769 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:59:54.294 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:59:54.294 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:45:51.790 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:45:51.797 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:45:52.144 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:45:52.144 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@228e1076[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:45:52.144 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753675164957_127.0.0.1_4227
15:45:52.147 [nacos-grpc-client-executor-2739] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753675164957_127.0.0.1_4227]Ignore complete event,isRunning:false,isAbandon=false
15:45:52.150 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7f30057[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 2740]
15:45:52.407 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:45:52.415 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:45:52.429 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:45:52.429 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:45:52.431 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:45:52.431 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:46:18.894 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:46:19.462 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 156bad3f-2167-4ce9-a6cf-ba3f0347c812_config-0
15:46:19.531 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
15:46:19.561 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
15:46:19.561 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
15:46:19.576 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 5 values 
15:46:19.583 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
15:46:19.589 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
15:46:19.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [156bad3f-2167-4ce9-a6cf-ba3f0347c812_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:46:19.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [156bad3f-2167-4ce9-a6cf-ba3f0347c812_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001dce53b8b08
15:46:19.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [156bad3f-2167-4ce9-a6cf-ba3f0347c812_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001dce53b8d28
15:46:19.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [156bad3f-2167-4ce9-a6cf-ba3f0347c812_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:46:19.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [156bad3f-2167-4ce9-a6cf-ba3f0347c812_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:46:19.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [156bad3f-2167-4ce9-a6cf-ba3f0347c812_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:46:20.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [156bad3f-2167-4ce9-a6cf-ba3f0347c812_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753688780127_127.0.0.1_7952
15:46:20.309 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [156bad3f-2167-4ce9-a6cf-ba3f0347c812_config-0] Notify connected event to listeners.
15:46:20.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [156bad3f-2167-4ce9-a6cf-ba3f0347c812_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:46:20.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [156bad3f-2167-4ce9-a6cf-ba3f0347c812_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001dce54f0668
15:46:20.439 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:46:22.859 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:46:22.859 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:46:22.859 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:46:22.975 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:46:23.415 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:46:23.416 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:46:23.416 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:46:28.422 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:46:30.900 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c94d8818-f78b-4cba-8a18-b67b2d3c8e8d
15:46:30.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c94d8818-f78b-4cba-8a18-b67b2d3c8e8d] RpcClient init label, labels = {module=naming, source=sdk}
15:46:30.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c94d8818-f78b-4cba-8a18-b67b2d3c8e8d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:46:30.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c94d8818-f78b-4cba-8a18-b67b2d3c8e8d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:46:30.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c94d8818-f78b-4cba-8a18-b67b2d3c8e8d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:46:30.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c94d8818-f78b-4cba-8a18-b67b2d3c8e8d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:46:31.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c94d8818-f78b-4cba-8a18-b67b2d3c8e8d] Success to connect to server [localhost:8848] on start up, connectionId = 1753688790912_127.0.0.1_8035
15:46:31.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c94d8818-f78b-4cba-8a18-b67b2d3c8e8d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:46:31.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c94d8818-f78b-4cba-8a18-b67b2d3c8e8d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001dce54f0668
15:46:31.033 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c94d8818-f78b-4cba-8a18-b67b2d3c8e8d] Notify connected event to listeners.
15:46:31.077 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:46:31.104 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:46:31.206 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.72 seconds (JVM running for 14.494)
15:46:31.218 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:46:31.221 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:46:31.222 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:46:31.650 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c94d8818-f78b-4cba-8a18-b67b2d3c8e8d] Receive server push request, request = NotifySubscriberRequest, requestId = 27
15:46:31.663 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c94d8818-f78b-4cba-8a18-b67b2d3c8e8d] Ack server push request, request = NotifySubscriberRequest, requestId = 27
15:46:43.031 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:46:44.137 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:46:44.137 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:28:45.783 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:28:45.795 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:28:46.158 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:28:46.160 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4d6e96c9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:28:46.160 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753688790912_127.0.0.1_8035
17:28:46.167 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1336af8f[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 1236]
17:28:46.370 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:28:46.371 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:28:46.382 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:28:46.383 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:28:46.384 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:28:46.385 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:28:53.248 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:28:53.991 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 79e2b056-b8a2-4d61-90d5-7b5928f04d50_config-0
17:28:54.081 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
17:28:54.122 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
17:28:54.132 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
17:28:54.141 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
17:28:54.153 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
17:28:54.167 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
17:28:54.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79e2b056-b8a2-4d61-90d5-7b5928f04d50_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:28:54.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79e2b056-b8a2-4d61-90d5-7b5928f04d50_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000297dc39dd00
17:28:54.172 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79e2b056-b8a2-4d61-90d5-7b5928f04d50_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000297dc39df20
17:28:54.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79e2b056-b8a2-4d61-90d5-7b5928f04d50_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:28:54.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79e2b056-b8a2-4d61-90d5-7b5928f04d50_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:28:54.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79e2b056-b8a2-4d61-90d5-7b5928f04d50_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:28:55.138 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79e2b056-b8a2-4d61-90d5-7b5928f04d50_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753694934924_127.0.0.1_6531
17:28:55.139 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79e2b056-b8a2-4d61-90d5-7b5928f04d50_config-0] Notify connected event to listeners.
17:28:55.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79e2b056-b8a2-4d61-90d5-7b5928f04d50_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:28:55.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79e2b056-b8a2-4d61-90d5-7b5928f04d50_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000297dc517cb0
17:28:55.305 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:28:59.732 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:28:59.733 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:28:59.733 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:28:59.917 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:29:00.615 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:29:00.617 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:29:00.618 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:29:08.705 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:29:11.556 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c495a9d4-664f-4cdb-b4ac-fd5737e08c67
17:29:11.556 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c495a9d4-664f-4cdb-b4ac-fd5737e08c67] RpcClient init label, labels = {module=naming, source=sdk}
17:29:11.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c495a9d4-664f-4cdb-b4ac-fd5737e08c67] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:29:11.558 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c495a9d4-664f-4cdb-b4ac-fd5737e08c67] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:29:11.558 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c495a9d4-664f-4cdb-b4ac-fd5737e08c67] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:29:11.559 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c495a9d4-664f-4cdb-b4ac-fd5737e08c67] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:29:11.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c495a9d4-664f-4cdb-b4ac-fd5737e08c67] Success to connect to server [localhost:8848] on start up, connectionId = 1753694951567_127.0.0.1_6642
17:29:11.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c495a9d4-664f-4cdb-b4ac-fd5737e08c67] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:29:11.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c495a9d4-664f-4cdb-b4ac-fd5737e08c67] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000297dc517cb0
17:29:11.688 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c495a9d4-664f-4cdb-b4ac-fd5737e08c67] Notify connected event to listeners.
17:29:11.773 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:29:11.816 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:29:11.969 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.353 seconds (JVM running for 20.314)
17:29:11.984 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:29:11.985 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:29:11.986 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:29:12.251 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c495a9d4-664f-4cdb-b4ac-fd5737e08c67] Receive server push request, request = NotifySubscriberRequest, requestId = 30
17:29:12.273 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c495a9d4-664f-4cdb-b4ac-fd5737e08c67] Ack server push request, request = NotifySubscriberRequest, requestId = 30
17:29:12.490 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:28:45.122 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:28:45.127 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:28:45.457 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:28:45.457 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7562fe3a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:28:45.457 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753694951567_127.0.0.1_6642
19:28:45.459 [nacos-grpc-client-executor-1442] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753694951567_127.0.0.1_6642]Ignore complete event,isRunning:false,isAbandon=false
19:28:45.463 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@25439f5e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1443]
19:28:45.655 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:28:45.660 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:28:45.677 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:28:45.677 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
