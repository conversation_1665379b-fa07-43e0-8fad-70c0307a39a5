09:15:25.813 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:15:27.420 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d3a2fda9-b143-447e-bdc1-7600a1973f3b_config-0
09:15:27.586 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 65 ms to scan 1 urls, producing 3 keys and 6 values 
09:15:27.682 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 4 keys and 9 values 
09:15:27.702 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 3 keys and 10 values 
09:15:27.724 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 5 values 
09:15:27.746 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 7 values 
09:15:27.762 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:15:27.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3a2fda9-b143-447e-bdc1-7600a1973f3b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:15:27.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3a2fda9-b143-447e-bdc1-7600a1973f3b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002b3d839cb90
09:15:27.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3a2fda9-b143-447e-bdc1-7600a1973f3b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002b3d839cdb0
09:15:27.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3a2fda9-b143-447e-bdc1-7600a1973f3b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:15:27.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3a2fda9-b143-447e-bdc1-7600a1973f3b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:15:27.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3a2fda9-b143-447e-bdc1-7600a1973f3b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:15:30.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3a2fda9-b143-447e-bdc1-7600a1973f3b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754356529905_127.0.0.1_8816
09:15:30.257 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3a2fda9-b143-447e-bdc1-7600a1973f3b_config-0] Notify connected event to listeners.
09:15:30.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3a2fda9-b143-447e-bdc1-7600a1973f3b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:15:30.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3a2fda9-b143-447e-bdc1-7600a1973f3b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002b3d8514d90
09:15:30.993 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:15:42.228 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:15:42.229 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:15:42.229 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:15:42.597 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:15:44.611 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:15:44.613 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:15:44.614 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:16:00.742 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:16:07.089 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b2a10ba2-b4d2-46c1-87d9-23b242e6c66b
09:16:07.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a10ba2-b4d2-46c1-87d9-23b242e6c66b] RpcClient init label, labels = {module=naming, source=sdk}
09:16:07.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a10ba2-b4d2-46c1-87d9-23b242e6c66b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:16:07.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a10ba2-b4d2-46c1-87d9-23b242e6c66b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:16:07.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a10ba2-b4d2-46c1-87d9-23b242e6c66b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:16:07.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a10ba2-b4d2-46c1-87d9-23b242e6c66b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:07.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a10ba2-b4d2-46c1-87d9-23b242e6c66b] Success to connect to server [localhost:8848] on start up, connectionId = 1754356567114_127.0.0.1_9217
09:16:07.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a10ba2-b4d2-46c1-87d9-23b242e6c66b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:16:07.253 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a10ba2-b4d2-46c1-87d9-23b242e6c66b] Notify connected event to listeners.
09:16:07.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a10ba2-b4d2-46c1-87d9-23b242e6c66b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002b3d8514d90
09:16:07.361 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:16:07.429 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:16:07.732 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 43.81 seconds (JVM running for 63.668)
09:16:07.767 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:16:07.768 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:16:07.770 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:16:07.908 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a10ba2-b4d2-46c1-87d9-23b242e6c66b] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:16:07.937 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a10ba2-b4d2-46c1-87d9-23b242e6c66b] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:25:12.462 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:25:14.902 [nacos-grpc-client-executor-119] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a10ba2-b4d2-46c1-87d9-23b242e6c66b] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:25:14.903 [nacos-grpc-client-executor-119] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2a10ba2-b4d2-46c1-87d9-23b242e6c66b] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:25:16.086 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:25:16.087 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:25:16.137 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:25:16.137 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
14:32:55.776 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:32:55.788 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:32:56.132 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:32:56.134 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@170e6752[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:32:56.136 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754356567114_127.0.0.1_9217
14:32:56.149 [nacos-grpc-client-executor-3790] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754356567114_127.0.0.1_9217]Ignore complete event,isRunning:false,isAbandon=false
14:32:56.164 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@113706b7[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3791]
14:32:56.590 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:32:56.605 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
14:32:56.619 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
14:32:56.620 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:32:56.622 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:32:56.622 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:32:56.623 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:32:56.623 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:33:25.052 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:33:25.685 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dad42b3c-df42-4c77-b032-b3e41b98d390_config-0
14:33:25.741 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 3 keys and 6 values 
14:33:25.770 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
14:33:25.776 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
14:33:25.784 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
14:33:25.792 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
14:33:25.800 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
14:33:25.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:33:25.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001b9493b71c0
14:33:25.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001b9493b73e0
14:33:25.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:33:25.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:33:25.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:33:26.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754375606310_127.0.0.1_11639
14:33:26.506 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] Notify connected event to listeners.
14:33:26.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:33:26.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001b9494f0668
14:33:26.632 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:33:29.876 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:33:29.877 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:33:29.877 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:33:30.034 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:33:30.717 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:33:30.719 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:33:30.719 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:33:39.569 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:33:42.096 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 996eaa59-758a-4630-a6e8-6f0aa8f3cbee
14:33:42.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [996eaa59-758a-4630-a6e8-6f0aa8f3cbee] RpcClient init label, labels = {module=naming, source=sdk}
14:33:42.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [996eaa59-758a-4630-a6e8-6f0aa8f3cbee] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:33:42.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [996eaa59-758a-4630-a6e8-6f0aa8f3cbee] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:33:42.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [996eaa59-758a-4630-a6e8-6f0aa8f3cbee] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:33:42.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [996eaa59-758a-4630-a6e8-6f0aa8f3cbee] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:33:42.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [996eaa59-758a-4630-a6e8-6f0aa8f3cbee] Success to connect to server [localhost:8848] on start up, connectionId = 1754375622107_127.0.0.1_11711
14:33:42.216 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [996eaa59-758a-4630-a6e8-6f0aa8f3cbee] Notify connected event to listeners.
14:33:42.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [996eaa59-758a-4630-a6e8-6f0aa8f3cbee] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:33:42.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [996eaa59-758a-4630-a6e8-6f0aa8f3cbee] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001b9494f0668
14:33:42.257 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:33:42.282 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:33:42.384 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 17.808 seconds (JVM running for 19.663)
14:33:42.396 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:33:42.397 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:33:42.397 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:33:42.786 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [996eaa59-758a-4630-a6e8-6f0aa8f3cbee] Receive server push request, request = NotifySubscriberRequest, requestId = 25
14:33:42.801 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [996eaa59-758a-4630-a6e8-6f0aa8f3cbee] Ack server push request, request = NotifySubscriberRequest, requestId = 25
14:33:58.189 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:33:59.200 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:33:59.201 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:35:04.249 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] Server healthy check fail, currentConnection = 1754375606310_127.0.0.1_11639
14:35:04.253 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:35:04.261 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [996eaa59-758a-4630-a6e8-6f0aa8f3cbee] Server check success, currentServer is localhost:8848 
14:35:04.372 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] Success to connect a server [localhost:8848], connectionId = 1754375704265_127.0.0.1_11969
14:35:04.372 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1754375606310_127.0.0.1_11639
14:35:04.372 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754375606310_127.0.0.1_11639
14:35:04.375 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754375606310_127.0.0.1_11639]Ignore complete event,isRunning:false,isAbandon=true
14:35:04.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] Notify disconnected event to listeners
14:35:04.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dad42b3c-df42-4c77-b032-b3e41b98d390_config-0] Notify connected event to listeners.
14:38:15.869 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:38:15.872 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:38:16.198 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:38:16.198 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5df0a346[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:38:16.198 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754375622107_127.0.0.1_11711
14:38:16.200 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7d3545ab[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 47]
14:38:16.200 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754375622107_127.0.0.1_11711]Ignore complete event,isRunning:false,isAbandon=false
14:38:16.349 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:38:16.352 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:38:16.359 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:38:16.360 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:38:16.361 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:38:16.361 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:38:25.940 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:38:27.161 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 89d35af2-6fdc-4f81-9c56-a05e354af55b_config-0
14:38:27.262 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
14:38:27.316 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
14:38:27.327 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:38:27.339 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:38:27.350 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
14:38:27.362 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:38:27.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89d35af2-6fdc-4f81-9c56-a05e354af55b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:38:27.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89d35af2-6fdc-4f81-9c56-a05e354af55b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001ea093b6d38
14:38:27.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89d35af2-6fdc-4f81-9c56-a05e354af55b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001ea093b6f58
14:38:27.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89d35af2-6fdc-4f81-9c56-a05e354af55b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:38:27.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89d35af2-6fdc-4f81-9c56-a05e354af55b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:38:27.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89d35af2-6fdc-4f81-9c56-a05e354af55b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:38:28.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89d35af2-6fdc-4f81-9c56-a05e354af55b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754375908174_127.0.0.1_12636
14:38:28.414 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89d35af2-6fdc-4f81-9c56-a05e354af55b_config-0] Notify connected event to listeners.
14:38:28.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89d35af2-6fdc-4f81-9c56-a05e354af55b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:38:28.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89d35af2-6fdc-4f81-9c56-a05e354af55b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ea094f0668
14:38:28.533 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:38:31.608 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:38:31.609 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:38:31.609 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:38:31.744 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:38:32.496 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:38:32.498 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:38:32.499 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:38:38.328 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:38:40.743 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1013f19c-b3c8-49e7-ae93-794bb329631e
14:38:40.743 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1013f19c-b3c8-49e7-ae93-794bb329631e] RpcClient init label, labels = {module=naming, source=sdk}
14:38:40.745 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1013f19c-b3c8-49e7-ae93-794bb329631e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:38:40.746 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1013f19c-b3c8-49e7-ae93-794bb329631e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:38:40.746 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1013f19c-b3c8-49e7-ae93-794bb329631e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:38:40.747 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1013f19c-b3c8-49e7-ae93-794bb329631e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:38:40.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1013f19c-b3c8-49e7-ae93-794bb329631e] Success to connect to server [localhost:8848] on start up, connectionId = 1754375920754_127.0.0.1_12683
14:38:40.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1013f19c-b3c8-49e7-ae93-794bb329631e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:38:40.878 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1013f19c-b3c8-49e7-ae93-794bb329631e] Notify connected event to listeners.
14:38:40.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1013f19c-b3c8-49e7-ae93-794bb329631e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ea094f0668
14:38:40.929 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:38:40.952 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:38:41.046 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.575 seconds (JVM running for 19.192)
14:38:41.060 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:38:41.060 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:38:41.060 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:38:41.495 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1013f19c-b3c8-49e7-ae93-794bb329631e] Receive server push request, request = NotifySubscriberRequest, requestId = 34
14:38:41.510 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1013f19c-b3c8-49e7-ae93-794bb329631e] Ack server push request, request = NotifySubscriberRequest, requestId = 34
14:39:00.874 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:39:01.995 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:39:01.995 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:44:16.221 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:44:16.223 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:44:16.539 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:44:16.539 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1cf626e4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:44:16.539 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754375920754_127.0.0.1_12683
14:44:16.539 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754375920754_127.0.0.1_12683]Ignore complete event,isRunning:false,isAbandon=false
14:44:16.544 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@429c66b9[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 34]
14:44:16.709 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:44:16.714 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:44:16.722 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:44:16.722 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:44:16.724 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:44:16.724 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:44:21.294 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:44:21.916 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 46f5851c-ab31-4b1d-b042-7a1bbf85a6a0_config-0
14:44:21.974 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 
14:44:22.004 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
14:44:22.011 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
14:44:22.017 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 3 ms to scan 1 urls, producing 1 keys and 5 values 
14:44:22.027 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
14:44:22.035 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
14:44:22.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46f5851c-ab31-4b1d-b042-7a1bbf85a6a0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:44:22.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46f5851c-ab31-4b1d-b042-7a1bbf85a6a0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000131c43ceaf8
14:44:22.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46f5851c-ab31-4b1d-b042-7a1bbf85a6a0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000131c43ced18
14:44:22.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46f5851c-ab31-4b1d-b042-7a1bbf85a6a0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:44:22.040 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46f5851c-ab31-4b1d-b042-7a1bbf85a6a0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:44:22.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46f5851c-ab31-4b1d-b042-7a1bbf85a6a0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:44:22.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46f5851c-ab31-4b1d-b042-7a1bbf85a6a0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754376262583_127.0.0.1_13810
14:44:22.767 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46f5851c-ab31-4b1d-b042-7a1bbf85a6a0_config-0] Notify connected event to listeners.
14:44:22.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46f5851c-ab31-4b1d-b042-7a1bbf85a6a0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:44:22.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46f5851c-ab31-4b1d-b042-7a1bbf85a6a0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000131c4508668
14:44:22.852 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:44:25.562 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:44:25.563 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:44:25.563 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:44:25.704 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:44:26.208 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:44:26.209 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:44:26.209 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:44:32.559 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:44:35.843 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0cd2be45-af94-47e6-8a04-2b66452efab3
14:44:35.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cd2be45-af94-47e6-8a04-2b66452efab3] RpcClient init label, labels = {module=naming, source=sdk}
14:44:35.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cd2be45-af94-47e6-8a04-2b66452efab3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:44:35.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cd2be45-af94-47e6-8a04-2b66452efab3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:44:35.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cd2be45-af94-47e6-8a04-2b66452efab3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:44:35.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cd2be45-af94-47e6-8a04-2b66452efab3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:44:35.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cd2be45-af94-47e6-8a04-2b66452efab3] Success to connect to server [localhost:8848] on start up, connectionId = 1754376275855_127.0.0.1_13855
14:44:35.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cd2be45-af94-47e6-8a04-2b66452efab3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:44:35.975 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cd2be45-af94-47e6-8a04-2b66452efab3] Notify connected event to listeners.
14:44:35.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cd2be45-af94-47e6-8a04-2b66452efab3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000131c4508668
14:44:36.025 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:44:36.058 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:44:36.195 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.438 seconds (JVM running for 16.376)
14:44:36.208 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:44:36.209 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:44:36.209 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:44:36.488 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:44:36.554 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cd2be45-af94-47e6-8a04-2b66452efab3] Receive server push request, request = NotifySubscriberRequest, requestId = 43
14:44:36.572 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cd2be45-af94-47e6-8a04-2b66452efab3] Ack server push request, request = NotifySubscriberRequest, requestId = 43
14:44:49.706 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:44:49.706 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:04:59.432 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:04:59.434 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:04:59.770 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:04:59.770 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7f2593f1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:04:59.771 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754376275855_127.0.0.1_13855
15:04:59.773 [nacos-grpc-client-executor-245] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754376275855_127.0.0.1_13855]Ignore complete event,isRunning:false,isAbandon=false
15:04:59.777 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@66bc039c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 246]
15:04:59.946 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:04:59.950 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:04:59.958 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:04:59.959 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:04:59.961 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:04:59.961 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:05:05.286 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:05:05.833 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 55e6888e-bb21-4305-a741-cc11742ccb4e_config-0
15:05:05.888 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 
15:05:05.919 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
15:05:05.926 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
15:05:05.932 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
15:05:05.939 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
15:05:05.947 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
15:05:05.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55e6888e-bb21-4305-a741-cc11742ccb4e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:05:05.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55e6888e-bb21-4305-a741-cc11742ccb4e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002520a39dd00
15:05:05.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55e6888e-bb21-4305-a741-cc11742ccb4e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002520a39df20
15:05:05.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55e6888e-bb21-4305-a741-cc11742ccb4e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:05:05.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55e6888e-bb21-4305-a741-cc11742ccb4e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:05:05.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55e6888e-bb21-4305-a741-cc11742ccb4e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:05:06.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55e6888e-bb21-4305-a741-cc11742ccb4e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754377506451_127.0.0.1_3542
15:05:06.632 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55e6888e-bb21-4305-a741-cc11742ccb4e_config-0] Notify connected event to listeners.
15:05:06.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55e6888e-bb21-4305-a741-cc11742ccb4e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:05:06.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55e6888e-bb21-4305-a741-cc11742ccb4e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002520a517b88
15:05:06.727 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:05:09.180 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:05:09.181 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:05:09.181 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:05:09.304 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:05:10.363 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:05:10.364 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:05:10.365 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:05:15.902 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:05:18.097 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5b377536-d506-470e-863d-b3d6a30df298
15:05:18.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b377536-d506-470e-863d-b3d6a30df298] RpcClient init label, labels = {module=naming, source=sdk}
15:05:18.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b377536-d506-470e-863d-b3d6a30df298] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:05:18.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b377536-d506-470e-863d-b3d6a30df298] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:05:18.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b377536-d506-470e-863d-b3d6a30df298] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:05:18.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b377536-d506-470e-863d-b3d6a30df298] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:05:18.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b377536-d506-470e-863d-b3d6a30df298] Success to connect to server [localhost:8848] on start up, connectionId = 1754377518106_127.0.0.1_3578
15:05:18.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b377536-d506-470e-863d-b3d6a30df298] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:05:18.227 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b377536-d506-470e-863d-b3d6a30df298] Notify connected event to listeners.
15:05:18.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b377536-d506-470e-863d-b3d6a30df298] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002520a517b88
15:05:18.265 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:05:18.288 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:05:18.376 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.552 seconds (JVM running for 14.368)
15:05:18.390 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:05:18.390 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:05:18.390 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:05:18.631 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:05:18.806 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b377536-d506-470e-863d-b3d6a30df298] Receive server push request, request = NotifySubscriberRequest, requestId = 52
15:05:18.820 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b377536-d506-470e-863d-b3d6a30df298] Ack server push request, request = NotifySubscriberRequest, requestId = 52
15:05:25.883 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:05:25.883 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:21:27.885 [nacos-grpc-client-executor-214] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55e6888e-bb21-4305-a741-cc11742ccb4e_config-0] Receive server push request, request = ConfigChangeNotifyRequest, requestId = 53
15:21:27.885 [nacos-grpc-client-executor-214] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55e6888e-bb21-4305-a741-cc11742ccb4e_config-0] Ack server push request, request = ConfigChangeNotifyRequest, requestId = 53
16:16:58.668 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:16:58.668 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:16:58.999 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:16:59.000 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5d336c0f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:16:59.000 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754377518106_127.0.0.1_3578
16:16:59.004 [nacos-grpc-client-executor-879] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754377518106_127.0.0.1_3578]Ignore complete event,isRunning:false,isAbandon=false
16:16:59.007 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3489f727[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 880]
16:16:59.227 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:16:59.231 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:16:59.241 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:16:59.241 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:16:59.243 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:16:59.243 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:17:10.423 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:17:11.698 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ed67367d-19c8-441f-bdfc-609f6191e3fb_config-0
16:17:11.798 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
16:17:11.860 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
16:17:11.868 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
16:17:11.882 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
16:17:11.900 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
16:17:11.907 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
16:17:11.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed67367d-19c8-441f-bdfc-609f6191e3fb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:17:11.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed67367d-19c8-441f-bdfc-609f6191e3fb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001c0de3b6af8
16:17:11.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed67367d-19c8-441f-bdfc-609f6191e3fb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001c0de3b6d18
16:17:11.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed67367d-19c8-441f-bdfc-609f6191e3fb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:17:11.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed67367d-19c8-441f-bdfc-609f6191e3fb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:17:11.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed67367d-19c8-441f-bdfc-609f6191e3fb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:17:13.087 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed67367d-19c8-441f-bdfc-609f6191e3fb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754381832834_127.0.0.1_4124
16:17:13.087 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed67367d-19c8-441f-bdfc-609f6191e3fb_config-0] Notify connected event to listeners.
16:17:13.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed67367d-19c8-441f-bdfc-609f6191e3fb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:17:13.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed67367d-19c8-441f-bdfc-609f6191e3fb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c0de4f0ad8
16:17:13.215 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:17:16.807 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:17:16.807 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:17:16.807 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:17:16.959 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:17:17.641 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:17:17.643 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:17:17.643 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:17:28.515 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:17:34.034 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of adc47550-f4be-4b20-b5fb-2adcd732414a
16:17:34.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adc47550-f4be-4b20-b5fb-2adcd732414a] RpcClient init label, labels = {module=naming, source=sdk}
16:17:34.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adc47550-f4be-4b20-b5fb-2adcd732414a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:17:34.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adc47550-f4be-4b20-b5fb-2adcd732414a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:17:34.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adc47550-f4be-4b20-b5fb-2adcd732414a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:17:34.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adc47550-f4be-4b20-b5fb-2adcd732414a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:17:34.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adc47550-f4be-4b20-b5fb-2adcd732414a] Success to connect to server [localhost:8848] on start up, connectionId = 1754381854052_127.0.0.1_4225
16:17:34.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adc47550-f4be-4b20-b5fb-2adcd732414a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:17:34.165 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adc47550-f4be-4b20-b5fb-2adcd732414a] Notify connected event to listeners.
16:17:34.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adc47550-f4be-4b20-b5fb-2adcd732414a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c0de4f0ad8
16:17:34.232 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:17:34.268 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:17:34.493 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.264 seconds (JVM running for 27.438)
16:17:34.515 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:17:34.515 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:17:34.515 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:17:34.732 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adc47550-f4be-4b20-b5fb-2adcd732414a] Receive server push request, request = NotifySubscriberRequest, requestId = 59
16:17:34.753 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adc47550-f4be-4b20-b5fb-2adcd732414a] Ack server push request, request = NotifySubscriberRequest, requestId = 59
16:17:56.101 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:17:57.282 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:17:57.282 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:29:12.603 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:29:12.790 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:29:13.113 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:29:13.113 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@53f587a2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:29:13.113 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754381854052_127.0.0.1_4225
17:29:13.115 [nacos-grpc-client-executor-869] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754381854052_127.0.0.1_4225]Ignore complete event,isRunning:false,isAbandon=false
17:29:13.118 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3e99f440[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 870]
17:29:13.279 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:29:13.293 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:29:13.304 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:29:13.304 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:29:13.307 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:29:13.307 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:29:19.937 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:29:20.587 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3f2c6552-559e-4328-9132-d6944f018911_config-0
17:29:20.649 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 3 keys and 6 values 
17:29:20.685 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
17:29:20.694 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
17:29:20.702 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
17:29:20.711 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
17:29:20.721 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
17:29:20.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f2c6552-559e-4328-9132-d6944f018911_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:29:20.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f2c6552-559e-4328-9132-d6944f018911_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000214b039f1c0
17:29:20.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f2c6552-559e-4328-9132-d6944f018911_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000214b039f3e0
17:29:20.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f2c6552-559e-4328-9132-d6944f018911_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:29:20.725 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f2c6552-559e-4328-9132-d6944f018911_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:29:20.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f2c6552-559e-4328-9132-d6944f018911_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:29:21.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f2c6552-559e-4328-9132-d6944f018911_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754386161314_127.0.0.1_4801
17:29:21.500 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f2c6552-559e-4328-9132-d6944f018911_config-0] Notify connected event to listeners.
17:29:21.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f2c6552-559e-4328-9132-d6944f018911_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:29:21.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f2c6552-559e-4328-9132-d6944f018911_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000214b0518fb0
17:29:21.589 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:29:24.308 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:29:24.308 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:29:24.308 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:29:24.450 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:29:25.015 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:29:25.015 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:29:25.015 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:29:35.749 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:29:38.534 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d201cbf8-ae1b-48f8-913d-3d2153999ed6
17:29:38.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d201cbf8-ae1b-48f8-913d-3d2153999ed6] RpcClient init label, labels = {module=naming, source=sdk}
17:29:38.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d201cbf8-ae1b-48f8-913d-3d2153999ed6] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:29:38.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d201cbf8-ae1b-48f8-913d-3d2153999ed6] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:29:38.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d201cbf8-ae1b-48f8-913d-3d2153999ed6] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:29:38.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d201cbf8-ae1b-48f8-913d-3d2153999ed6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:29:38.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d201cbf8-ae1b-48f8-913d-3d2153999ed6] Success to connect to server [localhost:8848] on start up, connectionId = 1754386178543_127.0.0.1_4857
17:29:38.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d201cbf8-ae1b-48f8-913d-3d2153999ed6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:29:38.660 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d201cbf8-ae1b-48f8-913d-3d2153999ed6] Notify connected event to listeners.
17:29:38.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d201cbf8-ae1b-48f8-913d-3d2153999ed6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000214b0518fb0
17:29:38.710 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:29:38.728 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:29:38.845 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.567 seconds (JVM running for 20.847)
17:29:38.848 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:29:38.860 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:29:38.861 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:29:39.134 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:29:39.266 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d201cbf8-ae1b-48f8-913d-3d2153999ed6] Receive server push request, request = NotifySubscriberRequest, requestId = 70
17:29:39.289 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d201cbf8-ae1b-48f8-913d-3d2153999ed6] Ack server push request, request = NotifySubscriberRequest, requestId = 70
17:30:03.027 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:30:03.028 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:34:12.459 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:34:12.460 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:34:12.796 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:34:12.796 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2bdad2b6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:34:12.797 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754386178543_127.0.0.1_4857
17:34:12.799 [nacos-grpc-client-executor-67] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754386178543_127.0.0.1_4857]Ignore complete event,isRunning:false,isAbandon=false
17:34:12.801 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@78c1f3fc[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 68]
17:34:12.965 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:34:12.967 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:34:12.968 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:34:12.968 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:34:12.968 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:34:12.968 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:34:18.895 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:34:19.455 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 78f654ed-7878-4b2e-922c-db8a6a8de3d1_config-0
17:34:19.504 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
17:34:19.546 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
17:34:19.558 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
17:34:19.571 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
17:34:19.584 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
17:34:19.595 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
17:34:19.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f654ed-7878-4b2e-922c-db8a6a8de3d1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:34:19.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f654ed-7878-4b2e-922c-db8a6a8de3d1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000024c813ceaf8
17:34:19.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f654ed-7878-4b2e-922c-db8a6a8de3d1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000024c813ced18
17:34:19.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f654ed-7878-4b2e-922c-db8a6a8de3d1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:34:19.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f654ed-7878-4b2e-922c-db8a6a8de3d1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:34:19.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f654ed-7878-4b2e-922c-db8a6a8de3d1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:34:20.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f654ed-7878-4b2e-922c-db8a6a8de3d1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754386460167_127.0.0.1_5821
17:34:20.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f654ed-7878-4b2e-922c-db8a6a8de3d1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:34:20.351 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f654ed-7878-4b2e-922c-db8a6a8de3d1_config-0] Notify connected event to listeners.
17:34:20.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f654ed-7878-4b2e-922c-db8a6a8de3d1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024c81508ad8
17:34:20.441 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:34:23.110 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:34:23.111 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:34:23.111 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:34:23.241 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:34:24.086 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:34:24.088 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:34:24.088 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:34:31.230 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:34:33.499 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3def6563-35e5-4050-96bb-dd200fe2705d
17:34:33.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3def6563-35e5-4050-96bb-dd200fe2705d] RpcClient init label, labels = {module=naming, source=sdk}
17:34:33.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3def6563-35e5-4050-96bb-dd200fe2705d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:34:33.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3def6563-35e5-4050-96bb-dd200fe2705d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:34:33.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3def6563-35e5-4050-96bb-dd200fe2705d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:34:33.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3def6563-35e5-4050-96bb-dd200fe2705d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:34:33.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3def6563-35e5-4050-96bb-dd200fe2705d] Success to connect to server [localhost:8848] on start up, connectionId = 1754386473508_127.0.0.1_5886
17:34:33.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3def6563-35e5-4050-96bb-dd200fe2705d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:34:33.633 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3def6563-35e5-4050-96bb-dd200fe2705d] Notify connected event to listeners.
17:34:33.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3def6563-35e5-4050-96bb-dd200fe2705d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024c81508ad8
17:34:33.676 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:34:33.701 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:34:33.787 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.42 seconds (JVM running for 16.338)
17:34:33.800 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:34:33.800 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:34:33.801 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:34:34.210 [RMI TCP Connection(13)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:34:34.215 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3def6563-35e5-4050-96bb-dd200fe2705d] Receive server push request, request = NotifySubscriberRequest, requestId = 77
17:34:34.231 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3def6563-35e5-4050-96bb-dd200fe2705d] Ack server push request, request = NotifySubscriberRequest, requestId = 77
17:34:45.126 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:34:45.126 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:36:16.843 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:36:16.855 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:36:17.160 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:36:17.160 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6030c930[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:36:17.160 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754386473508_127.0.0.1_5886
17:36:17.166 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754386473508_127.0.0.1_5886]Ignore complete event,isRunning:false,isAbandon=false
17:36:17.167 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6ff3b364[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 34]
17:36:17.333 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:36:17.333 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:36:17.333 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:36:17.333 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:36:17.343 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:36:17.343 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:36:23.003 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:36:23.984 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 730b9eaa-5bc7-4b1d-81da-ca3a87d3189a_config-0
17:36:24.088 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
17:36:24.132 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
17:36:24.142 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
17:36:24.153 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
17:36:24.164 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
17:36:24.178 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
17:36:24.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [730b9eaa-5bc7-4b1d-81da-ca3a87d3189a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:36:24.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [730b9eaa-5bc7-4b1d-81da-ca3a87d3189a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002e9cc39f1c0
17:36:24.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [730b9eaa-5bc7-4b1d-81da-ca3a87d3189a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002e9cc39f3e0
17:36:24.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [730b9eaa-5bc7-4b1d-81da-ca3a87d3189a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:36:24.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [730b9eaa-5bc7-4b1d-81da-ca3a87d3189a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:36:24.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [730b9eaa-5bc7-4b1d-81da-ca3a87d3189a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:36:25.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [730b9eaa-5bc7-4b1d-81da-ca3a87d3189a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754386585036_127.0.0.1_6273
17:36:25.252 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [730b9eaa-5bc7-4b1d-81da-ca3a87d3189a_config-0] Notify connected event to listeners.
17:36:25.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [730b9eaa-5bc7-4b1d-81da-ca3a87d3189a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:36:25.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [730b9eaa-5bc7-4b1d-81da-ca3a87d3189a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002e9cc518fb0
17:36:25.366 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:36:28.684 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:36:28.684 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:36:28.684 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:36:29.067 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:36:30.395 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:36:30.398 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:36:30.398 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:36:39.209 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:36:44.093 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e9344972-8778-4c16-89f4-f208cbd6ecb9
17:36:44.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9344972-8778-4c16-89f4-f208cbd6ecb9] RpcClient init label, labels = {module=naming, source=sdk}
17:36:44.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9344972-8778-4c16-89f4-f208cbd6ecb9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:36:44.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9344972-8778-4c16-89f4-f208cbd6ecb9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:36:44.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9344972-8778-4c16-89f4-f208cbd6ecb9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:36:44.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9344972-8778-4c16-89f4-f208cbd6ecb9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:36:44.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9344972-8778-4c16-89f4-f208cbd6ecb9] Success to connect to server [localhost:8848] on start up, connectionId = 1754386604105_127.0.0.1_6369
17:36:44.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9344972-8778-4c16-89f4-f208cbd6ecb9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:36:44.217 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9344972-8778-4c16-89f4-f208cbd6ecb9] Notify connected event to listeners.
17:36:44.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9344972-8778-4c16-89f4-f208cbd6ecb9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002e9cc518fb0
17:36:44.272 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:36:44.299 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:36:44.417 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 22.236 seconds (JVM running for 23.339)
17:36:44.430 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:36:44.431 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:36:44.431 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:36:44.802 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9344972-8778-4c16-89f4-f208cbd6ecb9] Receive server push request, request = NotifySubscriberRequest, requestId = 87
17:36:44.819 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9344972-8778-4c16-89f4-f208cbd6ecb9] Ack server push request, request = NotifySubscriberRequest, requestId = 87
17:36:44.851 [RMI TCP Connection(13)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:36:50.883 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:36:50.883 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:42:47.852 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [730b9eaa-5bc7-4b1d-81da-ca3a87d3189a_config-0] Server check success, currentServer is localhost:8848 
17:44:20.359 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:44:20.365 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:44:20.678 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:44:20.683 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7163a70c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:44:20.683 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754386604105_127.0.0.1_6369
17:44:20.691 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3259f035[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 59]
17:44:20.924 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:44:20.927 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:44:20.935 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:44:20.935 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:44:20.943 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:44:20.943 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:44:31.217 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:44:32.180 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 53900c9a-590d-4756-a7a0-d191f1237ab8_config-0
17:44:32.260 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
17:44:32.300 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
17:44:32.309 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
17:44:32.319 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
17:44:32.333 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
17:44:32.347 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
17:44:32.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53900c9a-590d-4756-a7a0-d191f1237ab8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:44:32.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53900c9a-590d-4756-a7a0-d191f1237ab8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f13739dd70
17:44:32.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53900c9a-590d-4756-a7a0-d191f1237ab8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001f13739df90
17:44:32.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53900c9a-590d-4756-a7a0-d191f1237ab8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:44:32.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53900c9a-590d-4756-a7a0-d191f1237ab8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:44:32.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53900c9a-590d-4756-a7a0-d191f1237ab8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:44:33.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53900c9a-590d-4756-a7a0-d191f1237ab8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754387073060_127.0.0.1_8164
17:44:33.266 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53900c9a-590d-4756-a7a0-d191f1237ab8_config-0] Notify connected event to listeners.
17:44:33.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53900c9a-590d-4756-a7a0-d191f1237ab8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:44:33.268 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53900c9a-590d-4756-a7a0-d191f1237ab8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f137517cb0
17:44:33.369 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:44:36.678 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:44:36.679 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:44:36.679 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:44:36.833 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:44:37.499 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:44:37.500 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:44:37.501 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:44:47.820 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:44:50.692 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2fdb3520-96c0-4651-8ae0-3ac7327dd552
17:44:50.693 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fdb3520-96c0-4651-8ae0-3ac7327dd552] RpcClient init label, labels = {module=naming, source=sdk}
17:44:50.694 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fdb3520-96c0-4651-8ae0-3ac7327dd552] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:44:50.694 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fdb3520-96c0-4651-8ae0-3ac7327dd552] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:44:50.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fdb3520-96c0-4651-8ae0-3ac7327dd552] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:44:50.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fdb3520-96c0-4651-8ae0-3ac7327dd552] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:44:50.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fdb3520-96c0-4651-8ae0-3ac7327dd552] Success to connect to server [localhost:8848] on start up, connectionId = 1754387090704_127.0.0.1_8239
17:44:50.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fdb3520-96c0-4651-8ae0-3ac7327dd552] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:44:50.826 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fdb3520-96c0-4651-8ae0-3ac7327dd552] Notify connected event to listeners.
17:44:50.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fdb3520-96c0-4651-8ae0-3ac7327dd552] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f137517cb0
17:44:50.871 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:44:50.897 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:44:50.998 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.63 seconds (JVM running for 22.248)
17:44:51.012 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:44:51.013 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:44:51.013 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:44:51.384 [RMI TCP Connection(10)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:44:51.432 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fdb3520-96c0-4651-8ae0-3ac7327dd552] Receive server push request, request = NotifySubscriberRequest, requestId = 98
17:44:51.448 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fdb3520-96c0-4651-8ae0-3ac7327dd552] Ack server push request, request = NotifySubscriberRequest, requestId = 98
17:44:56.726 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:44:56.726 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:45:52.726 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:45:52.726 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:45:53.059 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:45:53.059 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4b81e169[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:45:53.059 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754387090704_127.0.0.1_8239
17:45:53.062 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754387090704_127.0.0.1_8239]Ignore complete event,isRunning:false,isAbandon=false
17:45:53.064 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1dcb6c3a[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 24]
17:45:53.209 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:45:53.209 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:45:53.218 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:45:53.218 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:45:53.218 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:45:53.218 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:45:58.607 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:45:59.179 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7872978c-86be-4e30-8098-eb46b2015012_config-0
17:45:59.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 6 values 
17:45:59.266 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
17:45:59.273 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
17:45:59.280 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
17:45:59.287 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
17:45:59.296 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
17:45:59.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7872978c-86be-4e30-8098-eb46b2015012_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:45:59.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7872978c-86be-4e30-8098-eb46b2015012_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001dd0339ed38
17:45:59.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7872978c-86be-4e30-8098-eb46b2015012_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001dd0339ef58
17:45:59.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7872978c-86be-4e30-8098-eb46b2015012_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:45:59.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7872978c-86be-4e30-8098-eb46b2015012_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:45:59.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7872978c-86be-4e30-8098-eb46b2015012_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:46:00.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7872978c-86be-4e30-8098-eb46b2015012_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754387159827_127.0.0.1_8486
17:46:00.008 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7872978c-86be-4e30-8098-eb46b2015012_config-0] Notify connected event to listeners.
17:46:00.009 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7872978c-86be-4e30-8098-eb46b2015012_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:46:00.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7872978c-86be-4e30-8098-eb46b2015012_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001dd03518ad8
17:46:00.102 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:46:02.707 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:46:02.708 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:46:02.708 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:46:02.820 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:46:03.360 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:46:03.360 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:46:03.361 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:46:08.546 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:46:11.056 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6669fbd2-7647-468e-9e4d-48dd7218aad2
17:46:11.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6669fbd2-7647-468e-9e4d-48dd7218aad2] RpcClient init label, labels = {module=naming, source=sdk}
17:46:11.059 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6669fbd2-7647-468e-9e4d-48dd7218aad2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:46:11.059 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6669fbd2-7647-468e-9e4d-48dd7218aad2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:46:11.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6669fbd2-7647-468e-9e4d-48dd7218aad2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:46:11.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6669fbd2-7647-468e-9e4d-48dd7218aad2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:46:11.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6669fbd2-7647-468e-9e4d-48dd7218aad2] Success to connect to server [localhost:8848] on start up, connectionId = 1754387171068_127.0.0.1_8560
17:46:11.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6669fbd2-7647-468e-9e4d-48dd7218aad2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:46:11.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6669fbd2-7647-468e-9e4d-48dd7218aad2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001dd03518ad8
17:46:11.182 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6669fbd2-7647-468e-9e4d-48dd7218aad2] Notify connected event to listeners.
17:46:11.230 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:46:11.255 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:46:11.374 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.226 seconds (JVM running for 14.129)
17:46:11.387 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:46:11.388 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:46:11.388 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:46:11.642 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:46:11.726 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6669fbd2-7647-468e-9e4d-48dd7218aad2] Receive server push request, request = NotifySubscriberRequest, requestId = 104
17:46:11.746 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6669fbd2-7647-468e-9e4d-48dd7218aad2] Ack server push request, request = NotifySubscriberRequest, requestId = 104
17:46:16.310 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:46:16.310 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:48:46.575 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:48:46.576 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:48:46.909 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:48:46.909 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@********[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:48:46.909 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754387171068_127.0.0.1_8560
17:48:46.911 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754387171068_127.0.0.1_8560]Ignore complete event,isRunning:false,isAbandon=false
17:48:46.913 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1148675d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 45]
17:48:47.063 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:48:47.067 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:48:47.072 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:48:47.072 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:48:47.072 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:48:47.072 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:48:52.252 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:48:52.859 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 414f13e3-8f5d-44ed-8ed7-c23e3ebb7cc4_config-0
17:48:52.920 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 
17:48:52.953 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
17:48:52.963 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
17:48:52.972 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
17:48:52.979 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
17:48:52.989 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
17:48:52.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [414f13e3-8f5d-44ed-8ed7-c23e3ebb7cc4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:48:52.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [414f13e3-8f5d-44ed-8ed7-c23e3ebb7cc4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000227a03be8d8
17:48:52.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [414f13e3-8f5d-44ed-8ed7-c23e3ebb7cc4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000227a03beaf8
17:48:52.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [414f13e3-8f5d-44ed-8ed7-c23e3ebb7cc4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:48:52.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [414f13e3-8f5d-44ed-8ed7-c23e3ebb7cc4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:48:52.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [414f13e3-8f5d-44ed-8ed7-c23e3ebb7cc4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:48:53.746 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [414f13e3-8f5d-44ed-8ed7-c23e3ebb7cc4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754387333561_127.0.0.1_9340
17:48:53.747 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [414f13e3-8f5d-44ed-8ed7-c23e3ebb7cc4_config-0] Notify connected event to listeners.
17:48:53.747 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [414f13e3-8f5d-44ed-8ed7-c23e3ebb7cc4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:48:53.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [414f13e3-8f5d-44ed-8ed7-c23e3ebb7cc4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000227a04f8668
17:48:53.830 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:48:56.486 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:48:56.487 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:48:56.487 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:48:56.651 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:48:57.426 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:48:57.428 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:48:57.428 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:49:05.020 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:49:08.130 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2a169828-8472-4df1-ac8f-ce0365140eee
17:49:08.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a169828-8472-4df1-ac8f-ce0365140eee] RpcClient init label, labels = {module=naming, source=sdk}
17:49:08.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a169828-8472-4df1-ac8f-ce0365140eee] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:49:08.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a169828-8472-4df1-ac8f-ce0365140eee] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:49:08.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a169828-8472-4df1-ac8f-ce0365140eee] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:49:08.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a169828-8472-4df1-ac8f-ce0365140eee] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:49:08.268 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a169828-8472-4df1-ac8f-ce0365140eee] Success to connect to server [localhost:8848] on start up, connectionId = 1754387348143_127.0.0.1_9394
17:49:08.268 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a169828-8472-4df1-ac8f-ce0365140eee] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:49:08.269 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a169828-8472-4df1-ac8f-ce0365140eee] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000227a04f8668
17:49:08.269 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a169828-8472-4df1-ac8f-ce0365140eee] Notify connected event to listeners.
17:49:08.313 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:49:08.342 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:49:08.459 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.738 seconds (JVM running for 17.602)
17:49:08.474 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:49:08.474 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:49:08.474 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:49:08.588 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:49:08.885 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a169828-8472-4df1-ac8f-ce0365140eee] Receive server push request, request = NotifySubscriberRequest, requestId = 116
17:49:08.905 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a169828-8472-4df1-ac8f-ce0365140eee] Ack server push request, request = NotifySubscriberRequest, requestId = 116
17:49:14.342 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:49:14.342 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:24:48.818 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:24:48.828 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:24:49.159 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:24:49.159 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@34cc78ee[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:24:49.159 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754387348143_127.0.0.1_9394
20:24:49.162 [nacos-grpc-client-executor-1865] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754387348143_127.0.0.1_9394]Ignore complete event,isRunning:false,isAbandon=false
20:24:49.169 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@415a8b9e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1866]
20:24:49.335 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:24:49.340 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:24:49.349 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:24:49.349 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:24:49.352 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:24:49.352 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
