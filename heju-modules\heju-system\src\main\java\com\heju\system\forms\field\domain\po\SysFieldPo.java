package com.heju.system.forms.field.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.annotation.Excel;
import com.heju.common.core.web.tenant.base.TBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 字段管理 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_field")
public class SysFieldPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** API名称 */
    @Excel(name = "API名称")
    protected String apiName;

    /** 字段系统类型（1-默认字段；2=自定义字段）*/
    @Excel(name = "字段系统类型（1-默认字段；2=自定义字段）")
    protected Integer fieldSystemType;

    /** 字段类型 */
    @Excel(name = "字段类型")
    protected String fieldType;

    /** 是否可编辑 */
    @Excel(name = "是否可编辑（1-是；0-否）")
    protected Integer isEdit;

    /** 显示长度（1-短；2-中，3-长） */
    @Excel(name = "显示长度", readConverterExp = "1=-短；2-中，3-长")
    protected Integer showLength;

    /** 字段长度 */
    @Excel(name = "字段长度")
    protected Integer fieldLength;

    /** 小数位数（仅货币类型需填） */
    @Excel(name = "小数位数", readConverterExp = "仅=货币类型需填")
    protected Integer decimalLength;

    /** 小数位数（仅货币类型需填） */
    @Excel(name = "选项类型", readConverterExp = "单选/多选类型需填(1-自定义选项；2-通用选项；3-业务选项)")
    protected Integer optionType;

    /** 选项id（仅单选/多选类型需填） */
    @Excel(name = "选项id", readConverterExp = "仅=单选/多选类型需填")
    protected Long optionId;

    /** 日期类型（1-日期，2-时间）（仅日期类型需填） */
    @Excel(name = "日期类型", readConverterExp = "1=-日期，2-时间")
    protected Integer dateType;

    /** 文件/图片数量（1-一；2-多）（仅文件/图片类型需填） */
    @Excel(name = "文件/图片数量", readConverterExp = "1=-一；2-多")
    protected Integer fileType;

    /** 引用表id（仅引用类型需填） */
    @Excel(name = "引用表id", readConverterExp = "仅=引用类型需填")
    protected Long quoteSheetId;

    /** 引用字段id（仅引用类型需填） */
    @Excel(name = "引用字段id", readConverterExp = "仅=引用类型需填")
    protected Long quoteSheetFieldId;

    /** 引用规则 */
    @Excel(name = "被引用表关联字段", readConverterExp = "仅=引用类型需填")
    protected Long referencedFieldId;

    /** 引用规则 */
    @Excel(name = "引用表关联字段", readConverterExp = "仅=引用类型需填")
    protected Long referencingFieldId;

    /** 关联表id（仅关联类型需填） */
    @Excel(name = "关联表id", readConverterExp = "仅=关联类型需填")
    protected Long relationSheetId;

    /** 当前表字段id（仅关联类型需填） */
    @Excel(name = "当前表字段id", readConverterExp = "仅=关联类型需填")
    protected Long currentFieldId;

    /** 关联字段id（仅关联类型需填） */
    @Excel(name = "关联字段id", readConverterExp = "仅=关联类型需填")
    protected Long relationSheetFieldId;

    /** 关联类型（仅关联类型需填） */
    @Excel(name = "关联类型", readConverterExp = "0一 1多")
    protected Integer relationType;

    /** 级联id（仅级联类型需填） */
    @Excel(name = "级联id", readConverterExp = "仅=级联类型需填")
    protected Long cascadeId;

    /** 所属表id */
    @Excel(name = "所属表id")
    protected Long sheetId;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;

    /**
     * 是否可申请修改 0-是,1-否
     */
    @Excel(name = "是否可申请修改")
    protected Integer applyUpt;

    /**
     * 是否可修改 0-是,1-否
     */
    @Excel(name = "是否可修改")
    protected Integer isUpdate;
}
