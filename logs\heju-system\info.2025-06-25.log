09:18:30.602 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:18:31.878 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 571b2783-8581-40d7-8d64-8b63fe42accc_config-0
09:18:32.006 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 61 ms to scan 1 urls, producing 3 keys and 6 values 
09:18:32.069 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 4 keys and 9 values 
09:18:32.083 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:18:32.098 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:18:32.113 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:18:32.124 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:18:32.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [571b2783-8581-40d7-8d64-8b63fe42accc_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:32.129 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [571b2783-8581-40d7-8d64-8b63fe42accc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000197e73b7b00
09:18:32.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [571b2783-8581-40d7-8d64-8b63fe42accc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000197e73b7d20
09:18:32.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [571b2783-8581-40d7-8d64-8b63fe42accc_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:32.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [571b2783-8581-40d7-8d64-8b63fe42accc_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:32.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [571b2783-8581-40d7-8d64-8b63fe42accc_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:33.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [571b2783-8581-40d7-8d64-8b63fe42accc_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750814313239_127.0.0.1_5104
09:18:33.489 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [571b2783-8581-40d7-8d64-8b63fe42accc_config-0] Notify connected event to listeners.
09:18:33.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [571b2783-8581-40d7-8d64-8b63fe42accc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:33.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [571b2783-8581-40d7-8d64-8b63fe42accc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000197e74f1450
09:18:33.680 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:18:37.480 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:18:37.481 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:18:37.481 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:18:37.648 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:18:38.411 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:18:38.413 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:18:38.413 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:18:44.819 [main] INFO  c.h.s.w.RedisMessageConfig - [redisMessageListenerContainer,32] - Redis消息监听器配置完成
09:18:45.083 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:18:48.638 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b8166701-0a94-4490-9ca4-e34cf1cf3c0b
09:18:48.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8166701-0a94-4490-9ca4-e34cf1cf3c0b] RpcClient init label, labels = {module=naming, source=sdk}
09:18:48.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8166701-0a94-4490-9ca4-e34cf1cf3c0b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:48.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8166701-0a94-4490-9ca4-e34cf1cf3c0b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:48.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8166701-0a94-4490-9ca4-e34cf1cf3c0b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:48.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8166701-0a94-4490-9ca4-e34cf1cf3c0b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:48.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8166701-0a94-4490-9ca4-e34cf1cf3c0b] Success to connect to server [localhost:8848] on start up, connectionId = 1750814328660_127.0.0.1_5119
09:18:48.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8166701-0a94-4490-9ca4-e34cf1cf3c0b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:48.779 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8166701-0a94-4490-9ca4-e34cf1cf3c0b] Notify connected event to listeners.
09:18:48.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8166701-0a94-4490-9ca4-e34cf1cf3c0b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000197e74f1450
09:18:48.839 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:18:48.887 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:18:49.380 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8166701-0a94-4490-9ca4-e34cf1cf3c0b] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:18:49.393 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8166701-0a94-4490-9ca4-e34cf1cf3c0b] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:18:49.587 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.284 seconds (JVM running for 23.463)
09:18:49.610 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:18:49.610 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:18:49.610 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:31:53.051 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:31:53.112 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为1
09:32:03.790 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:32:03.790 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:32:13.787 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:32:13.789 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:32:23.800 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:32:23.801 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:32:33.797 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:32:33.797 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:32:43.719 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:32:43.719 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:32:53.795 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:32:53.796 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:33:03.278 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:33:03.279 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:33:11.358 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为0
09:33:12.964 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为1
09:33:23.290 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:33:23.291 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:33:33.793 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:33:33.793 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:33:43.784 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:33:43.784 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:33:53.793 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:33:53.793 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:34:03.793 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:34:03.793 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:34:08.079 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为1
09:34:09.735 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为0
09:34:13.794 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:34:13.794 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:all, 报文:PING
09:34:19.955 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为0
09:34:20.978 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为1
09:34:31.803 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:34:31.803 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:34:41.789 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:34:41.790 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:34:51.784 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:34:51.784 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:35:01.778 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:35:01.778 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:35:11.793 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:35:11.793 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:35:21.349 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:35:21.350 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:35:31.360 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:35:31.360 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:35:41.374 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:35:41.374 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:35:51.355 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:35:51.355 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:36:01.352 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:36:01.353 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:36:11.348 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:36:11.348 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:36:21.774 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:36:21.774 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:36:31.787 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:36:31.787 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:36:41.778 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:36:41.778 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:36:51.349 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:36:51.349 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:37:01.787 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:37:01.787 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:37:11.773 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:37:11.773 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:37:21.787 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:37:21.788 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:37:31.779 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:37:31.779 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:37:41.773 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:37:41.774 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:37:51.794 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:37:51.795 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:38:01.799 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:38:01.800 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:38:11.850 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:38:11.851 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:38:21.769 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:38:21.769 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:38:31.777 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:38:31.777 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:38:41.784 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:38:41.784 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:38:51.778 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:38:51.778 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:39:01.768 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:39:01.768 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:39:11.773 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:39:11.773 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:39:21.780 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:39:21.780 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:39:31.783 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:39:31.783 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:39:41.780 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:39:41.780 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:39:51.764 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:39:51.765 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:40:01.766 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:40:01.767 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:40:11.781 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:40:11.782 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:40:21.350 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:40:21.350 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:40:31.330 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:40:31.330 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:40:41.338 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:40:41.340 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:40:51.344 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:40:51.345 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:41:01.344 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:41:01.344 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:41:11.336 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:41:11.336 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:41:22.120 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:41:22.121 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:41:31.401 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:41:31.402 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:41:37.601 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为0
09:41:38.784 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为1
09:41:49.777 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:41:49.777 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:41:59.226 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:41:59.226 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:42:09.786 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:42:09.787 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:42:19.770 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:42:19.770 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:42:29.783 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:42:29.783 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:42:39.759 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:42:39.759 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:42:49.764 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:42:49.765 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:42:59.770 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:42:59.770 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:43:08.999 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:43:08.999 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:43:19.768 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:43:19.768 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:43:29.762 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:43:29.762 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:43:38.991 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:43:38.991 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:43:48.982 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:43:48.982 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:43:59.755 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:43:59.755 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:44:09.752 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:44:09.752 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:44:19.759 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:44:19.760 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:44:29.796 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:44:29.796 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:44:38.998 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:44:38.998 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:44:48.997 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:44:48.997 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:44:58.993 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:44:58.994 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:45:08.978 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:45:08.978 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:45:18.990 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:45:18.991 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:45:28.976 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:45:28.976 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:45:39.758 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:45:39.758 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:45:49.754 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:45:49.754 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:45:59.753 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:45:59.753 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:46:09.758 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:46:09.759 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:46:18.977 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:46:18.977 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:46:28.976 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:46:28.977 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:46:38.978 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:46:38.981 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:46:48.980 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:46:48.981 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:46:58.978 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:46:58.978 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:47:09.757 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:47:09.758 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:47:19.744 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:47:19.744 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:47:29.754 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:47:29.754 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:47:39.749 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:47:39.749 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:47:49.745 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:47:49.745 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:47:59.756 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:47:59.757 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:48:38.644 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:48:38.644 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:48:49.753 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:48:49.753 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:48:59.747 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:48:59.747 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:49:09.748 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:49:09.749 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:49:19.742 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:49:19.742 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:49:29.740 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:49:29.740 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:49:43.757 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
09:49:43.757 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
09:50:01.637 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为0
09:50:01.674 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:50:01.676 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:50:02.004 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:50:02.004 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3e0fbea6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:50:02.004 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750814328660_127.0.0.1_5119
09:50:02.006 [nacos-grpc-client-executor-390] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750814328660_127.0.0.1_5119]Ignore complete event,isRunning:false,isAbandon=false
09:50:02.008 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@20a9268c[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 391]
09:50:02.142 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:50:02.149 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:50:02.157 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:50:02.157 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:04:05.530 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:04:06.206 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2cb58862-d203-4341-82aa-f0efc945fd5f_config-0
10:04:06.269 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 
10:04:06.307 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
10:04:06.315 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
10:04:06.327 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
10:04:06.337 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
10:04:06.347 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
10:04:06.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cb58862-d203-4341-82aa-f0efc945fd5f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:04:06.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cb58862-d203-4341-82aa-f0efc945fd5f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b11e39dd70
10:04:06.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cb58862-d203-4341-82aa-f0efc945fd5f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001b11e39df90
10:04:06.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cb58862-d203-4341-82aa-f0efc945fd5f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:04:06.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cb58862-d203-4341-82aa-f0efc945fd5f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:04:06.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cb58862-d203-4341-82aa-f0efc945fd5f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:07.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cb58862-d203-4341-82aa-f0efc945fd5f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750817047154_127.0.0.1_9550
10:04:07.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cb58862-d203-4341-82aa-f0efc945fd5f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:07.398 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cb58862-d203-4341-82aa-f0efc945fd5f_config-0] Notify connected event to listeners.
10:04:07.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cb58862-d203-4341-82aa-f0efc945fd5f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b11e518440
10:04:07.599 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:04:12.139 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:04:12.140 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:04:12.140 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:04:12.370 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:04:13.318 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:04:13.319 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:04:13.319 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:04:20.952 [main] INFO  c.h.s.w.RedisMessageConfig - [redisMessageListenerContainer,32] - Redis消息监听器配置完成
10:04:21.318 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:04:25.476 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of efd8fc21-ecc7-4282-beee-084fabe0c8e7
10:04:25.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd8fc21-ecc7-4282-beee-084fabe0c8e7] RpcClient init label, labels = {module=naming, source=sdk}
10:04:25.478 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd8fc21-ecc7-4282-beee-084fabe0c8e7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:04:25.478 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd8fc21-ecc7-4282-beee-084fabe0c8e7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:04:25.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd8fc21-ecc7-4282-beee-084fabe0c8e7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:04:25.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd8fc21-ecc7-4282-beee-084fabe0c8e7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:25.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd8fc21-ecc7-4282-beee-084fabe0c8e7] Success to connect to server [localhost:8848] on start up, connectionId = 1750817065491_127.0.0.1_9577
10:04:25.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd8fc21-ecc7-4282-beee-084fabe0c8e7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:25.618 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd8fc21-ecc7-4282-beee-084fabe0c8e7] Notify connected event to listeners.
10:04:25.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd8fc21-ecc7-4282-beee-084fabe0c8e7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b11e518440
10:04:25.683 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:04:25.724 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:04:26.158 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd8fc21-ecc7-4282-beee-084fabe0c8e7] Receive server push request, request = NotifySubscriberRequest, requestId = 5
10:04:26.184 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efd8fc21-ecc7-4282-beee-084fabe0c8e7] Ack server push request, request = NotifySubscriberRequest, requestId = 5
10:04:26.590 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.684 seconds (JVM running for 22.645)
10:04:26.605 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:04:26.605 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:04:26.606 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:04:27.068 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:04:28.945 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接121-222, 当前在线人数为1
10:04:39.715 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:04:39.716 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:04:48.993 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:04:48.994 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:04:58.991 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:04:58.992 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:05:09.705 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:05:09.705 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:05:18.988 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:05:18.988 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:05:29.706 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:05:29.707 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:05:39.711 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:05:39.712 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:05:49.706 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:05:49.706 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:05:59.704 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:05:59.704 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:06:09.717 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:06:09.717 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:06:19.700 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:06:19.701 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:06:19.915 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出121-222, 当前在线人数为0
10:06:19.941 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:06:19.945 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:06:20.274 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:06:20.274 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3eab4cf4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:06:20.275 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750817065491_127.0.0.1_9577
10:06:20.282 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@658c1956[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 31]
10:06:20.284 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750817065491_127.0.0.1_9577]Ignore complete event,isRunning:false,isAbandon=false
10:06:20.454 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:06:20.457 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:06:20.472 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:06:20.472 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:06:29.591 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:06:31.209 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 14e97ceb-4582-456f-8508-69c63b4c4ead_config-0
10:06:31.347 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 71 ms to scan 1 urls, producing 3 keys and 6 values 
10:06:31.411 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 4 keys and 9 values 
10:06:31.427 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
10:06:31.447 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
10:06:31.461 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
10:06:31.474 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
10:06:31.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14e97ceb-4582-456f-8508-69c63b4c4ead_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:06:31.478 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14e97ceb-4582-456f-8508-69c63b4c4ead_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000021a443b68d8
10:06:31.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14e97ceb-4582-456f-8508-69c63b4c4ead_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000021a443b6af8
10:06:31.480 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14e97ceb-4582-456f-8508-69c63b4c4ead_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:06:31.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14e97ceb-4582-456f-8508-69c63b4c4ead_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:06:31.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14e97ceb-4582-456f-8508-69c63b4c4ead_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:06:33.019 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14e97ceb-4582-456f-8508-69c63b4c4ead_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750817192720_127.0.0.1_9863
10:06:33.020 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14e97ceb-4582-456f-8508-69c63b4c4ead_config-0] Notify connected event to listeners.
10:06:33.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14e97ceb-4582-456f-8508-69c63b4c4ead_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:06:33.023 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14e97ceb-4582-456f-8508-69c63b4c4ead_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000021a444f0440
10:06:33.219 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:06:37.079 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:06:37.080 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:06:37.080 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:06:37.264 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:06:37.980 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:06:37.982 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:06:37.984 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:06:43.787 [main] INFO  c.h.s.w.RedisMessageConfig - [redisMessageListenerContainer,32] - Redis消息监听器配置完成
10:06:44.023 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:06:47.301 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7fa5782e-fc32-44d3-9032-3c4a4464a650
10:06:47.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa5782e-fc32-44d3-9032-3c4a4464a650] RpcClient init label, labels = {module=naming, source=sdk}
10:06:47.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa5782e-fc32-44d3-9032-3c4a4464a650] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:06:47.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa5782e-fc32-44d3-9032-3c4a4464a650] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:06:47.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa5782e-fc32-44d3-9032-3c4a4464a650] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:06:47.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa5782e-fc32-44d3-9032-3c4a4464a650] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:06:47.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa5782e-fc32-44d3-9032-3c4a4464a650] Success to connect to server [localhost:8848] on start up, connectionId = 1750817207315_127.0.0.1_9893
10:06:47.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa5782e-fc32-44d3-9032-3c4a4464a650] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:06:47.436 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa5782e-fc32-44d3-9032-3c4a4464a650] Notify connected event to listeners.
10:06:47.436 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa5782e-fc32-44d3-9032-3c4a4464a650] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000021a444f0440
10:06:47.489 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:06:47.519 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:06:48.025 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa5782e-fc32-44d3-9032-3c4a4464a650] Receive server push request, request = NotifySubscriberRequest, requestId = 6
10:06:48.040 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa5782e-fc32-44d3-9032-3c4a4464a650] Ack server push request, request = NotifySubscriberRequest, requestId = 6
10:06:48.263 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.607 seconds (JVM running for 24.466)
10:06:48.278 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:06:48.279 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:06:48.280 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:06:52.805 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:06:52.862 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接121-222, 当前在线人数为1
10:07:03.707 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:07:03.708 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:07:12.862 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:07:12.862 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:07:22.858 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:07:22.859 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:07:33.763 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:07:33.763 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:07:41.001 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接1-1, 当前在线人数为2
10:07:43.069 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:07:43.070 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:07:47.339 [redisMessageListenerContainer-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":1,"totalSize":171975820,"totalChunks":83,"progress":1.2048192771084338},"type":"upload_progress","fileId":null}
10:07:47.340 [redisMessageListenerContainer-1] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=1, totalSize=171975820, totalChunks=83, progress=1.2048192771084338}
10:07:47.789 [redisMessageListenerContainer-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":2,"totalSize":171975820,"totalChunks":83,"progress":2.4096385542168677},"type":"upload_progress","fileId":null}
10:07:47.791 [redisMessageListenerContainer-2] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=2, totalSize=171975820, totalChunks=83, progress=2.4096385542168677}
10:07:48.486 [redisMessageListenerContainer-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":3,"totalSize":171975820,"totalChunks":83,"progress":3.614457831325301},"type":"upload_progress","fileId":null}
10:07:48.493 [redisMessageListenerContainer-3] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=3, totalSize=171975820, totalChunks=83, progress=3.614457831325301}
10:07:49.172 [redisMessageListenerContainer-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":4,"totalSize":171975820,"totalChunks":83,"progress":4.819277108433735},"type":"upload_progress","fileId":null}
10:07:49.177 [redisMessageListenerContainer-4] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=4, totalSize=171975820, totalChunks=83, progress=4.819277108433735}
10:07:49.740 [redisMessageListenerContainer-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":5,"totalSize":171975820,"totalChunks":83,"progress":6.024096385542169},"type":"upload_progress","fileId":null}
10:07:49.761 [redisMessageListenerContainer-5] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=5, totalSize=171975820, totalChunks=83, progress=6.024096385542169}
10:07:50.423 [redisMessageListenerContainer-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":6,"totalSize":171975820,"totalChunks":83,"progress":7.228915662650602},"type":"upload_progress","fileId":null}
10:07:50.423 [redisMessageListenerContainer-6] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=6, totalSize=171975820, totalChunks=83, progress=7.228915662650602}
10:07:51.023 [redisMessageListenerContainer-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":7,"totalSize":171975820,"totalChunks":83,"progress":8.433734939759036},"type":"upload_progress","fileId":null}
10:07:51.026 [redisMessageListenerContainer-7] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=7, totalSize=171975820, totalChunks=83, progress=8.433734939759036}
10:07:51.752 [redisMessageListenerContainer-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":8,"totalSize":171975820,"totalChunks":83,"progress":9.63855421686747},"type":"upload_progress","fileId":null}
10:07:51.766 [redisMessageListenerContainer-8] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=8, totalSize=171975820, totalChunks=83, progress=9.63855421686747}
10:07:52.322 [redisMessageListenerContainer-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":9,"totalSize":171975820,"totalChunks":83,"progress":10.843373493975903},"type":"upload_progress","fileId":null}
10:07:52.324 [redisMessageListenerContainer-9] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=9, totalSize=171975820, totalChunks=83, progress=10.843373493975903}
10:07:52.852 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:07:52.852 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:07:52.922 [redisMessageListenerContainer-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":10,"totalSize":171975820,"totalChunks":83,"progress":12.048192771084338},"type":"upload_progress","fileId":null}
10:07:52.923 [redisMessageListenerContainer-10] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=10, totalSize=171975820, totalChunks=83, progress=12.048192771084338}
10:07:53.543 [redisMessageListenerContainer-11] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":11,"totalSize":171975820,"totalChunks":83,"progress":13.253012048192772},"type":"upload_progress","fileId":null}
10:07:53.555 [redisMessageListenerContainer-11] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=11, totalSize=171975820, totalChunks=83, progress=13.253012048192772}
10:07:54.125 [redisMessageListenerContainer-12] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":12,"totalSize":171975820,"totalChunks":83,"progress":14.457831325301203},"type":"upload_progress","fileId":null}
10:07:54.129 [redisMessageListenerContainer-12] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=12, totalSize=171975820, totalChunks=83, progress=14.457831325301203}
10:07:54.564 [redisMessageListenerContainer-13] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":13,"totalSize":171975820,"totalChunks":83,"progress":15.66265060240964},"type":"upload_progress","fileId":null}
10:07:54.569 [redisMessageListenerContainer-13] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=13, totalSize=171975820, totalChunks=83, progress=15.66265060240964}
10:07:55.117 [redisMessageListenerContainer-14] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":14,"totalSize":171975820,"totalChunks":83,"progress":16.867469879518072},"type":"upload_progress","fileId":null}
10:07:55.118 [redisMessageListenerContainer-14] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=14, totalSize=171975820, totalChunks=83, progress=16.867469879518072}
10:07:55.769 [redisMessageListenerContainer-15] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":15,"totalSize":171975820,"totalChunks":83,"progress":18.072289156626507},"type":"upload_progress","fileId":null}
10:07:55.781 [redisMessageListenerContainer-15] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=15, totalSize=171975820, totalChunks=83, progress=18.072289156626507}
10:07:56.371 [redisMessageListenerContainer-16] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":16,"totalSize":171975820,"totalChunks":83,"progress":19.27710843373494},"type":"upload_progress","fileId":null}
10:07:56.386 [redisMessageListenerContainer-16] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=16, totalSize=171975820, totalChunks=83, progress=19.27710843373494}
10:07:58.926 [redisMessageListenerContainer-17] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":17,"totalSize":171975820,"totalChunks":83,"progress":20.481927710843372},"type":"upload_progress","fileId":null}
10:07:58.926 [redisMessageListenerContainer-17] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=17, totalSize=171975820, totalChunks=83, progress=20.481927710843372}
10:08:00.838 [redisMessageListenerContainer-18] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":18,"totalSize":171975820,"totalChunks":83,"progress":21.686746987951807},"type":"upload_progress","fileId":null}
10:08:00.839 [redisMessageListenerContainer-18] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=18, totalSize=171975820, totalChunks=83, progress=21.686746987951807}
10:08:01.449 [redisMessageListenerContainer-19] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":19,"totalSize":171975820,"totalChunks":83,"progress":22.89156626506024},"type":"upload_progress","fileId":null}
10:08:01.449 [redisMessageListenerContainer-19] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=19, totalSize=171975820, totalChunks=83, progress=22.89156626506024}
10:08:02.153 [redisMessageListenerContainer-20] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":20,"totalSize":171975820,"totalChunks":83,"progress":24.096385542168676},"type":"upload_progress","fileId":null}
10:08:02.154 [redisMessageListenerContainer-20] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=20, totalSize=171975820, totalChunks=83, progress=24.096385542168676}
10:08:02.761 [redisMessageListenerContainer-21] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":21,"totalSize":171975820,"totalChunks":83,"progress":25.301204819277107},"type":"upload_progress","fileId":null}
10:08:02.762 [redisMessageListenerContainer-21] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=21, totalSize=171975820, totalChunks=83, progress=25.301204819277107}
10:08:02.856 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:08:02.857 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:08:05.687 [redisMessageListenerContainer-22] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":22,"totalSize":171975820,"totalChunks":83,"progress":26.506024096385545},"type":"upload_progress","fileId":null}
10:08:05.695 [redisMessageListenerContainer-22] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=22, totalSize=171975820, totalChunks=83, progress=26.506024096385545}
10:08:06.376 [redisMessageListenerContainer-23] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":23,"totalSize":171975820,"totalChunks":83,"progress":27.710843373493976},"type":"upload_progress","fileId":null}
10:08:06.390 [redisMessageListenerContainer-23] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=23, totalSize=171975820, totalChunks=83, progress=27.710843373493976}
10:08:07.275 [redisMessageListenerContainer-24] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":24,"totalSize":171975820,"totalChunks":83,"progress":28.915662650602407},"type":"upload_progress","fileId":null}
10:08:07.276 [redisMessageListenerContainer-24] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=24, totalSize=171975820, totalChunks=83, progress=28.915662650602407}
10:08:07.930 [redisMessageListenerContainer-25] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":25,"totalSize":171975820,"totalChunks":83,"progress":30.120481927710845},"type":"upload_progress","fileId":null}
10:08:07.946 [redisMessageListenerContainer-25] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=25, totalSize=171975820, totalChunks=83, progress=30.120481927710845}
10:08:08.632 [redisMessageListenerContainer-26] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":26,"totalSize":171975820,"totalChunks":83,"progress":31.32530120481928},"type":"upload_progress","fileId":null}
10:08:08.634 [redisMessageListenerContainer-26] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=26, totalSize=171975820, totalChunks=83, progress=31.32530120481928}
10:08:09.221 [redisMessageListenerContainer-27] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":27,"totalSize":171975820,"totalChunks":83,"progress":32.53012048192771},"type":"upload_progress","fileId":null}
10:08:09.222 [redisMessageListenerContainer-27] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=27, totalSize=171975820, totalChunks=83, progress=32.53012048192771}
10:08:09.813 [redisMessageListenerContainer-28] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":28,"totalSize":171975820,"totalChunks":83,"progress":33.734939759036145},"type":"upload_progress","fileId":null}
10:08:09.815 [redisMessageListenerContainer-28] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=28, totalSize=171975820, totalChunks=83, progress=33.734939759036145}
10:08:10.444 [redisMessageListenerContainer-29] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":29,"totalSize":171975820,"totalChunks":83,"progress":34.93975903614458},"type":"upload_progress","fileId":null}
10:08:10.451 [redisMessageListenerContainer-29] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=29, totalSize=171975820, totalChunks=83, progress=34.93975903614458}
10:08:11.098 [redisMessageListenerContainer-30] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":30,"totalSize":171975820,"totalChunks":83,"progress":36.144578313253014},"type":"upload_progress","fileId":null}
10:08:11.100 [redisMessageListenerContainer-30] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=30, totalSize=171975820, totalChunks=83, progress=36.144578313253014}
10:08:11.716 [redisMessageListenerContainer-31] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":31,"totalSize":171975820,"totalChunks":83,"progress":37.34939759036144},"type":"upload_progress","fileId":null}
10:08:11.717 [redisMessageListenerContainer-31] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=31, totalSize=171975820, totalChunks=83, progress=37.34939759036144}
10:08:12.938 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:08:12.938 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:08:15.111 [redisMessageListenerContainer-32] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":32,"totalSize":171975820,"totalChunks":83,"progress":38.55421686746988},"type":"upload_progress","fileId":null}
10:08:15.127 [redisMessageListenerContainer-32] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=32, totalSize=171975820, totalChunks=83, progress=38.55421686746988}
10:08:16.215 [redisMessageListenerContainer-33] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":33,"totalSize":171975820,"totalChunks":83,"progress":39.75903614457831},"type":"upload_progress","fileId":null}
10:08:16.217 [redisMessageListenerContainer-33] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=33, totalSize=171975820, totalChunks=83, progress=39.75903614457831}
10:08:16.905 [redisMessageListenerContainer-34] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":34,"totalSize":171975820,"totalChunks":83,"progress":40.963855421686745},"type":"upload_progress","fileId":null}
10:08:16.907 [redisMessageListenerContainer-34] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=34, totalSize=171975820, totalChunks=83, progress=40.963855421686745}
10:08:17.357 [redisMessageListenerContainer-35] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":35,"totalSize":171975820,"totalChunks":83,"progress":42.168674698795186},"type":"upload_progress","fileId":null}
10:08:17.361 [redisMessageListenerContainer-35] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=35, totalSize=171975820, totalChunks=83, progress=42.168674698795186}
10:08:18.047 [redisMessageListenerContainer-36] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":36,"totalSize":171975820,"totalChunks":83,"progress":43.373493975903614},"type":"upload_progress","fileId":null}
10:08:18.065 [redisMessageListenerContainer-36] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=36, totalSize=171975820, totalChunks=83, progress=43.373493975903614}
10:08:18.817 [redisMessageListenerContainer-37] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":37,"totalSize":171975820,"totalChunks":83,"progress":44.57831325301205},"type":"upload_progress","fileId":null}
10:08:18.819 [redisMessageListenerContainer-37] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=37, totalSize=171975820, totalChunks=83, progress=44.57831325301205}
10:08:19.523 [redisMessageListenerContainer-38] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":38,"totalSize":171975820,"totalChunks":83,"progress":45.78313253012048},"type":"upload_progress","fileId":null}
10:08:19.524 [redisMessageListenerContainer-38] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=38, totalSize=171975820, totalChunks=83, progress=45.78313253012048}
10:08:20.187 [redisMessageListenerContainer-39] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":39,"totalSize":171975820,"totalChunks":83,"progress":46.98795180722892},"type":"upload_progress","fileId":null}
10:08:20.189 [redisMessageListenerContainer-39] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=39, totalSize=171975820, totalChunks=83, progress=46.98795180722892}
10:08:21.004 [redisMessageListenerContainer-40] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":40,"totalSize":171975820,"totalChunks":83,"progress":48.19277108433735},"type":"upload_progress","fileId":null}
10:08:21.015 [redisMessageListenerContainer-40] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=40, totalSize=171975820, totalChunks=83, progress=48.19277108433735}
10:08:21.753 [redisMessageListenerContainer-41] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":41,"totalSize":171975820,"totalChunks":83,"progress":49.39759036144578},"type":"upload_progress","fileId":null}
10:08:21.754 [redisMessageListenerContainer-41] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=41, totalSize=171975820, totalChunks=83, progress=49.39759036144578}
10:08:22.443 [redisMessageListenerContainer-42] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":42,"totalSize":171975820,"totalChunks":83,"progress":50.602409638554214},"type":"upload_progress","fileId":null}
10:08:22.444 [redisMessageListenerContainer-42] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=42, totalSize=171975820, totalChunks=83, progress=50.602409638554214}
10:08:23.172 [redisMessageListenerContainer-43] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":43,"totalSize":171975820,"totalChunks":83,"progress":51.80722891566265},"type":"upload_progress","fileId":null}
10:08:23.187 [redisMessageListenerContainer-43] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=43, totalSize=171975820, totalChunks=83, progress=51.80722891566265}
10:08:23.711 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:08:23.711 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:08:23.858 [redisMessageListenerContainer-44] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":44,"totalSize":171975820,"totalChunks":83,"progress":53.01204819277109},"type":"upload_progress","fileId":null}
10:08:23.859 [redisMessageListenerContainer-44] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=44, totalSize=171975820, totalChunks=83, progress=53.01204819277109}
10:08:24.514 [redisMessageListenerContainer-45] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":45,"totalSize":171975820,"totalChunks":83,"progress":54.21686746987952},"type":"upload_progress","fileId":null}
10:08:24.523 [redisMessageListenerContainer-45] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=45, totalSize=171975820, totalChunks=83, progress=54.21686746987952}
10:08:25.158 [redisMessageListenerContainer-46] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":46,"totalSize":171975820,"totalChunks":83,"progress":55.42168674698795},"type":"upload_progress","fileId":null}
10:08:25.167 [redisMessageListenerContainer-46] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=46, totalSize=171975820, totalChunks=83, progress=55.42168674698795}
10:08:25.888 [redisMessageListenerContainer-47] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":47,"totalSize":171975820,"totalChunks":83,"progress":56.62650602409639},"type":"upload_progress","fileId":null}
10:08:25.889 [redisMessageListenerContainer-47] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=47, totalSize=171975820, totalChunks=83, progress=56.62650602409639}
10:08:26.568 [redisMessageListenerContainer-48] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":48,"totalSize":171975820,"totalChunks":83,"progress":57.831325301204814},"type":"upload_progress","fileId":null}
10:08:26.597 [redisMessageListenerContainer-48] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=48, totalSize=171975820, totalChunks=83, progress=57.831325301204814}
10:08:29.344 [redisMessageListenerContainer-49] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":49,"totalSize":171975820,"totalChunks":83,"progress":59.036144578313255},"type":"upload_progress","fileId":null}
10:08:29.345 [redisMessageListenerContainer-49] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=49, totalSize=171975820, totalChunks=83, progress=59.036144578313255}
10:08:30.591 [redisMessageListenerContainer-50] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":50,"totalSize":171975820,"totalChunks":83,"progress":60.24096385542169},"type":"upload_progress","fileId":null}
10:08:30.592 [redisMessageListenerContainer-50] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=50, totalSize=171975820, totalChunks=83, progress=60.24096385542169}
10:08:31.332 [redisMessageListenerContainer-51] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":51,"totalSize":171975820,"totalChunks":83,"progress":61.44578313253012},"type":"upload_progress","fileId":null}
10:08:31.332 [redisMessageListenerContainer-51] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=51, totalSize=171975820, totalChunks=83, progress=61.44578313253012}
10:08:32.070 [redisMessageListenerContainer-52] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":52,"totalSize":171975820,"totalChunks":83,"progress":62.65060240963856},"type":"upload_progress","fileId":null}
10:08:32.070 [redisMessageListenerContainer-52] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=52, totalSize=171975820, totalChunks=83, progress=62.65060240963856}
10:08:32.978 [redisMessageListenerContainer-53] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":53,"totalSize":171975820,"totalChunks":83,"progress":63.85542168674698},"type":"upload_progress","fileId":null}
10:08:32.979 [redisMessageListenerContainer-53] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=53, totalSize=171975820, totalChunks=83, progress=63.85542168674698}
10:08:33.708 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:08:33.708 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:08:33.788 [redisMessageListenerContainer-54] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":54,"totalSize":171975820,"totalChunks":83,"progress":65.06024096385542},"type":"upload_progress","fileId":null}
10:08:33.790 [redisMessageListenerContainer-54] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=54, totalSize=171975820, totalChunks=83, progress=65.06024096385542}
10:08:34.664 [redisMessageListenerContainer-55] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":55,"totalSize":171975820,"totalChunks":83,"progress":66.26506024096386},"type":"upload_progress","fileId":null}
10:08:34.664 [redisMessageListenerContainer-55] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=55, totalSize=171975820, totalChunks=83, progress=66.26506024096386}
10:08:35.535 [redisMessageListenerContainer-56] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":56,"totalSize":171975820,"totalChunks":83,"progress":67.46987951807229},"type":"upload_progress","fileId":null}
10:08:35.536 [redisMessageListenerContainer-56] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=56, totalSize=171975820, totalChunks=83, progress=67.46987951807229}
10:08:36.314 [redisMessageListenerContainer-57] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":57,"totalSize":171975820,"totalChunks":83,"progress":68.67469879518072},"type":"upload_progress","fileId":null}
10:08:36.315 [redisMessageListenerContainer-57] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=57, totalSize=171975820, totalChunks=83, progress=68.67469879518072}
10:08:37.267 [redisMessageListenerContainer-58] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":58,"totalSize":171975820,"totalChunks":83,"progress":69.87951807228916},"type":"upload_progress","fileId":null}
10:08:37.272 [redisMessageListenerContainer-58] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=58, totalSize=171975820, totalChunks=83, progress=69.87951807228916}
10:08:38.101 [redisMessageListenerContainer-59] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":59,"totalSize":171975820,"totalChunks":83,"progress":71.08433734939759},"type":"upload_progress","fileId":null}
10:08:38.102 [redisMessageListenerContainer-59] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=59, totalSize=171975820, totalChunks=83, progress=71.08433734939759}
10:08:38.862 [redisMessageListenerContainer-60] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":60,"totalSize":171975820,"totalChunks":83,"progress":72.28915662650603},"type":"upload_progress","fileId":null}
10:08:38.863 [redisMessageListenerContainer-60] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=60, totalSize=171975820, totalChunks=83, progress=72.28915662650603}
10:08:39.610 [redisMessageListenerContainer-61] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":61,"totalSize":171975820,"totalChunks":83,"progress":73.49397590361446},"type":"upload_progress","fileId":null}
10:08:39.611 [redisMessageListenerContainer-61] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=61, totalSize=171975820, totalChunks=83, progress=73.49397590361446}
10:08:40.760 [redisMessageListenerContainer-62] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":62,"totalSize":171975820,"totalChunks":83,"progress":74.69879518072288},"type":"upload_progress","fileId":null}
10:08:40.760 [redisMessageListenerContainer-62] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=62, totalSize=171975820, totalChunks=83, progress=74.69879518072288}
10:08:41.920 [redisMessageListenerContainer-63] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":63,"totalSize":171975820,"totalChunks":83,"progress":75.90361445783132},"type":"upload_progress","fileId":null}
10:08:41.921 [redisMessageListenerContainer-63] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=63, totalSize=171975820, totalChunks=83, progress=75.90361445783132}
10:08:45.346 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:08:45.346 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:08:47.971 [redisMessageListenerContainer-64] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":64,"totalSize":171975820,"totalChunks":83,"progress":77.10843373493977},"type":"upload_progress","fileId":null}
10:08:47.971 [redisMessageListenerContainer-64] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=64, totalSize=171975820, totalChunks=83, progress=77.10843373493977}
10:08:48.645 [redisMessageListenerContainer-65] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":65,"totalSize":171975820,"totalChunks":83,"progress":78.3132530120482},"type":"upload_progress","fileId":null}
10:08:48.648 [redisMessageListenerContainer-65] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=65, totalSize=171975820, totalChunks=83, progress=78.3132530120482}
10:08:49.315 [redisMessageListenerContainer-66] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":66,"totalSize":171975820,"totalChunks":83,"progress":79.51807228915662},"type":"upload_progress","fileId":null}
10:08:49.317 [redisMessageListenerContainer-66] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=66, totalSize=171975820, totalChunks=83, progress=79.51807228915662}
10:08:50.027 [redisMessageListenerContainer-67] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":67,"totalSize":171975820,"totalChunks":83,"progress":80.72289156626506},"type":"upload_progress","fileId":null}
10:08:50.029 [redisMessageListenerContainer-67] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=67, totalSize=171975820, totalChunks=83, progress=80.72289156626506}
10:08:50.632 [redisMessageListenerContainer-68] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":68,"totalSize":171975820,"totalChunks":83,"progress":81.92771084337349},"type":"upload_progress","fileId":null}
10:08:50.635 [redisMessageListenerContainer-68] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=68, totalSize=171975820, totalChunks=83, progress=81.92771084337349}
10:08:51.261 [redisMessageListenerContainer-69] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":69,"totalSize":171975820,"totalChunks":83,"progress":83.13253012048193},"type":"upload_progress","fileId":null}
10:08:51.264 [redisMessageListenerContainer-69] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=69, totalSize=171975820, totalChunks=83, progress=83.13253012048193}
10:08:51.972 [redisMessageListenerContainer-70] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":70,"totalSize":171975820,"totalChunks":83,"progress":84.33734939759037},"type":"upload_progress","fileId":null}
10:08:51.974 [redisMessageListenerContainer-70] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=70, totalSize=171975820, totalChunks=83, progress=84.33734939759037}
10:08:52.605 [redisMessageListenerContainer-71] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":71,"totalSize":171975820,"totalChunks":83,"progress":85.54216867469879},"type":"upload_progress","fileId":null}
10:08:52.610 [redisMessageListenerContainer-71] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=71, totalSize=171975820, totalChunks=83, progress=85.54216867469879}
10:08:53.176 [redisMessageListenerContainer-72] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":72,"totalSize":171975820,"totalChunks":83,"progress":86.74698795180723},"type":"upload_progress","fileId":null}
10:08:53.183 [redisMessageListenerContainer-72] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=72, totalSize=171975820, totalChunks=83, progress=86.74698795180723}
10:08:53.699 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:08:53.699 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:08:53.816 [redisMessageListenerContainer-73] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":73,"totalSize":171975820,"totalChunks":83,"progress":87.95180722891565},"type":"upload_progress","fileId":null}
10:08:53.817 [redisMessageListenerContainer-73] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=73, totalSize=171975820, totalChunks=83, progress=87.95180722891565}
10:08:54.420 [redisMessageListenerContainer-74] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":74,"totalSize":171975820,"totalChunks":83,"progress":89.1566265060241},"type":"upload_progress","fileId":null}
10:08:54.430 [redisMessageListenerContainer-74] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=74, totalSize=171975820, totalChunks=83, progress=89.1566265060241}
10:08:55.038 [redisMessageListenerContainer-75] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":75,"totalSize":171975820,"totalChunks":83,"progress":90.36144578313254},"type":"upload_progress","fileId":null}
10:08:55.043 [redisMessageListenerContainer-75] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=75, totalSize=171975820, totalChunks=83, progress=90.36144578313254}
10:08:55.611 [redisMessageListenerContainer-76] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":76,"totalSize":171975820,"totalChunks":83,"progress":91.56626506024097},"type":"upload_progress","fileId":null}
10:08:55.614 [redisMessageListenerContainer-76] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=76, totalSize=171975820, totalChunks=83, progress=91.56626506024097}
10:08:56.172 [redisMessageListenerContainer-77] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":77,"totalSize":171975820,"totalChunks":83,"progress":92.7710843373494},"type":"upload_progress","fileId":null}
10:08:56.177 [redisMessageListenerContainer-77] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=77, totalSize=171975820, totalChunks=83, progress=92.7710843373494}
10:08:56.791 [redisMessageListenerContainer-78] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":78,"totalSize":171975820,"totalChunks":83,"progress":93.97590361445783},"type":"upload_progress","fileId":null}
10:08:56.791 [redisMessageListenerContainer-78] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=78, totalSize=171975820, totalChunks=83, progress=93.97590361445783}
10:08:57.670 [redisMessageListenerContainer-79] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":79,"totalSize":171975820,"totalChunks":83,"progress":95.18072289156626},"type":"upload_progress","fileId":null}
10:08:57.672 [redisMessageListenerContainer-79] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=79, totalSize=171975820, totalChunks=83, progress=95.18072289156626}
10:08:58.378 [redisMessageListenerContainer-80] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":80,"totalSize":171975820,"totalChunks":83,"progress":96.3855421686747},"type":"upload_progress","fileId":null}
10:08:58.380 [redisMessageListenerContainer-80] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=80, totalSize=171975820, totalChunks=83, progress=96.3855421686747}
10:08:59.007 [redisMessageListenerContainer-81] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":81,"totalSize":171975820,"totalChunks":83,"progress":97.59036144578313},"type":"upload_progress","fileId":null}
10:08:59.008 [redisMessageListenerContainer-81] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=81, totalSize=171975820, totalChunks=83, progress=97.59036144578313}
10:08:59.697 [redisMessageListenerContainer-82] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":82,"totalSize":171975820,"totalChunks":83,"progress":98.79518072289156},"type":"upload_progress","fileId":null}
10:08:59.701 [redisMessageListenerContainer-82] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=82, totalSize=171975820, totalChunks=83, progress=98.79518072289156}
10:09:00.054 [redisMessageListenerContainer-83] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1|1, 报文:{"data":{"fileName":"iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts","uploadedChunks":83,"totalSize":171975820,"totalChunks":83,"progress":100.0},"type":"upload_progress","fileId":null}
10:09:00.056 [redisMessageListenerContainer-83] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1|1, fileId: null, progress: {fileName=iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts, uploadedChunks=83, totalSize=171975820, totalChunks=83, progress=100.0}
10:09:03.707 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:09:03.708 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:09:13.695 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:09:13.695 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:09:23.702 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:09:23.702 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:09:43.696 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:09:43.697 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:09:57.384 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:09:57.384 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:10:03.688 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:10:03.688 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:10:13.688 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:10:13.688 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:10:23.697 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:10:23.697 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:10:33.694 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:10:33.694 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:10:43.743 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:10:43.743 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:10:52.847 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:10:52.847 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:11:02.850 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:11:02.862 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:11:13.689 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:11:13.689 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:11:22.851 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:11:22.851 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:11:32.855 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:11:32.855 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:11:42.853 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:11:42.854 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:11:52.842 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:11:52.843 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:12:02.854 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:12:02.854 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:12:12.848 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:12:12.848 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:12:22.859 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:12:22.860 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:12:32.853 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:12:32.853 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:12:42.844 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:12:42.845 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:12:52.840 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:12:52.840 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:13:02.842 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:13:02.842 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:13:12.847 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:13:12.848 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:13:22.849 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:13:22.849 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:13:32.848 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:13:32.849 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:13:42.841 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:13:42.841 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:13:53.692 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:13:53.692 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:14:03.687 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:14:03.687 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:14:12.839 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:14:12.840 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:14:22.860 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:121-222,报文:PING
10:14:22.860 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:121-222, 报文:PONG
10:14:27.118 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出121-222, 当前在线人数为1
10:14:28.214 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:14:38.682 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:14:38.682 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:14:48.674 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:14:48.675 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:14:58.549 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:14:58.549 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:15:08.525 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:15:08.525 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:15:18.678 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:15:18.678 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:15:28.677 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:15:28.678 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:15:38.678 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:15:38.678 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:15:48.528 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:15:48.528 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:15:58.720 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:15:58.720 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:16:08.521 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:16:08.521 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:16:18.742 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:16:18.742 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:16:28.677 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:16:28.677 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:16:38.746 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:16:38.746 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:16:48.536 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:16:48.536 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:16:58.682 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:16:58.682 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:17:08.863 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:17:08.863 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:17:18.530 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:17:18.530 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:17:24.454 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:17:25.579 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:17:35.954 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:17:35.954 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:17:46.670 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:17:46.671 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:17:56.677 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:17:56.678 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:18:06.665 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:18:06.665 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:18:16.669 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:18:16.669 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:18:26.665 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:18:26.665 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:18:36.677 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:18:36.677 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:19:05.703 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:19:05.703 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:19:16.664 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:19:16.664 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:19:26.673 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:19:26.673 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:19:36.666 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:19:36.668 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:19:46.674 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:19:46.674 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:19:55.919 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:19:55.919 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:20:05.930 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:20:05.930 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:20:11.922 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:20:13.344 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:20:23.753 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:20:23.753 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:20:34.671 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:20:34.672 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:20:43.757 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:20:43.757 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:20:54.184 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:20:54.185 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:21:04.659 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:21:04.659 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:21:14.666 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:21:14.666 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:21:24.665 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:21:24.665 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:21:34.659 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:21:34.659 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:21:44.670 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:21:44.670 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:21:54.659 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:21:54.659 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:22:04.659 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:22:04.659 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:22:13.206 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:22:14.337 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:22:24.645 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:22:24.646 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:22:34.651 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:22:34.651 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:22:44.670 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:22:44.670 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:22:54.649 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:22:54.649 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:23:04.665 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:23:04.665 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:23:14.656 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:23:14.656 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:23:24.667 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:23:24.667 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:23:34.655 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:23:34.655 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:23:44.664 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:23:44.665 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:23:54.658 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:23:54.659 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:24:04.654 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:24:04.654 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:24:14.660 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:24:14.660 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:24:24.652 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:24:24.652 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:24:34.653 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:24:34.653 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:24:44.661 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:24:44.662 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:24:54.652 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:24:54.652 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:25:12.745 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:25:12.745 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:25:14.641 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:25:14.641 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:25:24.647 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:25:24.647 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:25:34.648 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:25:34.648 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:25:44.658 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:25:44.659 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:25:54.656 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:25:54.657 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:26:04.639 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:26:04.639 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:26:14.639 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:26:14.639 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:26:24.686 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:26:24.686 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:26:33.305 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:26:34.501 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:26:44.721 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:26:44.721 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:26:54.716 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:26:54.716 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:27:04.720 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:27:04.720 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:27:14.723 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:27:14.723 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:27:25.647 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:27:25.647 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:27:34.723 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:27:34.723 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:27:44.716 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:27:44.716 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:27:55.644 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:27:55.645 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:28:04.886 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:28:04.886 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:28:15.643 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:28:15.643 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:28:25.658 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:28:25.659 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:28:35.645 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:28:35.645 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:28:45.645 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:28:45.646 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:28:55.640 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:28:55.640 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:29:05.060 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:29:05.061 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:29:14.713 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:29:14.713 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:29:24.718 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:29:24.718 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:29:34.717 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:29:34.717 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:29:45.648 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:29:45.648 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:29:55.653 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:29:55.653 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:30:04.752 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:30:04.752 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:30:14.766 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:30:14.766 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:30:25.645 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:30:25.645 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:30:35.633 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:30:35.635 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:30:44.705 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:30:44.705 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:30:54.715 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:30:54.716 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:31:05.637 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:31:05.638 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:31:14.704 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:31:14.705 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:31:25.640 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:31:25.640 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:31:35.637 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:31:35.637 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:31:45.634 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:31:45.634 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:31:54.711 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:31:54.711 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:32:05.647 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:32:05.647 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:32:15.633 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:32:15.634 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:32:25.633 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:32:25.633 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:32:35.643 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:32:35.643 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:32:45.636 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:32:45.637 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:32:55.637 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:32:55.637 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:33:05.637 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:33:05.637 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:33:15.632 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:33:15.632 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:33:25.642 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:33:25.642 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:33:35.642 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:33:35.643 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:33:45.626 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:33:45.627 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:34:07.388 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:34:07.388 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:34:15.633 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:34:15.633 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:34:25.628 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:34:25.628 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:34:35.639 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:34:35.639 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:34:45.634 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:34:45.634 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:34:55.622 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:34:55.622 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:35:05.643 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:35:05.643 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:35:43.631 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:35:43.631 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:36:43.620 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:36:43.620 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:37:02.465 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:37:02.466 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:37:05.627 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:37:05.627 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:37:15.616 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:37:15.616 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:37:25.623 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:37:25.623 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:37:35.628 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:37:35.628 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:37:45.625 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:37:45.625 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:37:55.626 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:37:55.626 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:38:43.613 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:38:43.613 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:39:43.622 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:39:43.623 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:39:54.608 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:39:54.608 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:40:05.078 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:40:05.079 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:40:14.825 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:40:14.826 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:40:24.686 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:40:24.686 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:40:32.918 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:40:34.583 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:40:36.833 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:40:37.120 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:40:45.622 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:40:46.059 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:40:56.320 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:40:56.321 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:40:56.641 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:40:57.352 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:41:07.618 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:41:07.618 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:41:17.435 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:41:17.436 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:41:27.431 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:41:27.432 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:41:31.568 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:41:31.847 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:41:42.618 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:41:42.618 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:41:48.306 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:41:48.474 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:41:59.246 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:41:59.246 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:42:02.733 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:42:24.000 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:42:27.953 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:43:30.017 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:43:40.605 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:43:40.605 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:43:50.610 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:43:50.610 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:44:00.108 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:44:00.108 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:44:01.831 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:44:02.122 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:44:12.606 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:44:12.606 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:44:14.229 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出1-1, 当前在线人数为1
10:44:22.415 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:44:22.415 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:44:24.034 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为0
10:44:24.210 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为1
10:44:29.847 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接1-1, 当前在线人数为2
10:44:33.427 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:44:34.932 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:44:45.313 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:44:45.314 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:44:46.663 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:44:48.167 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:44:56.907 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出1-1, 当前在线人数为1
10:44:58.503 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:44:58.503 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:45:00.970 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为0
10:45:01.878 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:45:01.881 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:45:02.223 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:45:02.223 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@76aaa359[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:45:02.224 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750817207315_127.0.0.1_9893
10:45:02.228 [nacos-grpc-client-executor-464] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750817207315_127.0.0.1_9893]Ignore complete event,isRunning:false,isAbandon=false
10:45:02.230 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@828987d[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 465]
10:45:02.377 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:45:02.380 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:45:02.386 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:45:02.386 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:45:16.936 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:45:17.549 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 878529de-1c53-4606-bc64-058e7c87fd60_config-0
10:45:17.631 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
10:45:17.663 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
10:45:17.672 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
10:45:17.681 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
10:45:17.687 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
10:45:17.694 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
10:45:17.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:45:17.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001dd2e3c2cc8
10:45:17.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001dd2e3c2ee8
10:45:17.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:45:17.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:45:17.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:45:18.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750819518311_127.0.0.1_14167
10:45:18.493 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] Notify connected event to listeners.
10:45:18.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:45:18.497 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001dd2e4fc200
10:45:18.625 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:45:21.388 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:45:21.389 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:45:21.389 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:45:21.565 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:45:22.130 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:45:22.132 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:45:22.133 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:45:27.029 [main] INFO  c.h.s.w.RedisMessageConfig - [redisMessageListenerContainer,32] - Redis消息监听器配置完成
10:45:27.308 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:45:29.919 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 13c9f894-3224-4b7a-97a2-dccaf88046a7
10:45:29.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] RpcClient init label, labels = {module=naming, source=sdk}
10:45:29.920 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:45:29.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:45:29.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:45:29.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:45:30.041 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Success to connect to server [localhost:8848] on start up, connectionId = 1750819529930_127.0.0.1_14186
10:45:30.041 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:45:30.041 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001dd2e4fc200
10:45:30.042 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Notify connected event to listeners.
10:45:30.087 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:45:30.115 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:45:30.621 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Receive server push request, request = NotifySubscriberRequest, requestId = 7
10:45:30.640 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Ack server push request, request = NotifySubscriberRequest, requestId = 7
10:45:30.728 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 14.265 seconds (JVM running for 14.993)
10:45:30.738 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:45:30.740 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:45:30.742 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:45:31.101 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:45:31.676 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为1
10:45:41.674 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:45:41.675 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:45:51.682 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:45:51.684 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:46:01.671 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:46:01.671 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:46:11.675 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:46:11.675 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:46:21.672 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:46:21.672 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:46:31.604 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接1-1, 当前在线人数为2
10:46:31.815 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:46:31.815 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:46:41.670 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:46:41.671 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:46:47.222 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
10:46:48.731 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
10:46:59.095 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:46:59.096 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:47:09.086 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:47:09.086 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:47:19.086 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:47:19.086 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:47:29.604 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:47:29.605 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:47:39.590 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:47:39.596 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:47:49.600 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:47:49.600 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:47:59.596 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:47:59.596 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:48:09.595 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:48:09.596 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:48:19.592 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:48:19.592 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:48:27.774 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出1-1, 当前在线人数为1
10:48:43.591 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:48:43.592 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:49:43.595 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:49:43.596 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:50:11.404 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:50:11.405 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:50:13.089 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为0
10:50:14.689 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为1
10:50:25.588 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:50:25.589 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:50:35.594 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:50:35.595 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:50:38.334 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接1-1, 当前在线人数为2
10:50:45.591 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:50:45.591 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:50:55.586 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:50:55.586 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:51:05.591 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:51:05.591 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:51:15.585 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:51:15.585 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:51:25.585 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:51:25.585 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:51:35.622 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:51:35.623 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:51:45.900 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:51:45.901 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:51:55.596 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:51:55.598 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:52:43.583 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:52:43.583 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:53:43.600 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:53:43.600 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:53:58.438 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:53:58.438 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:54:05.012 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:54:05.012 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:54:15.035 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:54:15.035 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:54:25.581 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:54:25.581 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:54:35.018 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:54:35.018 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:54:45.016 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:54:45.016 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:54:55.009 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:54:55.010 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:55:05.009 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:55:05.009 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:55:15.016 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:55:15.016 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:55:22.638 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出1-1, 当前在线人数为1
10:55:25.576 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:55:25.576 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:55:35.582 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:55:35.582 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:55:45.578 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:55:45.579 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:55:55.574 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:55:55.574 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:56:05.571 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:56:05.571 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:56:15.016 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:56:15.016 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:56:25.011 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:56:25.011 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:56:35.009 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:56:35.010 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:56:45.005 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:56:45.005 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:56:55.003 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:56:55.003 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:57:05.574 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:57:05.574 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:57:15.581 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:57:15.581 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:57:25.579 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:57:25.579 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:57:35.572 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:57:35.572 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:57:44.997 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:57:44.997 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:57:49.337 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为0
10:57:51.045 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为1
10:58:01.566 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:58:01.566 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:58:11.477 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:58:11.477 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:58:21.566 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:58:21.566 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:58:31.567 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:58:31.567 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:58:41.475 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:58:41.475 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:58:51.562 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:58:51.562 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:59:01.565 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:59:01.565 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:59:11.484 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:59:11.485 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:59:21.480 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:59:21.481 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:59:31.574 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:59:31.574 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:59:41.584 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:59:41.584 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
10:59:51.567 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
10:59:51.567 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:00:01.576 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:00:01.576 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:00:11.562 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:00:11.562 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:00:21.489 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:00:21.489 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:00:31.571 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:00:31.572 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:00:41.567 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:00:41.568 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:00:46.314 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为0
11:00:47.948 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为1
11:00:58.565 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:00:58.565 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:01:08.564 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:01:08.565 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:01:13.327 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接1-1, 当前在线人数为2
11:01:18.570 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:01:18.570 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:01:28.292 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:01:28.292 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:01:38.564 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:01:38.565 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:01:48.566 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:01:48.566 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:01:58.557 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:01:58.558 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:02:08.570 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:02:08.571 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:02:18.554 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:02:18.554 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:02:28.620 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:02:28.620 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:02:38.561 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:02:38.561 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:02:48.554 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:02:48.554 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:02:58.563 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:02:58.563 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:03:08.599 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:03:08.599 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:03:18.551 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:03:18.552 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:03:28.555 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:03:28.555 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:03:38.559 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:03:38.559 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:03:48.387 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:03:48.387 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:03:58.284 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:03:58.284 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:04:08.303 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:04:08.304 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:04:18.550 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:04:18.554 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:04:28.553 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:04:28.554 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:04:38.561 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:04:38.561 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:04:48.550 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:04:48.551 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:04:58.555 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:04:58.555 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:05:08.555 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:05:08.556 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:05:22.055 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:05:22.055 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:05:28.253 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出1-1, 当前在线人数为1
11:05:28.305 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:05:28.305 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:05:38.564 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:05:38.564 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:05:48.275 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:05:48.275 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:05:58.549 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:05:58.554 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:06:08.563 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:06:08.569 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:06:18.554 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:06:18.555 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:06:28.554 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:06:28.554 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:06:38.565 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:06:38.565 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:06:48.562 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:06:48.562 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:06:50.448 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接1-1, 当前在线人数为2
11:06:58.543 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:06:58.543 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:07:08.265 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:07:08.265 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:07:18.554 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:07:18.554 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:07:28.544 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:07:28.544 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:07:38.540 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:07:38.540 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:07:48.542 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:07:48.543 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:07:58.545 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:07:58.546 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:08:08.549 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:08:08.550 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:08:18.550 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:08:18.550 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:08:28.544 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:08:28.544 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:08:38.539 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:08:38.539 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:08:48.549 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:08:48.549 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:09:43.536 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:09:43.536 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:09:50.935 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:09:50.935 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:09:58.538 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:09:58.538 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:10:08.258 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:10:08.258 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:10:18.262 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:10:18.262 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:10:28.261 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:10:28.262 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:10:38.336 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:10:38.336 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:10:48.323 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:10:48.324 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:10:58.266 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:10:58.266 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:11:08.255 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:11:08.255 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:11:18.253 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:11:18.253 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:11:28.253 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:11:28.253 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:11:38.257 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:11:38.257 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:11:48.268 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:11:48.268 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:11:58.285 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:11:58.285 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:12:08.262 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:12:08.263 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:12:18.253 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:12:18.253 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:12:25.772 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
11:12:27.605 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
11:12:37.760 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:12:37.761 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:12:47.765 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:12:47.765 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:12:57.768 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:12:57.769 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:13:07.765 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:13:07.765 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:13:18.535 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:13:18.536 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:13:28.528 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:13:28.528 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:13:38.531 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:13:38.531 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:13:48.523 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:13:48.523 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:13:58.523 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:13:58.523 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:14:08.522 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:14:08.522 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:14:17.756 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:14:17.756 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:14:27.762 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:14:27.763 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:14:37.763 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:14:37.764 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:14:47.763 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:14:47.763 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:14:57.752 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:14:57.752 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:15:07.767 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:15:07.767 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:15:17.758 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:15:17.758 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:15:27.755 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:15:27.755 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:15:37.764 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:15:37.765 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:15:47.770 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:15:47.770 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:15:57.760 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:15:57.760 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:16:07.749 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:16:07.749 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:16:17.764 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:16:17.764 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:16:27.756 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:16:27.756 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:16:37.761 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:16:37.761 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:16:47.749 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:16:47.749 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:16:57.745 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:16:57.746 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:17:07.758 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:17:07.760 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:17:17.760 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:17:17.760 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:17:27.751 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:17:27.751 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:17:37.753 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:17:37.754 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:17:47.760 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:17:47.760 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:17:58.528 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:17:58.529 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:18:08.532 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:18:08.532 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:18:18.536 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:18:18.536 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:18:28.569 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:18:28.569 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:18:38.530 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:18:38.530 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:18:48.520 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:18:48.521 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:19:43.555 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:19:43.555 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:19:48.519 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:19:48.520 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:19:58.521 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:19:58.521 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:20:08.522 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:20:08.522 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:20:10.162 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出1-1, 当前在线人数为1
11:20:18.526 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:20:18.526 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:20:20.179 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接1-1, 当前在线人数为2
11:20:28.512 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:20:28.513 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:20:38.508 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:20:38.508 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:21:39.506 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:21:39.506 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:22:40.512 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:22:40.512 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:23:41.502 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:23:41.503 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:24:42.502 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:24:42.504 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:25:43.504 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:25:43.504 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:26:24.641 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:26:24.641 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:26:25.710 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
11:26:27.208 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
11:26:37.519 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:26:37.519 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:26:47.527 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:26:47.528 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:26:57.294 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
11:26:58.746 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
11:27:09.126 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:27:09.126 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:27:11.718 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
11:27:12.663 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
11:27:23.015 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:27:23.015 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:27:33.506 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:27:33.506 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:27:43.494 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:27:43.497 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:27:53.027 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:27:53.027 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:27:57.224 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
11:27:58.370 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
11:28:08.687 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:28:08.687 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:28:18.684 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:28:18.684 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:28:28.701 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:28:28.701 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:28:38.693 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:28:38.694 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:28:48.690 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:28:48.690 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:28:58.689 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:28:58.689 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:29:08.695 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:29:08.695 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:29:18.689 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:29:18.689 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:29:28.689 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:29:28.690 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:29:38.700 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:29:38.700 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:29:48.695 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:29:48.697 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:29:58.697 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:29:58.698 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:30:08.703 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:30:08.703 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:30:18.694 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:30:18.694 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:30:28.693 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:30:28.693 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:30:38.689 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:30:38.690 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:30:48.684 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:30:48.684 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:30:58.687 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:30:58.687 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:31:08.682 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:31:08.682 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:31:18.689 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:31:18.689 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:31:28.697 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:31:28.698 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:31:38.690 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:31:38.692 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:31:48.693 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:31:48.693 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:32:01.402 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] Server healthy check fail, currentConnection = 1750819518311_127.0.0.1_14167
11:32:01.402 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:32:01.716 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Server healthy check fail, currentConnection = 1750819529930_127.0.0.1_14186
11:32:01.716 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:32:05.885 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:32:05.885 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:32:06.438 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Success to connect a server [localhost:8848], connectionId = 1750822326110_127.0.0.1_4738
11:32:06.439 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Abandon prev connection, server is localhost:8848, connectionId is 1750819529930_127.0.0.1_14186
11:32:06.439 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750819529930_127.0.0.1_14186
11:32:06.443 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Notify disconnected event to listeners
11:32:06.448 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] Success to connect a server [localhost:8848], connectionId = 1750822326031_127.0.0.1_4737
11:32:06.448 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1750819518311_127.0.0.1_14167
11:32:06.448 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750819518311_127.0.0.1_14167
11:32:06.450 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] Notify disconnected event to listeners
11:32:06.451 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [878529de-1c53-4606-bc64-058e7c87fd60_config-0] Notify connected event to listeners.
11:32:06.445 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Notify connected event to listeners.
11:32:08.689 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:32:08.689 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:32:09.426 [nacos-grpc-client-executor-575] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Receive server push request, request = NotifySubscriberRequest, requestId = 14
11:32:09.426 [nacos-grpc-client-executor-575] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13c9f894-3224-4b7a-97a2-dccaf88046a7] Ack server push request, request = NotifySubscriberRequest, requestId = 14
11:32:18.678 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:32:18.678 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:32:28.694 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:32:28.694 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:32:38.688 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:32:38.688 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:32:48.687 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:32:48.688 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:32:58.687 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:32:58.687 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:33:08.683 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:33:08.683 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:33:18.677 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:33:18.679 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:33:28.676 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:33:28.676 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:33:38.676 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:33:38.676 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:33:48.675 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:33:48.675 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:33:58.685 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:33:58.685 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:34:08.677 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:34:08.677 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:34:18.683 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:34:18.684 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:34:28.687 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:34:28.688 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:34:38.668 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:34:38.668 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:34:48.679 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:34:48.679 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:34:58.679 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:34:58.679 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:35:08.681 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:35:08.681 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:35:18.682 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:35:18.683 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:35:28.669 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:35:28.669 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:35:37.342 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出1-1, 当前在线人数为1
11:35:38.672 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:35:38.672 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:35:43.430 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接1-1, 当前在线人数为2
11:35:48.673 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:35:48.674 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:35:58.678 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:35:58.678 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:36:08.683 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:36:08.683 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:36:18.665 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:36:18.665 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:36:23.772 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
11:36:24.769 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
11:36:35.152 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:36:35.152 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:36:35.916 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
11:36:36.768 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
11:36:37.627 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
11:36:38.443 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
11:36:48.776 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:36:48.776 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:36:56.034 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
11:36:56.871 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
11:37:05.160 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
11:37:06.054 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
11:37:16.476 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:37:16.476 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:37:26.461 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:37:26.462 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:37:36.473 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:37:36.473 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:37:46.479 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:37:46.479 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:37:56.476 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:37:56.476 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:38:06.459 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:38:06.459 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:38:43.466 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:38:43.467 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:39:43.466 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:39:43.466 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:40:43.473 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:40:43.474 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:41:18.742 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出1-1, 当前在线人数为1
11:41:43.455 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:41:43.455 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:41:48.179 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接1-1, 当前在线人数为2
11:42:43.454 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:42:43.455 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:43:03.521 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出1-1, 当前在线人数为1
11:43:03.522 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为0
11:43:03.608 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:43:03.611 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:43:03.930 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:43:03.930 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@32f9e9d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:43:03.931 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750822326110_127.0.0.1_4738
11:43:03.931 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6a1f5f94[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 707]
11:43:04.124 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:43:04.129 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:43:04.145 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:43:04.146 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:43:09.576 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:43:10.313 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 84eddfca-ac43-478d-98ac-f38f4c2cc713_config-0
11:43:10.386 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 3 keys and 6 values 
11:43:10.426 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
11:43:10.436 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:43:10.446 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
11:43:10.458 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
11:43:10.469 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
11:43:10.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84eddfca-ac43-478d-98ac-f38f4c2cc713_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:43:10.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84eddfca-ac43-478d-98ac-f38f4c2cc713_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023a8b39eaf8
11:43:10.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84eddfca-ac43-478d-98ac-f38f4c2cc713_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000023a8b39ed18
11:43:10.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84eddfca-ac43-478d-98ac-f38f4c2cc713_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:43:10.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84eddfca-ac43-478d-98ac-f38f4c2cc713_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:43:10.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84eddfca-ac43-478d-98ac-f38f4c2cc713_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:11.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84eddfca-ac43-478d-98ac-f38f4c2cc713_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750822991300_127.0.0.1_5975
11:43:11.513 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84eddfca-ac43-478d-98ac-f38f4c2cc713_config-0] Notify connected event to listeners.
11:43:11.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84eddfca-ac43-478d-98ac-f38f4c2cc713_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:43:11.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84eddfca-ac43-478d-98ac-f38f4c2cc713_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023a8b518668
11:43:11.632 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:43:15.519 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:43:15.521 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:43:15.521 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:43:15.835 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:43:17.044 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:43:17.047 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:43:17.048 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:43:36.543 [main] INFO  c.h.s.w.RedisMessageConfig - [redisMessageListenerContainer,32] - Redis消息监听器配置完成
11:43:36.988 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:43:41.304 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 79ba078a-99ca-409c-bcfa-4a3d569105ca
11:43:41.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79ba078a-99ca-409c-bcfa-4a3d569105ca] RpcClient init label, labels = {module=naming, source=sdk}
11:43:41.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79ba078a-99ca-409c-bcfa-4a3d569105ca] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:43:41.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79ba078a-99ca-409c-bcfa-4a3d569105ca] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:43:41.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79ba078a-99ca-409c-bcfa-4a3d569105ca] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:43:41.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79ba078a-99ca-409c-bcfa-4a3d569105ca] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:41.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79ba078a-99ca-409c-bcfa-4a3d569105ca] Success to connect to server [localhost:8848] on start up, connectionId = 1750823021324_127.0.0.1_6052
11:43:41.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79ba078a-99ca-409c-bcfa-4a3d569105ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:43:41.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79ba078a-99ca-409c-bcfa-4a3d569105ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023a8b518668
11:43:41.443 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79ba078a-99ca-409c-bcfa-4a3d569105ca] Notify connected event to listeners.
11:43:41.529 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:43:41.593 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:43:41.992 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79ba078a-99ca-409c-bcfa-4a3d569105ca] Receive server push request, request = NotifySubscriberRequest, requestId = 17
11:43:42.020 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79ba078a-99ca-409c-bcfa-4a3d569105ca] Ack server push request, request = NotifySubscriberRequest, requestId = 17
11:43:42.582 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 33.685 seconds (JVM running for 34.81)
11:43:42.627 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:43:42.628 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:43:42.629 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:43:42.943 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:43:43.819 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为1
11:43:52.336 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接1-1, 当前在线人数为2
11:43:54.487 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:43:54.487 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:43:56.618 [redisMessageListenerContainer-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1-1, 报文:{"data":{"fileName":"natapp.exe","uploadedChunks":1,"totalSize":8793600,"@type":"java.util.HashMap","totalChunks":1,"progress":100.0,"fileId":"1750823031677_3rtp74a9z"},"type":"upload_progress","fileId":"1750823031677_3rtp74a9z"}
11:43:56.618 [redisMessageListenerContainer-1] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1-1, fileId: 1750823031677_3rtp74a9z, progress: {fileName=natapp.exe, uploadedChunks=1, totalSize=8793600, @type=java.util.HashMap, totalChunks=1, progress=100.0, fileId=1750823031677_3rtp74a9z}
11:44:04.470 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:44:04.470 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:44:14.460 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:44:14.460 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:44:24.521 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:44:24.521 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:44:34.455 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:44:34.455 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:44:44.488 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:44:44.497 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:45:43.477 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:45:43.478 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:46:43.444 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:46:43.444 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:47:43.451 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:47:43.451 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:48:43.446 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:48:43.446 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:49:43.508 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:49:43.510 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:50:43.432 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:50:43.439 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:51:43.437 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:51:43.438 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:52:43.425 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:52:43.425 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:52:51.166 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
11:53:01.423 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:53:01.423 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:53:11.426 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:53:11.427 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:53:18.236 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
11:53:18.770 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:53:18.770 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:all, 报文:PING
11:53:24.450 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:53:24.450 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:all, 报文:PING
11:53:28.988 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
11:53:34.015 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
11:53:34.212 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
11:53:35.754 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
11:53:46.090 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:53:46.093 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:53:56.539 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:53:56.539 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:54:06.416 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:54:06.416 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:54:16.430 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:54:16.431 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:54:26.539 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:54:26.539 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:54:36.426 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:54:36.426 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:54:46.425 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:54:46.425 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:55:43.435 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:55:43.435 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:56:43.449 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:56:43.449 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:57:43.453 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:57:43.453 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:58:43.464 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:58:43.465 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
11:59:43.466 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
11:59:43.466 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:00:43.476 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:00:43.476 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:01:43.408 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:01:43.409 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:02:43.409 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:02:43.409 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:03:43.396 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:03:43.397 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:04:43.504 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:04:43.505 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:05:43.417 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:05:43.418 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:06:28.466 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:06:28.467 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:06:30.103 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
12:06:31.455 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
12:06:42.401 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:06:42.401 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:06:52.426 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:06:52.427 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:07:02.463 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:07:02.463 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:07:12.397 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:07:12.397 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:07:22.432 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:07:22.433 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:07:32.467 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:07:32.467 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:07:43.398 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:07:43.398 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:08:43.392 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:08:43.392 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:09:43.436 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:09:43.436 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:10:27.154 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接1-1, 当前在线人数为2
12:10:30.268 [redisMessageListenerContainer-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:1-1, 报文:{"data":{"fileName":"natapp.exe","uploadedChunks":1,"totalSize":8793600,"@type":"java.util.HashMap","totalChunks":1,"progress":100.0,"fileId":"1750824627083_gkx1ahx3r"},"type":"upload_progress","fileId":"1750824627083_gkx1ahx3r"}
12:10:30.269 [redisMessageListenerContainer-2] INFO  c.h.s.w.FileUploadWebSocketService - [pushUploadProgress,41] - 推送上传进度成功，userInfo: 1-1, fileId: 1750824627083_gkx1ahx3r, progress: {fileName=natapp.exe, uploadedChunks=1, totalSize=8793600, @type=java.util.HashMap, totalChunks=1, progress=100.0, fileId=1750824627083_gkx1ahx3r}
12:10:43.387 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:10:43.387 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:11:43.453 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:11:43.454 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:12:43.457 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:12:43.458 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:12:51.731 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:12:51.732 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:13:02.401 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:13:02.402 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:13:12.443 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:13:12.443 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:13:22.386 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:13:22.386 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:13:32.406 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:13:32.407 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:13:42.439 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:13:42.440 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:13:52.476 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:13:52.476 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:14:43.371 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:14:43.371 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:15:43.406 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:15:43.407 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:16:43.386 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:16:43.387 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:17:43.364 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:17:43.364 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:18:43.394 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:18:43.395 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:19:43.357 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:19:43.357 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:20:43.415 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:20:43.416 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:21:43.415 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:21:43.416 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:22:43.421 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:22:43.422 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:23:43.346 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:23:43.347 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:24:43.436 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:24:43.437 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:25:43.444 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:25:43.444 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:26:43.351 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:26:43.352 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:27:43.339 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:27:43.340 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:28:43.362 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:28:43.363 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:29:43.367 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:29:43.368 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:30:43.375 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:30:43.375 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:31:43.329 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:31:43.330 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:32:43.336 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:32:43.336 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:33:43.395 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:33:43.396 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:34:43.324 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:34:43.324 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:35:43.411 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:35:43.412 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:36:43.418 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:36:43.418 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:37:43.321 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:37:43.322 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:38:43.319 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:38:43.320 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:39:43.314 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:39:43.315 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:40:43.321 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:40:43.322 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:41:43.351 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:41:43.352 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:42:43.355 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:42:43.355 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:43:43.308 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:43:43.308 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:44:43.306 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:44:43.306 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:45:43.334 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:45:43.335 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:46:43.298 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:46:43.298 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:47:43.290 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:47:43.290 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:48:43.397 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:48:43.399 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:49:43.294 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:49:43.296 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:50:43.310 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:50:43.312 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:51:43.308 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:51:43.309 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:52:43.284 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:52:43.284 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:53:43.332 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:53:43.332 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:54:43.334 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:54:43.334 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:55:43.265 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:55:43.266 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:56:43.350 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:56:43.351 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:57:43.275 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:57:43.276 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:58:43.365 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:58:43.366 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
12:59:43.271 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
12:59:43.271 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:00:43.274 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:00:43.274 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:01:43.275 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:01:43.275 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:02:43.261 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:02:43.262 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:03:43.293 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:03:43.296 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:04:43.251 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:04:43.251 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:05:43.308 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:05:43.308 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:06:43.323 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:06:43.324 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:07:43.241 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:07:43.241 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:08:43.248 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:08:43.250 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:09:43.233 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:09:43.233 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:10:21.004 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:10:21.004 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:10:21.589 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:10:21.591 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:10:23.620 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为1
13:10:25.228 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为2
13:10:36.237 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:10:36.238 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:10:45.302 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:10:45.302 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:10:55.296 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:10:55.296 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:11:05.645 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:11:05.645 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:11:15.288 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:11:15.288 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:11:26.230 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:11:26.231 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:11:36.262 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:11:36.263 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:11:46.240 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:11:46.241 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:11:48.342 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出1-1, 当前在线人数为1
13:11:56.237 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:11:56.237 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:12:06.231 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:12:06.232 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:12:16.236 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:12:16.236 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:12:43.242 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:12:43.242 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:13:43.221 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:13:43.222 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:14:43.232 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:14:43.232 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:15:43.232 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:15:43.232 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:16:43.281 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:16:43.282 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:17:43.235 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:17:43.243 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:18:12.595 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出1-1, 当前在线人数为1
13:18:43.295 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:18:43.295 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:18:54.281 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:18:54.282 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:18:55.583 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:18:55.584 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:19:05.266 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:19:05.266 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:19:16.231 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:19:16.231 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:19:26.230 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:19:26.230 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:19:35.955 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:19:35.955 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:19:46.234 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:19:46.235 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:19:56.213 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:19:56.213 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:20:06.235 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:20:06.237 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:20:16.218 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:20:16.218 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:20:26.214 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:20:26.214 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:20:36.212 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:20:36.212 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:21:37.218 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:21:37.218 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:22:15.321 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:22:15.321 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:22:26.249 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:22:26.249 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:22:36.211 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:22:36.212 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:22:46.205 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:22:46.205 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:22:56.016 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:22:56.016 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:23:06.235 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:23:06.236 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:23:15.363 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:23:15.364 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:23:26.231 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:23:26.231 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:23:36.205 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:23:36.205 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:23:46.298 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:23:46.299 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:23:56.199 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:23:56.199 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:24:06.201 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:24:06.201 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:24:16.234 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:24:16.234 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:24:26.444 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:24:26.445 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:24:35.251 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:24:35.252 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:24:45.251 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:24:45.251 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:24:56.233 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:24:56.234 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:25:06.274 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:25:06.275 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:25:13.648 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为0
13:25:15.116 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,69] - 用户连接undefined-undefined, 当前在线人数为1
13:25:25.448 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:25:25.449 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:25:36.206 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:25:36.206 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:25:46.205 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:25:46.206 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:25:56.219 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:25:56.220 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:26:06.192 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:26:06.193 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:26:16.201 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:26:16.202 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:26:26.192 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:26:26.192 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:26:43.247 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:26:43.248 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:27:19.599 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:27:19.599 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:27:26.200 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:27:26.200 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:27:35.881 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:27:35.882 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:27:46.223 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:27:46.223 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:27:56.230 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:27:56.230 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:28:06.293 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,97] - 用户消息:undefined-undefined,报文:PING
13:28:06.295 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,144] - 发送消息到:undefined-undefined, 报文:PONG
13:28:06.998 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,86] - 用户退出undefined-undefined, 当前在线人数为0
13:28:07.071 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:28:07.090 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:28:07.406 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:28:07.407 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@11a33785[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:28:07.407 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750823021324_127.0.0.1_6052
13:28:07.410 [nacos-grpc-client-executor-1262] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750823021324_127.0.0.1_6052]Ignore complete event,isRunning:false,isAbandon=false
13:28:07.411 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@73c1cf39[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1263]
13:28:07.573 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:28:07.578 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:28:07.590 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:28:07.590 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:28:16.108 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:28:17.165 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ec5254b6-5905-45d4-ab14-7505dfca0fa0_config-0
13:28:17.264 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
13:28:17.312 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
13:28:17.323 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
13:28:17.336 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
13:28:17.349 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
13:28:17.364 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
13:28:17.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5254b6-5905-45d4-ab14-7505dfca0fa0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:28:17.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5254b6-5905-45d4-ab14-7505dfca0fa0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000291a539dd70
13:28:17.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5254b6-5905-45d4-ab14-7505dfca0fa0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000291a539df90
13:28:17.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5254b6-5905-45d4-ab14-7505dfca0fa0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:28:17.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5254b6-5905-45d4-ab14-7505dfca0fa0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:28:17.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5254b6-5905-45d4-ab14-7505dfca0fa0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:28:18.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5254b6-5905-45d4-ab14-7505dfca0fa0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750829298318_127.0.0.1_1640
13:28:18.580 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5254b6-5905-45d4-ab14-7505dfca0fa0_config-0] Notify connected event to listeners.
13:28:18.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5254b6-5905-45d4-ab14-7505dfca0fa0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:28:18.583 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5254b6-5905-45d4-ab14-7505dfca0fa0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000291a5518440
13:28:18.755 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:28:24.010 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:28:24.011 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:28:24.011 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:28:24.258 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:28:25.201 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:28:25.203 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:28:25.203 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:28:34.361 [main] INFO  c.h.s.w.RedisMessageConfig - [redisMessageListenerContainer,32] - Redis消息监听器配置完成
13:28:34.820 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:28:39.354 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 992e1fb4-288b-498c-ac04-1b5bf073ef6c
13:28:39.355 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992e1fb4-288b-498c-ac04-1b5bf073ef6c] RpcClient init label, labels = {module=naming, source=sdk}
13:28:39.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992e1fb4-288b-498c-ac04-1b5bf073ef6c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:28:39.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992e1fb4-288b-498c-ac04-1b5bf073ef6c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:28:39.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992e1fb4-288b-498c-ac04-1b5bf073ef6c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:28:39.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992e1fb4-288b-498c-ac04-1b5bf073ef6c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:28:39.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992e1fb4-288b-498c-ac04-1b5bf073ef6c] Success to connect to server [localhost:8848] on start up, connectionId = 1750829319371_127.0.0.1_1674
13:28:39.500 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992e1fb4-288b-498c-ac04-1b5bf073ef6c] Notify connected event to listeners.
13:28:39.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992e1fb4-288b-498c-ac04-1b5bf073ef6c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:28:39.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992e1fb4-288b-498c-ac04-1b5bf073ef6c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000291a5518440
13:28:39.614 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:28:39.660 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:28:40.137 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992e1fb4-288b-498c-ac04-1b5bf073ef6c] Receive server push request, request = NotifySubscriberRequest, requestId = 18
13:28:40.167 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992e1fb4-288b-498c-ac04-1b5bf073ef6c] Ack server push request, request = NotifySubscriberRequest, requestId = 18
13:28:40.397 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:28:40.514 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:28:40.731 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.533 seconds (JVM running for 26.997)
13:28:40.754 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:28:40.755 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:28:40.756 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:28:46.245 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:28:52.284 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:28:57.313 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:28:59.618 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:29:04.986 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:29:10.251 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:29:16.221 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:29:22.238 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:29:28.224 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:29:34.269 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:29:40.206 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:29:46.242 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:29:52.288 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:29:58.227 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:30:04.254 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:30:10.203 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:30:16.196 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:30:22.246 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:30:28.201 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:30:34.336 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:30:40.213 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:30:46.230 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:30:52.203 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:30:58.199 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:31:04.277 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:31:10.201 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:31:16.196 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:31:22.199 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:31:28.200 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:31:34.195 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:31:40.203 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:31:46.192 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:31:52.195 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:31:58.251 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:32:04.194 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:32:10.221 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:32:16.195 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:32:22.204 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:32:28.189 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:32:34.284 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:32:40.196 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:32:46.240 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:32:52.205 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:32:58.194 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:33:04.233 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:33:10.232 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:33:16.192 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:33:22.211 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:33:28.234 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:33:34.295 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:33:40.237 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:33:46.197 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:33:52.215 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:33:58.183 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:34:04.234 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:34:10.245 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:34:16.197 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:34:22.197 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:34:28.260 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:34:34.300 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:34:40.200 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:34:46.281 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:34:52.246 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:34:58.204 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:35:04.185 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:35:10.181 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:35:16.254 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:35:22.197 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:35:28.179 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:35:34.209 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:35:40.180 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:35:46.288 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:35:52.185 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:35:58.185 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:36:04.186 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:36:10.186 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:36:16.183 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:36:22.199 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:36:28.243 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:36:34.212 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:36:40.238 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:36:46.193 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:36:52.183 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:36:58.180 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:03.889 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:06.770 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:08.015 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:12.119 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:13.227 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:17.168 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:18.256 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:22.239 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:23.296 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:27.262 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:28.330 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:32.303 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:33.368 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:37.404 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:38.427 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:42.442 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:43.453 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:47.496 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:48.498 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:53.176 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:54.219 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:37:59.173 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:00.180 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:05.180 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:06.185 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:11.180 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:12.173 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:17.186 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:18.176 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:23.177 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:24.193 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:29.208 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:30.215 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:35.251 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:36.273 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:41.178 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:42.182 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:47.191 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:48.183 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:53.184 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:54.210 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:38:59.179 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:00.193 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:05.256 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:06.236 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:11.174 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:12.216 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:17.234 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:18.178 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:23.178 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:24.174 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:29.179 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:30.179 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:35.186 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:36.182 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:41.167 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:42.165 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:46.205 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:47.236 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:51.332 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:51.637 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:52.266 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:56.383 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:56.667 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:39:57.372 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:01.469 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:01.738 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:02.415 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:06.499 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:06.780 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:07.514 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:12.174 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:13.171 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:18.229 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:19.165 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:24.203 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:25.175 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:30.167 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:31.202 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:36.087 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:37.001 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:41.327 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:42.169 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:47.172 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:48.181 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:53.166 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:54.206 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:40:59.168 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:00.166 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:05.266 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:06.167 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:11.178 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:12.166 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:17.176 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:18.180 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:23.184 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:24.212 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:29.176 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:30.166 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:35.171 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:36.184 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:41.215 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:42.234 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:47.214 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:48.273 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:53.189 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:54.176 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:41:59.176 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:00.164 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:02.391 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:04.062 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:08.245 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:09.173 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:12.180 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:13.300 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:14.282 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:17.267 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:18.318 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:19.322 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:22.570 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:24.203 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:25.176 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:28.155 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:30.201 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:31.176 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:34.170 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:36.170 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:37.179 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:40.159 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:42.173 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:43.170 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:46.166 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:48.163 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:49.162 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:52.184 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:54.161 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:55.233 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:42:58.172 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:00.176 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:01.176 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:04.164 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:06.159 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:07.163 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:10.193 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:12.235 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:13.163 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:14.501 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:15.213 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:16.872 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:17.276 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:18.270 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:19.619 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:20.269 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:21.978 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:22.300 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:23.311 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:24.652 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:25.297 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:27.157 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:28.162 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:29.175 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:30.186 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:31.155 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:33.154 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:34.160 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:35.159 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:36.159 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:37.168 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:39.154 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:40.164 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:41.167 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:42.162 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:43.166 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:45.154 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:46.234 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:47.171 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:48.184 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:49.211 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:51.168 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:52.180 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:53.188 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:54.159 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:55.254 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:57.235 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:58.225 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:43:59.159 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:00.272 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:01.199 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:03.172 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:04.197 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:05.172 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:06.205 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:06.475 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:07.047 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:08.253 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:09.277 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:10.221 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:11.249 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:11.519 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:12.221 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:13.369 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:14.425 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:15.320 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:16.307 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:16.567 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:17.242 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:18.432 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:19.583 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:21.160 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:21.663 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:22.279 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:23.479 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:25.173 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:26.201 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:26.706 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:27.314 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:28.512 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:30.228 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:31.231 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:31.765 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:32.348 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:33.549 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:35.271 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:36.259 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:36.799 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:37.544 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:38.630 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:41.220 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:42.151 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:43.299 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:44.217 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:47.287 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:48.286 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:49.213 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:50.181 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:53.209 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:54.178 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:55.200 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:56.155 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:44:59.165 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:00.193 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:01.229 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:02.253 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:04.881 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:05.395 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:06.289 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:07.311 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:09.925 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:10.477 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:11.382 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:12.493 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:14.984 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:15.530 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:16.429 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:17.641 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:20.264 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:21.165 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:22.187 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:23.161 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:26.894 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:27.292 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:28.282 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:28.790 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:32.347 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:33.313 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:34.301 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:38.151 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:39.161 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:40.164 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:44.151 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:45.183 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:46.154 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:50.253 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:51.154 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:52.159 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:56.423 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:57.187 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:45:58.161 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:02.150 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:03.146 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:04.160 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:08.002 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:08.203 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:08.509 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:10.151 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:13.647 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:14.156 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:16.149 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:19.254 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:20.210 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:22.338 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:25.622 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:26.241 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:28.167 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:31.154 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:32.195 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:34.289 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:37.049 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:37.420 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:39.380 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:42.199 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:42.636 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:44.463 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:47.224 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:47.693 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:49.484 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:53.297 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:55.165 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:46:59.217 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:01.232 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:05.176 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:07.147 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:11.249 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:13.149 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:17.342 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:19.259 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:23.219 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:25.196 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:29.149 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:31.159 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:35.167 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:37.239 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:41.146 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:43.164 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:47.170 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:49.155 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:53.221 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:55.161 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:47:59.202 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:01.156 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:05.208 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:07.159 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:11.252 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:13.173 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:17.262 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:19.227 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:23.178 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:25.237 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:29.177 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:31.207 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:35.190 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:37.263 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:41.209 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:43.212 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:47.148 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:49.267 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:53.186 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:55.199 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:48:59.181 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:01.206 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:05.232 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:07.273 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:11.156 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:13.145 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:17.326 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:19.271 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:23.294 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:25.490 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:29.184 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:31.150 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:36.043 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:37.192 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:41.222 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:43.250 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:47.260 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:49.155 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:53.152 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:55.428 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:49:59.203 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:01.193 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:06.123 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:07.204 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:12.156 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:13.147 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:18.153 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:19.143 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:24.204 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:25.192 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:30.172 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:31.187 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:36.171 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:37.147 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:42.227 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:43.152 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:48.138 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:49.274 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:54.197 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:50:55.132 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:00.210 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:01.239 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:06.175 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:07.149 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:12.144 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:13.134 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:18.175 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:19.134 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:24.154 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:25.138 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:29.499 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:30.012 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:30.175 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:35.231 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:36.134 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:41.144 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:42.197 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:47.146 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:48.238 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:52.230 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:53.259 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:58.168 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:51:59.170 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:04.212 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:05.199 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:10.154 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:11.171 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:16.142 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:17.216 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:22.237 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:23.131 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:28.150 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:29.144 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:34.142 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:35.135 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:40.133 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:41.144 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:46.171 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:47.249 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:52.236 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:53.162 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:58.171 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:52:59.172 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:04.179 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:05.187 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:10.159 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:11.183 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:16.200 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:17.225 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:22.243 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:23.165 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:28.180 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:29.204 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:34.225 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:35.143 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:40.160 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:41.137 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:46.199 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:47.228 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:52.231 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:53.167 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:58.184 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:53:59.214 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:04.236 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:05.131 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:10.141 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:11.146 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:16.143 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:17.146 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:22.144 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:23.136 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:28.135 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:29.136 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:34.201 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:35.133 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:40.166 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:41.135 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:46.210 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:47.144 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:52.131 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:53.176 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:58.194 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:54:59.234 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:04.133 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:05.154 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:10.169 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:11.132 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:16.130 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:17.231 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:22.152 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:23.176 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:28.196 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:29.233 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:34.136 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:35.130 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:40.180 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:41.196 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:46.134 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:47.139 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:52.158 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:53.123 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:58.197 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:55:59.121 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:04.239 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:05.240 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:10.180 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:11.151 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:16.137 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:17.123 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:22.122 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:23.124 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:28.206 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:29.229 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:34.135 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:35.169 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:40.125 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:41.205 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:46.124 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:47.251 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:52.128 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:53.187 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:58.215 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:56:59.127 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:04.130 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:05.167 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:10.184 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:11.208 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:16.134 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:17.151 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:22.135 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:23.189 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:28.127 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:29.232 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:34.188 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:35.171 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:40.188 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:41.213 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:46.230 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:47.154 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:52.118 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:53.192 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:58.213 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:57:59.246 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:04.153 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:05.127 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:10.120 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:11.225 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:16.132 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:17.129 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:22.118 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:23.122 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:28.212 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:29.237 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:34.155 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:35.193 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:40.122 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:41.125 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:46.124 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:47.125 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:52.140 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:53.168 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:54.805 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:57.173 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:58.193 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:58:59.960 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:02.212 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:03.220 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:05.126 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:07.247 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:08.356 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:10.165 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:12.269 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:13.476 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:15.194 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:18.187 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:19.137 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:21.125 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:24.228 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:25.118 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:27.175 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:30.133 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:31.130 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:33.126 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:36.129 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:37.115 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:39.130 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:42.121 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:43.157 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:45.222 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:48.117 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:49.111 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:51.159 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:54.114 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:55.111 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:57.120 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
13:59:59.455 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:00.145 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:02.143 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:04.476 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:05.181 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:07.183 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:09.505 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:10.218 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:12.209 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:14.524 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:15.245 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:17.271 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:19.554 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:20.273 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:22.298 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:24.584 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:25.306 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:27.321 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:29.620 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:30.358 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:32.350 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:34.656 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:35.400 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:37.393 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:39.700 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:40.427 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:42.424 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:44.731 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:46.150 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:48.109 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:50.146 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:52.126 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:54.124 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:56.126 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:00:58.127 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:00.111 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:02.117 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:03.162 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:05.192 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:07.143 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:08.243 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:10.215 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:12.174 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:14.118 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:16.112 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:18.117 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:20.112 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:22.120 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:24.140 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:26.112 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:28.121 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:30.193 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:31.143 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:33.252 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:35.283 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:36.182 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:38.476 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:40.302 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:41.246 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:43.511 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:45.451 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:46.268 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:48.533 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:50.486 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:51.292 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:53.629 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:55.516 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:56.321 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:01:58.659 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:00.570 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:01.368 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:03.686 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:05.713 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:06.409 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:08.782 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:10.751 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:11.514 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:13.894 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:15.776 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:16.551 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:19.016 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:20.814 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:21.587 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:24.047 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:25.877 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:26.616 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:29.113 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:30.909 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:31.726 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:34.182 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:35.988 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:36.757 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:39.211 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:41.009 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:41.859 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:44.247 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:46.045 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:46.897 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:49.289 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:51.091 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:51.915 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:54.326 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:56.125 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:57.009 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:02:59.359 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:01.214 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:02.059 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:04.395 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:06.251 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:07.099 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:09.429 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:11.300 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:12.169 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:14.444 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:16.336 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:17.212 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:19.581 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:21.388 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:22.244 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:24.662 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:26.425 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:27.263 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:29.787 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:31.442 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:32.308 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:34.811 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:36.476 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:37.326 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:39.837 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:41.500 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:42.381 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:44.900 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:46.530 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:47.413 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:49.923 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:51.570 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:52.445 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:54.959 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:56.613 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:03:57.505 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:00.009 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:01.659 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:02.531 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:05.041 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:06.691 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:07.566 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:10.129 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:11.733 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:12.595 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:15.178 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:16.979 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:17.650 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:20.240 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:22.012 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:22.733 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:25.270 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:27.042 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:27.763 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:30.310 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:32.080 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:32.863 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,87] - 用户退出, 当前在线人数为0
14:04:33.891 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:04:33.898 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:04:34.231 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:04:34.232 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@78c65ae3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:04:34.232 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750829319371_127.0.0.1_1674
14:04:34.235 [nacos-grpc-client-executor-438] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750829319371_127.0.0.1_1674]Ignore complete event,isRunning:false,isAbandon=false
14:04:34.240 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2b67509[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 439]
14:04:34.452 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:04:34.460 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:04:34.485 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:04:34.485 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:04:44.046 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:04:45.041 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4c166eef-0e19-4af5-a96a-b8119aaacbbb_config-0
14:04:45.156 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
14:04:45.209 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
14:04:45.219 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
14:04:45.234 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
14:04:45.245 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:04:45.255 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:04:45.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c166eef-0e19-4af5-a96a-b8119aaacbbb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:04:45.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c166eef-0e19-4af5-a96a-b8119aaacbbb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002ca0139ed38
14:04:45.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c166eef-0e19-4af5-a96a-b8119aaacbbb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002ca0139ef58
14:04:45.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c166eef-0e19-4af5-a96a-b8119aaacbbb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:04:45.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c166eef-0e19-4af5-a96a-b8119aaacbbb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:04:45.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c166eef-0e19-4af5-a96a-b8119aaacbbb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:46.468 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c166eef-0e19-4af5-a96a-b8119aaacbbb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750831486218_127.0.0.1_6024
14:04:46.470 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c166eef-0e19-4af5-a96a-b8119aaacbbb_config-0] Notify connected event to listeners.
14:04:46.470 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c166eef-0e19-4af5-a96a-b8119aaacbbb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:04:46.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c166eef-0e19-4af5-a96a-b8119aaacbbb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002ca01518ad8
14:04:46.649 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:04:51.582 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:04:51.583 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:04:51.583 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:04:51.816 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:04:52.683 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:04:52.685 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:04:52.686 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:05:01.335 [main] INFO  c.h.s.w.RedisMessageConfig - [redisMessageListenerContainer,32] - Redis消息监听器配置完成
14:05:01.766 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:05:06.003 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1bdac499-ca39-487d-b140-08f221a29878
14:05:06.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bdac499-ca39-487d-b140-08f221a29878] RpcClient init label, labels = {module=naming, source=sdk}
14:05:06.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bdac499-ca39-487d-b140-08f221a29878] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:05:06.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bdac499-ca39-487d-b140-08f221a29878] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:05:06.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bdac499-ca39-487d-b140-08f221a29878] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:05:06.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bdac499-ca39-487d-b140-08f221a29878] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:05:06.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bdac499-ca39-487d-b140-08f221a29878] Success to connect to server [localhost:8848] on start up, connectionId = 1750831506021_127.0.0.1_6054
14:05:06.141 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bdac499-ca39-487d-b140-08f221a29878] Notify connected event to listeners.
14:05:06.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bdac499-ca39-487d-b140-08f221a29878] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:05:06.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bdac499-ca39-487d-b140-08f221a29878] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002ca01518ad8
14:05:06.236 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:05:06.290 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:05:06.761 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bdac499-ca39-487d-b140-08f221a29878] Receive server push request, request = NotifySubscriberRequest, requestId = 19
14:05:06.793 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bdac499-ca39-487d-b140-08f221a29878] Ack server push request, request = NotifySubscriberRequest, requestId = 19
14:05:07.271 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.063 seconds (JVM running for 27.214)
14:05:07.292 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:05:07.292 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:05:07.295 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:05:07.765 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:22:11.114 [nacos-grpc-client-executor-213] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bdac499-ca39-487d-b140-08f221a29878] Receive server push request, request = NotifySubscriberRequest, requestId = 30
14:22:11.115 [nacos-grpc-client-executor-213] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bdac499-ca39-487d-b140-08f221a29878] Ack server push request, request = NotifySubscriberRequest, requestId = 30
14:27:01.776 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:27:01.779 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:27:02.114 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:27:02.114 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1389557e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:27:02.115 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750831506021_127.0.0.1_6054
14:27:02.117 [nacos-grpc-client-executor-274] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750831506021_127.0.0.1_6054]Ignore complete event,isRunning:false,isAbandon=false
14:27:02.121 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@47281ffc[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 275]
14:27:02.309 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:27:02.314 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:27:02.334 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:27:02.335 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:27:27.749 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:27:28.870 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0d741752-f6b1-4f08-b3ec-db582cdc00d8_config-0
14:27:28.979 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
14:27:29.033 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:27:29.051 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
14:27:29.065 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:27:29.078 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
14:27:29.092 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
14:27:29.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d741752-f6b1-4f08-b3ec-db582cdc00d8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:27:29.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d741752-f6b1-4f08-b3ec-db582cdc00d8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000016da739dd70
14:27:29.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d741752-f6b1-4f08-b3ec-db582cdc00d8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000016da739df90
14:27:29.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d741752-f6b1-4f08-b3ec-db582cdc00d8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:27:29.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d741752-f6b1-4f08-b3ec-db582cdc00d8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:27:29.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d741752-f6b1-4f08-b3ec-db582cdc00d8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:27:30.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d741752-f6b1-4f08-b3ec-db582cdc00d8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750832850536_127.0.0.1_10015
14:27:30.798 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d741752-f6b1-4f08-b3ec-db582cdc00d8_config-0] Notify connected event to listeners.
14:27:30.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d741752-f6b1-4f08-b3ec-db582cdc00d8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:30.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d741752-f6b1-4f08-b3ec-db582cdc00d8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000016da7517cb0
14:27:30.968 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:27:36.819 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:27:36.820 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:27:36.821 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:27:37.078 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:27:37.947 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:27:37.950 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:27:37.950 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:27:46.973 [main] INFO  c.h.s.w.RedisMessageConfig - [redisMessageListenerContainer,32] - Redis消息监听器配置完成
14:27:47.529 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:27:52.261 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3f46fc1f-b79a-49ff-a4cb-d5010cf8e5b7
14:27:52.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f46fc1f-b79a-49ff-a4cb-d5010cf8e5b7] RpcClient init label, labels = {module=naming, source=sdk}
14:27:52.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f46fc1f-b79a-49ff-a4cb-d5010cf8e5b7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:27:52.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f46fc1f-b79a-49ff-a4cb-d5010cf8e5b7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:27:52.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f46fc1f-b79a-49ff-a4cb-d5010cf8e5b7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:27:52.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f46fc1f-b79a-49ff-a4cb-d5010cf8e5b7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:27:52.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f46fc1f-b79a-49ff-a4cb-d5010cf8e5b7] Success to connect to server [localhost:8848] on start up, connectionId = 1750832872279_127.0.0.1_10067
14:27:52.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f46fc1f-b79a-49ff-a4cb-d5010cf8e5b7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:52.412 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f46fc1f-b79a-49ff-a4cb-d5010cf8e5b7] Notify connected event to listeners.
14:27:52.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f46fc1f-b79a-49ff-a4cb-d5010cf8e5b7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000016da7517cb0
14:27:52.508 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:27:52.549 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:27:53.079 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f46fc1f-b79a-49ff-a4cb-d5010cf8e5b7] Receive server push request, request = NotifySubscriberRequest, requestId = 45
14:27:53.102 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f46fc1f-b79a-49ff-a4cb-d5010cf8e5b7] Ack server push request, request = NotifySubscriberRequest, requestId = 45
14:27:53.936 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 27.085 seconds (JVM running for 28.646)
14:27:53.963 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:27:53.965 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:27:53.967 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:27:54.149 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:28:35.030 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f46fc1f-b79a-49ff-a4cb-d5010cf8e5b7] Receive server push request, request = NotifySubscriberRequest, requestId = 48
14:28:35.032 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f46fc1f-b79a-49ff-a4cb-d5010cf8e5b7] Ack server push request, request = NotifySubscriberRequest, requestId = 48
14:30:00.933 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:30:00.939 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:30:01.252 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:30:01.252 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6164580d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:30:01.252 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750832872279_127.0.0.1_10067
14:30:01.255 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750832872279_127.0.0.1_10067]Ignore complete event,isRunning:false,isAbandon=false
14:30:01.258 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@230b15b0[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 41]
14:30:01.402 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:30:01.406 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:30:01.416 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:30:01.416 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:34:08.495 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:34:09.662 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b2247068-858f-41c7-8265-fb0bdd736811_config-0
14:34:09.780 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
14:34:09.834 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:34:09.846 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:34:09.860 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:34:09.872 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:34:09.887 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
14:34:09.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2247068-858f-41c7-8265-fb0bdd736811_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:34:09.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2247068-858f-41c7-8265-fb0bdd736811_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000227ce3ceaf8
14:34:09.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2247068-858f-41c7-8265-fb0bdd736811_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000227ce3ced18
14:34:09.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2247068-858f-41c7-8265-fb0bdd736811_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:34:09.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2247068-858f-41c7-8265-fb0bdd736811_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:34:09.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2247068-858f-41c7-8265-fb0bdd736811_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:34:11.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2247068-858f-41c7-8265-fb0bdd736811_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750833251014_127.0.0.1_10899
14:34:11.281 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2247068-858f-41c7-8265-fb0bdd736811_config-0] Notify connected event to listeners.
14:34:11.281 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2247068-858f-41c7-8265-fb0bdd736811_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:11.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2247068-858f-41c7-8265-fb0bdd736811_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000227ce508ad8
14:34:11.446 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:34:17.332 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:34:17.333 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:34:17.333 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:34:17.616 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:34:18.600 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:34:18.601 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:34:18.602 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:34:29.488 [main] INFO  c.h.s.w.RedisMessageConfig - [redisMessageListenerContainer,32] - Redis消息监听器配置完成
14:34:30.117 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:34:34.605 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 583f2b6a-67a4-4f3f-99d6-503807e72745
14:34:34.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [583f2b6a-67a4-4f3f-99d6-503807e72745] RpcClient init label, labels = {module=naming, source=sdk}
14:34:34.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [583f2b6a-67a4-4f3f-99d6-503807e72745] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:34:34.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [583f2b6a-67a4-4f3f-99d6-503807e72745] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:34:34.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [583f2b6a-67a4-4f3f-99d6-503807e72745] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:34:34.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [583f2b6a-67a4-4f3f-99d6-503807e72745] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:34:34.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [583f2b6a-67a4-4f3f-99d6-503807e72745] Success to connect to server [localhost:8848] on start up, connectionId = 1750833274626_127.0.0.1_11004
14:34:34.743 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [583f2b6a-67a4-4f3f-99d6-503807e72745] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:34.743 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [583f2b6a-67a4-4f3f-99d6-503807e72745] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000227ce508ad8
14:34:34.743 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [583f2b6a-67a4-4f3f-99d6-503807e72745] Notify connected event to listeners.
14:34:34.827 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:34:34.874 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:34:35.298 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [583f2b6a-67a4-4f3f-99d6-503807e72745] Receive server push request, request = NotifySubscriberRequest, requestId = 59
14:34:35.321 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [583f2b6a-67a4-4f3f-99d6-503807e72745] Ack server push request, request = NotifySubscriberRequest, requestId = 59
14:34:35.890 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 28.259 seconds (JVM running for 29.745)
14:34:35.911 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:34:35.912 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:34:35.913 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:34:36.415 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:35:33.010 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [583f2b6a-67a4-4f3f-99d6-503807e72745] Receive server push request, request = NotifySubscriberRequest, requestId = 65
14:35:33.012 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [583f2b6a-67a4-4f3f-99d6-503807e72745] Ack server push request, request = NotifySubscriberRequest, requestId = 65
14:36:52.381 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:36:52.385 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:36:52.693 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:36:52.694 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3d5e9a58[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:36:52.694 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750833274626_127.0.0.1_11004
14:36:52.696 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750833274626_127.0.0.1_11004]Ignore complete event,isRunning:false,isAbandon=false
14:36:52.698 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6eb49da2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 41]
14:36:52.857 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:36:52.862 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:36:52.879 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:36:52.879 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:40:07.918 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:40:09.048 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c6095c09-5270-4644-b290-1062af76d3e6_config-0
14:40:09.139 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
14:40:09.206 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
14:40:09.221 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
14:40:09.239 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
14:40:09.255 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
14:40:09.277 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
14:40:09.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6095c09-5270-4644-b290-1062af76d3e6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:40:09.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6095c09-5270-4644-b290-1062af76d3e6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001478239dd70
14:40:09.288 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6095c09-5270-4644-b290-1062af76d3e6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001478239df90
14:40:09.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6095c09-5270-4644-b290-1062af76d3e6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:40:09.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6095c09-5270-4644-b290-1062af76d3e6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:40:09.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6095c09-5270-4644-b290-1062af76d3e6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:40:10.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6095c09-5270-4644-b290-1062af76d3e6_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750833610425_127.0.0.1_11810
14:40:10.683 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6095c09-5270-4644-b290-1062af76d3e6_config-0] Notify connected event to listeners.
14:40:10.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6095c09-5270-4644-b290-1062af76d3e6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:40:10.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6095c09-5270-4644-b290-1062af76d3e6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000014782517cb0
14:40:10.842 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:40:16.577 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:40:16.578 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:40:16.578 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:40:16.811 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:40:17.674 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:40:17.674 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:40:17.676 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:40:27.189 [main] INFO  c.h.s.w.RedisMessageConfig - [redisMessageListenerContainer,32] - Redis消息监听器配置完成
14:40:27.689 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:40:32.163 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9c378a8a-e4bd-40fa-a2f3-95837a6311b7
14:40:32.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c378a8a-e4bd-40fa-a2f3-95837a6311b7] RpcClient init label, labels = {module=naming, source=sdk}
14:40:32.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c378a8a-e4bd-40fa-a2f3-95837a6311b7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:40:32.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c378a8a-e4bd-40fa-a2f3-95837a6311b7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:40:32.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c378a8a-e4bd-40fa-a2f3-95837a6311b7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:40:32.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c378a8a-e4bd-40fa-a2f3-95837a6311b7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:40:32.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c378a8a-e4bd-40fa-a2f3-95837a6311b7] Success to connect to server [localhost:8848] on start up, connectionId = 1750833632174_127.0.0.1_11895
14:40:32.299 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c378a8a-e4bd-40fa-a2f3-95837a6311b7] Notify connected event to listeners.
14:40:32.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c378a8a-e4bd-40fa-a2f3-95837a6311b7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:40:32.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c378a8a-e4bd-40fa-a2f3-95837a6311b7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000014782517cb0
14:40:32.372 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:40:32.410 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:40:32.929 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c378a8a-e4bd-40fa-a2f3-95837a6311b7] Receive server push request, request = NotifySubscriberRequest, requestId = 73
14:40:32.957 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c378a8a-e4bd-40fa-a2f3-95837a6311b7] Ack server push request, request = NotifySubscriberRequest, requestId = 73
14:40:33.456 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.445 seconds (JVM running for 28.061)
14:40:33.478 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:40:33.478 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:40:33.480 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:40:33.841 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:43:35.761 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c378a8a-e4bd-40fa-a2f3-95837a6311b7] Receive server push request, request = NotifySubscriberRequest, requestId = 79
14:43:35.762 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c378a8a-e4bd-40fa-a2f3-95837a6311b7] Ack server push request, request = NotifySubscriberRequest, requestId = 79
14:53:01.319 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:53:01.331 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:53:01.660 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:53:01.660 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7072d934[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:53:01.660 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750833632174_127.0.0.1_11895
14:53:01.664 [nacos-grpc-client-executor-162] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750833632174_127.0.0.1_11895]Ignore complete event,isRunning:false,isAbandon=false
14:53:01.666 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@60a22f74[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 163]
14:53:02.517 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:53:02.520 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:53:02.531 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:53:02.532 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:53:12.882 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:53:13.969 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2f7ef8e1-a756-41f0-9b07-1a28880aea12_config-0
14:53:14.081 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
14:53:14.134 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:53:14.146 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
14:53:14.160 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:53:14.176 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
14:53:14.192 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
14:53:14.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7ef8e1-a756-41f0-9b07-1a28880aea12_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:53:14.198 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7ef8e1-a756-41f0-9b07-1a28880aea12_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d1c039eaf8
14:53:14.198 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7ef8e1-a756-41f0-9b07-1a28880aea12_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001d1c039ed18
14:53:14.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7ef8e1-a756-41f0-9b07-1a28880aea12_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:53:14.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7ef8e1-a756-41f0-9b07-1a28880aea12_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:53:14.218 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7ef8e1-a756-41f0-9b07-1a28880aea12_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:53:15.532 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7ef8e1-a756-41f0-9b07-1a28880aea12_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750834395271_127.0.0.1_14212
14:53:15.534 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7ef8e1-a756-41f0-9b07-1a28880aea12_config-0] Notify connected event to listeners.
14:53:15.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7ef8e1-a756-41f0-9b07-1a28880aea12_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:53:15.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7ef8e1-a756-41f0-9b07-1a28880aea12_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d1c0518ad8
14:53:15.725 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:53:20.754 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:53:20.756 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:53:20.756 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:53:21.012 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:53:21.875 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:53:21.877 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:53:21.877 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:53:30.431 [main] INFO  c.h.s.w.RedisMessageConfig - [redisMessageListenerContainer,32] - Redis消息监听器配置完成
14:53:30.893 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:53:34.981 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c0bb5325-87e3-489f-9677-014d2fcc3adb
14:53:34.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0bb5325-87e3-489f-9677-014d2fcc3adb] RpcClient init label, labels = {module=naming, source=sdk}
14:53:34.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0bb5325-87e3-489f-9677-014d2fcc3adb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:53:34.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0bb5325-87e3-489f-9677-014d2fcc3adb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:53:34.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0bb5325-87e3-489f-9677-014d2fcc3adb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:53:34.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0bb5325-87e3-489f-9677-014d2fcc3adb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:53:35.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0bb5325-87e3-489f-9677-014d2fcc3adb] Success to connect to server [localhost:8848] on start up, connectionId = 1750834415000_127.0.0.1_14248
14:53:35.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0bb5325-87e3-489f-9677-014d2fcc3adb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:53:35.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0bb5325-87e3-489f-9677-014d2fcc3adb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d1c0518ad8
14:53:35.131 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0bb5325-87e3-489f-9677-014d2fcc3adb] Notify connected event to listeners.
14:53:35.227 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:53:35.282 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:53:35.751 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0bb5325-87e3-489f-9677-014d2fcc3adb] Receive server push request, request = NotifySubscriberRequest, requestId = 81
14:53:35.772 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0bb5325-87e3-489f-9677-014d2fcc3adb] Ack server push request, request = NotifySubscriberRequest, requestId = 81
14:53:36.357 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.318 seconds (JVM running for 25.722)
14:53:36.379 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:53:36.380 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:53:36.381 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:53:36.578 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:56:16.354 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:56:16.358 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:56:16.689 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:56:16.690 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3af81b71[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:56:16.690 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750834415000_127.0.0.1_14248
14:56:16.693 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750834415000_127.0.0.1_14248]Ignore complete event,isRunning:false,isAbandon=false
14:56:16.696 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@12f5493d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 43]
14:56:16.857 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:56:16.861 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:56:16.871 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:56:16.872 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:57:47.886 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:57:48.922 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6c995775-6427-43c3-9d54-52c415274a75_config-0
14:57:49.020 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 51 ms to scan 1 urls, producing 3 keys and 6 values 
14:57:49.078 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
14:57:49.093 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
14:57:49.108 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
14:57:49.120 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
14:57:49.138 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
14:57:49.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c995775-6427-43c3-9d54-52c415274a75_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:57:49.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c995775-6427-43c3-9d54-52c415274a75_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001a92e3b6af8
14:57:49.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c995775-6427-43c3-9d54-52c415274a75_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001a92e3b6d18
14:57:49.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c995775-6427-43c3-9d54-52c415274a75_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:57:49.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c995775-6427-43c3-9d54-52c415274a75_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:57:49.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c995775-6427-43c3-9d54-52c415274a75_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:57:50.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c995775-6427-43c3-9d54-52c415274a75_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750834670123_127.0.0.1_14787
14:57:50.394 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c995775-6427-43c3-9d54-52c415274a75_config-0] Notify connected event to listeners.
14:57:50.395 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c995775-6427-43c3-9d54-52c415274a75_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:57:50.396 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c995775-6427-43c3-9d54-52c415274a75_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001a92e4f0ad8
14:57:50.587 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:57:55.592 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:57:55.593 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:57:55.593 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:57:55.840 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:57:56.793 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:57:56.795 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:57:56.795 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:58:05.708 [main] INFO  c.h.s.w.RedisMessageConfig - [redisMessageListenerContainer,32] - Redis消息监听器配置完成
14:58:06.181 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:58:11.587 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2f43a23e-fcfa-41b5-8334-5b024a3b6e23
14:58:11.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f43a23e-fcfa-41b5-8334-5b024a3b6e23] RpcClient init label, labels = {module=naming, source=sdk}
14:58:11.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f43a23e-fcfa-41b5-8334-5b024a3b6e23] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:58:11.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f43a23e-fcfa-41b5-8334-5b024a3b6e23] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:58:11.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f43a23e-fcfa-41b5-8334-5b024a3b6e23] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:58:11.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f43a23e-fcfa-41b5-8334-5b024a3b6e23] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:58:11.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f43a23e-fcfa-41b5-8334-5b024a3b6e23] Success to connect to server [localhost:8848] on start up, connectionId = 1750834691599_127.0.0.1_14845
14:58:11.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f43a23e-fcfa-41b5-8334-5b024a3b6e23] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:58:11.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f43a23e-fcfa-41b5-8334-5b024a3b6e23] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001a92e4f0ad8
14:58:11.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f43a23e-fcfa-41b5-8334-5b024a3b6e23] Notify connected event to listeners.
14:58:11.788 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:58:11.834 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:58:12.331 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f43a23e-fcfa-41b5-8334-5b024a3b6e23] Receive server push request, request = NotifySubscriberRequest, requestId = 102
14:58:12.352 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f43a23e-fcfa-41b5-8334-5b024a3b6e23] Ack server push request, request = NotifySubscriberRequest, requestId = 102
14:58:12.969 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.904 seconds (JVM running for 27.27)
14:58:12.995 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:58:12.996 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:58:12.997 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:59:20.107 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:04:24.229 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:04:24.231 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:29:41.337 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:29:41.342 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:29:41.682 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:29:41.683 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@570fd7ce[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:29:41.683 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750834691599_127.0.0.1_14845
16:29:41.693 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@10edf95d[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 1110]
16:29:42.079 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:29:42.087 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:29:42.107 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:29:42.108 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:29:42.111 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:29:42.111 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
