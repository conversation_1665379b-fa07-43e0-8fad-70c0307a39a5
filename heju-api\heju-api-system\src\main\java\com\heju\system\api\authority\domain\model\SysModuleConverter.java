package com.heju.system.api.authority.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.api.authority.domain.dto.SysModuleDto;
import com.heju.system.api.authority.domain.po.SysModulePo;
import com.heju.system.api.authority.domain.query.SysModuleQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 模块 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysModuleConverter extends BaseConverter<SysModuleQuery, SysModuleDto, SysModulePo> {
}
