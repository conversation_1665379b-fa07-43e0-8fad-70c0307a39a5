package com.heju.system.api.authority.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.api.authority.domain.dto.SysRoleGroupDto;
import com.heju.system.api.authority.domain.po.SysRoleGroupPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:51+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysRoleGroupConverterImpl implements SysRoleGroupConverter {

    @Override
    public SysRoleGroupDto mapperDto(SysRoleGroupPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysRoleGroupDto sysRoleGroupDto = new SysRoleGroupDto();

        sysRoleGroupDto.setSourceName( arg0.getSourceName() );
        sysRoleGroupDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysRoleGroupDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysRoleGroupDto.setName( arg0.getName() );
        sysRoleGroupDto.setStatus( arg0.getStatus() );
        sysRoleGroupDto.setSort( arg0.getSort() );
        sysRoleGroupDto.setRemark( arg0.getRemark() );
        sysRoleGroupDto.setCreateBy( arg0.getCreateBy() );
        sysRoleGroupDto.setCreateTime( arg0.getCreateTime() );
        sysRoleGroupDto.setUpdateBy( arg0.getUpdateBy() );
        sysRoleGroupDto.setUpdateTime( arg0.getUpdateTime() );
        sysRoleGroupDto.setDelFlag( arg0.getDelFlag() );
        sysRoleGroupDto.setCreateName( arg0.getCreateName() );
        sysRoleGroupDto.setUpdateName( arg0.getUpdateName() );
        sysRoleGroupDto.setId( arg0.getId() );
        sysRoleGroupDto.setRoleGroupName( arg0.getRoleGroupName() );
        sysRoleGroupDto.setUserId( arg0.getUserId() );

        return sysRoleGroupDto;
    }

    @Override
    public List<SysRoleGroupDto> mapperDto(Collection<SysRoleGroupPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysRoleGroupDto> list = new ArrayList<SysRoleGroupDto>( arg0.size() );
        for ( SysRoleGroupPo sysRoleGroupPo : arg0 ) {
            list.add( mapperDto( sysRoleGroupPo ) );
        }

        return list;
    }

    @Override
    public Page<SysRoleGroupDto> mapperPageDto(Collection<SysRoleGroupPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysRoleGroupDto> page = new Page<SysRoleGroupDto>();
        for ( SysRoleGroupPo sysRoleGroupPo : arg0 ) {
            page.add( mapperDto( sysRoleGroupPo ) );
        }

        return page;
    }

    @Override
    public SysRoleGroupPo mapperPo(SysRoleGroupDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysRoleGroupPo sysRoleGroupPo = new SysRoleGroupPo();

        sysRoleGroupPo.setSourceName( arg0.getSourceName() );
        sysRoleGroupPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysRoleGroupPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysRoleGroupPo.setName( arg0.getName() );
        sysRoleGroupPo.setStatus( arg0.getStatus() );
        sysRoleGroupPo.setSort( arg0.getSort() );
        sysRoleGroupPo.setRemark( arg0.getRemark() );
        sysRoleGroupPo.setCreateBy( arg0.getCreateBy() );
        sysRoleGroupPo.setCreateTime( arg0.getCreateTime() );
        sysRoleGroupPo.setUpdateBy( arg0.getUpdateBy() );
        sysRoleGroupPo.setUpdateTime( arg0.getUpdateTime() );
        sysRoleGroupPo.setDelFlag( arg0.getDelFlag() );
        sysRoleGroupPo.setCreateName( arg0.getCreateName() );
        sysRoleGroupPo.setUpdateName( arg0.getUpdateName() );
        sysRoleGroupPo.setId( arg0.getId() );
        sysRoleGroupPo.setRoleGroupName( arg0.getRoleGroupName() );
        sysRoleGroupPo.setUserId( arg0.getUserId() );

        return sysRoleGroupPo;
    }

    @Override
    public List<SysRoleGroupPo> mapperPo(Collection<SysRoleGroupDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysRoleGroupPo> list = new ArrayList<SysRoleGroupPo>( arg0.size() );
        for ( SysRoleGroupDto sysRoleGroupDto : arg0 ) {
            list.add( mapperPo( sysRoleGroupDto ) );
        }

        return list;
    }

    @Override
    public Page<SysRoleGroupPo> mapperPagePo(Collection<SysRoleGroupDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysRoleGroupPo> page = new Page<SysRoleGroupPo>();
        for ( SysRoleGroupDto sysRoleGroupDto : arg0 ) {
            page.add( mapperPo( sysRoleGroupDto ) );
        }

        return page;
    }
}
