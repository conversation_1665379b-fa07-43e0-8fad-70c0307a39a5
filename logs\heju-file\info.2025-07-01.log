13:26:52.270 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:26:53.957 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f721678f-d9fa-46e7-9e48-b6ef614f18c4_config-0
13:26:54.158 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 88 ms to scan 1 urls, producing 3 keys and 6 values 
13:26:54.219 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 4 keys and 9 values 
13:26:54.238 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 3 keys and 10 values 
13:26:54.259 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 5 values 
13:26:54.280 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 1 keys and 7 values 
13:26:54.304 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
13:26:54.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f721678f-d9fa-46e7-9e48-b6ef614f18c4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:26:54.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f721678f-d9fa-46e7-9e48-b6ef614f18c4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001b4523b3b48
13:26:54.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f721678f-d9fa-46e7-9e48-b6ef614f18c4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001b4523b3d68
13:26:54.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f721678f-d9fa-46e7-9e48-b6ef614f18c4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:26:54.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f721678f-d9fa-46e7-9e48-b6ef614f18c4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:26:54.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f721678f-d9fa-46e7-9e48-b6ef614f18c4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:26:56.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f721678f-d9fa-46e7-9e48-b6ef614f18c4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751347615716_127.0.0.1_11367
13:26:56.004 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f721678f-d9fa-46e7-9e48-b6ef614f18c4_config-0] Notify connected event to listeners.
13:26:56.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f721678f-d9fa-46e7-9e48-b6ef614f18c4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:26:56.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f721678f-d9fa-46e7-9e48-b6ef614f18c4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001b4524efb88
13:26:56.204 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:27:03.086 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
13:27:03.087 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:27:03.088 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:27:03.709 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:27:08.584 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:27:14.468 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f95180c4-e174-44d0-8719-a6cce40a5d34
13:27:14.470 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f95180c4-e174-44d0-8719-a6cce40a5d34] RpcClient init label, labels = {module=naming, source=sdk}
13:27:14.478 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f95180c4-e174-44d0-8719-a6cce40a5d34] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:27:14.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f95180c4-e174-44d0-8719-a6cce40a5d34] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:27:14.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f95180c4-e174-44d0-8719-a6cce40a5d34] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:27:14.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f95180c4-e174-44d0-8719-a6cce40a5d34] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:27:14.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f95180c4-e174-44d0-8719-a6cce40a5d34] Success to connect to server [localhost:8848] on start up, connectionId = 1751347634512_127.0.0.1_11400
13:27:14.642 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f95180c4-e174-44d0-8719-a6cce40a5d34] Notify connected event to listeners.
13:27:14.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f95180c4-e174-44d0-8719-a6cce40a5d34] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:27:14.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f95180c4-e174-44d0-8719-a6cce40a5d34] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001b4524efb88
13:27:14.749 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
13:27:14.794 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
13:27:15.023 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 24.815 seconds (JVM running for 29.772)
13:27:15.042 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
13:27:15.043 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
13:27:15.047 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
13:27:15.299 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f95180c4-e174-44d0-8719-a6cce40a5d34] Receive server push request, request = NotifySubscriberRequest, requestId = 5
13:27:15.323 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f95180c4-e174-44d0-8719-a6cce40a5d34] Ack server push request, request = NotifySubscriberRequest, requestId = 5
21:23:31.732 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
21:23:31.738 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
21:23:32.071 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
21:23:32.071 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4262e493[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
21:23:32.071 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751347634512_127.0.0.1_11400
21:23:32.074 [nacos-grpc-client-executor-5723] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751347634512_127.0.0.1_11400]Ignore complete event,isRunning:false,isAbandon=false
21:23:32.080 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@302beb4f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5724]
