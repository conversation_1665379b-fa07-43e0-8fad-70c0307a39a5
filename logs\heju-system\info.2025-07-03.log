09:40:26.405 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:40:27.504 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 25586ea6-50c3-46a0-8ccf-583f238449a4_config-0
09:40:27.635 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 55 ms to scan 1 urls, producing 3 keys and 6 values 
09:40:27.707 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 4 keys and 9 values 
09:40:27.721 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:40:27.739 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:40:27.752 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:40:27.768 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:40:27.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:40:27.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002074539ed38
09:40:27.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002074539ef58
09:40:27.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:40:27.774 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:40:27.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:40:28.830 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:40:28.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:40:28.854 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:40:28.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:40:28.855 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000207454e9000
09:40:28.998 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:29.214 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:29.538 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:29.988 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:30.513 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:30.948 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:40:31.127 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:31.842 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:32.677 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:33.609 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:34.720 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:35.991 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:37.348 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:38.800 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:38.923 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:40:38.924 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:40:38.926 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:40:39.353 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:40:40.359 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:40.847 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:40:40.851 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:40:40.852 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:40:42.010 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:43.847 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:45.712 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:47.601 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:49.600 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:51.709 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:54.123 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:54.764 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:40:56.426 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:40:58.967 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Success to connect a server [localhost:8848], connectionId = 1751506858821_127.0.0.1_14886
09:40:58.968 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25586ea6-50c3-46a0-8ccf-583f238449a4_config-0] Notify connected event to listeners.
09:40:59.426 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a845cdae-d878-4ac1-928c-1cfd6d257ba3
09:40:59.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a845cdae-d878-4ac1-928c-1cfd6d257ba3] RpcClient init label, labels = {module=naming, source=sdk}
09:40:59.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a845cdae-d878-4ac1-928c-1cfd6d257ba3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:40:59.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a845cdae-d878-4ac1-928c-1cfd6d257ba3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:40:59.430 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a845cdae-d878-4ac1-928c-1cfd6d257ba3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:40:59.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a845cdae-d878-4ac1-928c-1cfd6d257ba3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:40:59.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a845cdae-d878-4ac1-928c-1cfd6d257ba3] Success to connect to server [localhost:8848] on start up, connectionId = 1751506859444_127.0.0.1_14894
09:40:59.571 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a845cdae-d878-4ac1-928c-1cfd6d257ba3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:40:59.571 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a845cdae-d878-4ac1-928c-1cfd6d257ba3] Notify connected event to listeners.
09:40:59.571 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a845cdae-d878-4ac1-928c-1cfd6d257ba3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000207454e9000
09:40:59.673 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:40:59.728 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:40:59.949 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 34.48 seconds (JVM running for 36.046)
09:40:59.966 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:40:59.967 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:40:59.969 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:41:00.468 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a845cdae-d878-4ac1-928c-1cfd6d257ba3] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:41:00.488 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a845cdae-d878-4ac1-928c-1cfd6d257ba3] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:41:00.591 [RMI TCP Connection(16)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:43:06.804 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:43:06.804 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:55:00.529 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:55:00.535 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:55:00.865 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:55:00.867 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@371c9885[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:55:00.867 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751506859444_127.0.0.1_14894
09:55:00.868 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3885d839[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 181]
09:55:01.031 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:55:01.037 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
09:55:01.048 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
09:55:01.049 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:55:01.053 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:55:01.053 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:55:06.043 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:55:07.129 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d8433784-905b-4b25-90aa-e501cf459a6e_config-0
09:55:07.229 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
09:55:07.279 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:55:07.291 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:55:07.303 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:55:07.316 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:55:07.333 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
09:55:07.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8433784-905b-4b25-90aa-e501cf459a6e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:55:07.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8433784-905b-4b25-90aa-e501cf459a6e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000264813b6d38
09:55:07.340 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8433784-905b-4b25-90aa-e501cf459a6e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000264813b6f58
09:55:07.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8433784-905b-4b25-90aa-e501cf459a6e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:55:07.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8433784-905b-4b25-90aa-e501cf459a6e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:55:07.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8433784-905b-4b25-90aa-e501cf459a6e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:55:08.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8433784-905b-4b25-90aa-e501cf459a6e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751507708410_127.0.0.1_5522
09:55:08.722 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8433784-905b-4b25-90aa-e501cf459a6e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:55:08.721 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8433784-905b-4b25-90aa-e501cf459a6e_config-0] Notify connected event to listeners.
09:55:08.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8433784-905b-4b25-90aa-e501cf459a6e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000264814f0668
09:55:08.932 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:55:14.571 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:55:14.571 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:55:14.571 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:55:14.811 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:55:15.695 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:55:15.697 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:55:15.697 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:55:26.988 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:55:31.329 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fb616a76-cc78-477b-8f0c-e7cb8f66942d
09:55:31.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb616a76-cc78-477b-8f0c-e7cb8f66942d] RpcClient init label, labels = {module=naming, source=sdk}
09:55:31.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb616a76-cc78-477b-8f0c-e7cb8f66942d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:55:31.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb616a76-cc78-477b-8f0c-e7cb8f66942d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:55:31.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb616a76-cc78-477b-8f0c-e7cb8f66942d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:55:31.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb616a76-cc78-477b-8f0c-e7cb8f66942d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:55:31.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb616a76-cc78-477b-8f0c-e7cb8f66942d] Success to connect to server [localhost:8848] on start up, connectionId = 1751507731350_127.0.0.1_5639
09:55:31.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb616a76-cc78-477b-8f0c-e7cb8f66942d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:55:31.483 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb616a76-cc78-477b-8f0c-e7cb8f66942d] Notify connected event to listeners.
09:55:31.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb616a76-cc78-477b-8f0c-e7cb8f66942d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000264814f0668
09:55:31.563 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:55:31.616 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:55:31.783 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.608 seconds (JVM running for 28.192)
09:55:31.805 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:55:31.805 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:55:31.807 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:55:32.046 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb616a76-cc78-477b-8f0c-e7cb8f66942d] Receive server push request, request = NotifySubscriberRequest, requestId = 24
09:55:32.074 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb616a76-cc78-477b-8f0c-e7cb8f66942d] Ack server push request, request = NotifySubscriberRequest, requestId = 24
09:55:51.547 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:55:53.669 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:55:53.670 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:12:58.612 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:12:58.617 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:12:58.960 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:12:58.961 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1313b559[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:12:58.962 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751507731350_127.0.0.1_5639
10:12:58.964 [nacos-grpc-client-executor-220] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751507731350_127.0.0.1_5639]Ignore complete event,isRunning:false,isAbandon=false
10:12:58.967 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4f5e6cde[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 221]
10:12:59.120 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:12:59.125 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:12:59.135 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:12:59.135 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:12:59.136 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:12:59.136 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:13:07.255 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:13:08.341 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f42575f4-c270-44fe-a0f5-caad2c66aa40_config-0
10:13:08.439 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
10:13:08.507 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
10:13:08.519 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:13:08.532 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
10:13:08.542 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
10:13:08.560 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
10:13:08.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f42575f4-c270-44fe-a0f5-caad2c66aa40_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:13:08.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f42575f4-c270-44fe-a0f5-caad2c66aa40_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000143c139ed38
10:13:08.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f42575f4-c270-44fe-a0f5-caad2c66aa40_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000143c139ef58
10:13:08.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f42575f4-c270-44fe-a0f5-caad2c66aa40_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:13:08.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f42575f4-c270-44fe-a0f5-caad2c66aa40_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:13:08.580 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f42575f4-c270-44fe-a0f5-caad2c66aa40_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:13:09.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f42575f4-c270-44fe-a0f5-caad2c66aa40_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751508789578_127.0.0.1_9113
10:13:09.825 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f42575f4-c270-44fe-a0f5-caad2c66aa40_config-0] Notify connected event to listeners.
10:13:09.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f42575f4-c270-44fe-a0f5-caad2c66aa40_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:13:09.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f42575f4-c270-44fe-a0f5-caad2c66aa40_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000143c1518ad8
10:13:10.026 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:13:15.386 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:13:15.387 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:13:15.387 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:13:15.635 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:13:16.463 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:13:16.465 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:13:16.465 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:13:26.978 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:13:31.047 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7fa4686a-247e-4f74-9a7b-72222f3e9cd8
10:13:31.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa4686a-247e-4f74-9a7b-72222f3e9cd8] RpcClient init label, labels = {module=naming, source=sdk}
10:13:31.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa4686a-247e-4f74-9a7b-72222f3e9cd8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:13:31.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa4686a-247e-4f74-9a7b-72222f3e9cd8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:13:31.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa4686a-247e-4f74-9a7b-72222f3e9cd8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:13:31.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa4686a-247e-4f74-9a7b-72222f3e9cd8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:13:31.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa4686a-247e-4f74-9a7b-72222f3e9cd8] Success to connect to server [localhost:8848] on start up, connectionId = 1751508811062_127.0.0.1_9241
10:13:31.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa4686a-247e-4f74-9a7b-72222f3e9cd8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:13:31.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa4686a-247e-4f74-9a7b-72222f3e9cd8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000143c1518ad8
10:13:31.186 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa4686a-247e-4f74-9a7b-72222f3e9cd8] Notify connected event to listeners.
10:13:31.275 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:13:31.328 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:13:31.495 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.135 seconds (JVM running for 26.613)
10:13:31.520 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:13:31.520 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:13:31.520 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:13:31.758 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa4686a-247e-4f74-9a7b-72222f3e9cd8] Receive server push request, request = NotifySubscriberRequest, requestId = 45
10:13:31.787 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fa4686a-247e-4f74-9a7b-72222f3e9cd8] Ack server push request, request = NotifySubscriberRequest, requestId = 45
10:13:31.983 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:14:13.462 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:14:13.463 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:15:14.495 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:15:14.506 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:15:14.837 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:15:14.837 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2bf0c23e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:15:14.838 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751508811062_127.0.0.1_9241
10:15:14.841 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751508811062_127.0.0.1_9241]Ignore complete event,isRunning:false,isAbandon=false
10:15:14.844 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4c707c3b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 32]
10:15:15.004 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:15:15.009 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:15:15.018 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:15:15.018 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:15:15.020 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:15:15.021 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:15:22.643 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:15:23.703 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c2f84057-150c-422c-9d92-9afdcec97465_config-0
10:15:23.825 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 60 ms to scan 1 urls, producing 3 keys and 6 values 
10:15:23.883 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
10:15:23.897 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
10:15:23.913 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
10:15:23.927 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
10:15:23.944 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
10:15:23.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2f84057-150c-422c-9d92-9afdcec97465_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:15:23.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2f84057-150c-422c-9d92-9afdcec97465_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000209813b6af8
10:15:23.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2f84057-150c-422c-9d92-9afdcec97465_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000209813b6d18
10:15:23.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2f84057-150c-422c-9d92-9afdcec97465_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:15:23.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2f84057-150c-422c-9d92-9afdcec97465_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:15:23.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2f84057-150c-422c-9d92-9afdcec97465_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:15:25.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2f84057-150c-422c-9d92-9afdcec97465_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751508924894_127.0.0.1_9469
10:15:25.171 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2f84057-150c-422c-9d92-9afdcec97465_config-0] Notify connected event to listeners.
10:15:25.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2f84057-150c-422c-9d92-9afdcec97465_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:15:25.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2f84057-150c-422c-9d92-9afdcec97465_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000209814f0668
10:15:25.374 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:15:30.206 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:15:30.207 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:15:30.207 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:15:30.450 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:15:31.289 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:15:31.291 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:15:31.292 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:15:47.001 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:15:54.668 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4919ad6b-1ced-4307-b389-b3e16d3c14ac
10:15:54.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4919ad6b-1ced-4307-b389-b3e16d3c14ac] RpcClient init label, labels = {module=naming, source=sdk}
10:15:54.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4919ad6b-1ced-4307-b389-b3e16d3c14ac] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:15:54.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4919ad6b-1ced-4307-b389-b3e16d3c14ac] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:15:54.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4919ad6b-1ced-4307-b389-b3e16d3c14ac] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:15:54.676 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4919ad6b-1ced-4307-b389-b3e16d3c14ac] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:15:54.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4919ad6b-1ced-4307-b389-b3e16d3c14ac] Success to connect to server [localhost:8848] on start up, connectionId = 1751508954696_127.0.0.1_9617
10:15:54.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4919ad6b-1ced-4307-b389-b3e16d3c14ac] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:15:54.826 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4919ad6b-1ced-4307-b389-b3e16d3c14ac] Notify connected event to listeners.
10:15:54.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4919ad6b-1ced-4307-b389-b3e16d3c14ac] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000209814f0668
10:15:54.945 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:15:55.021 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:15:55.360 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 33.583 seconds (JVM running for 34.953)
10:15:55.393 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:15:55.395 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:15:55.396 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:15:55.450 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4919ad6b-1ced-4307-b389-b3e16d3c14ac] Receive server push request, request = NotifySubscriberRequest, requestId = 60
10:15:55.475 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4919ad6b-1ced-4307-b389-b3e16d3c14ac] Ack server push request, request = NotifySubscriberRequest, requestId = 60
10:18:07.790 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:18:09.712 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:18:09.713 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:32:59.902 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:32:59.905 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:33:00.236 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:33:00.236 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@143c6653[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:33:00.237 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751508954696_127.0.0.1_9617
10:33:00.239 [nacos-grpc-client-executor-215] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751508954696_127.0.0.1_9617]Ignore complete event,isRunning:false,isAbandon=false
10:33:00.242 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@626e8cc1[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 216]
10:33:00.414 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:33:00.419 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:33:00.429 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:33:00.430 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:33:00.431 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:33:00.431 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:33:06.867 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:33:08.110 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 038e9b5e-8cb5-4af2-aafe-9c89277c27b9_config-0
10:33:08.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 60 ms to scan 1 urls, producing 3 keys and 6 values 
10:33:08.305 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
10:33:08.323 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
10:33:08.338 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
10:33:08.351 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
10:33:08.367 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
10:33:08.373 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [038e9b5e-8cb5-4af2-aafe-9c89277c27b9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:33:08.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [038e9b5e-8cb5-4af2-aafe-9c89277c27b9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000022b9c39e480
10:33:08.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [038e9b5e-8cb5-4af2-aafe-9c89277c27b9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000022b9c39e6a0
10:33:08.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [038e9b5e-8cb5-4af2-aafe-9c89277c27b9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:33:08.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [038e9b5e-8cb5-4af2-aafe-9c89277c27b9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:33:08.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [038e9b5e-8cb5-4af2-aafe-9c89277c27b9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:33:09.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [038e9b5e-8cb5-4af2-aafe-9c89277c27b9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751509989588_127.0.0.1_13023
10:33:09.839 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [038e9b5e-8cb5-4af2-aafe-9c89277c27b9_config-0] Notify connected event to listeners.
10:33:09.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [038e9b5e-8cb5-4af2-aafe-9c89277c27b9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:33:09.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [038e9b5e-8cb5-4af2-aafe-9c89277c27b9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022b9c518228
10:33:10.067 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:33:17.426 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:33:17.428 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:33:17.428 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:33:17.867 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:33:19.265 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:33:19.268 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:33:19.268 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:33:32.819 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:33:36.876 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c6ccad6e-46a7-4b64-bcad-3bd3952d335d
10:33:36.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6ccad6e-46a7-4b64-bcad-3bd3952d335d] RpcClient init label, labels = {module=naming, source=sdk}
10:33:36.879 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6ccad6e-46a7-4b64-bcad-3bd3952d335d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:33:36.879 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6ccad6e-46a7-4b64-bcad-3bd3952d335d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:33:36.880 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6ccad6e-46a7-4b64-bcad-3bd3952d335d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:33:36.880 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6ccad6e-46a7-4b64-bcad-3bd3952d335d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:33:37.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6ccad6e-46a7-4b64-bcad-3bd3952d335d] Success to connect to server [localhost:8848] on start up, connectionId = 1751510016894_127.0.0.1_13190
10:33:37.023 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6ccad6e-46a7-4b64-bcad-3bd3952d335d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:33:37.024 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6ccad6e-46a7-4b64-bcad-3bd3952d335d] Notify connected event to listeners.
10:33:37.024 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6ccad6e-46a7-4b64-bcad-3bd3952d335d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022b9c518228
10:33:37.113 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:33:37.170 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:33:37.327 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 31.312 seconds (JVM running for 32.668)
10:33:37.350 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:33:37.351 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:33:37.352 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:33:37.613 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6ccad6e-46a7-4b64-bcad-3bd3952d335d] Receive server push request, request = NotifySubscriberRequest, requestId = 73
10:33:37.642 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6ccad6e-46a7-4b64-bcad-3bd3952d335d] Ack server push request, request = NotifySubscriberRequest, requestId = 73
10:33:37.969 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:34:13.447 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:34:13.447 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:40:01.890 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:40:01.893 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:40:02.229 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:40:02.230 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2c4c8632[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:40:02.230 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751510016894_127.0.0.1_13190
10:40:02.236 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6ec7a587[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 86]
10:40:02.239 [nacos-grpc-client-executor-86] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751510016894_127.0.0.1_13190]Ignore complete event,isRunning:false,isAbandon=false
10:40:02.415 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:40:02.419 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:40:02.432 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:40:02.432 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:40:02.434 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:40:02.435 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:40:09.495 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:40:10.477 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 40b2fb18-bdbd-40ad-9f02-d74298774fc2_config-0
10:40:10.582 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
10:40:10.633 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
10:40:10.645 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:40:10.659 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
10:40:10.676 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
10:40:10.692 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
10:40:10.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40b2fb18-bdbd-40ad-9f02-d74298774fc2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:40:10.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40b2fb18-bdbd-40ad-9f02-d74298774fc2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002055339daf0
10:40:10.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40b2fb18-bdbd-40ad-9f02-d74298774fc2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002055339dd10
10:40:10.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40b2fb18-bdbd-40ad-9f02-d74298774fc2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:40:10.701 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40b2fb18-bdbd-40ad-9f02-d74298774fc2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:40:10.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40b2fb18-bdbd-40ad-9f02-d74298774fc2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:40:12.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40b2fb18-bdbd-40ad-9f02-d74298774fc2_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751510411852_127.0.0.1_14366
10:40:12.120 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40b2fb18-bdbd-40ad-9f02-d74298774fc2_config-0] Notify connected event to listeners.
10:40:12.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40b2fb18-bdbd-40ad-9f02-d74298774fc2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:40:12.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40b2fb18-bdbd-40ad-9f02-d74298774fc2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020553517d88
10:40:12.310 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:40:17.110 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:40:17.111 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:40:17.111 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:40:17.344 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:40:18.188 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:40:18.190 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:40:18.191 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:40:28.442 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:40:32.563 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 07f85e73-f7f2-4102-94b8-9b2afa3d9915
10:40:32.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f85e73-f7f2-4102-94b8-9b2afa3d9915] RpcClient init label, labels = {module=naming, source=sdk}
10:40:32.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f85e73-f7f2-4102-94b8-9b2afa3d9915] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:40:32.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f85e73-f7f2-4102-94b8-9b2afa3d9915] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:40:32.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f85e73-f7f2-4102-94b8-9b2afa3d9915] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:40:32.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f85e73-f7f2-4102-94b8-9b2afa3d9915] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:40:32.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f85e73-f7f2-4102-94b8-9b2afa3d9915] Success to connect to server [localhost:8848] on start up, connectionId = 1751510432582_127.0.0.1_14471
10:40:32.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f85e73-f7f2-4102-94b8-9b2afa3d9915] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:40:32.707 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f85e73-f7f2-4102-94b8-9b2afa3d9915] Notify connected event to listeners.
10:40:32.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f85e73-f7f2-4102-94b8-9b2afa3d9915] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020553517d88
10:40:32.807 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:40:32.851 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:40:33.028 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.363 seconds (JVM running for 25.752)
10:40:33.049 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:40:33.050 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:40:33.051 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:40:33.333 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f85e73-f7f2-4102-94b8-9b2afa3d9915] Receive server push request, request = NotifySubscriberRequest, requestId = 82
10:40:33.355 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07f85e73-f7f2-4102-94b8-9b2afa3d9915] Ack server push request, request = NotifySubscriberRequest, requestId = 82
10:40:33.474 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:40:40.718 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:40:40.718 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:42:58.585 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:42:58.591 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:42:58.918 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:42:58.918 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@42b9c282[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:42:58.918 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751510432582_127.0.0.1_14471
10:42:58.922 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@45fde620[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 39]
10:42:59.055 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:42:59.057 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:42:59.064 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:42:59.064 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:42:59.065 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:42:59.065 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:43:03.598 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:43:04.278 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 27198c92-8d1b-4158-8e3b-efcefb0478d8_config-0
10:43:04.332 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
10:43:04.383 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
10:43:04.396 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:43:04.434 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 1 keys and 5 values 
10:43:04.445 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
10:43:04.454 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
10:43:04.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27198c92-8d1b-4158-8e3b-efcefb0478d8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:43:04.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27198c92-8d1b-4158-8e3b-efcefb0478d8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f3c33cdd70
10:43:04.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27198c92-8d1b-4158-8e3b-efcefb0478d8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001f3c33cdf90
10:43:04.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27198c92-8d1b-4158-8e3b-efcefb0478d8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:43:04.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27198c92-8d1b-4158-8e3b-efcefb0478d8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:43:04.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27198c92-8d1b-4158-8e3b-efcefb0478d8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:43:05.415 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27198c92-8d1b-4158-8e3b-efcefb0478d8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751510585219_127.0.0.1_1262
10:43:05.417 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27198c92-8d1b-4158-8e3b-efcefb0478d8_config-0] Notify connected event to listeners.
10:43:05.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27198c92-8d1b-4158-8e3b-efcefb0478d8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:43:05.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27198c92-8d1b-4158-8e3b-efcefb0478d8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f3c3507b78
10:43:05.529 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:43:08.713 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:43:08.713 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:43:08.714 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:43:08.846 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:43:09.385 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:43:09.388 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:43:09.388 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:43:15.452 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:43:18.506 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1f8d2414-8767-4112-b276-0ec7c1b004b9
10:43:18.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f8d2414-8767-4112-b276-0ec7c1b004b9] RpcClient init label, labels = {module=naming, source=sdk}
10:43:18.507 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f8d2414-8767-4112-b276-0ec7c1b004b9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:43:18.508 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f8d2414-8767-4112-b276-0ec7c1b004b9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:43:18.508 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f8d2414-8767-4112-b276-0ec7c1b004b9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:43:18.508 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f8d2414-8767-4112-b276-0ec7c1b004b9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:43:18.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f8d2414-8767-4112-b276-0ec7c1b004b9] Success to connect to server [localhost:8848] on start up, connectionId = 1751510598516_127.0.0.1_1343
10:43:18.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f8d2414-8767-4112-b276-0ec7c1b004b9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:43:18.644 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f8d2414-8767-4112-b276-0ec7c1b004b9] Notify connected event to listeners.
10:43:18.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f8d2414-8767-4112-b276-0ec7c1b004b9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f3c3507b78
10:43:18.840 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:43:18.911 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:43:19.050 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.012 seconds (JVM running for 16.857)
10:43:19.065 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:43:19.065 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:43:19.066 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:43:19.291 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f8d2414-8767-4112-b276-0ec7c1b004b9] Receive server push request, request = NotifySubscriberRequest, requestId = 89
10:43:19.295 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:43:19.305 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f8d2414-8767-4112-b276-0ec7c1b004b9] Ack server push request, request = NotifySubscriberRequest, requestId = 89
10:43:24.211 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:43:24.211 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:23:38.083 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:23:38.086 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:23:38.422 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:23:38.425 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@25a16878[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:23:38.425 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751510598516_127.0.0.1_1343
11:23:38.428 [nacos-grpc-client-executor-494] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751510598516_127.0.0.1_1343]Ignore complete event,isRunning:false,isAbandon=false
11:23:38.430 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@296c24ea[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 495]
11:23:38.587 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:23:38.592 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:23:38.596 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:23:38.596 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:23:38.598 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:23:38.598 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:23:44.677 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:23:45.385 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 696c434d-ba3c-4264-8790-6acd89ef602b_config-0
11:23:45.448 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 3 keys and 6 values 
11:23:45.494 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
11:23:45.502 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:23:45.509 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:23:45.515 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
11:23:45.528 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
11:23:45.532 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [696c434d-ba3c-4264-8790-6acd89ef602b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:23:45.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [696c434d-ba3c-4264-8790-6acd89ef602b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000020f9a39e8d8
11:23:45.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [696c434d-ba3c-4264-8790-6acd89ef602b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000020f9a39eaf8
11:23:45.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [696c434d-ba3c-4264-8790-6acd89ef602b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:23:45.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [696c434d-ba3c-4264-8790-6acd89ef602b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:23:45.542 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [696c434d-ba3c-4264-8790-6acd89ef602b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:23:46.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [696c434d-ba3c-4264-8790-6acd89ef602b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751513026112_127.0.0.1_1335
11:23:46.307 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [696c434d-ba3c-4264-8790-6acd89ef602b_config-0] Notify connected event to listeners.
11:23:46.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [696c434d-ba3c-4264-8790-6acd89ef602b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:23:46.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [696c434d-ba3c-4264-8790-6acd89ef602b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020f9a518ad8
11:23:46.410 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:23:49.833 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:23:49.833 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:23:49.833 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:23:49.966 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:23:50.585 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:23:50.585 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:23:50.585 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:23:59.951 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:24:06.680 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dc5eca2f-7bb4-4227-a6fe-70bafccff877
11:24:06.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc5eca2f-7bb4-4227-a6fe-70bafccff877] RpcClient init label, labels = {module=naming, source=sdk}
11:24:06.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc5eca2f-7bb4-4227-a6fe-70bafccff877] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:24:06.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc5eca2f-7bb4-4227-a6fe-70bafccff877] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:24:06.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc5eca2f-7bb4-4227-a6fe-70bafccff877] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:24:06.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc5eca2f-7bb4-4227-a6fe-70bafccff877] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:24:06.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc5eca2f-7bb4-4227-a6fe-70bafccff877] Success to connect to server [localhost:8848] on start up, connectionId = 1751513046703_127.0.0.1_1495
11:24:06.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc5eca2f-7bb4-4227-a6fe-70bafccff877] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:24:06.821 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc5eca2f-7bb4-4227-a6fe-70bafccff877] Notify connected event to listeners.
11:24:06.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc5eca2f-7bb4-4227-a6fe-70bafccff877] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020f9a518ad8
11:24:06.881 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:24:06.919 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:24:07.075 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.016 seconds (JVM running for 23.949)
11:24:07.098 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:24:07.098 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:24:07.098 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:24:07.500 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:24:07.673 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc5eca2f-7bb4-4227-a6fe-70bafccff877] Receive server push request, request = NotifySubscriberRequest, requestId = 96
11:24:07.716 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc5eca2f-7bb4-4227-a6fe-70bafccff877] Ack server push request, request = NotifySubscriberRequest, requestId = 96
11:24:30.075 [http-nio-9600-exec-9] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:24:30.076 [http-nio-9600-exec-9] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:45:55.267 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:45:55.275 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:45:55.610 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:45:55.610 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6d639a21[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:45:55.610 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751513046703_127.0.0.1_1495
11:45:55.612 [nacos-grpc-client-executor-271] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751513046703_127.0.0.1_1495]Ignore complete event,isRunning:false,isAbandon=false
11:45:55.616 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@484c2c8[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 272]
11:45:55.770 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:45:55.773 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:45:55.781 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:45:55.781 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:45:55.782 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:45:55.782 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:46:13.467 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:46:16.416 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 79bad9d6-9c87-4c5c-9e97-d34a4871918e_config-0
11:46:16.555 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 64 ms to scan 1 urls, producing 3 keys and 6 values 
11:46:16.652 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 4 keys and 9 values 
11:46:16.672 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
11:46:16.694 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
11:46:16.713 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
11:46:16.732 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
11:46:16.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79bad9d6-9c87-4c5c-9e97-d34a4871918e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:46:16.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79bad9d6-9c87-4c5c-9e97-d34a4871918e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001455c3b6480
11:46:16.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79bad9d6-9c87-4c5c-9e97-d34a4871918e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001455c3b66a0
11:46:16.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79bad9d6-9c87-4c5c-9e97-d34a4871918e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:46:16.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79bad9d6-9c87-4c5c-9e97-d34a4871918e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:46:16.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79bad9d6-9c87-4c5c-9e97-d34a4871918e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:46:18.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79bad9d6-9c87-4c5c-9e97-d34a4871918e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751514378565_127.0.0.1_9335
11:46:18.929 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79bad9d6-9c87-4c5c-9e97-d34a4871918e_config-0] Notify connected event to listeners.
11:46:18.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79bad9d6-9c87-4c5c-9e97-d34a4871918e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:46:18.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79bad9d6-9c87-4c5c-9e97-d34a4871918e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001455c4f0228
11:46:19.136 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:46:25.924 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:46:25.925 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:46:25.926 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:46:26.223 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:46:27.292 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:46:27.295 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:46:27.295 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:46:39.392 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:46:43.670 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 620264c8-4b21-4898-90dd-55306c865ed0
11:46:43.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [620264c8-4b21-4898-90dd-55306c865ed0] RpcClient init label, labels = {module=naming, source=sdk}
11:46:43.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [620264c8-4b21-4898-90dd-55306c865ed0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:46:43.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [620264c8-4b21-4898-90dd-55306c865ed0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:46:43.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [620264c8-4b21-4898-90dd-55306c865ed0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:46:43.676 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [620264c8-4b21-4898-90dd-55306c865ed0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:46:43.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [620264c8-4b21-4898-90dd-55306c865ed0] Success to connect to server [localhost:8848] on start up, connectionId = 1751514403682_127.0.0.1_9499
11:46:43.804 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [620264c8-4b21-4898-90dd-55306c865ed0] Notify connected event to listeners.
11:46:43.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [620264c8-4b21-4898-90dd-55306c865ed0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:46:43.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [620264c8-4b21-4898-90dd-55306c865ed0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001455c4f0228
11:46:43.870 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:46:43.935 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:46:44.078 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 34.397 seconds (JVM running for 41.101)
11:46:44.098 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:46:44.099 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:46:44.101 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:46:44.396 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [620264c8-4b21-4898-90dd-55306c865ed0] Receive server push request, request = NotifySubscriberRequest, requestId = 103
11:46:44.418 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [620264c8-4b21-4898-90dd-55306c865ed0] Ack server push request, request = NotifySubscriberRequest, requestId = 103
11:49:03.762 [http-nio-9600-exec-9] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:49:04.930 [http-nio-9600-exec-9] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:49:04.931 [http-nio-9600-exec-9] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:00:23.157 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:00:23.160 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:00:23.503 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:00:23.504 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@ed6e8a8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:00:23.504 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751514403682_127.0.0.1_9499
12:00:23.506 [nacos-grpc-client-executor-171] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751514403682_127.0.0.1_9499]Ignore complete event,isRunning:false,isAbandon=false
12:00:23.509 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@76b3046c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 172]
12:00:23.679 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:00:23.682 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:00:23.691 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:00:23.691 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:00:23.693 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:00:23.693 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:00:33.014 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:00:35.741 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3aa38952-1a2f-43d6-b52e-32d6e6cddf11_config-0
12:00:35.923 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 87 ms to scan 1 urls, producing 3 keys and 6 values 
12:00:36.010 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 4 keys and 9 values 
12:00:36.024 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
12:00:36.042 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
12:00:36.062 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
12:00:36.075 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
12:00:36.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aa38952-1a2f-43d6-b52e-32d6e6cddf11_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:00:36.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aa38952-1a2f-43d6-b52e-32d6e6cddf11_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001c8813b8200
12:00:36.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aa38952-1a2f-43d6-b52e-32d6e6cddf11_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001c8813b8420
12:00:36.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aa38952-1a2f-43d6-b52e-32d6e6cddf11_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:00:36.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aa38952-1a2f-43d6-b52e-32d6e6cddf11_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:00:36.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aa38952-1a2f-43d6-b52e-32d6e6cddf11_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:00:38.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aa38952-1a2f-43d6-b52e-32d6e6cddf11_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751515237891_127.0.0.1_14583
12:00:38.194 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aa38952-1a2f-43d6-b52e-32d6e6cddf11_config-0] Notify connected event to listeners.
12:00:38.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aa38952-1a2f-43d6-b52e-32d6e6cddf11_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:00:38.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aa38952-1a2f-43d6-b52e-32d6e6cddf11_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c8814f0228
12:00:38.372 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:00:47.195 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:00:47.196 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:00:47.196 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:00:47.372 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:00:48.053 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:00:48.055 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:00:48.056 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:00:55.519 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:00:59.445 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 875c7078-0e58-4522-833f-ffb24b3d4dc9
12:00:59.445 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [875c7078-0e58-4522-833f-ffb24b3d4dc9] RpcClient init label, labels = {module=naming, source=sdk}
12:00:59.447 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [875c7078-0e58-4522-833f-ffb24b3d4dc9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:00:59.447 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [875c7078-0e58-4522-833f-ffb24b3d4dc9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:00:59.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [875c7078-0e58-4522-833f-ffb24b3d4dc9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:00:59.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [875c7078-0e58-4522-833f-ffb24b3d4dc9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:00:59.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [875c7078-0e58-4522-833f-ffb24b3d4dc9] Success to connect to server [localhost:8848] on start up, connectionId = 1751515259456_127.0.0.1_14715
12:00:59.583 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [875c7078-0e58-4522-833f-ffb24b3d4dc9] Notify connected event to listeners.
12:00:59.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [875c7078-0e58-4522-833f-ffb24b3d4dc9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:00:59.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [875c7078-0e58-4522-833f-ffb24b3d4dc9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c8814f0228
12:00:59.668 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:00:59.713 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:00:59.847 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 29.043 seconds (JVM running for 33.539)
12:00:59.861 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:00:59.862 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:00:59.862 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:01:00.180 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [875c7078-0e58-4522-833f-ffb24b3d4dc9] Receive server push request, request = NotifySubscriberRequest, requestId = 108
12:01:00.194 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [875c7078-0e58-4522-833f-ffb24b3d4dc9] Ack server push request, request = NotifySubscriberRequest, requestId = 108
12:01:52.956 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:01:55.731 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:01:55.731 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:08:31.755 [nacos-grpc-client-executor-98] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [875c7078-0e58-4522-833f-ffb24b3d4dc9] Receive server push request, request = NotifySubscriberRequest, requestId = 113
12:08:31.759 [nacos-grpc-client-executor-98] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [875c7078-0e58-4522-833f-ffb24b3d4dc9] Ack server push request, request = NotifySubscriberRequest, requestId = 113
12:08:31.892 [http-nio-9600-exec-10] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:08:31.892 [http-nio-9600-exec-10] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
12:15:50.919 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:15:50.930 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:15:51.255 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:15:51.257 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@330f5071[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:15:51.257 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751515259456_127.0.0.1_14715
12:15:51.260 [nacos-grpc-client-executor-188] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751515259456_127.0.0.1_14715]Ignore complete event,isRunning:false,isAbandon=false
12:15:51.267 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6f9de5c2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 189]
12:15:51.447 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:15:51.452 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
12:15:51.461 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
12:15:51.462 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:15:51.466 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:15:51.466 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:15:51.469 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:15:51.469 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:15:59.045 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:16:00.043 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 769d95a0-7263-4caa-aae9-5743cebbfed5_config-0
12:16:00.155 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 62 ms to scan 1 urls, producing 3 keys and 6 values 
12:16:00.204 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
12:16:00.215 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
12:16:00.230 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
12:16:00.245 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
12:16:00.257 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
12:16:00.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [769d95a0-7263-4caa-aae9-5743cebbfed5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:16:00.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [769d95a0-7263-4caa-aae9-5743cebbfed5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000207d83b6480
12:16:00.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [769d95a0-7263-4caa-aae9-5743cebbfed5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000207d83b66a0
12:16:00.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [769d95a0-7263-4caa-aae9-5743cebbfed5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:16:00.268 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [769d95a0-7263-4caa-aae9-5743cebbfed5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:16:00.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [769d95a0-7263-4caa-aae9-5743cebbfed5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:16:01.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [769d95a0-7263-4caa-aae9-5743cebbfed5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751516161303_127.0.0.1_6628
12:16:01.584 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [769d95a0-7263-4caa-aae9-5743cebbfed5_config-0] Notify connected event to listeners.
12:16:01.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [769d95a0-7263-4caa-aae9-5743cebbfed5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:16:01.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [769d95a0-7263-4caa-aae9-5743cebbfed5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000207d84f0228
12:16:01.758 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:16:06.588 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:16:06.588 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:16:06.589 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:16:06.824 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:16:07.739 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:16:07.741 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:16:07.742 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:16:18.184 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:16:22.412 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7851713c-1a70-4f77-b921-eca831b44f4f
12:16:22.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7851713c-1a70-4f77-b921-eca831b44f4f] RpcClient init label, labels = {module=naming, source=sdk}
12:16:22.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7851713c-1a70-4f77-b921-eca831b44f4f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:16:22.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7851713c-1a70-4f77-b921-eca831b44f4f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:16:22.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7851713c-1a70-4f77-b921-eca831b44f4f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:16:22.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7851713c-1a70-4f77-b921-eca831b44f4f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:16:22.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7851713c-1a70-4f77-b921-eca831b44f4f] Success to connect to server [localhost:8848] on start up, connectionId = 1751516182432_127.0.0.1_6765
12:16:22.556 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7851713c-1a70-4f77-b921-eca831b44f4f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:16:22.556 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7851713c-1a70-4f77-b921-eca831b44f4f] Notify connected event to listeners.
12:16:22.556 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7851713c-1a70-4f77-b921-eca831b44f4f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000207d84f0228
12:16:22.656 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:16:22.706 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:16:22.899 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.72 seconds (JVM running for 26.128)
12:16:22.919 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:16:22.920 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:16:22.921 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:16:23.217 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7851713c-1a70-4f77-b921-eca831b44f4f] Receive server push request, request = NotifySubscriberRequest, requestId = 135
12:16:23.240 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7851713c-1a70-4f77-b921-eca831b44f4f] Ack server push request, request = NotifySubscriberRequest, requestId = 135
12:16:44.576 [http-nio-9600-exec-4] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:16:47.056 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:16:47.056 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:17:41.250 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:17:41.250 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
12:17:41.550 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7851713c-1a70-4f77-b921-eca831b44f4f] Receive server push request, request = NotifySubscriberRequest, requestId = 137
12:17:41.551 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7851713c-1a70-4f77-b921-eca831b44f4f] Ack server push request, request = NotifySubscriberRequest, requestId = 137
12:25:09.201 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:25:09.206 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:25:09.534 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:25:09.535 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@b350c1e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:25:09.535 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751516182432_127.0.0.1_6765
12:25:09.537 [nacos-grpc-client-executor-113] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751516182432_127.0.0.1_6765]Ignore complete event,isRunning:false,isAbandon=false
12:25:09.541 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2ba1158b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 114]
12:25:09.750 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:25:09.757 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
12:25:09.762 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
12:25:09.763 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:25:09.766 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:25:09.766 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:25:09.769 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:25:09.769 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:25:17.369 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:25:18.349 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5f728458-d916-444b-8820-6bc1f587d4a7_config-0
12:25:18.466 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
12:25:18.528 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
12:25:18.544 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
12:25:18.556 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
12:25:18.569 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
12:25:18.580 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
12:25:18.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f728458-d916-444b-8820-6bc1f587d4a7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:25:18.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f728458-d916-444b-8820-6bc1f587d4a7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002538139e8d8
12:25:18.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f728458-d916-444b-8820-6bc1f587d4a7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002538139eaf8
12:25:18.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f728458-d916-444b-8820-6bc1f587d4a7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:25:18.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f728458-d916-444b-8820-6bc1f587d4a7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:25:18.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f728458-d916-444b-8820-6bc1f587d4a7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:25:19.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f728458-d916-444b-8820-6bc1f587d4a7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751516719643_127.0.0.1_9035
12:25:19.918 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f728458-d916-444b-8820-6bc1f587d4a7_config-0] Notify connected event to listeners.
12:25:19.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f728458-d916-444b-8820-6bc1f587d4a7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:25:19.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f728458-d916-444b-8820-6bc1f587d4a7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025381518fb0
12:25:20.125 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:25:25.079 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:25:25.080 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:25:25.080 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:25:25.325 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:25:26.149 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:25:26.151 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:25:26.152 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:25:36.849 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:25:40.988 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-35e4-45ca-98d6-94127c0f1da7
12:25:40.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] RpcClient init label, labels = {module=naming, source=sdk}
12:25:40.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:25:40.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:25:40.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:25:40.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:25:41.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] Success to connect to server [localhost:8848] on start up, connectionId = 1751516741005_127.0.0.1_9191
12:25:41.124 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:25:41.124 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] Notify connected event to listeners.
12:25:41.124 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025381518fb0
12:25:41.205 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:25:41.251 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:25:41.448 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.856 seconds (JVM running for 26.307)
12:25:41.470 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:25:41.471 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:25:41.473 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:25:41.674 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] Receive server push request, request = NotifySubscriberRequest, requestId = 145
12:25:41.699 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] Ack server push request, request = NotifySubscriberRequest, requestId = 145
12:25:41.711 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:26:38.231 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:26:38.232 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:27:01.627 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:27:01.628 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
12:27:01.864 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] Receive server push request, request = NotifySubscriberRequest, requestId = 147
12:27:01.865 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] Ack server push request, request = NotifySubscriberRequest, requestId = 147
12:32:14.432 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] Receive server push request, request = NotifySubscriberRequest, requestId = 148
12:32:14.454 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-35e4-45ca-98d6-94127c0f1da7] Ack server push request, request = NotifySubscriberRequest, requestId = 148
12:32:14.662 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:32:14.691 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:32:15.024 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:32:15.025 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@18d020b7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:32:15.025 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751516741005_127.0.0.1_9191
12:32:15.030 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1b3ea972[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 48]
12:32:15.189 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:32:15.193 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
12:32:15.197 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
12:32:15.197 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:32:15.199 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:32:15.199 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:32:15.201 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:32:15.201 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:32:56.650 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:32:57.733 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7e6736fd-8682-49c3-81ed-4b79973cf10a_config-0
12:32:57.842 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 57 ms to scan 1 urls, producing 3 keys and 6 values 
12:32:57.901 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
12:32:57.913 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
12:32:57.926 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
12:32:57.942 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
12:32:57.953 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
12:32:57.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e6736fd-8682-49c3-81ed-4b79973cf10a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:32:57.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e6736fd-8682-49c3-81ed-4b79973cf10a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002112b3b6d38
12:32:57.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e6736fd-8682-49c3-81ed-4b79973cf10a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002112b3b6f58
12:32:57.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e6736fd-8682-49c3-81ed-4b79973cf10a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:32:57.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e6736fd-8682-49c3-81ed-4b79973cf10a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:32:57.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e6736fd-8682-49c3-81ed-4b79973cf10a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:32:59.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e6736fd-8682-49c3-81ed-4b79973cf10a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751517179064_127.0.0.1_11094
12:32:59.365 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e6736fd-8682-49c3-81ed-4b79973cf10a_config-0] Notify connected event to listeners.
12:32:59.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e6736fd-8682-49c3-81ed-4b79973cf10a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:32:59.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e6736fd-8682-49c3-81ed-4b79973cf10a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002112b4f0668
12:32:59.529 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:33:05.184 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:33:05.185 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:33:05.185 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:33:05.430 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:33:06.344 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:33:06.346 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:33:06.346 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:33:17.747 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:33:21.850 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b0b480de-b94c-432c-a683-497befb8ce5b
12:33:21.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b480de-b94c-432c-a683-497befb8ce5b] RpcClient init label, labels = {module=naming, source=sdk}
12:33:21.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b480de-b94c-432c-a683-497befb8ce5b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:33:21.857 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b480de-b94c-432c-a683-497befb8ce5b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:33:21.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b480de-b94c-432c-a683-497befb8ce5b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:33:21.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b480de-b94c-432c-a683-497befb8ce5b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:33:22.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b480de-b94c-432c-a683-497befb8ce5b] Success to connect to server [localhost:8848] on start up, connectionId = 1751517201873_127.0.0.1_11303
12:33:22.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b480de-b94c-432c-a683-497befb8ce5b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:33:22.008 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b480de-b94c-432c-a683-497befb8ce5b] Notify connected event to listeners.
12:33:22.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b480de-b94c-432c-a683-497befb8ce5b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002112b4f0668
12:33:22.091 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:33:22.151 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:33:22.375 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.614 seconds (JVM running for 28.094)
12:33:22.398 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:33:22.399 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:33:22.400 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:33:22.624 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b480de-b94c-432c-a683-497befb8ce5b] Receive server push request, request = NotifySubscriberRequest, requestId = 158
12:33:22.645 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b480de-b94c-432c-a683-497befb8ce5b] Ack server push request, request = NotifySubscriberRequest, requestId = 158
12:36:07.664 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:36:27.221 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:36:27.222 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:37:31.954 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b480de-b94c-432c-a683-497befb8ce5b] Receive server push request, request = NotifySubscriberRequest, requestId = 167
12:37:31.955 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b480de-b94c-432c-a683-497befb8ce5b] Ack server push request, request = NotifySubscriberRequest, requestId = 167
12:38:03.557 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:38:03.558 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
13:33:05.886 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:33:05.891 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:33:06.233 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:33:06.234 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@9d66338[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:33:06.234 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751517201873_127.0.0.1_11303
13:33:06.237 [nacos-grpc-client-executor-719] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751517201873_127.0.0.1_11303]Ignore complete event,isRunning:false,isAbandon=false
13:33:06.242 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5e18ba98[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 720]
13:33:06.426 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:33:06.429 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
13:33:06.439 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
13:33:06.439 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:33:06.441 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:33:06.441 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:33:06.443 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:33:06.443 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:28:10.683 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:28:11.820 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0
14:28:11.912 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
14:28:11.962 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:28:11.975 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
14:28:11.989 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:28:12.004 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
14:28:12.022 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
14:28:12.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:28:12.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b88c3c6480
14:28:12.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001b88c3c66a0
14:28:12.029 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:28:12.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:28:12.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:28:15.795 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:28:18.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:28:21.829 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:28:21.830 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:28:21.831 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b88c4d5230
14:28:23.966 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:28:27.973 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:29.735 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:28:29.736 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:28:29.737 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:28:30.019 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:28:31.224 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:31.284 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:28:31.285 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:28:31.286 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:28:34.545 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:37.967 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:41.495 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:42.095 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:28:45.117 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:46.359 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d9131952-b688-437a-8f55-da031f436335
14:28:46.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9131952-b688-437a-8f55-da031f436335] RpcClient init label, labels = {module=naming, source=sdk}
14:28:46.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9131952-b688-437a-8f55-da031f436335] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:28:46.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9131952-b688-437a-8f55-da031f436335] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:28:46.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9131952-b688-437a-8f55-da031f436335] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:28:46.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9131952-b688-437a-8f55-da031f436335] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:28:48.826 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:49.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9131952-b688-437a-8f55-da031f436335] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:28:52.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9131952-b688-437a-8f55-da031f436335] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:28:52.656 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:55.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9131952-b688-437a-8f55-da031f436335] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:28:55.412 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9131952-b688-437a-8f55-da031f436335] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:28:55.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9131952-b688-437a-8f55-da031f436335] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b88c4d5230
14:28:55.760 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:28:56.577 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:56.673 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:28:56.674 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@57f2a68c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:28:56.674 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2c63f1fe[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3]
14:28:56.680 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a4377223-080d-4046-8101-71c46252375a
14:28:56.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4377223-080d-4046-8101-71c46252375a] RpcClient init label, labels = {module=naming, source=sdk}
14:28:56.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4377223-080d-4046-8101-71c46252375a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:28:56.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4377223-080d-4046-8101-71c46252375a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:28:56.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4377223-080d-4046-8101-71c46252375a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:28:56.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4377223-080d-4046-8101-71c46252375a] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:28:56.793 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9131952-b688-437a-8f55-da031f436335] Client is shutdown, stop reconnect to server
14:28:59.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4377223-080d-4046-8101-71c46252375a] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:29:00.603 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:29:02.705 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4377223-080d-4046-8101-71c46252375a] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:29:04.726 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aaa67e5-6dfa-49ae-b4f8-ac2b4404ecf9_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:29:05.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4377223-080d-4046-8101-71c46252375a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:29:05.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4377223-080d-4046-8101-71c46252375a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b88c4d5230
14:29:05.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4377223-080d-4046-8101-71c46252375a] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:29:06.106 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:29:06.113 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:29:06.128 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:29:06.129 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:29:06.155 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9600"]
14:29:06.155 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:29:06.185 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9600"]
14:29:06.191 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9600"]
14:33:42.044 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:33:43.259 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c12d3132-a1e5-44cc-a196-b5b9814775f6_config-0
14:33:43.385 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 59 ms to scan 1 urls, producing 3 keys and 6 values 
14:33:43.450 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
14:33:43.463 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:33:43.479 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
14:33:43.495 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
14:33:43.509 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
14:33:43.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c12d3132-a1e5-44cc-a196-b5b9814775f6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:33:43.515 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c12d3132-a1e5-44cc-a196-b5b9814775f6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001adb03b68d8
14:33:43.515 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c12d3132-a1e5-44cc-a196-b5b9814775f6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001adb03b6af8
14:33:43.516 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c12d3132-a1e5-44cc-a196-b5b9814775f6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:33:43.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c12d3132-a1e5-44cc-a196-b5b9814775f6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:33:43.530 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c12d3132-a1e5-44cc-a196-b5b9814775f6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:33:44.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c12d3132-a1e5-44cc-a196-b5b9814775f6_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751524424660_127.0.0.1_6395
14:33:44.974 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c12d3132-a1e5-44cc-a196-b5b9814775f6_config-0] Notify connected event to listeners.
14:33:44.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c12d3132-a1e5-44cc-a196-b5b9814775f6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:33:44.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c12d3132-a1e5-44cc-a196-b5b9814775f6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001adb04f0668
14:33:45.133 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:33:51.178 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:33:51.179 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:33:51.180 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:33:51.590 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:33:52.526 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:33:52.528 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:33:52.528 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:34:03.331 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:34:07.586 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 04a8d44e-581f-4c24-8973-339d91fdfce2
14:34:07.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04a8d44e-581f-4c24-8973-339d91fdfce2] RpcClient init label, labels = {module=naming, source=sdk}
14:34:07.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04a8d44e-581f-4c24-8973-339d91fdfce2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:34:07.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04a8d44e-581f-4c24-8973-339d91fdfce2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:34:07.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04a8d44e-581f-4c24-8973-339d91fdfce2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:34:07.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04a8d44e-581f-4c24-8973-339d91fdfce2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:34:07.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04a8d44e-581f-4c24-8973-339d91fdfce2] Success to connect to server [localhost:8848] on start up, connectionId = 1751524447610_127.0.0.1_6630
14:34:07.743 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04a8d44e-581f-4c24-8973-339d91fdfce2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:07.744 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04a8d44e-581f-4c24-8973-339d91fdfce2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001adb04f0668
14:34:07.744 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04a8d44e-581f-4c24-8973-339d91fdfce2] Notify connected event to listeners.
14:34:07.832 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:34:07.874 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:34:08.045 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.909 seconds (JVM running for 28.38)
14:34:08.068 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:34:08.068 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:34:08.071 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:34:08.317 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04a8d44e-581f-4c24-8973-339d91fdfce2] Receive server push request, request = NotifySubscriberRequest, requestId = 180
14:34:08.336 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04a8d44e-581f-4c24-8973-339d91fdfce2] Ack server push request, request = NotifySubscriberRequest, requestId = 180
14:48:41.369 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:48:42.748 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:48:42.748 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
14:49:03.996 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
14:49:03.996 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:48:27.056 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:48:27.069 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:48:27.414 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:48:27.415 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7d2687b9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:48:27.415 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751524447610_127.0.0.1_6630
15:48:27.419 [nacos-grpc-client-executor-899] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751524447610_127.0.0.1_6630]Ignore complete event,isRunning:false,isAbandon=false
15:48:27.420 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@34c8852[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 900]
15:48:27.602 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:48:27.607 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:48:27.617 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:48:27.618 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:48:27.621 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:48:27.623 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:48:27.626 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:48:27.626 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:48:35.645 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:48:36.753 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 55fcfd08-0bc7-4e8f-89cd-44a0e547d7f9_config-0
15:48:36.885 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 66 ms to scan 1 urls, producing 3 keys and 6 values 
15:48:36.938 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
15:48:36.950 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
15:48:36.964 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
15:48:36.976 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
15:48:36.988 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
15:48:36.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55fcfd08-0bc7-4e8f-89cd-44a0e547d7f9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:48:36.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55fcfd08-0bc7-4e8f-89cd-44a0e547d7f9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000147263b68d8
15:48:36.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55fcfd08-0bc7-4e8f-89cd-44a0e547d7f9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000147263b6af8
15:48:36.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55fcfd08-0bc7-4e8f-89cd-44a0e547d7f9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:48:36.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55fcfd08-0bc7-4e8f-89cd-44a0e547d7f9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:48:37.009 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55fcfd08-0bc7-4e8f-89cd-44a0e547d7f9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:48:38.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55fcfd08-0bc7-4e8f-89cd-44a0e547d7f9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751528918010_127.0.0.1_1792
15:48:38.309 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55fcfd08-0bc7-4e8f-89cd-44a0e547d7f9_config-0] Notify connected event to listeners.
15:48:38.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55fcfd08-0bc7-4e8f-89cd-44a0e547d7f9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:48:38.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55fcfd08-0bc7-4e8f-89cd-44a0e547d7f9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000147264f0ad8
15:48:38.486 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:48:43.267 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:48:43.268 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:48:43.268 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:48:43.515 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:48:44.405 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:48:44.408 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:48:44.408 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:48:54.385 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:48:58.382 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6ee181e9-e4fc-4bd4-919f-3b1d14420fd1
15:48:58.383 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ee181e9-e4fc-4bd4-919f-3b1d14420fd1] RpcClient init label, labels = {module=naming, source=sdk}
15:48:58.384 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ee181e9-e4fc-4bd4-919f-3b1d14420fd1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:48:58.386 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ee181e9-e4fc-4bd4-919f-3b1d14420fd1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:48:58.386 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ee181e9-e4fc-4bd4-919f-3b1d14420fd1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:48:58.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ee181e9-e4fc-4bd4-919f-3b1d14420fd1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:48:58.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ee181e9-e4fc-4bd4-919f-3b1d14420fd1] Success to connect to server [localhost:8848] on start up, connectionId = 1751528938401_127.0.0.1_1948
15:48:58.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ee181e9-e4fc-4bd4-919f-3b1d14420fd1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:48:58.518 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ee181e9-e4fc-4bd4-919f-3b1d14420fd1] Notify connected event to listeners.
15:48:58.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ee181e9-e4fc-4bd4-919f-3b1d14420fd1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000147264f0ad8
15:48:58.603 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:48:58.673 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:48:58.833 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.129 seconds (JVM running for 25.563)
15:48:58.853 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:48:58.854 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:48:58.855 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:48:59.136 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ee181e9-e4fc-4bd4-919f-3b1d14420fd1] Receive server push request, request = NotifySubscriberRequest, requestId = 232
15:48:59.153 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ee181e9-e4fc-4bd4-919f-3b1d14420fd1] Ack server push request, request = NotifySubscriberRequest, requestId = 232
15:49:01.344 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:49:02.823 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:49:02.824 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:49:03.388 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
15:49:03.389 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
15:57:29.060 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:57:29.065 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:57:29.480 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:57:29.480 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1c03fe6c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:57:29.481 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751528938401_127.0.0.1_1948
15:57:29.486 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@30bbb1be[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 110]
15:57:29.526 [nacos-grpc-client-executor-110] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751528938401_127.0.0.1_1948]Ignore complete event,isRunning:false,isAbandon=false
15:57:29.642 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:57:29.646 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:57:29.651 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:57:29.651 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:57:29.652 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:57:29.652 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:57:29.653 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:57:29.653 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:58:25.445 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:58:26.242 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b43666b9-4fff-49fa-a2e8-b8d2dbf5fc72_config-0
15:58:26.304 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
15:58:26.338 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
15:58:26.345 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
15:58:26.353 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
15:58:26.362 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
15:58:26.372 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
15:58:26.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43666b9-4fff-49fa-a2e8-b8d2dbf5fc72_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:58:26.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43666b9-4fff-49fa-a2e8-b8d2dbf5fc72_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002945d39e230
15:58:26.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43666b9-4fff-49fa-a2e8-b8d2dbf5fc72_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002945d39e450
15:58:26.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43666b9-4fff-49fa-a2e8-b8d2dbf5fc72_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:58:26.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43666b9-4fff-49fa-a2e8-b8d2dbf5fc72_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:58:26.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43666b9-4fff-49fa-a2e8-b8d2dbf5fc72_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:58:27.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43666b9-4fff-49fa-a2e8-b8d2dbf5fc72_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751529507037_127.0.0.1_4483
15:58:27.242 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43666b9-4fff-49fa-a2e8-b8d2dbf5fc72_config-0] Notify connected event to listeners.
15:58:27.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43666b9-4fff-49fa-a2e8-b8d2dbf5fc72_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:58:27.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43666b9-4fff-49fa-a2e8-b8d2dbf5fc72_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002945d5188c8
15:58:27.365 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:58:32.177 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:58:32.178 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:58:32.178 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:58:32.522 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:58:33.563 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:58:33.565 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:58:33.566 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:58:42.118 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:58:45.634 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1afc4427-796e-4493-b5ba-0a025ac82cc3
15:58:45.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1afc4427-796e-4493-b5ba-0a025ac82cc3] RpcClient init label, labels = {module=naming, source=sdk}
15:58:45.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1afc4427-796e-4493-b5ba-0a025ac82cc3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:58:45.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1afc4427-796e-4493-b5ba-0a025ac82cc3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:58:45.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1afc4427-796e-4493-b5ba-0a025ac82cc3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:58:45.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1afc4427-796e-4493-b5ba-0a025ac82cc3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:58:45.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1afc4427-796e-4493-b5ba-0a025ac82cc3] Success to connect to server [localhost:8848] on start up, connectionId = 1751529525649_127.0.0.1_4600
15:58:45.786 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1afc4427-796e-4493-b5ba-0a025ac82cc3] Notify connected event to listeners.
15:58:45.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1afc4427-796e-4493-b5ba-0a025ac82cc3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:58:45.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1afc4427-796e-4493-b5ba-0a025ac82cc3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002945d5188c8
15:58:45.854 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:58:45.881 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:58:46.003 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.718 seconds (JVM running for 22.701)
15:58:46.017 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:58:46.017 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:58:46.019 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:58:46.470 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1afc4427-796e-4493-b5ba-0a025ac82cc3] Receive server push request, request = NotifySubscriberRequest, requestId = 242
15:58:46.486 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1afc4427-796e-4493-b5ba-0a025ac82cc3] Ack server push request, request = NotifySubscriberRequest, requestId = 242
15:58:46.549 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:59:24.525 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:59:24.526 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
16:01:43.206 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:01:43.206 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:01:43.232 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:01:43.235 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:01:43.242 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:01:43.242 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:14:21.898 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:14:21.904 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:14:22.235 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:14:22.235 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5f723787[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:14:22.235 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751529525649_127.0.0.1_4600
16:14:22.239 [nacos-grpc-client-executor-188] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751529525649_127.0.0.1_4600]Ignore complete event,isRunning:false,isAbandon=false
16:14:22.240 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@74d8d2db[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 189]
16:14:22.387 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:14:22.387 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:14:22.388 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:14:22.388 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
16:14:22.389 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
16:14:22.389 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:14:22.390 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:14:22.391 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:51:01.762 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:51:05.729 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5ddc25e6-33b3-469b-b278-66d42c110b9e_config-0
16:51:06.061 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 164 ms to scan 1 urls, producing 3 keys and 6 values 
16:51:06.240 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 4 keys and 9 values 
16:51:06.276 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 3 keys and 10 values 
16:51:06.308 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 1 keys and 5 values 
16:51:06.348 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 1 keys and 7 values 
16:51:06.384 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 2 keys and 8 values 
16:51:06.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ddc25e6-33b3-469b-b278-66d42c110b9e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:51:06.395 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ddc25e6-33b3-469b-b278-66d42c110b9e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001ca0b3b8b08
16:51:06.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ddc25e6-33b3-469b-b278-66d42c110b9e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001ca0b3b8d28
16:51:06.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ddc25e6-33b3-469b-b278-66d42c110b9e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:51:06.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ddc25e6-33b3-469b-b278-66d42c110b9e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:51:06.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ddc25e6-33b3-469b-b278-66d42c110b9e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:51:12.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ddc25e6-33b3-469b-b278-66d42c110b9e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751532671787_127.0.0.1_3795
16:51:12.829 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ddc25e6-33b3-469b-b278-66d42c110b9e_config-0] Notify connected event to listeners.
16:51:12.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ddc25e6-33b3-469b-b278-66d42c110b9e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:51:12.835 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ddc25e6-33b3-469b-b278-66d42c110b9e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ca0b4f0668
16:51:13.578 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:51:28.848 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:51:28.849 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:51:28.850 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:51:29.361 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:51:30.997 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:51:31.000 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:51:31.000 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:51:48.543 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:51:56.280 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of aaa30703-98a9-48f8-85f4-a1ae182d5f5f
16:51:56.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaa30703-98a9-48f8-85f4-a1ae182d5f5f] RpcClient init label, labels = {module=naming, source=sdk}
16:51:56.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaa30703-98a9-48f8-85f4-a1ae182d5f5f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:51:56.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaa30703-98a9-48f8-85f4-a1ae182d5f5f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:51:56.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaa30703-98a9-48f8-85f4-a1ae182d5f5f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:51:56.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaa30703-98a9-48f8-85f4-a1ae182d5f5f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:51:56.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaa30703-98a9-48f8-85f4-a1ae182d5f5f] Success to connect to server [localhost:8848] on start up, connectionId = 1751532716296_127.0.0.1_3949
16:51:56.422 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaa30703-98a9-48f8-85f4-a1ae182d5f5f] Notify connected event to listeners.
16:51:56.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaa30703-98a9-48f8-85f4-a1ae182d5f5f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:51:56.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaa30703-98a9-48f8-85f4-a1ae182d5f5f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ca0b4f0668
16:51:56.489 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:51:56.653 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:51:56.879 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 59.33 seconds (JVM running for 68.564)
16:51:56.949 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:51:56.949 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:51:56.949 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:51:57.143 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaa30703-98a9-48f8-85f4-a1ae182d5f5f] Receive server push request, request = NotifySubscriberRequest, requestId = 288
16:51:57.353 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaa30703-98a9-48f8-85f4-a1ae182d5f5f] Ack server push request, request = NotifySubscriberRequest, requestId = 288
16:52:20.081 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:52:22.539 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:52:22.544 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:35:11.350 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:35:11.356 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:35:11.686 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:35:11.687 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6f4b2fd7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:35:11.687 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751532716296_127.0.0.1_3949
17:35:11.690 [nacos-grpc-client-executor-532] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751532716296_127.0.0.1_3949]Ignore complete event,isRunning:false,isAbandon=false
17:35:11.695 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@399da408[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 533]
17:35:11.865 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:35:11.869 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:35:11.879 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:35:11.880 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:35:11.881 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:35:11.882 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:35:19.919 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:35:21.290 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5f6d4302-a28a-44d1-9863-36a33bf7aa4b_config-0
17:35:21.400 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
17:35:21.458 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
17:35:21.471 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
17:35:21.491 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 5 values 
17:35:21.510 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
17:35:21.526 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
17:35:21.532 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6d4302-a28a-44d1-9863-36a33bf7aa4b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:35:21.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6d4302-a28a-44d1-9863-36a33bf7aa4b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d81e39df80
17:35:21.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6d4302-a28a-44d1-9863-36a33bf7aa4b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001d81e39e1a0
17:35:21.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6d4302-a28a-44d1-9863-36a33bf7aa4b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:35:21.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6d4302-a28a-44d1-9863-36a33bf7aa4b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:35:21.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6d4302-a28a-44d1-9863-36a33bf7aa4b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:35:22.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6d4302-a28a-44d1-9863-36a33bf7aa4b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751535322590_127.0.0.1_12429
17:35:22.856 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6d4302-a28a-44d1-9863-36a33bf7aa4b_config-0] Notify connected event to listeners.
17:35:22.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6d4302-a28a-44d1-9863-36a33bf7aa4b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:22.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6d4302-a28a-44d1-9863-36a33bf7aa4b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d81e518228
17:35:23.056 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:35:28.218 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:35:28.219 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:35:28.219 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:35:28.476 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:35:29.421 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:35:29.423 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:35:29.423 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:35:39.640 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:35:43.491 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bd0c8c2f-470a-47c9-b7c9-fb3da4c81a40
17:35:43.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0c8c2f-470a-47c9-b7c9-fb3da4c81a40] RpcClient init label, labels = {module=naming, source=sdk}
17:35:43.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0c8c2f-470a-47c9-b7c9-fb3da4c81a40] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:35:43.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0c8c2f-470a-47c9-b7c9-fb3da4c81a40] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:35:43.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0c8c2f-470a-47c9-b7c9-fb3da4c81a40] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:35:43.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0c8c2f-470a-47c9-b7c9-fb3da4c81a40] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:35:43.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0c8c2f-470a-47c9-b7c9-fb3da4c81a40] Success to connect to server [localhost:8848] on start up, connectionId = 1751535343508_127.0.0.1_12546
17:35:43.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0c8c2f-470a-47c9-b7c9-fb3da4c81a40] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:43.629 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0c8c2f-470a-47c9-b7c9-fb3da4c81a40] Notify connected event to listeners.
17:35:43.630 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0c8c2f-470a-47c9-b7c9-fb3da4c81a40] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d81e518228
17:35:43.719 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:35:43.766 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:35:43.935 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.861 seconds (JVM running for 25.999)
17:35:43.959 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:35:43.960 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:35:43.962 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:35:44.206 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0c8c2f-470a-47c9-b7c9-fb3da4c81a40] Receive server push request, request = NotifySubscriberRequest, requestId = 294
17:35:44.231 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0c8c2f-470a-47c9-b7c9-fb3da4c81a40] Ack server push request, request = NotifySubscriberRequest, requestId = 294
17:35:44.602 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:42:08.421 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:42:08.422 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:57:37.827 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:57:37.835 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:57:38.180 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:57:38.182 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5a27ac8f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:57:38.186 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751535343508_127.0.0.1_12546
17:57:38.194 [nacos-grpc-client-executor-273] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751535343508_127.0.0.1_12546]Ignore complete event,isRunning:false,isAbandon=false
17:57:38.198 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@501e2b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 274]
17:57:38.361 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:57:38.366 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:57:38.377 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:57:38.378 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:57:38.383 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:57:38.384 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:57:47.000 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:57:48.159 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d4d6ff2f-2347-4bff-9a99-9ebe82218bd9_config-0
17:57:48.271 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
17:57:48.330 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
17:57:48.345 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
17:57:48.362 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
17:57:48.376 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
17:57:48.388 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
17:57:48.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4d6ff2f-2347-4bff-9a99-9ebe82218bd9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:57:48.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4d6ff2f-2347-4bff-9a99-9ebe82218bd9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000022cb339dd70
17:57:48.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4d6ff2f-2347-4bff-9a99-9ebe82218bd9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000022cb339df90
17:57:48.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4d6ff2f-2347-4bff-9a99-9ebe82218bd9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:57:48.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4d6ff2f-2347-4bff-9a99-9ebe82218bd9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:57:48.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4d6ff2f-2347-4bff-9a99-9ebe82218bd9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:57:49.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4d6ff2f-2347-4bff-9a99-9ebe82218bd9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751536669465_127.0.0.1_3025
17:57:49.750 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4d6ff2f-2347-4bff-9a99-9ebe82218bd9_config-0] Notify connected event to listeners.
17:57:49.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4d6ff2f-2347-4bff-9a99-9ebe82218bd9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:57:49.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4d6ff2f-2347-4bff-9a99-9ebe82218bd9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022cb3517b78
17:57:49.985 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:57:55.259 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:57:55.260 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:57:55.261 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:57:55.528 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:57:56.437 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:57:56.439 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:57:56.440 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:58:07.126 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:58:11.260 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7081f0d0-8a8c-4449-9ced-ea30eab0e90b
17:58:11.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7081f0d0-8a8c-4449-9ced-ea30eab0e90b] RpcClient init label, labels = {module=naming, source=sdk}
17:58:11.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7081f0d0-8a8c-4449-9ced-ea30eab0e90b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:58:11.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7081f0d0-8a8c-4449-9ced-ea30eab0e90b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:58:11.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7081f0d0-8a8c-4449-9ced-ea30eab0e90b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:58:11.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7081f0d0-8a8c-4449-9ced-ea30eab0e90b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:58:11.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7081f0d0-8a8c-4449-9ced-ea30eab0e90b] Success to connect to server [localhost:8848] on start up, connectionId = 1751536691278_127.0.0.1_3145
17:58:11.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7081f0d0-8a8c-4449-9ced-ea30eab0e90b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:58:11.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7081f0d0-8a8c-4449-9ced-ea30eab0e90b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022cb3517b78
17:58:11.461 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7081f0d0-8a8c-4449-9ced-ea30eab0e90b] Notify connected event to listeners.
17:58:11.561 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:58:11.604 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:58:11.785 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.687 seconds (JVM running for 27.151)
17:58:11.808 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:58:11.809 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:58:11.811 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:58:12.037 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7081f0d0-8a8c-4449-9ced-ea30eab0e90b] Receive server push request, request = NotifySubscriberRequest, requestId = 298
17:58:12.060 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7081f0d0-8a8c-4449-9ced-ea30eab0e90b] Ack server push request, request = NotifySubscriberRequest, requestId = 298
17:58:12.357 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:58:21.616 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:58:21.617 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:11:15.044 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:11:15.059 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:11:15.396 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:11:15.397 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@606c577d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:11:15.397 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751536691278_127.0.0.1_3145
19:11:15.400 [nacos-grpc-client-executor-889] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751536691278_127.0.0.1_3145]Ignore complete event,isRunning:false,isAbandon=false
19:11:15.403 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@27c1ed37[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 890]
19:11:15.580 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:11:15.587 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:11:15.601 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:11:15.601 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:11:15.604 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:11:15.604 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:11:23.595 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:11:24.678 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 99725e3b-3927-4361-833f-7b3e916f298c_config-0
19:11:24.777 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
19:11:24.826 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
19:11:24.836 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
19:11:24.851 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
19:11:24.865 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
19:11:24.879 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
19:11:24.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99725e3b-3927-4361-833f-7b3e916f298c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:11:24.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99725e3b-3927-4361-833f-7b3e916f298c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001c5dc3b6af8
19:11:24.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99725e3b-3927-4361-833f-7b3e916f298c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001c5dc3b6d18
19:11:24.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99725e3b-3927-4361-833f-7b3e916f298c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:11:24.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99725e3b-3927-4361-833f-7b3e916f298c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:11:24.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99725e3b-3927-4361-833f-7b3e916f298c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:11:26.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99725e3b-3927-4361-833f-7b3e916f298c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751541085923_127.0.0.1_1960
19:11:26.235 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99725e3b-3927-4361-833f-7b3e916f298c_config-0] Notify connected event to listeners.
19:11:26.235 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99725e3b-3927-4361-833f-7b3e916f298c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:11:26.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99725e3b-3927-4361-833f-7b3e916f298c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c5dc4f0ad8
19:11:26.421 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:11:32.812 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:11:32.812 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:11:32.812 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:11:33.191 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:11:34.597 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:11:34.599 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:11:34.599 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:11:52.416 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:11:56.609 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9b5b6096-0bbb-46e6-879e-3bd8a8081fc6
19:11:56.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b5b6096-0bbb-46e6-879e-3bd8a8081fc6] RpcClient init label, labels = {module=naming, source=sdk}
19:11:56.613 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b5b6096-0bbb-46e6-879e-3bd8a8081fc6] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:11:56.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b5b6096-0bbb-46e6-879e-3bd8a8081fc6] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:11:56.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b5b6096-0bbb-46e6-879e-3bd8a8081fc6] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:11:56.615 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b5b6096-0bbb-46e6-879e-3bd8a8081fc6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:11:56.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b5b6096-0bbb-46e6-879e-3bd8a8081fc6] Success to connect to server [localhost:8848] on start up, connectionId = 1751541116628_127.0.0.1_2143
19:11:56.750 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b5b6096-0bbb-46e6-879e-3bd8a8081fc6] Notify connected event to listeners.
19:11:56.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b5b6096-0bbb-46e6-879e-3bd8a8081fc6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:11:56.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b5b6096-0bbb-46e6-879e-3bd8a8081fc6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c5dc4f0ad8
19:11:56.852 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:11:56.899 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:11:57.068 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 34.403 seconds (JVM running for 36.093)
19:11:57.117 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:11:57.119 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:11:57.121 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:11:57.368 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b5b6096-0bbb-46e6-879e-3bd8a8081fc6] Receive server push request, request = NotifySubscriberRequest, requestId = 305
19:11:57.399 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b5b6096-0bbb-46e6-879e-3bd8a8081fc6] Ack server push request, request = NotifySubscriberRequest, requestId = 305
19:12:19.820 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:12:21.259 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:12:21.259 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:17:11.366 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:17:11.381 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:17:11.737 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:17:11.738 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@427d2932[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:17:11.738 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751541116628_127.0.0.1_2143
20:17:11.744 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@9add882[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 792]
20:17:11.754 [nacos-grpc-client-executor-792] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751541116628_127.0.0.1_2143]Ignore complete event,isRunning:false,isAbandon=false
20:17:11.910 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:17:11.914 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:17:11.921 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:17:11.921 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:17:11.923 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:17:11.923 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:17:49.795 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:17:50.774 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a8b11395-d19b-4ac6-8c9f-ece850471dcd_config-0
20:17:50.870 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
20:17:50.912 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
20:17:50.924 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
20:17:50.938 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
20:17:50.958 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 7 values 
20:17:50.968 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
20:17:50.971 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8b11395-d19b-4ac6-8c9f-ece850471dcd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:17:50.971 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8b11395-d19b-4ac6-8c9f-ece850471dcd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000299983b8b08
20:17:50.971 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8b11395-d19b-4ac6-8c9f-ece850471dcd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000299983b8d28
20:17:50.971 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8b11395-d19b-4ac6-8c9f-ece850471dcd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:17:50.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8b11395-d19b-4ac6-8c9f-ece850471dcd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:17:50.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8b11395-d19b-4ac6-8c9f-ece850471dcd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:17:56.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8b11395-d19b-4ac6-8c9f-ece850471dcd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:17:57.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8b11395-d19b-4ac6-8c9f-ece850471dcd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751545076991_127.0.0.1_12283
20:17:57.590 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8b11395-d19b-4ac6-8c9f-ece850471dcd_config-0] Notify connected event to listeners.
20:17:57.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8b11395-d19b-4ac6-8c9f-ece850471dcd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:17:57.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8b11395-d19b-4ac6-8c9f-ece850471dcd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000299984f5210
20:17:57.811 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:18:03.171 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:18:03.201 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:18:03.202 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:18:03.522 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:18:04.493 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:18:04.493 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:18:04.493 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:18:20.894 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:18:24.504 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3ed3ef00-8ae1-4ebf-a96b-b189057801e5
20:18:24.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3ef00-8ae1-4ebf-a96b-b189057801e5] RpcClient init label, labels = {module=naming, source=sdk}
20:18:24.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3ef00-8ae1-4ebf-a96b-b189057801e5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:18:24.507 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3ef00-8ae1-4ebf-a96b-b189057801e5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:18:24.507 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3ef00-8ae1-4ebf-a96b-b189057801e5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:18:24.508 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3ef00-8ae1-4ebf-a96b-b189057801e5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:18:24.654 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3ef00-8ae1-4ebf-a96b-b189057801e5] Success to connect to server [localhost:8848] on start up, connectionId = 1751545104518_127.0.0.1_12381
20:18:24.654 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3ef00-8ae1-4ebf-a96b-b189057801e5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:18:24.655 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3ef00-8ae1-4ebf-a96b-b189057801e5] Notify connected event to listeners.
20:18:24.654 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3ef00-8ae1-4ebf-a96b-b189057801e5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000299984f5210
20:18:24.734 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:18:24.763 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:18:25.176 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 36.202 seconds (JVM running for 44.025)
20:18:25.191 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:18:25.192 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:18:25.193 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:18:25.242 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3ef00-8ae1-4ebf-a96b-b189057801e5] Receive server push request, request = NotifySubscriberRequest, requestId = 309
20:18:25.258 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3ef00-8ae1-4ebf-a96b-b189057801e5] Ack server push request, request = NotifySubscriberRequest, requestId = 309
20:20:04.487 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:20:05.700 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
20:20:05.700 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:21:22.763 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:21:22.766 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:21:23.106 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:21:23.106 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@70a53a6d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:21:23.106 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751545104518_127.0.0.1_12381
20:21:23.110 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@8e06db2[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 43]
20:21:23.272 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:21:23.274 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:21:23.279 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:21:23.279 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:21:23.281 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:21:23.281 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:21:32.242 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:21:39.831 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 133a2288-794d-46a8-acb6-80907ef06f93_config-0
20:21:40.445 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 273 ms to scan 1 urls, producing 3 keys and 6 values 
20:21:40.645 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 4 keys and 9 values 
20:21:40.680 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 3 keys and 10 values 
20:21:40.713 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 1 keys and 5 values 
20:21:40.748 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 1 keys and 7 values 
20:21:40.782 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 2 keys and 8 values 
20:21:40.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:21:40.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000270b03b68d8
20:21:40.797 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000270b03b6af8
20:21:40.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:21:40.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:21:40.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:21:42.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751545302569_127.0.0.1_12876
20:21:42.899 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] Notify connected event to listeners.
20:21:42.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:21:42.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000270b04f0668
20:21:43.091 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:21:49.040 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:21:49.041 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:21:49.041 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:21:49.327 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:21:50.403 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:21:50.405 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:21:50.405 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:22:00.414 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:22:04.386 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0c159b35-41d7-41df-846d-4b89fca7e224
20:22:04.386 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c159b35-41d7-41df-846d-4b89fca7e224] RpcClient init label, labels = {module=naming, source=sdk}
20:22:04.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c159b35-41d7-41df-846d-4b89fca7e224] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:22:04.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c159b35-41d7-41df-846d-4b89fca7e224] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:22:04.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c159b35-41d7-41df-846d-4b89fca7e224] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:22:04.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c159b35-41d7-41df-846d-4b89fca7e224] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:22:04.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c159b35-41d7-41df-846d-4b89fca7e224] Success to connect to server [localhost:8848] on start up, connectionId = 1751545324399_127.0.0.1_12950
20:22:04.513 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c159b35-41d7-41df-846d-4b89fca7e224] Notify connected event to listeners.
20:22:04.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c159b35-41d7-41df-846d-4b89fca7e224] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:22:04.515 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c159b35-41d7-41df-846d-4b89fca7e224] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000270b04f0668
20:22:04.581 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:22:04.611 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:22:04.721 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 35.033 seconds (JVM running for 36.946)
20:22:04.735 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:22:04.735 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:22:04.736 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:22:05.131 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c159b35-41d7-41df-846d-4b89fca7e224] Receive server push request, request = NotifySubscriberRequest, requestId = 317
20:22:05.148 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c159b35-41d7-41df-846d-4b89fca7e224] Ack server push request, request = NotifySubscriberRequest, requestId = 317
20:24:59.947 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:25:02.241 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
20:25:02.241 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:26:53.199 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] Server healthy check fail, currentConnection = 1751545302569_127.0.0.1_12876
20:26:53.266 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:26:53.772 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] Success to connect a server [localhost:8848], connectionId = 1751545613584_127.0.0.1_13438
20:26:53.772 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751545302569_127.0.0.1_12876
20:26:53.772 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751545302569_127.0.0.1_12876
20:26:53.806 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] Notify disconnected event to listeners
20:26:53.806 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133a2288-794d-46a8-acb6-80907ef06f93_config-0] Notify connected event to listeners.
20:26:53.833 [nacos-grpc-client-executor-82] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751545302569_127.0.0.1_12876]Ignore complete event,isRunning:true,isAbandon=true
20:46:23.757 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:46:23.760 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:46:24.099 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:46:24.099 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1d66ab23[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:46:24.099 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751545324399_127.0.0.1_12950
20:46:24.103 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@369729[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 304]
20:46:24.104 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c159b35-41d7-41df-846d-4b89fca7e224] Notify disconnected event to listeners
20:46:24.118 [nacos-grpc-client-executor-304] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751545324399_127.0.0.1_12950]Ignore complete event,isRunning:false,isAbandon=false
20:46:24.268 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:46:24.270 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:46:24.276 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:46:24.277 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:46:24.278 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:46:24.279 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
