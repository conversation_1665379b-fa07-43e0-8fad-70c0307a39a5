package com.heju.job.api.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.job.api.domain.dto.SysJobLogDto;
import com.heju.job.api.domain.po.SysJobLogPo;
import com.heju.job.api.domain.query.SysJobLogQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 调度日志 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysJobLogConverter extends BaseConverter<SysJobLogQuery, SysJobLogDto, SysJobLogPo> {
}
