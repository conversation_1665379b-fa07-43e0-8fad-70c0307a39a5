package com.heju.system.api.authority.feign.factory;

import com.heju.common.core.web.result.R;
import com.heju.system.api.authority.feign.RemoteLoginService;
import com.heju.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 登录服务 降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteLoginFallbackFactory implements FallbackFactory<RemoteLoginService> {

    @Override
    public RemoteLoginService create(Throwable throwable) {
        log.error("登录服务调用失败:{}", throwable.getMessage());
        return new RemoteLoginService() {
            @Override
            public R<LoginUser> getLoginInfoInner(String enterpriseName, String userName, String password, String source) {
                return R.fail("获取登录信息失败:" + throwable.getMessage());
            }

            @Override
            public R<LoginUser> loginInfoByTelephone(String enterpriseName, String telephone,Long userId,String source) {
                return R.fail("获取登录信息失败:" + throwable.getMessage());
            }

            @Override
            public R<LoginUser> loginInfoByUnionId(String enterpriseName, String unionId, String source) {
                return R.fail("获取登录信息失败:" + throwable.getMessage());
            }
        };
    }
}
