package com.heju.system.api.dict.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.api.dict.domain.dto.SysDictDataDto;
import com.heju.system.api.dict.domain.po.SysDictDataPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:51+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysDictDataConverterImpl implements SysDictDataConverter {

    @Override
    public SysDictDataDto mapperDto(SysDictDataPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysDictDataDto sysDictDataDto = new SysDictDataDto();

        sysDictDataDto.setId( arg0.getId() );
        sysDictDataDto.setSourceName( arg0.getSourceName() );
        sysDictDataDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysDictDataDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysDictDataDto.setName( arg0.getName() );
        sysDictDataDto.setStatus( arg0.getStatus() );
        sysDictDataDto.setSort( arg0.getSort() );
        sysDictDataDto.setRemark( arg0.getRemark() );
        sysDictDataDto.setCreateBy( arg0.getCreateBy() );
        sysDictDataDto.setCreateTime( arg0.getCreateTime() );
        sysDictDataDto.setUpdateBy( arg0.getUpdateBy() );
        sysDictDataDto.setUpdateTime( arg0.getUpdateTime() );
        sysDictDataDto.setDelFlag( arg0.getDelFlag() );
        sysDictDataDto.setCreateName( arg0.getCreateName() );
        sysDictDataDto.setUpdateName( arg0.getUpdateName() );
        sysDictDataDto.setCode( arg0.getCode() );
        sysDictDataDto.setValue( arg0.getValue() );
        sysDictDataDto.setLabel( arg0.getLabel() );
        sysDictDataDto.setCssClass( arg0.getCssClass() );
        sysDictDataDto.setListClass( arg0.getListClass() );
        sysDictDataDto.setIsDefault( arg0.getIsDefault() );

        return sysDictDataDto;
    }

    @Override
    public List<SysDictDataDto> mapperDto(Collection<SysDictDataPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysDictDataDto> list = new ArrayList<SysDictDataDto>( arg0.size() );
        for ( SysDictDataPo sysDictDataPo : arg0 ) {
            list.add( mapperDto( sysDictDataPo ) );
        }

        return list;
    }

    @Override
    public Page<SysDictDataDto> mapperPageDto(Collection<SysDictDataPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysDictDataDto> page = new Page<SysDictDataDto>();
        for ( SysDictDataPo sysDictDataPo : arg0 ) {
            page.add( mapperDto( sysDictDataPo ) );
        }

        return page;
    }

    @Override
    public SysDictDataPo mapperPo(SysDictDataDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysDictDataPo sysDictDataPo = new SysDictDataPo();

        sysDictDataPo.setId( arg0.getId() );
        sysDictDataPo.setSourceName( arg0.getSourceName() );
        sysDictDataPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysDictDataPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysDictDataPo.setName( arg0.getName() );
        sysDictDataPo.setStatus( arg0.getStatus() );
        sysDictDataPo.setSort( arg0.getSort() );
        sysDictDataPo.setRemark( arg0.getRemark() );
        sysDictDataPo.setCreateBy( arg0.getCreateBy() );
        sysDictDataPo.setCreateTime( arg0.getCreateTime() );
        sysDictDataPo.setUpdateBy( arg0.getUpdateBy() );
        sysDictDataPo.setUpdateTime( arg0.getUpdateTime() );
        sysDictDataPo.setDelFlag( arg0.getDelFlag() );
        sysDictDataPo.setCreateName( arg0.getCreateName() );
        sysDictDataPo.setUpdateName( arg0.getUpdateName() );
        sysDictDataPo.setCode( arg0.getCode() );
        sysDictDataPo.setValue( arg0.getValue() );
        sysDictDataPo.setLabel( arg0.getLabel() );
        sysDictDataPo.setCssClass( arg0.getCssClass() );
        sysDictDataPo.setListClass( arg0.getListClass() );
        sysDictDataPo.setIsDefault( arg0.getIsDefault() );

        return sysDictDataPo;
    }

    @Override
    public List<SysDictDataPo> mapperPo(Collection<SysDictDataDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysDictDataPo> list = new ArrayList<SysDictDataPo>( arg0.size() );
        for ( SysDictDataDto sysDictDataDto : arg0 ) {
            list.add( mapperPo( sysDictDataDto ) );
        }

        return list;
    }

    @Override
    public Page<SysDictDataPo> mapperPagePo(Collection<SysDictDataDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysDictDataPo> page = new Page<SysDictDataPo>();
        for ( SysDictDataDto sysDictDataDto : arg0 ) {
            page.add( mapperPo( sysDictDataDto ) );
        }

        return page;
    }
}
