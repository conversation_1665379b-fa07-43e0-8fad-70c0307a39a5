package com.heju.system.file.domain.dto;

import com.heju.system.file.domain.po.SysFileBorrowRecordPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 文件借阅记录 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysFileBorrowRecordDto extends SysFileBorrowRecordPo {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 借阅人姓名
     */
    private String nickName;

}
