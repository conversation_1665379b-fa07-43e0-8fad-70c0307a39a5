package com.heju.system.api.authority.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.api.authority.domain.dto.SysMenuDto;
import com.heju.system.api.authority.domain.po.SysMenuPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:51+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysMenuConverterImpl implements SysMenuConverter {

    @Override
    public Page<SysMenuDto> mapperPageDto(Collection<SysMenuPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysMenuDto> page = new Page<SysMenuDto>();
        for ( SysMenuPo sysMenuPo : arg0 ) {
            page.add( mapperDto( sysMenuPo ) );
        }

        return page;
    }

    @Override
    public Page<SysMenuPo> mapperPagePo(Collection<SysMenuDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysMenuPo> page = new Page<SysMenuPo>();
        for ( SysMenuDto sysMenuDto : arg0 ) {
            page.add( mapperPo( sysMenuDto ) );
        }

        return page;
    }

    @Override
    public SysMenuDto mapperDto(SysMenuPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysMenuDto sysMenuDto = new SysMenuDto();

        sysMenuDto.setId( arg0.getId() );
        sysMenuDto.setSourceName( arg0.getSourceName() );
        sysMenuDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysMenuDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysMenuDto.setStatus( arg0.getStatus() );
        sysMenuDto.setSort( arg0.getSort() );
        sysMenuDto.setRemark( arg0.getRemark() );
        sysMenuDto.setCreateBy( arg0.getCreateBy() );
        sysMenuDto.setCreateTime( arg0.getCreateTime() );
        sysMenuDto.setUpdateBy( arg0.getUpdateBy() );
        sysMenuDto.setUpdateTime( arg0.getUpdateTime() );
        sysMenuDto.setDelFlag( arg0.getDelFlag() );
        sysMenuDto.setCreateName( arg0.getCreateName() );
        sysMenuDto.setUpdateName( arg0.getUpdateName() );
        sysMenuDto.setParentId( arg0.getParentId() );
        sysMenuDto.setParentName( arg0.getParentName() );
        sysMenuDto.setAncestors( arg0.getAncestors() );
        sysMenuDto.setLevel( arg0.getLevel() );
        sysMenuDto.setDefaultNode( arg0.getDefaultNode() );
        sysMenuDto.setOldAncestors( arg0.getOldAncestors() );
        sysMenuDto.setOldLevel( arg0.getOldLevel() );
        sysMenuDto.setIsCommon( arg0.getIsCommon() );
        sysMenuDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysMenuDto.setName( arg0.getName() );
        sysMenuDto.setModuleId( arg0.getModuleId() );
        sysMenuDto.setTitle( arg0.getTitle() );
        sysMenuDto.setPath( arg0.getPath() );
        sysMenuDto.setFrameSrc( arg0.getFrameSrc() );
        sysMenuDto.setComponent( arg0.getComponent() );
        sysMenuDto.setParamPath( arg0.getParamPath() );
        sysMenuDto.setTransitionName( arg0.getTransitionName() );
        sysMenuDto.setIgnoreRoute( arg0.getIgnoreRoute() );
        sysMenuDto.setIsCache( arg0.getIsCache() );
        sysMenuDto.setIsAffix( arg0.getIsAffix() );
        sysMenuDto.setIsDisabled( arg0.getIsDisabled() );
        sysMenuDto.setFrameType( arg0.getFrameType() );
        sysMenuDto.setMenuType( arg0.getMenuType() );
        sysMenuDto.setHideTab( arg0.getHideTab() );
        sysMenuDto.setHideMenu( arg0.getHideMenu() );
        sysMenuDto.setHideBreadcrumb( arg0.getHideBreadcrumb() );
        sysMenuDto.setHideChildren( arg0.getHideChildren() );
        sysMenuDto.setHidePathForChildren( arg0.getHidePathForChildren() );
        sysMenuDto.setDynamicLevel( arg0.getDynamicLevel() );
        sysMenuDto.setRealPath( arg0.getRealPath() );
        sysMenuDto.setPerms( arg0.getPerms() );
        sysMenuDto.setIcon( arg0.getIcon() );
        sysMenuDto.setIsDefault( arg0.getIsDefault() );

        return sysMenuDto;
    }

    @Override
    public List<SysMenuDto> mapperDto(Collection<SysMenuPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysMenuDto> list = new ArrayList<SysMenuDto>( arg0.size() );
        for ( SysMenuPo sysMenuPo : arg0 ) {
            list.add( mapperDto( sysMenuPo ) );
        }

        return list;
    }

    @Override
    public SysMenuPo mapperPo(SysMenuDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysMenuPo sysMenuPo = new SysMenuPo();

        sysMenuPo.setId( arg0.getId() );
        sysMenuPo.setSourceName( arg0.getSourceName() );
        sysMenuPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysMenuPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysMenuPo.setStatus( arg0.getStatus() );
        sysMenuPo.setSort( arg0.getSort() );
        sysMenuPo.setRemark( arg0.getRemark() );
        sysMenuPo.setCreateBy( arg0.getCreateBy() );
        sysMenuPo.setCreateTime( arg0.getCreateTime() );
        sysMenuPo.setUpdateBy( arg0.getUpdateBy() );
        sysMenuPo.setUpdateTime( arg0.getUpdateTime() );
        sysMenuPo.setDelFlag( arg0.getDelFlag() );
        sysMenuPo.setCreateName( arg0.getCreateName() );
        sysMenuPo.setUpdateName( arg0.getUpdateName() );
        sysMenuPo.setParentId( arg0.getParentId() );
        sysMenuPo.setParentName( arg0.getParentName() );
        sysMenuPo.setAncestors( arg0.getAncestors() );
        sysMenuPo.setLevel( arg0.getLevel() );
        sysMenuPo.setDefaultNode( arg0.getDefaultNode() );
        sysMenuPo.setOldAncestors( arg0.getOldAncestors() );
        sysMenuPo.setOldLevel( arg0.getOldLevel() );
        sysMenuPo.setIsCommon( arg0.getIsCommon() );
        sysMenuPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysMenuPo.setName( arg0.getName() );
        sysMenuPo.setModuleId( arg0.getModuleId() );
        sysMenuPo.setTitle( arg0.getTitle() );
        sysMenuPo.setPath( arg0.getPath() );
        sysMenuPo.setFrameSrc( arg0.getFrameSrc() );
        sysMenuPo.setComponent( arg0.getComponent() );
        sysMenuPo.setParamPath( arg0.getParamPath() );
        sysMenuPo.setTransitionName( arg0.getTransitionName() );
        sysMenuPo.setIgnoreRoute( arg0.getIgnoreRoute() );
        sysMenuPo.setIsCache( arg0.getIsCache() );
        sysMenuPo.setIsAffix( arg0.getIsAffix() );
        sysMenuPo.setIsDisabled( arg0.getIsDisabled() );
        sysMenuPo.setFrameType( arg0.getFrameType() );
        sysMenuPo.setMenuType( arg0.getMenuType() );
        sysMenuPo.setHideTab( arg0.getHideTab() );
        sysMenuPo.setHideMenu( arg0.getHideMenu() );
        sysMenuPo.setHideBreadcrumb( arg0.getHideBreadcrumb() );
        sysMenuPo.setHideChildren( arg0.getHideChildren() );
        sysMenuPo.setHidePathForChildren( arg0.getHidePathForChildren() );
        sysMenuPo.setDynamicLevel( arg0.getDynamicLevel() );
        sysMenuPo.setRealPath( arg0.getRealPath() );
        sysMenuPo.setPerms( arg0.getPerms() );
        sysMenuPo.setIcon( arg0.getIcon() );
        sysMenuPo.setIsDefault( arg0.getIsDefault() );

        return sysMenuPo;
    }

    @Override
    public List<SysMenuPo> mapperPo(Collection<SysMenuDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysMenuPo> list = new ArrayList<SysMenuPo>( arg0.size() );
        for ( SysMenuDto sysMenuDto : arg0 ) {
            list.add( mapperPo( sysMenuDto ) );
        }

        return list;
    }
}
