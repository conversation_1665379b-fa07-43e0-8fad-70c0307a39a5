package com.heju.system.api.dict.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.api.dict.domain.dto.SysDictDataDto;
import com.heju.system.api.dict.domain.po.SysDictDataPo;
import com.heju.system.api.dict.domain.query.SysDictDataQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 字典数据 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysDictDataConverter extends BaseConverter<SysDictDataQuery, SysDictDataDto, SysDictDataPo> {
}