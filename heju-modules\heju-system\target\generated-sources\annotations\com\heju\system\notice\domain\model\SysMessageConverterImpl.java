package com.heju.system.notice.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.notice.domain.dto.SysMessageDto;
import com.heju.system.notice.domain.po.SysMessagePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysMessageConverterImpl implements SysMessageConverter {

    @Override
    public SysMessageDto mapperDto(SysMessagePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysMessageDto sysMessageDto = new SysMessageDto();

        sysMessageDto.setSourceName( arg0.getSourceName() );
        sysMessageDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysMessageDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysMessageDto.setName( arg0.getName() );
        sysMessageDto.setSort( arg0.getSort() );
        sysMessageDto.setRemark( arg0.getRemark() );
        sysMessageDto.setCreateBy( arg0.getCreateBy() );
        sysMessageDto.setCreateTime( arg0.getCreateTime() );
        sysMessageDto.setUpdateBy( arg0.getUpdateBy() );
        sysMessageDto.setUpdateTime( arg0.getUpdateTime() );
        sysMessageDto.setDelFlag( arg0.getDelFlag() );
        sysMessageDto.setCreateName( arg0.getCreateName() );
        sysMessageDto.setUpdateName( arg0.getUpdateName() );
        sysMessageDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysMessageDto.setTitle( arg0.getTitle() );
        sysMessageDto.setContent( arg0.getContent() );
        sysMessageDto.setType( arg0.getType() );
        sysMessageDto.setStatus( arg0.getStatus() );
        sysMessageDto.setSendUserId( arg0.getSendUserId() );
        sysMessageDto.setReceiveUserId( arg0.getReceiveUserId() );
        sysMessageDto.setChangeMessageId( arg0.getChangeMessageId() );
        sysMessageDto.setId( arg0.getId() );

        return sysMessageDto;
    }

    @Override
    public List<SysMessageDto> mapperDto(Collection<SysMessagePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysMessageDto> list = new ArrayList<SysMessageDto>( arg0.size() );
        for ( SysMessagePo sysMessagePo : arg0 ) {
            list.add( mapperDto( sysMessagePo ) );
        }

        return list;
    }

    @Override
    public Page<SysMessageDto> mapperPageDto(Collection<SysMessagePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysMessageDto> page = new Page<SysMessageDto>();
        for ( SysMessagePo sysMessagePo : arg0 ) {
            page.add( mapperDto( sysMessagePo ) );
        }

        return page;
    }

    @Override
    public SysMessagePo mapperPo(SysMessageDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysMessagePo sysMessagePo = new SysMessagePo();

        sysMessagePo.setId( arg0.getId() );
        sysMessagePo.setSourceName( arg0.getSourceName() );
        sysMessagePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysMessagePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysMessagePo.setName( arg0.getName() );
        sysMessagePo.setSort( arg0.getSort() );
        sysMessagePo.setRemark( arg0.getRemark() );
        sysMessagePo.setCreateBy( arg0.getCreateBy() );
        sysMessagePo.setCreateTime( arg0.getCreateTime() );
        sysMessagePo.setUpdateBy( arg0.getUpdateBy() );
        sysMessagePo.setUpdateTime( arg0.getUpdateTime() );
        sysMessagePo.setDelFlag( arg0.getDelFlag() );
        sysMessagePo.setCreateName( arg0.getCreateName() );
        sysMessagePo.setUpdateName( arg0.getUpdateName() );
        sysMessagePo.setEnterpriseId( arg0.getEnterpriseId() );
        sysMessagePo.setTitle( arg0.getTitle() );
        sysMessagePo.setContent( arg0.getContent() );
        sysMessagePo.setType( arg0.getType() );
        sysMessagePo.setStatus( arg0.getStatus() );
        sysMessagePo.setSendUserId( arg0.getSendUserId() );
        sysMessagePo.setReceiveUserId( arg0.getReceiveUserId() );
        sysMessagePo.setChangeMessageId( arg0.getChangeMessageId() );

        return sysMessagePo;
    }

    @Override
    public List<SysMessagePo> mapperPo(Collection<SysMessageDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysMessagePo> list = new ArrayList<SysMessagePo>( arg0.size() );
        for ( SysMessageDto sysMessageDto : arg0 ) {
            list.add( mapperPo( sysMessageDto ) );
        }

        return list;
    }

    @Override
    public Page<SysMessagePo> mapperPagePo(Collection<SysMessageDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysMessagePo> page = new Page<SysMessagePo>();
        for ( SysMessageDto sysMessageDto : arg0 ) {
            page.add( mapperPo( sysMessageDto ) );
        }

        return page;
    }
}
