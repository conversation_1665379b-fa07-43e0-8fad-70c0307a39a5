09:05:52.098 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:05:52.847 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ee42b7db-2f23-4167-952c-84b0cf35729f_config-0
09:05:52.937 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 39 ms to scan 1 urls, producing 3 keys and 6 values 
09:05:52.972 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 4 keys and 9 values 
09:05:52.980 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 5 ms to scan 1 urls, producing 3 keys and 10 values 
09:05:52.989 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:05:52.997 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 7 values 
09:05:53.010 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:05:53.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee42b7db-2f23-4167-952c-84b0cf35729f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:05:53.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee42b7db-2f23-4167-952c-84b0cf35729f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000022eaf396d88
09:05:53.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee42b7db-2f23-4167-952c-84b0cf35729f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000022eaf396fa8
09:05:53.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee42b7db-2f23-4167-952c-84b0cf35729f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:05:53.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee42b7db-2f23-4167-952c-84b0cf35729f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:05:53.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee42b7db-2f23-4167-952c-84b0cf35729f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:05:54.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee42b7db-2f23-4167-952c-84b0cf35729f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753664754528_127.0.0.1_11212
09:05:54.839 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee42b7db-2f23-4167-952c-84b0cf35729f_config-0] Notify connected event to listeners.
09:05:54.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee42b7db-2f23-4167-952c-84b0cf35729f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:05:54.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee42b7db-2f23-4167-952c-84b0cf35729f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000022eaf510ad8
09:05:56.426 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:06:03.102 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:06:03.103 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:06:03.104 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:06:03.409 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:06:06.383 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:06:10.633 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 745677dd-64f0-4788-8cac-e4b4b8ff07a0
09:06:10.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [745677dd-64f0-4788-8cac-e4b4b8ff07a0] RpcClient init label, labels = {module=naming, source=sdk}
09:06:10.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [745677dd-64f0-4788-8cac-e4b4b8ff07a0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:06:10.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [745677dd-64f0-4788-8cac-e4b4b8ff07a0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:06:10.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [745677dd-64f0-4788-8cac-e4b4b8ff07a0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:06:10.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [745677dd-64f0-4788-8cac-e4b4b8ff07a0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:10.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [745677dd-64f0-4788-8cac-e4b4b8ff07a0] Success to connect to server [localhost:8848] on start up, connectionId = 1753664770649_127.0.0.1_11289
09:06:10.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [745677dd-64f0-4788-8cac-e4b4b8ff07a0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:10.764 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [745677dd-64f0-4788-8cac-e4b4b8ff07a0] Notify connected event to listeners.
09:06:10.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [745677dd-64f0-4788-8cac-e4b4b8ff07a0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000022eaf510ad8
09:06:10.827 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:06:10.870 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:06:11.128 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 19.774 seconds (JVM running for 21.44)
09:06:11.145 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:06:11.146 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:06:11.151 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:06:11.329 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [745677dd-64f0-4788-8cac-e4b4b8ff07a0] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:06:11.338 [RMI TCP Connection(14)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:06:11.353 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [745677dd-64f0-4788-8cac-e4b4b8ff07a0] Ack server push request, request = NotifySubscriberRequest, requestId = 3
19:28:45.243 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:28:45.244 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:28:45.577 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:28:45.577 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@67fe30a6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:28:45.577 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753664770649_127.0.0.1_11289
19:28:45.577 [nacos-grpc-client-executor-7478] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753664770649_127.0.0.1_11289]Ignore complete event,isRunning:false,isAbandon=false
19:28:45.585 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3af7804e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 7479]
