package com.heju.tenant.api.tenant.domain.model;

import com.github.pagehelper.Page;
import com.heju.tenant.api.tenant.domain.dto.TeTenantApprovalDto;
import com.heju.tenant.api.tenant.domain.po.TeTenantApprovalPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:53+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class TeTenantApprovalConverterImpl implements TeTenantApprovalConverter {

    @Override
    public TeTenantApprovalDto mapperDto(TeTenantApprovalPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        TeTenantApprovalDto teTenantApprovalDto = new TeTenantApprovalDto();

        teTenantApprovalDto.setId( arg0.getId() );
        teTenantApprovalDto.setSourceName( arg0.getSourceName() );
        teTenantApprovalDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            teTenantApprovalDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        teTenantApprovalDto.setName( arg0.getName() );
        teTenantApprovalDto.setSort( arg0.getSort() );
        teTenantApprovalDto.setRemark( arg0.getRemark() );
        teTenantApprovalDto.setCreateBy( arg0.getCreateBy() );
        teTenantApprovalDto.setCreateTime( arg0.getCreateTime() );
        teTenantApprovalDto.setUpdateBy( arg0.getUpdateBy() );
        teTenantApprovalDto.setUpdateTime( arg0.getUpdateTime() );
        teTenantApprovalDto.setDelFlag( arg0.getDelFlag() );
        teTenantApprovalDto.setCreateName( arg0.getCreateName() );
        teTenantApprovalDto.setUpdateName( arg0.getUpdateName() );
        teTenantApprovalDto.setNick( arg0.getNick() );
        teTenantApprovalDto.setLogo( arg0.getLogo() );
        teTenantApprovalDto.setStatus( arg0.getStatus() );
        teTenantApprovalDto.setPhone( arg0.getPhone() );
        teTenantApprovalDto.setWechatId( arg0.getWechatId() );
        teTenantApprovalDto.setWechatName( arg0.getWechatName() );
        teTenantApprovalDto.setOpenid( arg0.getOpenid() );
        teTenantApprovalDto.setUniid( arg0.getUniid() );
        teTenantApprovalDto.setApplicationDate( arg0.getApplicationDate() );
        teTenantApprovalDto.setReason( arg0.getReason() );

        return teTenantApprovalDto;
    }

    @Override
    public List<TeTenantApprovalDto> mapperDto(Collection<TeTenantApprovalPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<TeTenantApprovalDto> list = new ArrayList<TeTenantApprovalDto>( arg0.size() );
        for ( TeTenantApprovalPo teTenantApprovalPo : arg0 ) {
            list.add( mapperDto( teTenantApprovalPo ) );
        }

        return list;
    }

    @Override
    public Page<TeTenantApprovalDto> mapperPageDto(Collection<TeTenantApprovalPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<TeTenantApprovalDto> page = new Page<TeTenantApprovalDto>();
        for ( TeTenantApprovalPo teTenantApprovalPo : arg0 ) {
            page.add( mapperDto( teTenantApprovalPo ) );
        }

        return page;
    }

    @Override
    public TeTenantApprovalPo mapperPo(TeTenantApprovalDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        TeTenantApprovalPo teTenantApprovalPo = new TeTenantApprovalPo();

        teTenantApprovalPo.setId( arg0.getId() );
        teTenantApprovalPo.setSourceName( arg0.getSourceName() );
        teTenantApprovalPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            teTenantApprovalPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        teTenantApprovalPo.setName( arg0.getName() );
        teTenantApprovalPo.setSort( arg0.getSort() );
        teTenantApprovalPo.setRemark( arg0.getRemark() );
        teTenantApprovalPo.setCreateBy( arg0.getCreateBy() );
        teTenantApprovalPo.setCreateTime( arg0.getCreateTime() );
        teTenantApprovalPo.setUpdateBy( arg0.getUpdateBy() );
        teTenantApprovalPo.setUpdateTime( arg0.getUpdateTime() );
        teTenantApprovalPo.setDelFlag( arg0.getDelFlag() );
        teTenantApprovalPo.setCreateName( arg0.getCreateName() );
        teTenantApprovalPo.setUpdateName( arg0.getUpdateName() );
        teTenantApprovalPo.setNick( arg0.getNick() );
        teTenantApprovalPo.setLogo( arg0.getLogo() );
        teTenantApprovalPo.setStatus( arg0.getStatus() );
        teTenantApprovalPo.setPhone( arg0.getPhone() );
        teTenantApprovalPo.setWechatId( arg0.getWechatId() );
        teTenantApprovalPo.setWechatName( arg0.getWechatName() );
        teTenantApprovalPo.setOpenid( arg0.getOpenid() );
        teTenantApprovalPo.setUniid( arg0.getUniid() );
        teTenantApprovalPo.setApplicationDate( arg0.getApplicationDate() );
        teTenantApprovalPo.setReason( arg0.getReason() );

        return teTenantApprovalPo;
    }

    @Override
    public List<TeTenantApprovalPo> mapperPo(Collection<TeTenantApprovalDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<TeTenantApprovalPo> list = new ArrayList<TeTenantApprovalPo>( arg0.size() );
        for ( TeTenantApprovalDto teTenantApprovalDto : arg0 ) {
            list.add( mapperPo( teTenantApprovalDto ) );
        }

        return list;
    }

    @Override
    public Page<TeTenantApprovalPo> mapperPagePo(Collection<TeTenantApprovalDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<TeTenantApprovalPo> page = new Page<TeTenantApprovalPo>();
        for ( TeTenantApprovalDto teTenantApprovalDto : arg0 ) {
            page.add( mapperPo( teTenantApprovalDto ) );
        }

        return page;
    }
}
