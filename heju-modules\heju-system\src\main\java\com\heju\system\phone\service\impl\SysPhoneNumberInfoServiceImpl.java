package com.heju.system.phone.service.impl;

import com.heju.common.security.utils.SecurityUtils;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.domain.query.SysUserQuery;
import com.heju.system.organize.manager.ISysUserManager;
import com.heju.system.phone.domain.dto.SysPhoneNumberInfoDto;
import com.heju.system.phone.domain.query.SysPhoneNumberInfoQuery;
import com.heju.system.phone.mapper.SysPhoneNumberInfoMapper;
import com.heju.system.phone.service.ISysPhoneNumberInfoService;
import com.heju.system.phone.manager.ISysPhoneNumberInfoManager;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 手机号管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysPhoneNumberInfoServiceImpl extends BaseServiceImpl<SysPhoneNumberInfoQuery, SysPhoneNumberInfoDto, ISysPhoneNumberInfoManager> implements ISysPhoneNumberInfoService {

    @Autowired
    private ISysUserManager userManager;

    @Autowired
    private ISysPhoneNumberInfoManager manager;

    @Autowired
    private SysPhoneNumberInfoMapper mapper;

    /**
     * 查询手机号对象列表 | 数据权限
     *
     * @param phoneNumberInfo 手机号对象
     * @return 手机号对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysPhoneNumberInfoMapper"})
    public List<SysPhoneNumberInfoDto> selectListScope(SysPhoneNumberInfoQuery phoneNumberInfo) {
        List<SysPhoneNumberInfoDto> list = baseManager.selectList(phoneNumberInfo);
        List<SysUserDto> sysUserDtos = userManager.selectList(new SysUserQuery());
        Map<Long, String> userMap = sysUserDtos.stream().collect(Collectors.toMap(SysUserDto::getId, SysUserDto::getNickName, (v1, v2) -> v1));
        for (SysPhoneNumberInfoDto dto : list) {
            if (dto.getCustody() != null) {
                dto.setNickName(userMap.get(dto.getCustody()));
            }
        }
        return list;
    }

    /**
     * 查询电话号码
     */
    @Override
    public List<String> selectPhoneList(SysPhoneNumberInfoQuery query) {
        if (!SecurityUtils.getUser().isAdmin()) {
            query.setUserId(SecurityUtils.getUserId());
        }
        List<Map<String, Object>> result  = mapper.selectPhoneList(query);
        return result.stream()
                .map(map -> (String) map.get("phone_number"))
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

}
