09:02:27.819 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:02:28.973 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6d26e03d-8453-4302-9f34-d6c8e2811eb8_config-0
09:02:29.118 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 76 ms to scan 1 urls, producing 3 keys and 6 values 
09:02:29.189 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 4 keys and 9 values 
09:02:29.201 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:02:29.232 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 22 ms to scan 1 urls, producing 1 keys and 5 values 
09:02:29.252 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 7 values 
09:02:29.276 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
09:02:29.281 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d26e03d-8453-4302-9f34-d6c8e2811eb8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:02:29.281 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d26e03d-8453-4302-9f34-d6c8e2811eb8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000018a113b5d00
09:02:29.281 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d26e03d-8453-4302-9f34-d6c8e2811eb8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000018a113b5f20
09:02:29.281 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d26e03d-8453-4302-9f34-d6c8e2811eb8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:02:29.281 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d26e03d-8453-4302-9f34-d6c8e2811eb8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:02:29.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d26e03d-8453-4302-9f34-d6c8e2811eb8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:02:31.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d26e03d-8453-4302-9f34-d6c8e2811eb8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754269350988_127.0.0.1_8837
09:02:31.358 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d26e03d-8453-4302-9f34-d6c8e2811eb8_config-0] Notify connected event to listeners.
09:02:31.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d26e03d-8453-4302-9f34-d6c8e2811eb8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:02:31.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d26e03d-8453-4302-9f34-d6c8e2811eb8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000018a114edf18
09:02:31.667 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:02:39.242 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:02:42.972 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:02:44.225 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 47f21f22-e1aa-4856-a2e0-b894b517b306_config-0
09:02:44.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47f21f22-e1aa-4856-a2e0-b894b517b306_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:02:44.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47f21f22-e1aa-4856-a2e0-b894b517b306_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000018a113b5d00
09:02:44.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47f21f22-e1aa-4856-a2e0-b894b517b306_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000018a113b5f20
09:02:44.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47f21f22-e1aa-4856-a2e0-b894b517b306_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:02:44.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47f21f22-e1aa-4856-a2e0-b894b517b306_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:02:44.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47f21f22-e1aa-4856-a2e0-b894b517b306_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:02:44.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47f21f22-e1aa-4856-a2e0-b894b517b306_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754269364252_127.0.0.1_9115
09:02:44.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47f21f22-e1aa-4856-a2e0-b894b517b306_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:02:44.369 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47f21f22-e1aa-4856-a2e0-b894b517b306_config-0] Notify connected event to listeners.
09:02:44.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47f21f22-e1aa-4856-a2e0-b894b517b306_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000018a114edf18
09:02:44.611 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 812b1605-8459-4130-9a52-a700ad28a69f
09:02:44.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] RpcClient init label, labels = {module=naming, source=sdk}
09:02:44.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:02:44.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:02:44.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:02:44.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:02:44.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Success to connect to server [localhost:8848] on start up, connectionId = 1754269364637_127.0.0.1_9116
09:02:44.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:02:44.773 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Notify connected event to listeners.
09:02:44.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000018a114edf18
09:02:45.396 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:02:45.396 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:02:45.592 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:02:45.593 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:02:45.758 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.59:8081 register finished
09:02:45.825 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 19.058 seconds (JVM running for 35.536)
09:02:45.837 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:02:45.839 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:02:45.839 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:02:46.327 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:02:46.327 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:03:15.685 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:03:15.685 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:03:15.690 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:03:15.693 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:03:15.696 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:03:15.696 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 10
10:09:06.102 [nacos-grpc-client-executor-1339] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 15
10:09:06.118 [nacos-grpc-client-executor-1339] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 15
10:25:04.731 [nacos-grpc-client-executor-1657] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 18
10:25:04.744 [nacos-grpc-client-executor-1657] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 18
10:43:24.631 [nacos-grpc-client-executor-2006] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 22
10:43:24.646 [nacos-grpc-client-executor-2006] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 22
10:43:42.427 [nacos-grpc-client-executor-2009] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 25
10:43:42.451 [nacos-grpc-client-executor-2009] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 25
11:43:53.000 [nacos-grpc-client-executor-3180] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 29
11:43:53.021 [nacos-grpc-client-executor-3180] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 29
11:44:14.410 [nacos-grpc-client-executor-3184] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 33
11:44:14.424 [nacos-grpc-client-executor-3184] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 33
11:50:10.400 [nacos-grpc-client-executor-3308] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 37
11:50:10.400 [nacos-grpc-client-executor-3308] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 37
13:35:14.319 [nacos-grpc-client-executor-5349] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 38
13:35:14.337 [nacos-grpc-client-executor-5349] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 38
13:35:47.530 [nacos-grpc-client-executor-5359] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Receive server push request, request = NotifySubscriberRequest, requestId = 43
13:35:47.544 [nacos-grpc-client-executor-5359] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [812b1605-8459-4130-9a52-a700ad28a69f] Ack server push request, request = NotifySubscriberRequest, requestId = 43
18:49:59.940 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:49:59.946 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:50:00.275 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:50:00.275 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@39342e10[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:50:00.275 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754269364637_127.0.0.1_9116
18:50:00.277 [nacos-grpc-client-executor-11264] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754269364637_127.0.0.1_9116]Ignore complete event,isRunning:false,isAbandon=false
18:50:00.281 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5a2152b7[Running, pool size = 10, active threads = 0, queued tasks = 0, completed tasks = 11265]
