14:15:54.066 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:15:54.843 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 69ce0dd8-8b54-4f57-9f98-d82c2df684e6_config-0
14:15:54.927 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 39 ms to scan 1 urls, producing 3 keys and 6 values 
14:15:54.958 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:15:54.971 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:15:54.984 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:15:55.008 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 7 values 
14:15:55.022 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
14:15:55.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69ce0dd8-8b54-4f57-9f98-d82c2df684e6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:15:55.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69ce0dd8-8b54-4f57-9f98-d82c2df684e6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000002461c3cbda8
14:15:55.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69ce0dd8-8b54-4f57-9f98-d82c2df684e6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000002461c3cbfc8
14:15:55.033 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69ce0dd8-8b54-4f57-9f98-d82c2df684e6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:15:55.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69ce0dd8-8b54-4f57-9f98-d82c2df684e6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:15:55.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69ce0dd8-8b54-4f57-9f98-d82c2df684e6_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:15:56.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69ce0dd8-8b54-4f57-9f98-d82c2df684e6_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750832156116_127.0.0.1_7472
14:15:56.408 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69ce0dd8-8b54-4f57-9f98-d82c2df684e6_config-0] Notify connected event to listeners.
14:15:56.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69ce0dd8-8b54-4f57-9f98-d82c2df684e6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:15:56.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69ce0dd8-8b54-4f57-9f98-d82c2df684e6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002461c505f18
14:15:56.589 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:16:01.745 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:16:03.334 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:16:04.088 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e50bdcce-2d23-40a1-b762-6acdeda866aa_config-0
14:16:04.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e50bdcce-2d23-40a1-b762-6acdeda866aa_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:16:04.091 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e50bdcce-2d23-40a1-b762-6acdeda866aa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000002461c3cbda8
14:16:04.091 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e50bdcce-2d23-40a1-b762-6acdeda866aa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000002461c3cbfc8
14:16:04.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e50bdcce-2d23-40a1-b762-6acdeda866aa_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:16:04.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e50bdcce-2d23-40a1-b762-6acdeda866aa_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:16:04.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e50bdcce-2d23-40a1-b762-6acdeda866aa_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:16:04.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e50bdcce-2d23-40a1-b762-6acdeda866aa_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750832164111_127.0.0.1_7494
14:16:04.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e50bdcce-2d23-40a1-b762-6acdeda866aa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:16:04.230 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e50bdcce-2d23-40a1-b762-6acdeda866aa_config-0] Notify connected event to listeners.
14:16:04.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e50bdcce-2d23-40a1-b762-6acdeda866aa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002461c505f18
14:16:04.419 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f49ef990-fa56-4587-8077-97fac1ab4398
14:16:04.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] RpcClient init label, labels = {module=naming, source=sdk}
14:16:04.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:16:04.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:16:04.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:16:04.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:16:04.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750832164446_127.0.0.1_7496
14:16:04.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:16:04.582 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Notify connected event to listeners.
14:16:04.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002461c505f18
14:16:05.163 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Receive server push request, request = NotifySubscriberRequest, requestId = 21
14:16:05.164 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Ack server push request, request = NotifySubscriberRequest, requestId = 21
14:16:05.359 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
14:16:05.438 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 12.198 seconds (JVM running for 13.616)
14:16:05.447 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
14:16:05.448 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
14:16:05.448 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
14:16:05.478 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Receive server push request, request = NotifySubscriberRequest, requestId = 22
14:16:05.480 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Ack server push request, request = NotifySubscriberRequest, requestId = 22
14:16:05.491 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Receive server push request, request = NotifySubscriberRequest, requestId = 23
14:16:05.492 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Ack server push request, request = NotifySubscriberRequest, requestId = 23
14:16:05.505 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Receive server push request, request = NotifySubscriberRequest, requestId = 24
14:16:05.505 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Ack server push request, request = NotifySubscriberRequest, requestId = 24
14:16:05.913 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Receive server push request, request = NotifySubscriberRequest, requestId = 25
14:16:05.914 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Ack server push request, request = NotifySubscriberRequest, requestId = 25
14:16:10.262 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Receive server push request, request = NotifySubscriberRequest, requestId = 26
14:16:10.263 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Ack server push request, request = NotifySubscriberRequest, requestId = 26
14:16:17.366 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Receive server push request, request = NotifySubscriberRequest, requestId = 28
14:16:17.458 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f49ef990-fa56-4587-8077-97fac1ab4398] Ack server push request, request = NotifySubscriberRequest, requestId = 28
14:27:01.128 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:27:01.132 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:27:01.473 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:27:01.474 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@72bc9b7f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:27:01.474 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750832164446_127.0.0.1_7496
14:27:01.477 [nacos-grpc-client-executor-249] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750832164446_127.0.0.1_7496]Ignore complete event,isRunning:false,isAbandon=false
14:27:01.479 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4b82188b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 250]
14:27:13.355 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:27:14.158 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b9234a1e-f82a-4ecb-afdc-26524488dc01_config-0
14:27:14.255 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
14:27:14.292 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:27:14.305 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
14:27:14.322 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
14:27:14.337 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:27:14.353 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
14:27:14.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9234a1e-f82a-4ecb-afdc-26524488dc01_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:27:14.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9234a1e-f82a-4ecb-afdc-26524488dc01_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000021c013cb400
14:27:14.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9234a1e-f82a-4ecb-afdc-26524488dc01_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000021c013cb620
14:27:14.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9234a1e-f82a-4ecb-afdc-26524488dc01_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:27:14.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9234a1e-f82a-4ecb-afdc-26524488dc01_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:27:14.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9234a1e-f82a-4ecb-afdc-26524488dc01_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:27:15.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9234a1e-f82a-4ecb-afdc-26524488dc01_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750832835516_127.0.0.1_9954
14:27:15.788 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9234a1e-f82a-4ecb-afdc-26524488dc01_config-0] Notify connected event to listeners.
14:27:15.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9234a1e-f82a-4ecb-afdc-26524488dc01_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:15.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9234a1e-f82a-4ecb-afdc-26524488dc01_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000021c01504f98
14:27:15.935 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:27:20.868 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:27:22.286 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:27:23.012 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3becc1e5-21fe-4461-a8ff-96c023db08ad_config-0
14:27:23.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3becc1e5-21fe-4461-a8ff-96c023db08ad_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:27:23.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3becc1e5-21fe-4461-a8ff-96c023db08ad_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000021c013cb400
14:27:23.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3becc1e5-21fe-4461-a8ff-96c023db08ad_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000021c013cb620
14:27:23.015 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3becc1e5-21fe-4461-a8ff-96c023db08ad_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:27:23.015 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3becc1e5-21fe-4461-a8ff-96c023db08ad_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:27:23.015 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3becc1e5-21fe-4461-a8ff-96c023db08ad_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:27:23.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3becc1e5-21fe-4461-a8ff-96c023db08ad_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750832843040_127.0.0.1_9977
14:27:23.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3becc1e5-21fe-4461-a8ff-96c023db08ad_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:23.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3becc1e5-21fe-4461-a8ff-96c023db08ad_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000021c01504f98
14:27:23.150 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3becc1e5-21fe-4461-a8ff-96c023db08ad_config-0] Notify connected event to listeners.
14:27:23.326 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7c07efda-43f9-4fb0-9486-0aa2d67b843a
14:27:23.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] RpcClient init label, labels = {module=naming, source=sdk}
14:27:23.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:27:23.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:27:23.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:27:23.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:27:23.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750832843349_127.0.0.1_9986
14:27:23.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:23.473 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Notify connected event to listeners.
14:27:23.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000021c01504f98
14:27:24.108 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Receive server push request, request = NotifySubscriberRequest, requestId = 34
14:27:24.109 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Ack server push request, request = NotifySubscriberRequest, requestId = 34
14:27:24.192 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Receive server push request, request = NotifySubscriberRequest, requestId = 36
14:27:24.193 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Ack server push request, request = NotifySubscriberRequest, requestId = 36
14:27:24.196 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
14:27:24.204 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Receive server push request, request = NotifySubscriberRequest, requestId = 37
14:27:24.205 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Ack server push request, request = NotifySubscriberRequest, requestId = 37
14:27:24.215 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Receive server push request, request = NotifySubscriberRequest, requestId = 35
14:27:24.216 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Ack server push request, request = NotifySubscriberRequest, requestId = 35
14:27:24.230 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Receive server push request, request = NotifySubscriberRequest, requestId = 38
14:27:24.233 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Ack server push request, request = NotifySubscriberRequest, requestId = 38
14:27:24.250 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 11.733 seconds (JVM running for 13.106)
14:27:24.261 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
14:27:24.262 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
14:27:24.262 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
14:27:24.795 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Receive server push request, request = NotifySubscriberRequest, requestId = 39
14:27:24.813 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Ack server push request, request = NotifySubscriberRequest, requestId = 39
14:27:33.909 [nacos-grpc-client-executor-52] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Receive server push request, request = NotifySubscriberRequest, requestId = 40
14:27:33.933 [nacos-grpc-client-executor-52] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Ack server push request, request = NotifySubscriberRequest, requestId = 40
14:27:37.736 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Receive server push request, request = NotifySubscriberRequest, requestId = 42
14:27:37.761 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Ack server push request, request = NotifySubscriberRequest, requestId = 42
14:27:53.020 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Receive server push request, request = NotifySubscriberRequest, requestId = 44
14:27:53.053 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Ack server push request, request = NotifySubscriberRequest, requestId = 44
14:30:01.516 [nacos-grpc-client-executor-100] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Receive server push request, request = NotifySubscriberRequest, requestId = 50
14:30:01.535 [nacos-grpc-client-executor-100] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c07efda-43f9-4fb0-9486-0aa2d67b843a] Ack server push request, request = NotifySubscriberRequest, requestId = 50
14:30:08.191 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:30:08.195 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:30:08.533 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:30:08.534 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@139638db[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:30:08.534 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750832843349_127.0.0.1_9986
14:30:08.537 [nacos-grpc-client-executor-103] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750832843349_127.0.0.1_9986]Ignore complete event,isRunning:false,isAbandon=false
14:30:08.540 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3c7fa12f[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 104]
14:34:16.984 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:34:17.828 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 40db94ea-2263-4578-824f-1930a559af03_config-0
14:34:17.948 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 67 ms to scan 1 urls, producing 3 keys and 6 values 
14:34:17.990 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 4 keys and 9 values 
14:34:18.006 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
14:34:18.025 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
14:34:18.036 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:34:18.053 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
14:34:18.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40db94ea-2263-4578-824f-1930a559af03_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:34:18.059 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40db94ea-2263-4578-824f-1930a559af03_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000023c273cb3d8
14:34:18.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40db94ea-2263-4578-824f-1930a559af03_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000023c273cb5f8
14:34:18.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40db94ea-2263-4578-824f-1930a559af03_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:34:18.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40db94ea-2263-4578-824f-1930a559af03_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:34:18.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40db94ea-2263-4578-824f-1930a559af03_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:34:19.630 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40db94ea-2263-4578-824f-1930a559af03_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750833259360_127.0.0.1_10942
14:34:19.631 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40db94ea-2263-4578-824f-1930a559af03_config-0] Notify connected event to listeners.
14:34:19.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40db94ea-2263-4578-824f-1930a559af03_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:19.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40db94ea-2263-4578-824f-1930a559af03_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000023c27504f98
14:34:19.782 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:34:25.437 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:34:26.976 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:34:27.718 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 369e0713-6b07-476d-bbd2-c69882daecab_config-0
14:34:27.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [369e0713-6b07-476d-bbd2-c69882daecab_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:34:27.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [369e0713-6b07-476d-bbd2-c69882daecab_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000023c273cb3d8
14:34:27.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [369e0713-6b07-476d-bbd2-c69882daecab_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000023c273cb5f8
14:34:27.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [369e0713-6b07-476d-bbd2-c69882daecab_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:34:27.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [369e0713-6b07-476d-bbd2-c69882daecab_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:34:27.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [369e0713-6b07-476d-bbd2-c69882daecab_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:34:27.845 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [369e0713-6b07-476d-bbd2-c69882daecab_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750833267732_127.0.0.1_10975
14:34:27.845 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [369e0713-6b07-476d-bbd2-c69882daecab_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:27.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [369e0713-6b07-476d-bbd2-c69882daecab_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000023c27504f98
14:34:27.846 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [369e0713-6b07-476d-bbd2-c69882daecab_config-0] Notify connected event to listeners.
14:34:27.974 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 92352474-4f2a-459f-a92a-77b96be24515
14:34:27.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] RpcClient init label, labels = {module=naming, source=sdk}
14:34:27.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:34:27.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:34:27.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:34:27.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:34:28.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750833267991_127.0.0.1_10976
14:34:28.108 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Notify connected event to listeners.
14:34:28.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:28.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000023c27504f98
14:34:28.728 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Receive server push request, request = NotifySubscriberRequest, requestId = 54
14:34:28.730 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Ack server push request, request = NotifySubscriberRequest, requestId = 54
14:34:28.820 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Receive server push request, request = NotifySubscriberRequest, requestId = 55
14:34:28.822 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Ack server push request, request = NotifySubscriberRequest, requestId = 55
14:34:29.075 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
14:34:29.120 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 13.025 seconds (JVM running for 14.444)
14:34:29.127 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
14:34:29.128 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
14:34:29.129 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
14:34:29.582 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Receive server push request, request = NotifySubscriberRequest, requestId = 56
14:34:29.582 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Ack server push request, request = NotifySubscriberRequest, requestId = 56
14:34:58.861 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Receive server push request, request = NotifySubscriberRequest, requestId = 60
14:34:58.864 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Ack server push request, request = NotifySubscriberRequest, requestId = 60
14:34:58.877 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Receive server push request, request = NotifySubscriberRequest, requestId = 61
14:34:58.878 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Ack server push request, request = NotifySubscriberRequest, requestId = 61
14:34:58.968 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Receive server push request, request = NotifySubscriberRequest, requestId = 62
14:34:58.969 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92352474-4f2a-459f-a92a-77b96be24515] Ack server push request, request = NotifySubscriberRequest, requestId = 62
14:36:52.151 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:36:52.158 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:36:52.488 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:36:52.488 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2ca23a0f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:36:52.489 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750833267991_127.0.0.1_10976
14:36:52.491 [nacos-grpc-client-executor-79] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750833267991_127.0.0.1_10976]Ignore complete event,isRunning:false,isAbandon=false
14:36:52.495 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@64960404[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 80]
14:39:56.553 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:39:57.333 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 30f55a1d-8505-49df-97a5-380673f79da3_config-0
14:39:57.413 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
14:39:57.443 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:39:57.454 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:39:57.466 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:39:57.484 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
14:39:57.496 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:39:57.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30f55a1d-8505-49df-97a5-380673f79da3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:39:57.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30f55a1d-8505-49df-97a5-380673f79da3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001f6013cb650
14:39:57.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30f55a1d-8505-49df-97a5-380673f79da3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001f6013cb870
14:39:57.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30f55a1d-8505-49df-97a5-380673f79da3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:39:57.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30f55a1d-8505-49df-97a5-380673f79da3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:39:57.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30f55a1d-8505-49df-97a5-380673f79da3_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:39:58.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30f55a1d-8505-49df-97a5-380673f79da3_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750833598704_127.0.0.1_11730
14:39:58.976 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30f55a1d-8505-49df-97a5-380673f79da3_config-0] Notify connected event to listeners.
14:39:58.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30f55a1d-8505-49df-97a5-380673f79da3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:39:58.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30f55a1d-8505-49df-97a5-380673f79da3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001f601504d90
14:39:59.127 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:40:05.027 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:40:06.704 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:40:07.452 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 10a3a07d-ade1-47bd-a1d1-db95fbb6b854_config-0
14:40:07.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [10a3a07d-ade1-47bd-a1d1-db95fbb6b854_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:40:07.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [10a3a07d-ade1-47bd-a1d1-db95fbb6b854_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001f6013cb650
14:40:07.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [10a3a07d-ade1-47bd-a1d1-db95fbb6b854_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001f6013cb870
14:40:07.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [10a3a07d-ade1-47bd-a1d1-db95fbb6b854_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:40:07.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [10a3a07d-ade1-47bd-a1d1-db95fbb6b854_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:40:07.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [10a3a07d-ade1-47bd-a1d1-db95fbb6b854_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:40:07.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [10a3a07d-ade1-47bd-a1d1-db95fbb6b854_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750833607465_127.0.0.1_11787
14:40:07.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [10a3a07d-ade1-47bd-a1d1-db95fbb6b854_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:40:07.577 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [10a3a07d-ade1-47bd-a1d1-db95fbb6b854_config-0] Notify connected event to listeners.
14:40:07.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [10a3a07d-ade1-47bd-a1d1-db95fbb6b854_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001f601504d90
14:40:07.712 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ebd96819-a9a8-4625-9415-084b89ec8c4e
14:40:07.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] RpcClient init label, labels = {module=naming, source=sdk}
14:40:07.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:40:07.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:40:07.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:40:07.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:40:07.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750833607723_127.0.0.1_11788
14:40:07.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:40:07.843 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Notify connected event to listeners.
14:40:07.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001f601504d90
14:40:08.401 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Receive server push request, request = NotifySubscriberRequest, requestId = 68
14:40:08.401 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Ack server push request, request = NotifySubscriberRequest, requestId = 68
14:40:08.505 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
14:40:08.553 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 12.844 seconds (JVM running for 14.154)
14:40:08.563 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
14:40:08.565 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
14:40:08.565 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
14:40:08.600 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Receive server push request, request = NotifySubscriberRequest, requestId = 69
14:40:08.602 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Ack server push request, request = NotifySubscriberRequest, requestId = 69
14:40:09.225 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Receive server push request, request = NotifySubscriberRequest, requestId = 70
14:40:09.227 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Ack server push request, request = NotifySubscriberRequest, requestId = 70
14:40:38.558 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Receive server push request, request = NotifySubscriberRequest, requestId = 75
14:40:38.558 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Ack server push request, request = NotifySubscriberRequest, requestId = 75
14:40:38.574 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Receive server push request, request = NotifySubscriberRequest, requestId = 74
14:40:38.577 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Ack server push request, request = NotifySubscriberRequest, requestId = 74
14:40:38.587 [nacos-grpc-client-executor-52] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Receive server push request, request = NotifySubscriberRequest, requestId = 76
14:40:38.589 [nacos-grpc-client-executor-52] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebd96819-a9a8-4625-9415-084b89ec8c4e] Ack server push request, request = NotifySubscriberRequest, requestId = 76
14:53:01.120 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:53:01.126 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:53:01.459 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:53:01.460 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@204557df[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:53:01.460 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750833607723_127.0.0.1_11788
14:53:01.462 [nacos-grpc-client-executor-287] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750833607723_127.0.0.1_11788]Ignore complete event,isRunning:false,isAbandon=false
14:53:01.463 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@47efdcb1[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 287]
14:54:09.404 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:54:10.160 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 727cff08-4c0c-4bc0-ac3b-c2444c7b9d85_config-0
14:54:10.263 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
14:54:10.299 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:54:10.311 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:54:10.324 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:54:10.339 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
14:54:10.357 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
14:54:10.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [727cff08-4c0c-4bc0-ac3b-c2444c7b9d85_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:54:10.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [727cff08-4c0c-4bc0-ac3b-c2444c7b9d85_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000275413cbda8
14:54:10.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [727cff08-4c0c-4bc0-ac3b-c2444c7b9d85_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000275413cbfc8
14:54:10.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [727cff08-4c0c-4bc0-ac3b-c2444c7b9d85_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:54:10.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [727cff08-4c0c-4bc0-ac3b-c2444c7b9d85_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:54:10.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [727cff08-4c0c-4bc0-ac3b-c2444c7b9d85_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:54:11.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [727cff08-4c0c-4bc0-ac3b-c2444c7b9d85_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750834451597_127.0.0.1_14329
14:54:11.853 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [727cff08-4c0c-4bc0-ac3b-c2444c7b9d85_config-0] Notify connected event to listeners.
14:54:11.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [727cff08-4c0c-4bc0-ac3b-c2444c7b9d85_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:54:11.855 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [727cff08-4c0c-4bc0-ac3b-c2444c7b9d85_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000027541505f18
14:54:12.011 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:54:16.572 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:54:18.068 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:54:18.843 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1cf74a3d-612e-458b-8fd6-a25f871658e4_config-0
14:54:18.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cf74a3d-612e-458b-8fd6-a25f871658e4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:54:18.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cf74a3d-612e-458b-8fd6-a25f871658e4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000275413cbda8
14:54:18.845 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cf74a3d-612e-458b-8fd6-a25f871658e4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000275413cbfc8
14:54:18.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cf74a3d-612e-458b-8fd6-a25f871658e4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:54:18.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cf74a3d-612e-458b-8fd6-a25f871658e4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:54:18.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cf74a3d-612e-458b-8fd6-a25f871658e4_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:54:18.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cf74a3d-612e-458b-8fd6-a25f871658e4_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750834458863_127.0.0.1_14349
14:54:18.978 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cf74a3d-612e-458b-8fd6-a25f871658e4_config-0] Notify connected event to listeners.
14:54:18.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cf74a3d-612e-458b-8fd6-a25f871658e4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:54:18.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1cf74a3d-612e-458b-8fd6-a25f871658e4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000027541505f18
14:54:19.135 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 044fe109-1013-4a6f-93f5-746e79ce1fd6
14:54:19.135 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] RpcClient init label, labels = {module=naming, source=sdk}
14:54:19.137 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:54:19.138 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:54:19.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:54:19.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:54:19.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750834459155_127.0.0.1_14351
14:54:19.280 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Notify connected event to listeners.
14:54:19.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:54:19.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000027541505f18
14:54:19.877 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Receive server push request, request = NotifySubscriberRequest, requestId = 83
14:54:19.877 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Ack server push request, request = NotifySubscriberRequest, requestId = 83
14:54:19.968 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Receive server push request, request = NotifySubscriberRequest, requestId = 85
14:54:19.968 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Ack server push request, request = NotifySubscriberRequest, requestId = 85
14:54:19.977 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Receive server push request, request = NotifySubscriberRequest, requestId = 87
14:54:19.977 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Ack server push request, request = NotifySubscriberRequest, requestId = 87
14:54:19.985 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Receive server push request, request = NotifySubscriberRequest, requestId = 86
14:54:19.986 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Ack server push request, request = NotifySubscriberRequest, requestId = 86
14:54:19.993 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
14:54:19.999 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Receive server push request, request = NotifySubscriberRequest, requestId = 84
14:54:20.002 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Ack server push request, request = NotifySubscriberRequest, requestId = 84
14:54:20.038 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 11.488 seconds (JVM running for 13.052)
14:54:20.047 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
14:54:20.048 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
14:54:20.048 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
14:54:20.514 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Receive server push request, request = NotifySubscriberRequest, requestId = 88
14:54:20.541 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Ack server push request, request = NotifySubscriberRequest, requestId = 88
14:54:36.728 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Receive server push request, request = NotifySubscriberRequest, requestId = 90
14:54:36.755 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Ack server push request, request = NotifySubscriberRequest, requestId = 90
14:54:53.260 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Receive server push request, request = NotifySubscriberRequest, requestId = 92
14:54:53.280 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [044fe109-1013-4a6f-93f5-746e79ce1fd6] Ack server push request, request = NotifySubscriberRequest, requestId = 92
14:56:16.276 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:56:16.282 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:56:16.616 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:56:16.617 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@d4d57ab[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:56:16.617 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750834459155_127.0.0.1_14351
14:56:16.622 [nacos-grpc-client-executor-87] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750834459155_127.0.0.1_14351]Ignore complete event,isRunning:false,isAbandon=false
14:56:16.626 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@48c06de9[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 88]
14:57:10.990 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:57:11.859 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bc281a59-7db7-4520-beb7-be8e976d7d15_config-0
14:57:11.936 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
14:57:11.988 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 4 keys and 9 values 
14:57:12.000 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
14:57:12.012 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:57:12.026 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:57:12.037 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:57:12.042 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc281a59-7db7-4520-beb7-be8e976d7d15_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:57:12.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc281a59-7db7-4520-beb7-be8e976d7d15_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000019a603cb650
14:57:12.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc281a59-7db7-4520-beb7-be8e976d7d15_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000019a603cb870
14:57:12.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc281a59-7db7-4520-beb7-be8e976d7d15_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:57:12.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc281a59-7db7-4520-beb7-be8e976d7d15_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:57:12.057 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc281a59-7db7-4520-beb7-be8e976d7d15_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:57:13.326 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc281a59-7db7-4520-beb7-be8e976d7d15_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750834633047_127.0.0.1_14718
14:57:13.328 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc281a59-7db7-4520-beb7-be8e976d7d15_config-0] Notify connected event to listeners.
14:57:13.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc281a59-7db7-4520-beb7-be8e976d7d15_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:57:13.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc281a59-7db7-4520-beb7-be8e976d7d15_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000019a60504f98
14:57:13.543 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:57:18.067 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:57:19.524 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:57:20.156 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 992c16e0-91df-467c-9a98-49c09f031ab7_config-0
14:57:20.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992c16e0-91df-467c-9a98-49c09f031ab7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:57:20.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992c16e0-91df-467c-9a98-49c09f031ab7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000019a603cb650
14:57:20.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992c16e0-91df-467c-9a98-49c09f031ab7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000019a603cb870
14:57:20.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992c16e0-91df-467c-9a98-49c09f031ab7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:57:20.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992c16e0-91df-467c-9a98-49c09f031ab7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:57:20.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992c16e0-91df-467c-9a98-49c09f031ab7_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:57:20.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992c16e0-91df-467c-9a98-49c09f031ab7_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750834640172_127.0.0.1_14736
14:57:20.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992c16e0-91df-467c-9a98-49c09f031ab7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:57:20.290 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992c16e0-91df-467c-9a98-49c09f031ab7_config-0] Notify connected event to listeners.
14:57:20.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [992c16e0-91df-467c-9a98-49c09f031ab7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000019a60504f98
14:57:20.414 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 22382773-744d-49d2-9f53-02298f37eb53
14:57:20.415 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] RpcClient init label, labels = {module=naming, source=sdk}
14:57:20.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:57:20.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:57:20.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:57:20.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:57:20.556 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750834640430_127.0.0.1_14737
14:57:20.556 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:57:20.556 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Notify connected event to listeners.
14:57:20.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000019a60504f98
14:57:21.171 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Receive server push request, request = NotifySubscriberRequest, requestId = 95
14:57:21.173 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Ack server push request, request = NotifySubscriberRequest, requestId = 95
14:57:21.215 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
14:57:21.261 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Receive server push request, request = NotifySubscriberRequest, requestId = 96
14:57:21.262 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Ack server push request, request = NotifySubscriberRequest, requestId = 96
14:57:21.274 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Receive server push request, request = NotifySubscriberRequest, requestId = 97
14:57:21.275 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Ack server push request, request = NotifySubscriberRequest, requestId = 97
14:57:21.285 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Receive server push request, request = NotifySubscriberRequest, requestId = 98
14:57:21.286 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Ack server push request, request = NotifySubscriberRequest, requestId = 98
14:57:21.288 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 11.152 seconds (JVM running for 12.387)
14:57:21.299 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
14:57:21.301 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
14:57:21.303 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
14:57:21.305 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Receive server push request, request = NotifySubscriberRequest, requestId = 99
14:57:21.307 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Ack server push request, request = NotifySubscriberRequest, requestId = 99
14:57:21.812 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Receive server push request, request = NotifySubscriberRequest, requestId = 100
14:57:21.841 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Ack server push request, request = NotifySubscriberRequest, requestId = 100
14:58:12.312 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Receive server push request, request = NotifySubscriberRequest, requestId = 101
14:58:12.333 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Ack server push request, request = NotifySubscriberRequest, requestId = 101
14:58:14.029 [nacos-grpc-client-executor-65] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Receive server push request, request = NotifySubscriberRequest, requestId = 103
14:58:14.061 [nacos-grpc-client-executor-65] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Ack server push request, request = NotifySubscriberRequest, requestId = 103
15:04:54.918 [nacos-grpc-client-executor-197] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Receive server push request, request = NotifySubscriberRequest, requestId = 107
15:04:54.943 [nacos-grpc-client-executor-197] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Ack server push request, request = NotifySubscriberRequest, requestId = 107
15:51:02.174 [nacos-grpc-client-executor-1091] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Receive server push request, request = NotifySubscriberRequest, requestId = 109
15:51:02.207 [nacos-grpc-client-executor-1091] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Ack server push request, request = NotifySubscriberRequest, requestId = 109
15:51:26.216 [nacos-grpc-client-executor-1102] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Receive server push request, request = NotifySubscriberRequest, requestId = 111
15:51:26.253 [nacos-grpc-client-executor-1102] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22382773-744d-49d2-9f53-02298f37eb53] Ack server push request, request = NotifySubscriberRequest, requestId = 111
16:29:41.293 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:29:41.303 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:29:41.653 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:29:41.653 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6e2b04d4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:29:41.655 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750834640430_127.0.0.1_14737
16:29:41.659 [nacos-grpc-client-executor-1835] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750834640430_127.0.0.1_14737]Ignore complete event,isRunning:false,isAbandon=false
16:29:41.706 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6b92289d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1836]
