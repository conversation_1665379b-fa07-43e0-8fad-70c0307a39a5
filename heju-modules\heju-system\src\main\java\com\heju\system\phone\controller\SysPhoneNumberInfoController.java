package com.heju.system.phone.controller;

import com.heju.common.core.context.SecurityContextHolder;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.phone.domain.dto.SysPhoneNumberInfoDto;
import com.heju.system.phone.domain.query.SysPhoneNumberInfoQuery;
import com.heju.system.phone.service.ISysPhoneNumberInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 手机号管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/phone/info")
public class SysPhoneNumberInfoController extends BaseController<SysPhoneNumberInfoQuery, SysPhoneNumberInfoDto, ISysPhoneNumberInfoService> {

    @Autowired
    private ISysPhoneNumberInfoService service;

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "手机号" ;
    }

    /**
     * 查询手机号列表
     */
    @Override
    @GetMapping("/list")
//    @RequiresPermissions(Auth.SYS_PHONE_NUMBER_INFO_LIST)
    public AjaxResult list(SysPhoneNumberInfoQuery phoneNumberInfo) {
        startPage();
        List<SysPhoneNumberInfoDto> list = baseService.selectListScope(phoneNumberInfo);
        return getDataTable(list);
    }

    /**
     * 查询手机号详细
     */
    @Override
    @GetMapping(value = "/{id}")
//    @RequiresPermissions(Auth.SYS_PHONE_NUMBER_INFO_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 手机号新增
     */
    @Override
    @PostMapping
//    @RequiresPermissions(Auth.SYS_PHONE_NUMBER_INFO_ADD)
    @Log(title = "手机号管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysPhoneNumberInfoDto phoneNumberInfo) {
        return super.add(phoneNumberInfo);
    }

    /**
     * 手机号修改
     */
    @Override
    @PutMapping
//    @RequiresPermissions(Auth.SYS_PHONE_NUMBER_INFO_EDIT)
    @Log(title = "手机号管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysPhoneNumberInfoDto phoneNumberInfo) {
        return super.edit(phoneNumberInfo);
    }

    /**
     * 手机号修改状态
     */
    @Override
    @PutMapping("/status")
//    @RequiresPermissions(value = {Auth.SYS_PHONE_NUMBER_INFO_EDIT, Auth.SYS_PHONE_NUMBER_INFO_ES}, logical = Logical.OR)
    @Log(title = "手机号管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysPhoneNumberInfoDto phoneNumberInfo) {
        return super.editStatus(phoneNumberInfo);
    }

    /**
     * 手机号批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
//    @RequiresPermissions(Auth.SYS_PHONE_NUMBER_INFO_DEL)
    @Log(title = "手机号管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取手机号选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     *  查询当前用户所有授权手机号
     */
    @GetMapping("/phone/list")
    public AjaxResult phoneList(SysPhoneNumberInfoQuery query) {
        List<String> phoneList = service.selectPhoneList(query);
        return AjaxResult.success(phoneList);
    }

    @GetMapping("/loginInfo")
    public AjaxResult loginInfo() {
        Long enterpriseId = SecurityContextHolder.getEnterpriseId();
        String isLessor = SecurityContextHolder.getIsLessor();
        String userType = SecurityContextHolder.getUserType();
        String sourceName = SecurityContextHolder.getSourceName();
        Map<String, Object> result = new HashMap<>();
        result.put("enterpriseId", enterpriseId);
        result.put("isLessor", isLessor);
        result.put("userType", userType);
        result.put("sourceName", sourceName);
        return AjaxResult.success(result);
    }

}
