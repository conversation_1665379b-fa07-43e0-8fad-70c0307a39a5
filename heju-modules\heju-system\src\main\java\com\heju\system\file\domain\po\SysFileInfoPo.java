package com.heju.system.file.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.annotation.Excel;
import com.heju.common.core.web.tenant.base.TBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;


/**
 * 文件信息 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_file_info")
public class SysFileInfoPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 文件分类id */
    @Excel(name = "文件分类id")
    protected Long classifyId;

    /** 存放位置id */
    @Excel(name = "存放位置id")
    protected Long positionId;

    /** 文件编码 */
    @Excel(name = "文件编码")
    protected String code;


    /** 文件大小（单位：字节） */
    @Excel(name = "文件大小", readConverterExp = "单=位：字节")
    protected Long size;

    /** 文件地址 */
    @Excel(name = "文件地址")
    protected String url;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;

    /**
     * 文件删除时间
     */
    @Excel(name = "文件删除时间")
    protected LocalDateTime deleteTime;

    /**
     * 文件删除人
     */
    @Excel(name = "文件删除人")
    protected Long deleteBy;

    /**
     * 业务信息
     */
    @Excel(name = "业务信息")
    protected String businessInfo;
}
