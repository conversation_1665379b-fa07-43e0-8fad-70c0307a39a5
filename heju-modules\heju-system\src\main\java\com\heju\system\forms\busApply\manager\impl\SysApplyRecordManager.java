package com.heju.system.forms.busApply.manager.impl;

import com.heju.system.forms.busApply.domain.po.SysApplyRecordPo;
import com.heju.system.forms.busApply.domain.dto.SysApplyRecordDto;
import com.heju.system.forms.busApply.domain.query.SysApplyRecordQuery;
import com.heju.system.forms.busApply.domain.model.SysApplyRecordConverter;
import com.heju.system.forms.busApply.mapper.SysApplyRecordMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.forms.busApply.manager.ISysApplyRecordManager;
import org.springframework.stereotype.Component;

/**
 * 行政申请管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysApplyRecordManager extends BaseManagerImpl<SysApplyRecordQuery, SysApplyR<PERSON><PERSON><PERSON><PERSON>, SysApplyRecordPo, SysApply<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SysApplyRecordConverter> implements ISysApplyRecordManager {
}
