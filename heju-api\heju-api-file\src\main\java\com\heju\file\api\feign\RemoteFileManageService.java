package com.heju.file.api.feign;

import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.constant.basic.ServiceConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.file.api.domain.SysFile;
import com.heju.file.api.feign.factory.RemoteFileManageFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件管理服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteFileManageService", value = ServiceConstants.SYSTEM_SERVICE, fallbackFactory = RemoteFileManageFallbackFactory.class)
public interface RemoteFileManageService {

    /**
     * 保存文件记录
     *
     * @param file         文件实体
     * @param source       请求来源
     * @return 结果
     */
    @PostMapping("/file")
    R<Boolean> saveFileLog(@RequestBody SysFile file, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}