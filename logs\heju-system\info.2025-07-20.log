10:18:44.133 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:18:45.190 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 55a3b736-bd43-4786-b5b8-709f2da6c322_config-0
10:18:45.347 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 56 ms to scan 1 urls, producing 3 keys and 6 values 
10:18:45.476 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
10:18:45.489 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:18:45.502 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
10:18:45.514 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
10:18:45.528 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
10:18:45.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:18:45.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001fee539eaf8
10:18:45.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001fee539ed18
10:18:45.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:18:45.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:18:45.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:18:46.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:18:46.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:18:46.989 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:18:46.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:18:46.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fee54ee600
10:18:47.125 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:47.341 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:47.658 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:48.090 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:48.608 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:49.149 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:18:49.244 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:49.959 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:50.789 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:51.701 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:52.769 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:53.993 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:54.008 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:18:54.009 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:18:54.009 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:18:54.214 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:18:55.284 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:55.360 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:18:55.362 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:18:55.363 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:18:56.676 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:58.165 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:18:59.831 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:01.514 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:03.302 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:05.160 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:05.649 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:19:07.135 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:09.220 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:09.314 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a8a6f2f9-4921-482a-a157-0d61aeec7756
10:19:09.315 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] RpcClient init label, labels = {module=naming, source=sdk}
10:19:09.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:19:09.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:19:09.318 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:19:09.319 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:19:09.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:19:09.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:19:09.372 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:19:09.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:19:09.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fee54ee600
10:19:09.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:09.720 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:19:09.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:10.038 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:10.476 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:10.719 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:19:10.720 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@33c9910e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:19:10.721 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8a6f2f9-4921-482a-a157-0d61aeec7756] Client is shutdown, stop reconnect to server
10:19:10.721 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@40ea5cd3[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
10:19:10.726 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 22999d3d-dd11-433e-905c-7f5c39964a61
10:19:10.727 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22999d3d-dd11-433e-905c-7f5c39964a61] RpcClient init label, labels = {module=naming, source=sdk}
10:19:10.728 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22999d3d-dd11-433e-905c-7f5c39964a61] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:19:10.728 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22999d3d-dd11-433e-905c-7f5c39964a61] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:19:10.728 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22999d3d-dd11-433e-905c-7f5c39964a61] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:19:10.728 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22999d3d-dd11-433e-905c-7f5c39964a61] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:19:10.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22999d3d-dd11-433e-905c-7f5c39964a61] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:19:10.746 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22999d3d-dd11-433e-905c-7f5c39964a61] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:19:10.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22999d3d-dd11-433e-905c-7f5c39964a61] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:19:10.753 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22999d3d-dd11-433e-905c-7f5c39964a61] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:19:10.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22999d3d-dd11-433e-905c-7f5c39964a61] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fee54ee600
10:19:10.880 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22999d3d-dd11-433e-905c-7f5c39964a61] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:11.100 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22999d3d-dd11-433e-905c-7f5c39964a61] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:11.148 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:19:11.153 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:19:11.191 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:19:11.191 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:19:11.211 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9600"]
10:19:11.211 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
10:19:11.275 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9600"]
10:19:11.283 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9600"]
10:19:11.334 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55a3b736-bd43-4786-b5b8-709f2da6c322_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:11.417 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22999d3d-dd11-433e-905c-7f5c39964a61] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:19:11.839 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22999d3d-dd11-433e-905c-7f5c39964a61] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:20:24.668 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:20:25.777 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e5ff7652-4a7e-4e1f-abcd-58d6f191f7ef_config-0
10:20:25.885 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
10:20:25.942 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
10:20:25.954 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:20:25.965 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
10:20:25.978 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
10:20:25.993 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
10:20:25.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5ff7652-4a7e-4e1f-abcd-58d6f191f7ef_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:20:25.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5ff7652-4a7e-4e1f-abcd-58d6f191f7ef_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001e49239ed38
10:20:25.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5ff7652-4a7e-4e1f-abcd-58d6f191f7ef_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001e49239ef58
10:20:25.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5ff7652-4a7e-4e1f-abcd-58d6f191f7ef_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:20:26.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5ff7652-4a7e-4e1f-abcd-58d6f191f7ef_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:20:26.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5ff7652-4a7e-4e1f-abcd-58d6f191f7ef_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:20:27.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5ff7652-4a7e-4e1f-abcd-58d6f191f7ef_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752978027035_127.0.0.1_1595
10:20:27.293 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5ff7652-4a7e-4e1f-abcd-58d6f191f7ef_config-0] Notify connected event to listeners.
10:20:27.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5ff7652-4a7e-4e1f-abcd-58d6f191f7ef_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:20:27.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5ff7652-4a7e-4e1f-abcd-58d6f191f7ef_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e492518668
10:20:27.434 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:20:32.268 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:20:32.269 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:20:32.269 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:20:32.530 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:20:33.409 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:20:33.411 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:20:33.412 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:20:41.886 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:20:44.777 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7e9d5208-16e5-48af-902e-8f038525aedc
10:20:44.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] RpcClient init label, labels = {module=naming, source=sdk}
10:20:44.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:20:44.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:20:44.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:20:44.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:20:44.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Success to connect to server [localhost:8848] on start up, connectionId = 1752978044790_127.0.0.1_1654
10:20:44.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:20:44.914 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Notify connected event to listeners.
10:20:44.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e492518668
10:20:45.001 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:20:45.051 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:20:45.205 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.302 seconds (JVM running for 22.556)
10:20:45.225 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:20:45.225 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:20:45.225 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:20:45.452 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:20:45.513 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Receive server push request, request = NotifySubscriberRequest, requestId = 8
10:20:45.555 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Ack server push request, request = NotifySubscriberRequest, requestId = 8
10:23:06.793 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Receive server push request, request = NotifySubscriberRequest, requestId = 12
10:23:06.793 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Ack server push request, request = NotifySubscriberRequest, requestId = 12
10:23:07.888 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:23:07.888 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:23:08.521 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:23:08.522 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
11:17:13.616 [nacos-grpc-client-executor-692] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Receive server push request, request = NotifySubscriberRequest, requestId = 14
11:17:13.616 [nacos-grpc-client-executor-692] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Ack server push request, request = NotifySubscriberRequest, requestId = 14
13:31:50.545 [nacos-grpc-client-executor-1679] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Receive server push request, request = NotifySubscriberRequest, requestId = 23
13:31:50.562 [nacos-grpc-client-executor-1679] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Ack server push request, request = NotifySubscriberRequest, requestId = 23
13:32:34.970 [nacos-grpc-client-executor-1691] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Receive server push request, request = NotifySubscriberRequest, requestId = 27
13:32:34.988 [nacos-grpc-client-executor-1691] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e9d5208-16e5-48af-902e-8f038525aedc] Ack server push request, request = NotifySubscriberRequest, requestId = 27
13:55:05.788 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:55:05.790 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:55:06.131 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:55:06.131 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7691c2eb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:55:06.131 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752978044790_127.0.0.1_1654
13:55:06.135 [nacos-grpc-client-executor-2007] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752978044790_127.0.0.1_1654]Ignore complete event,isRunning:false,isAbandon=false
13:55:06.136 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2008]
13:55:06.309 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:55:06.312 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
13:55:06.319 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
13:55:06.319 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:55:06.320 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:55:06.320 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:55:06.321 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:55:06.321 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:55:13.238 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:55:13.796 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ef143378-88bb-42e7-a377-f7b5b9b3de5b_config-0
13:55:13.850 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
13:55:13.878 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
13:55:13.885 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
13:55:13.891 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
13:55:13.898 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
13:55:13.905 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
13:55:13.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef143378-88bb-42e7-a377-f7b5b9b3de5b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:55:13.907 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef143378-88bb-42e7-a377-f7b5b9b3de5b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d0613bdd70
13:55:13.907 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef143378-88bb-42e7-a377-f7b5b9b3de5b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001d0613bdf90
13:55:13.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef143378-88bb-42e7-a377-f7b5b9b3de5b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:55:13.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef143378-88bb-42e7-a377-f7b5b9b3de5b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:55:13.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef143378-88bb-42e7-a377-f7b5b9b3de5b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:55:14.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef143378-88bb-42e7-a377-f7b5b9b3de5b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752990914519_127.0.0.1_8770
13:55:14.696 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef143378-88bb-42e7-a377-f7b5b9b3de5b_config-0] Notify connected event to listeners.
13:55:14.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef143378-88bb-42e7-a377-f7b5b9b3de5b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:55:14.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef143378-88bb-42e7-a377-f7b5b9b3de5b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d0614f7cb0
13:55:14.816 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:55:17.415 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:55:17.415 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:55:17.416 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:55:17.531 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:55:18.122 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:55:18.122 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:55:18.122 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:55:23.364 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:55:25.472 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 20b0196c-7ccd-44f6-9a3b-aad6d51148ba
13:55:25.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20b0196c-7ccd-44f6-9a3b-aad6d51148ba] RpcClient init label, labels = {module=naming, source=sdk}
13:55:25.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20b0196c-7ccd-44f6-9a3b-aad6d51148ba] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:55:25.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20b0196c-7ccd-44f6-9a3b-aad6d51148ba] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:55:25.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20b0196c-7ccd-44f6-9a3b-aad6d51148ba] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:55:25.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20b0196c-7ccd-44f6-9a3b-aad6d51148ba] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:55:25.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20b0196c-7ccd-44f6-9a3b-aad6d51148ba] Success to connect to server [localhost:8848] on start up, connectionId = 1752990925481_127.0.0.1_8791
13:55:25.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20b0196c-7ccd-44f6-9a3b-aad6d51148ba] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:55:25.598 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20b0196c-7ccd-44f6-9a3b-aad6d51148ba] Notify connected event to listeners.
13:55:25.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20b0196c-7ccd-44f6-9a3b-aad6d51148ba] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d0614f7cb0
13:55:25.636 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:55:25.660 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:55:25.778 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.058 seconds (JVM running for 13.939)
13:55:25.789 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:55:25.790 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:55:25.790 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:55:25.959 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:55:26.173 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20b0196c-7ccd-44f6-9a3b-aad6d51148ba] Receive server push request, request = NotifySubscriberRequest, requestId = 39
13:55:26.194 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20b0196c-7ccd-44f6-9a3b-aad6d51148ba] Ack server push request, request = NotifySubscriberRequest, requestId = 39
13:55:30.979 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
13:55:30.979 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:55:30.980 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
13:55:30.982 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
13:55:30.988 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
13:55:30.988 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
13:55:30.988 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20b0196c-7ccd-44f6-9a3b-aad6d51148ba] Receive server push request, request = NotifySubscriberRequest, requestId = 42
13:55:30.989 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20b0196c-7ccd-44f6-9a3b-aad6d51148ba] Ack server push request, request = NotifySubscriberRequest, requestId = 42
13:56:24.304 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
13:56:24.304 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:56:24.387 [http-nio-9600-exec-10] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
13:56:24.387 [http-nio-9600-exec-10] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
13:56:24.387 [http-nio-9600-exec-10] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
13:56:24.387 [http-nio-9600-exec-10] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:57:56.746 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:57:56.757 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:57:57.099 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:57:57.099 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3fa4ff0b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:57:57.099 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752990925481_127.0.0.1_8791
17:57:57.105 [nacos-grpc-client-executor-3081] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752990925481_127.0.0.1_8791]Ignore complete event,isRunning:false,isAbandon=false
17:57:57.116 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6e244550[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3082]
17:57:57.330 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:57:57.335 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:57:57.341 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:57:57.341 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
17:57:57.343 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
17:57:57.343 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:57:57.345 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:57:57.345 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
