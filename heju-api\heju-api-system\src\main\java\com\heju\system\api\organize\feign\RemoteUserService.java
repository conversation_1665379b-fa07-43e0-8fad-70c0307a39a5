package com.heju.system.api.organize.feign;

import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.constant.basic.ServiceConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.feign.factory.RemoteUserFallbackFactory;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteUserService", value = ServiceConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteUserService {

    /**
     * 新增用户
     *
     * @param user         用户对象
     * @param enterpriseId 企业Id
     * @param sourceName   策略源
     * @param source       请求来源
     * @return 结果
     */
    @PostMapping("/user/inner/add")
    R<SysUserDto> addInner(@RequestBody SysUserDto user, @RequestHeader(SecurityConstants.ENTERPRISE_ID) Long enterpriseId, @RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/user/inner/addUser")
    R<SysUserDto> addInnerUser(@RequestBody SysUserDto user, @RequestHeader(SecurityConstants.ENTERPRISE_ID) Long enterpriseId, @RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/organize/remoteOption")
    AjaxResult remoteOption(@RequestParam("sourceName")String sourceMame, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/organize/remote/organizeScope")
    R<List<T>> remoteGetOrganizeScope(@RequestParam("sourceName") String sourceMame, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /** 远程调用修改用户关联的角色Id集 */
    @PutMapping("/user/inner/auth")
    AjaxResult innerEditRoleAuth(@RequestBody SysUserDto user, @RequestHeader(SecurityConstants.FROM_SOURCE) String source,@RequestHeader(SecurityConstants.ENTERPRISE_ID) Long enterpriseId, @RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName);

    @GetMapping(value = "/user/inner/getInfo")
    R<SysUserDto> innerGetInfo(@RequestParam("openId") String openId,@RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询用户详情
     * @param id
     * @param sourceName
     * @param source
     * @return
     */
    @GetMapping(value = "/user/getInfoInner/{id}")
    R<SysUserDto> getInfoInner(@PathVariable("id") Long id,@RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}