09:18:57.258 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:19:00.015 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0
09:19:00.178 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 82 ms to scan 1 urls, producing 3 keys and 6 values 
09:19:00.252 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 36 ms to scan 1 urls, producing 4 keys and 9 values 
09:19:00.295 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 36 ms to scan 1 urls, producing 3 keys and 10 values 
09:19:00.330 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 24 ms to scan 1 urls, producing 1 keys and 5 values 
09:19:00.362 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 22 ms to scan 1 urls, producing 1 keys and 7 values 
09:19:00.389 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 2 keys and 8 values 
09:19:00.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:19:00.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001983739da58
09:19:00.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001983739dc78
09:19:00.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:19:00.408 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:19:00.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:04.237 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:04.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:04.560 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:19:04.560 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:04.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000198374ab938
09:19:04.807 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:05.038 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:05.416 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:05.852 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:06.446 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:06.926 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:19:07.099 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:07.831 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:08.693 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:09.633 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:10.933 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:12.624 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:14.818 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:16.676 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:18.871 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:20.956 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:23.306 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:25.427 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:19:25.429 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:19:25.430 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:19:25.441 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:26.402 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:19:27.647 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:28.893 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:19:28.908 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:19:28.908 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:19:29.811 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:32.187 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:34.761 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:37.179 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:38.750 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:19:39.834 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:42.364 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:44.941 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2fb31be9-9c89-42b8-8680-098e84bcab99
09:19:44.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] RpcClient init label, labels = {module=naming, source=sdk}
09:19:44.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:19:44.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:19:44.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:19:44.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:44.958 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:44.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:44.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:44.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:44.973 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:19:44.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000198374ab938
09:19:45.106 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:45.321 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:19:45.321 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:45.636 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:46.049 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:46.332 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:19:46.332 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2625eb84[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:19:46.332 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3d2a164e[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:19:46.332 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb31be9-9c89-42b8-8680-098e84bcab99] Client is shutdown, stop reconnect to server
09:19:46.332 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c2dbadc4-0be5-423b-9c7b-55ebb6f253a2
09:19:46.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2dbadc4-0be5-423b-9c7b-55ebb6f253a2] RpcClient init label, labels = {module=naming, source=sdk}
09:19:46.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2dbadc4-0be5-423b-9c7b-55ebb6f253a2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:19:46.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2dbadc4-0be5-423b-9c7b-55ebb6f253a2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:19:46.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2dbadc4-0be5-423b-9c7b-55ebb6f253a2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:19:46.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2dbadc4-0be5-423b-9c7b-55ebb6f253a2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:46.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2dbadc4-0be5-423b-9c7b-55ebb6f253a2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:46.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2dbadc4-0be5-423b-9c7b-55ebb6f253a2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:46.364 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2dbadc4-0be5-423b-9c7b-55ebb6f253a2] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:19:46.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2dbadc4-0be5-423b-9c7b-55ebb6f253a2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:46.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2dbadc4-0be5-423b-9c7b-55ebb6f253a2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000198374ab938
09:19:46.490 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2dbadc4-0be5-423b-9c7b-55ebb6f253a2] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:46.727 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2dbadc4-0be5-423b-9c7b-55ebb6f253a2] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:46.750 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:19:46.750 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:19:46.760 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:19:46.760 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:19:46.790 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
09:19:46.790 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:19:46.829 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
09:19:46.830 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
09:19:47.045 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2dbadc4-0be5-423b-9c7b-55ebb6f253a2] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:47.502 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2dbadc4-0be5-423b-9c7b-55ebb6f253a2] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:47.566 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fabb310-6d5f-47a4-8871-2a62efbae8ad_config-0] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:30:00.564 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:30:01.062 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0687b32a-ab89-4503-b775-1c302ea44ee8_config-0
09:30:01.097 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 6 values 
09:30:01.122 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:30:01.128 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
09:30:01.135 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
09:30:01.141 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
09:30:01.148 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
09:30:01.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:30:01.151 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000017b4d3beff8
09:30:01.151 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000017b4d3bf218
09:30:01.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:30:01.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:30:01.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:30:02.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752629401865_127.0.0.1_12679
09:30:02.039 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] Notify connected event to listeners.
09:30:02.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:30:02.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000017b4d4f8fb0
09:30:02.122 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:30:04.174 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:30:04.175 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:30:04.175 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:30:04.283 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:30:04.890 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:30:04.891 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:30:04.891 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:30:06.901 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:30:10.792 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a0347801-bc12-4d1b-a362-a00a7d6c1738
09:30:10.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] RpcClient init label, labels = {module=naming, source=sdk}
09:30:10.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:30:10.797 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:30:10.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:30:10.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:30:10.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Success to connect to server [localhost:8848] on start up, connectionId = 1752629410812_127.0.0.1_12703
09:30:10.931 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Notify connected event to listeners.
09:30:10.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:30:10.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000017b4d4f8fb0
09:30:11.001 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:30:11.050 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:30:11.337 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 11.235 seconds (JVM running for 12.063)
09:30:11.365 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:30:11.368 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:30:11.369 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:30:11.495 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:30:11.513 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:30:11.674 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:33:23.735 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:33:23.737 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Ack server push request, request = NotifySubscriberRequest, requestId = 13
10:18:15.592 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] Server healthy check fail, currentConnection = 1752629401865_127.0.0.1_12679
10:18:15.594 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:18:23.594 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Server check success, currentServer is localhost:8848 
10:18:23.803 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] Success to connect a server [localhost:8848], connectionId = 1752632303680_127.0.0.1_3108
10:18:23.803 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1752629401865_127.0.0.1_12679
10:18:23.804 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752629401865_127.0.0.1_12679
10:18:23.804 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] Notify disconnected event to listeners
10:18:23.806 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0687b32a-ab89-4503-b775-1c302ea44ee8_config-0] Notify connected event to listeners.
14:01:00.083 [nacos-grpc-client-executor-3430] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Receive server push request, request = NotifySubscriberRequest, requestId = 18
14:01:00.114 [nacos-grpc-client-executor-3430] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Ack server push request, request = NotifySubscriberRequest, requestId = 18
14:01:52.492 [nacos-grpc-client-executor-3442] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Receive server push request, request = NotifySubscriberRequest, requestId = 21
14:01:52.513 [nacos-grpc-client-executor-3442] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Ack server push request, request = NotifySubscriberRequest, requestId = 21
15:31:23.071 [nacos-grpc-client-executor-4517] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Receive server push request, request = NotifySubscriberRequest, requestId = 25
15:31:23.097 [nacos-grpc-client-executor-4517] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Ack server push request, request = NotifySubscriberRequest, requestId = 25
15:31:49.724 [nacos-grpc-client-executor-4523] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Receive server push request, request = NotifySubscriberRequest, requestId = 28
15:31:49.752 [nacos-grpc-client-executor-4523] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Ack server push request, request = NotifySubscriberRequest, requestId = 28
15:40:37.253 [nacos-grpc-client-executor-4629] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Receive server push request, request = NotifySubscriberRequest, requestId = 32
15:40:37.276 [nacos-grpc-client-executor-4629] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Ack server push request, request = NotifySubscriberRequest, requestId = 32
15:41:03.141 [nacos-grpc-client-executor-4635] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Receive server push request, request = NotifySubscriberRequest, requestId = 35
15:41:03.154 [nacos-grpc-client-executor-4635] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Ack server push request, request = NotifySubscriberRequest, requestId = 35
17:02:03.984 [nacos-grpc-client-executor-5609] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Receive server push request, request = NotifySubscriberRequest, requestId = 39
17:02:03.997 [nacos-grpc-client-executor-5609] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Ack server push request, request = NotifySubscriberRequest, requestId = 39
17:02:21.049 [nacos-grpc-client-executor-5613] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Receive server push request, request = NotifySubscriberRequest, requestId = 43
17:02:21.070 [nacos-grpc-client-executor-5613] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Ack server push request, request = NotifySubscriberRequest, requestId = 43
17:43:21.286 [nacos-grpc-client-executor-6105] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Receive server push request, request = NotifySubscriberRequest, requestId = 46
17:43:21.302 [nacos-grpc-client-executor-6105] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Ack server push request, request = NotifySubscriberRequest, requestId = 46
17:44:29.211 [nacos-grpc-client-executor-6119] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Receive server push request, request = NotifySubscriberRequest, requestId = 49
17:44:29.249 [nacos-grpc-client-executor-6119] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Ack server push request, request = NotifySubscriberRequest, requestId = 49
20:01:10.782 [nacos-grpc-client-executor-7756] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Receive server push request, request = NotifySubscriberRequest, requestId = 53
20:01:10.805 [nacos-grpc-client-executor-7756] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Ack server push request, request = NotifySubscriberRequest, requestId = 53
20:01:43.813 [nacos-grpc-client-executor-7763] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Receive server push request, request = NotifySubscriberRequest, requestId = 56
20:01:43.829 [nacos-grpc-client-executor-7763] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0347801-bc12-4d1b-a362-a00a7d6c1738] Ack server push request, request = NotifySubscriberRequest, requestId = 56
20:13:13.437 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:13:13.437 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:13:13.756 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:13:13.756 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3b851885[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:13:13.756 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752629410812_127.0.0.1_12703
20:13:13.756 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6afd906c[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 7902]
20:13:13.756 [nacos-grpc-client-executor-7902] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752629410812_127.0.0.1_12703]Ignore complete event,isRunning:false,isAbandon=false
20:13:13.892 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:13:13.892 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:13:13.892 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:13:13.892 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
