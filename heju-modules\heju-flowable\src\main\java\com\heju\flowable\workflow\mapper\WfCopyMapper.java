package com.heju.flowable.workflow.mapper;


import com.heju.common.datasource.annotation.Isolate;
import com.heju.flowable.core.mapper.BaseMapperPlus;
import com.heju.flowable.workflow.domain.WfCopy;
import com.heju.flowable.workflow.domain.vo.WfCopyVo;

/**
 * 流程抄送Mapper接口
 *
 * <AUTHOR>
 * @date 2022-05-19
 */
@Isolate
public interface WfCopyMapper extends BaseMapperPlus<WfCopyMapper, WfCopy, WfCopyVo> {

}
