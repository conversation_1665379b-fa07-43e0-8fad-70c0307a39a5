package com.heju.system.api.dict.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.api.dict.domain.dto.SysDictTypeDto;
import com.heju.system.api.dict.domain.po.SysDictTypePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:51+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysDictTypeConverterImpl implements SysDictTypeConverter {

    @Override
    public SysDictTypeDto mapperDto(SysDictTypePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysDictTypeDto sysDictTypeDto = new SysDictTypeDto();

        sysDictTypeDto.setId( arg0.getId() );
        sysDictTypeDto.setSourceName( arg0.getSourceName() );
        sysDictTypeDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysDictTypeDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysDictTypeDto.setStatus( arg0.getStatus() );
        sysDictTypeDto.setSort( arg0.getSort() );
        sysDictTypeDto.setRemark( arg0.getRemark() );
        sysDictTypeDto.setCreateBy( arg0.getCreateBy() );
        sysDictTypeDto.setCreateTime( arg0.getCreateTime() );
        sysDictTypeDto.setUpdateBy( arg0.getUpdateBy() );
        sysDictTypeDto.setUpdateTime( arg0.getUpdateTime() );
        sysDictTypeDto.setDelFlag( arg0.getDelFlag() );
        sysDictTypeDto.setCreateName( arg0.getCreateName() );
        sysDictTypeDto.setUpdateName( arg0.getUpdateName() );
        sysDictTypeDto.setCode( arg0.getCode() );
        sysDictTypeDto.setName( arg0.getName() );

        return sysDictTypeDto;
    }

    @Override
    public List<SysDictTypeDto> mapperDto(Collection<SysDictTypePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysDictTypeDto> list = new ArrayList<SysDictTypeDto>( arg0.size() );
        for ( SysDictTypePo sysDictTypePo : arg0 ) {
            list.add( mapperDto( sysDictTypePo ) );
        }

        return list;
    }

    @Override
    public Page<SysDictTypeDto> mapperPageDto(Collection<SysDictTypePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysDictTypeDto> page = new Page<SysDictTypeDto>();
        for ( SysDictTypePo sysDictTypePo : arg0 ) {
            page.add( mapperDto( sysDictTypePo ) );
        }

        return page;
    }

    @Override
    public SysDictTypePo mapperPo(SysDictTypeDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysDictTypePo sysDictTypePo = new SysDictTypePo();

        sysDictTypePo.setId( arg0.getId() );
        sysDictTypePo.setSourceName( arg0.getSourceName() );
        sysDictTypePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysDictTypePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysDictTypePo.setStatus( arg0.getStatus() );
        sysDictTypePo.setSort( arg0.getSort() );
        sysDictTypePo.setRemark( arg0.getRemark() );
        sysDictTypePo.setCreateBy( arg0.getCreateBy() );
        sysDictTypePo.setCreateTime( arg0.getCreateTime() );
        sysDictTypePo.setUpdateBy( arg0.getUpdateBy() );
        sysDictTypePo.setUpdateTime( arg0.getUpdateTime() );
        sysDictTypePo.setDelFlag( arg0.getDelFlag() );
        sysDictTypePo.setCreateName( arg0.getCreateName() );
        sysDictTypePo.setUpdateName( arg0.getUpdateName() );
        sysDictTypePo.setCode( arg0.getCode() );
        sysDictTypePo.setName( arg0.getName() );

        return sysDictTypePo;
    }

    @Override
    public List<SysDictTypePo> mapperPo(Collection<SysDictTypeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysDictTypePo> list = new ArrayList<SysDictTypePo>( arg0.size() );
        for ( SysDictTypeDto sysDictTypeDto : arg0 ) {
            list.add( mapperPo( sysDictTypeDto ) );
        }

        return list;
    }

    @Override
    public Page<SysDictTypePo> mapperPagePo(Collection<SysDictTypeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysDictTypePo> page = new Page<SysDictTypePo>();
        for ( SysDictTypeDto sysDictTypeDto : arg0 ) {
            page.add( mapperPo( sysDictTypeDto ) );
        }

        return page;
    }
}
