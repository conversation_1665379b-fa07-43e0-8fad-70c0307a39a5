13:36:53.866 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:36:54.527 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0
13:36:54.595 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 30 ms to scan 1 urls, producing 3 keys and 6 values 
13:36:54.628 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 4 keys and 9 values 
13:36:54.636 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 3 keys and 10 values 
13:36:54.646 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
13:36:54.653 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 5 ms to scan 1 urls, producing 1 keys and 7 values 
13:36:54.665 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
13:36:54.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:36:54.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001ae443bdd00
13:36:54.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001ae443bdf20
13:36:54.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:36:54.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:36:54.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:36:55.507 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751348215293_127.0.0.1_12450
13:36:55.509 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] Notify connected event to listeners.
13:36:55.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:36:55.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001ae444f8228
13:36:55.666 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:36:59.799 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:36:59.800 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:36:59.800 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:36:59.996 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:37:00.770 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:37:00.772 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:37:00.772 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:37:08.565 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:37:12.979 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 32dcdfb4-d949-47e2-be64-f1221dcc2610
13:37:12.981 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] RpcClient init label, labels = {module=naming, source=sdk}
13:37:12.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:37:12.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:37:12.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:37:12.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:37:13.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Success to connect to server [localhost:8848] on start up, connectionId = 1751348232998_127.0.0.1_12508
13:37:13.125 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Notify connected event to listeners.
13:37:13.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:37:13.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001ae444f8228
13:37:13.222 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:37:13.271 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:37:13.477 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.135 seconds (JVM running for 24.209)
13:37:13.504 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:37:13.505 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:37:13.505 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:37:13.683 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:37:13.962 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Receive server push request, request = NotifySubscriberRequest, requestId = 9
13:37:13.999 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Ack server push request, request = NotifySubscriberRequest, requestId = 9
17:24:33.888 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] Server healthy check fail, currentConnection = 1751348215293_127.0.0.1_12450
17:24:35.974 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:24:33.401 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Server healthy check fail, currentConnection = 1751348232998_127.0.0.1_12508
17:24:36.682 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:24:37.019 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Success to connect a server [localhost:8848], connectionId = 1751361876778_127.0.0.1_10145
17:24:37.019 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Abandon prev connection, server is localhost:8848, connectionId is 1751348232998_127.0.0.1_12508
17:24:37.019 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751348232998_127.0.0.1_12508
17:24:37.060 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] Success to connect a server [localhost:8848], connectionId = 1751361876814_127.0.0.1_10146
17:24:37.083 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751348215293_127.0.0.1_12450
17:24:37.083 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751348215293_127.0.0.1_12450
17:24:37.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] Notify disconnected event to listeners
17:24:37.116 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6e06c8e-9ec2-4401-9d30-5db3bee1942d_config-0] Notify connected event to listeners.
17:24:37.121 [nacos-grpc-client-executor-2738] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751348232998_127.0.0.1_12508]Ignore complete event,isRunning:false,isAbandon=true
17:24:37.126 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Notify disconnected event to listeners
17:24:37.136 [nacos-grpc-client-executor-2742] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751348215293_127.0.0.1_12450]Ignore complete event,isRunning:true,isAbandon=true
17:24:37.153 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Notify connected event to listeners.
17:24:40.283 [nacos-grpc-client-executor-2741] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Receive server push request, request = NotifySubscriberRequest, requestId = 16
17:24:40.285 [nacos-grpc-client-executor-2741] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32dcdfb4-d949-47e2-be64-f1221dcc2610] Ack server push request, request = NotifySubscriberRequest, requestId = 16
18:28:05.926 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:28:05.933 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:28:06.277 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:28:06.278 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7b55189e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:28:06.278 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751361876778_127.0.0.1_10145
18:28:06.283 [nacos-grpc-client-executor-3502] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751361876778_127.0.0.1_10145]Ignore complete event,isRunning:false,isAbandon=false
18:28:06.286 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6239936e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3503]
18:28:06.516 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:28:06.570 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:28:06.595 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:28:06.596 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:28:52.048 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:28:53.205 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8a570095-40c9-46e7-886c-41a75d0a2490_config-0
18:28:53.316 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
18:28:53.377 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
18:28:53.392 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
18:28:53.405 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
18:28:53.417 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
18:28:53.430 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
18:28:53.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a570095-40c9-46e7-886c-41a75d0a2490_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:28:53.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a570095-40c9-46e7-886c-41a75d0a2490_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000222a239bda8
18:28:53.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a570095-40c9-46e7-886c-41a75d0a2490_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000222a239bfc8
18:28:53.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a570095-40c9-46e7-886c-41a75d0a2490_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:28:53.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a570095-40c9-46e7-886c-41a75d0a2490_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:28:53.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a570095-40c9-46e7-886c-41a75d0a2490_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:28:55.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a570095-40c9-46e7-886c-41a75d0a2490_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751365734707_127.0.0.1_6122
18:28:55.496 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a570095-40c9-46e7-886c-41a75d0a2490_config-0] Notify connected event to listeners.
18:28:55.497 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a570095-40c9-46e7-886c-41a75d0a2490_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:28:55.498 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a570095-40c9-46e7-886c-41a75d0a2490_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000222a2513db0
18:28:55.867 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:29:05.724 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:29:05.727 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:29:05.728 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:29:06.270 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:29:08.063 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:29:08.067 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:29:08.068 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:29:27.063 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:29:34.968 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6c6d192b-b551-4fae-9318-49b447d3a9fa
18:29:34.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c6d192b-b551-4fae-9318-49b447d3a9fa] RpcClient init label, labels = {module=naming, source=sdk}
18:29:34.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c6d192b-b551-4fae-9318-49b447d3a9fa] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:29:34.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c6d192b-b551-4fae-9318-49b447d3a9fa] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:29:34.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c6d192b-b551-4fae-9318-49b447d3a9fa] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:29:34.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c6d192b-b551-4fae-9318-49b447d3a9fa] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:29:35.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c6d192b-b551-4fae-9318-49b447d3a9fa] Success to connect to server [localhost:8848] on start up, connectionId = 1751365774990_127.0.0.1_6240
18:29:35.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c6d192b-b551-4fae-9318-49b447d3a9fa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:29:35.116 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c6d192b-b551-4fae-9318-49b447d3a9fa] Notify connected event to listeners.
18:29:35.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c6d192b-b551-4fae-9318-49b447d3a9fa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000222a2513db0
18:29:35.235 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:29:35.320 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:29:35.681 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 45.111 seconds (JVM running for 49.117)
18:29:35.697 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c6d192b-b551-4fae-9318-49b447d3a9fa] Receive server push request, request = NotifySubscriberRequest, requestId = 19
18:29:35.725 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c6d192b-b551-4fae-9318-49b447d3a9fa] Ack server push request, request = NotifySubscriberRequest, requestId = 19
18:29:35.725 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:29:35.727 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:29:35.729 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:06:54.099 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:06:54.105 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:06:54.436 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:06:54.436 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@50fc87a7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:06:54.437 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751365774990_127.0.0.1_6240
19:06:54.439 [nacos-grpc-client-executor-456] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751365774990_127.0.0.1_6240]Ignore complete event,isRunning:false,isAbandon=false
19:06:54.442 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3e183fe2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 457]
19:06:54.500 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:06:54.507 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:06:54.526 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:06:54.527 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:06:59.016 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:07:00.228 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f2ea16e0-cd47-4c1e-a04f-73a865ad627f_config-0
19:07:00.350 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 55 ms to scan 1 urls, producing 3 keys and 6 values 
19:07:00.401 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
19:07:00.414 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
19:07:00.429 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
19:07:00.443 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
19:07:00.457 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
19:07:00.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ea16e0-cd47-4c1e-a04f-73a865ad627f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:07:00.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ea16e0-cd47-4c1e-a04f-73a865ad627f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001989d3b6af8
19:07:00.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ea16e0-cd47-4c1e-a04f-73a865ad627f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001989d3b6d18
19:07:00.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ea16e0-cd47-4c1e-a04f-73a865ad627f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:07:00.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ea16e0-cd47-4c1e-a04f-73a865ad627f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:07:00.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ea16e0-cd47-4c1e-a04f-73a865ad627f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:07:01.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ea16e0-cd47-4c1e-a04f-73a865ad627f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751368021471_127.0.0.1_10809
19:07:01.739 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ea16e0-cd47-4c1e-a04f-73a865ad627f_config-0] Notify connected event to listeners.
19:07:01.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ea16e0-cd47-4c1e-a04f-73a865ad627f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:07:01.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ea16e0-cd47-4c1e-a04f-73a865ad627f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001989d4f0668
19:07:01.929 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:07:07.501 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:07:07.502 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:07:07.502 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:07:07.755 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:07:08.706 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:07:08.708 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:07:08.709 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:07:20.111 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:07:26.260 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 475da5d9-48b9-4894-8152-3b75161c9578
19:07:26.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [475da5d9-48b9-4894-8152-3b75161c9578] RpcClient init label, labels = {module=naming, source=sdk}
19:07:26.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [475da5d9-48b9-4894-8152-3b75161c9578] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:07:26.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [475da5d9-48b9-4894-8152-3b75161c9578] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:07:26.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [475da5d9-48b9-4894-8152-3b75161c9578] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:07:26.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [475da5d9-48b9-4894-8152-3b75161c9578] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:07:26.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [475da5d9-48b9-4894-8152-3b75161c9578] Success to connect to server [localhost:8848] on start up, connectionId = 1751368046283_127.0.0.1_10857
19:07:26.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [475da5d9-48b9-4894-8152-3b75161c9578] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:07:26.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [475da5d9-48b9-4894-8152-3b75161c9578] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001989d4f0668
19:07:26.412 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [475da5d9-48b9-4894-8152-3b75161c9578] Notify connected event to listeners.
19:07:26.510 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:07:26.574 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:07:26.854 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 28.747 seconds (JVM running for 30.26)
19:07:26.911 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:07:26.912 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:07:26.913 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:07:27.073 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [475da5d9-48b9-4894-8152-3b75161c9578] Receive server push request, request = NotifySubscriberRequest, requestId = 22
19:07:27.100 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [475da5d9-48b9-4894-8152-3b75161c9578] Ack server push request, request = NotifySubscriberRequest, requestId = 22
19:19:04.137 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:19:06.013 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:19:06.014 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:26:14.564 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:26:14.576 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:26:14.918 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:26:14.918 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@25bc8c5e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:26:14.919 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751368046283_127.0.0.1_10857
19:26:14.922 [nacos-grpc-client-executor-237] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751368046283_127.0.0.1_10857]Ignore complete event,isRunning:false,isAbandon=false
19:26:14.927 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@17eed38f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 238]
19:26:15.130 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:26:15.137 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:26:15.149 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:26:15.150 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:26:15.152 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:26:15.152 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:26:25.803 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:26:26.869 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 469ea3a8-b6d4-4d0f-8bc8-1e8e6a857f8e_config-0
19:26:26.987 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
19:26:27.048 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
19:26:27.064 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
19:26:27.078 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
19:26:27.093 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
19:26:27.110 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
19:26:27.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [469ea3a8-b6d4-4d0f-8bc8-1e8e6a857f8e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:26:27.115 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [469ea3a8-b6d4-4d0f-8bc8-1e8e6a857f8e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001350139eaf8
19:26:27.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [469ea3a8-b6d4-4d0f-8bc8-1e8e6a857f8e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001350139ed18
19:26:27.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [469ea3a8-b6d4-4d0f-8bc8-1e8e6a857f8e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:26:27.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [469ea3a8-b6d4-4d0f-8bc8-1e8e6a857f8e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:26:27.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [469ea3a8-b6d4-4d0f-8bc8-1e8e6a857f8e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:26:28.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [469ea3a8-b6d4-4d0f-8bc8-1e8e6a857f8e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751369188180_127.0.0.1_12839
19:26:28.433 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [469ea3a8-b6d4-4d0f-8bc8-1e8e6a857f8e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:26:28.433 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [469ea3a8-b6d4-4d0f-8bc8-1e8e6a857f8e_config-0] Notify connected event to listeners.
19:26:28.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [469ea3a8-b6d4-4d0f-8bc8-1e8e6a857f8e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000013501518ad8
19:26:28.712 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:26:34.116 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:26:34.117 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:26:34.118 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:26:34.371 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:26:35.318 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:26:35.320 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:26:35.320 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:26:47.142 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:26:54.516 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of de90dd4d-d629-4564-a6a1-896eb5461fb5
19:26:54.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de90dd4d-d629-4564-a6a1-896eb5461fb5] RpcClient init label, labels = {module=naming, source=sdk}
19:26:54.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de90dd4d-d629-4564-a6a1-896eb5461fb5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:26:54.522 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de90dd4d-d629-4564-a6a1-896eb5461fb5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:26:54.523 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de90dd4d-d629-4564-a6a1-896eb5461fb5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:26:54.524 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de90dd4d-d629-4564-a6a1-896eb5461fb5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:26:54.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de90dd4d-d629-4564-a6a1-896eb5461fb5] Success to connect to server [localhost:8848] on start up, connectionId = 1751369214540_127.0.0.1_12916
19:26:54.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de90dd4d-d629-4564-a6a1-896eb5461fb5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:26:54.669 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de90dd4d-d629-4564-a6a1-896eb5461fb5] Notify connected event to listeners.
19:26:54.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de90dd4d-d629-4564-a6a1-896eb5461fb5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000013501518ad8
19:26:54.792 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:26:54.870 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:26:55.219 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 30.276 seconds (JVM running for 31.71)
19:26:55.240 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de90dd4d-d629-4564-a6a1-896eb5461fb5] Receive server push request, request = NotifySubscriberRequest, requestId = 26
19:26:55.257 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:26:55.259 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:26:55.260 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:26:55.269 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de90dd4d-d629-4564-a6a1-896eb5461fb5] Ack server push request, request = NotifySubscriberRequest, requestId = 26
19:26:55.625 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:27:09.919 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:27:09.919 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:33:09.728 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:33:09.733 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:33:10.073 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:33:10.074 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2b9a0b3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:33:10.074 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751369214540_127.0.0.1_12916
20:33:10.081 [nacos-grpc-client-executor-802] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751369214540_127.0.0.1_12916]Ignore complete event,isRunning:false,isAbandon=false
20:33:10.089 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6a2053f6[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 803]
20:33:10.268 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:33:10.273 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:33:10.291 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:33:10.292 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:33:10.297 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:33:10.298 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:34:47.025 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:34:48.066 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d5aa1558-a6ec-413a-9b78-ad8cce8ed719_config-0
20:34:48.175 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
20:34:48.234 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
20:34:48.246 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
20:34:48.260 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
20:34:48.272 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
20:34:48.288 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
20:34:48.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5aa1558-a6ec-413a-9b78-ad8cce8ed719_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:34:48.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5aa1558-a6ec-413a-9b78-ad8cce8ed719_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000018a2339f1c0
20:34:48.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5aa1558-a6ec-413a-9b78-ad8cce8ed719_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000018a2339f3e0
20:34:48.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5aa1558-a6ec-413a-9b78-ad8cce8ed719_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:34:48.296 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5aa1558-a6ec-413a-9b78-ad8cce8ed719_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:34:48.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5aa1558-a6ec-413a-9b78-ad8cce8ed719_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:34:49.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5aa1558-a6ec-413a-9b78-ad8cce8ed719_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751373289293_127.0.0.1_5716
20:34:49.541 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5aa1558-a6ec-413a-9b78-ad8cce8ed719_config-0] Notify connected event to listeners.
20:34:49.542 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5aa1558-a6ec-413a-9b78-ad8cce8ed719_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:34:49.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5aa1558-a6ec-413a-9b78-ad8cce8ed719_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018a23518fb0
20:34:49.737 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:34:54.434 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:34:54.435 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:34:54.435 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:34:54.674 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:34:55.611 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:34:55.614 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:34:55.614 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:35:05.072 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:35:09.005 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e7334f5d-74f0-49f2-ae47-1e9abf94caad
20:35:09.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7334f5d-74f0-49f2-ae47-1e9abf94caad] RpcClient init label, labels = {module=naming, source=sdk}
20:35:09.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7334f5d-74f0-49f2-ae47-1e9abf94caad] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:35:09.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7334f5d-74f0-49f2-ae47-1e9abf94caad] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:35:09.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7334f5d-74f0-49f2-ae47-1e9abf94caad] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:35:09.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7334f5d-74f0-49f2-ae47-1e9abf94caad] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:35:09.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7334f5d-74f0-49f2-ae47-1e9abf94caad] Success to connect to server [localhost:8848] on start up, connectionId = 1751373309022_127.0.0.1_5751
20:35:09.149 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7334f5d-74f0-49f2-ae47-1e9abf94caad] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:35:09.149 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7334f5d-74f0-49f2-ae47-1e9abf94caad] Notify connected event to listeners.
20:35:09.149 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7334f5d-74f0-49f2-ae47-1e9abf94caad] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018a23518fb0
20:35:09.233 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:35:09.286 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:35:09.486 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.428 seconds (JVM running for 37.863)
20:35:09.513 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:35:09.513 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:35:09.514 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:35:09.672 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:35:09.726 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7334f5d-74f0-49f2-ae47-1e9abf94caad] Receive server push request, request = NotifySubscriberRequest, requestId = 28
20:35:09.763 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e7334f5d-74f0-49f2-ae47-1e9abf94caad] Ack server push request, request = NotifySubscriberRequest, requestId = 28
20:35:19.756 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
20:35:19.756 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:35:54.364 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:35:54.386 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:35:54.720 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:35:54.721 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@********[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:35:54.721 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751373309022_127.0.0.1_5751
20:35:54.725 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751373309022_127.0.0.1_5751]Ignore complete event,isRunning:false,isAbandon=false
20:35:54.729 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5af6e5a3[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 22]
20:35:54.909 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:35:54.915 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:35:54.928 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:35:54.928 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:35:54.930 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:35:54.931 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:36:04.155 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:36:05.558 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c7d64a5f-a33b-43d9-b639-bb333b4d5217_config-0
20:36:05.661 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 51 ms to scan 1 urls, producing 3 keys and 6 values 
20:36:05.713 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
20:36:05.725 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
20:36:05.739 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
20:36:05.753 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
20:36:05.772 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
20:36:05.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7d64a5f-a33b-43d9-b639-bb333b4d5217_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:36:05.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7d64a5f-a33b-43d9-b639-bb333b4d5217_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f72639eaf8
20:36:05.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7d64a5f-a33b-43d9-b639-bb333b4d5217_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001f72639ed18
20:36:05.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7d64a5f-a33b-43d9-b639-bb333b4d5217_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:36:05.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7d64a5f-a33b-43d9-b639-bb333b4d5217_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:36:05.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7d64a5f-a33b-43d9-b639-bb333b4d5217_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:36:07.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7d64a5f-a33b-43d9-b639-bb333b4d5217_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751373366793_127.0.0.1_5898
20:36:07.065 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7d64a5f-a33b-43d9-b639-bb333b4d5217_config-0] Notify connected event to listeners.
20:36:07.066 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7d64a5f-a33b-43d9-b639-bb333b4d5217_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:36:07.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c7d64a5f-a33b-43d9-b639-bb333b4d5217_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f726518ad8
20:36:07.269 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:36:12.657 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:36:12.659 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:36:12.660 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:36:12.916 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:36:13.796 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:36:13.799 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:36:13.799 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:36:23.797 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:36:27.965 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5e7f3729-8b5b-4ae8-b29d-2857a6f4c3f5
20:36:27.966 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7f3729-8b5b-4ae8-b29d-2857a6f4c3f5] RpcClient init label, labels = {module=naming, source=sdk}
20:36:27.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7f3729-8b5b-4ae8-b29d-2857a6f4c3f5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:36:27.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7f3729-8b5b-4ae8-b29d-2857a6f4c3f5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:36:27.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7f3729-8b5b-4ae8-b29d-2857a6f4c3f5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:36:27.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7f3729-8b5b-4ae8-b29d-2857a6f4c3f5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:36:28.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7f3729-8b5b-4ae8-b29d-2857a6f4c3f5] Success to connect to server [localhost:8848] on start up, connectionId = 1751373387985_127.0.0.1_5932
20:36:28.108 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7f3729-8b5b-4ae8-b29d-2857a6f4c3f5] Notify connected event to listeners.
20:36:28.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7f3729-8b5b-4ae8-b29d-2857a6f4c3f5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:36:28.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7f3729-8b5b-4ae8-b29d-2857a6f4c3f5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f726518ad8
20:36:28.240 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:36:28.430 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.184 seconds (JVM running for 28.206)
20:36:28.452 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:36:28.453 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:36:28.454 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:36:28.522 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:36:28.528 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:36:28.694 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7f3729-8b5b-4ae8-b29d-2857a6f4c3f5] Receive server push request, request = NotifySubscriberRequest, requestId = 32
20:36:28.695 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e7f3729-8b5b-4ae8-b29d-2857a6f4c3f5] Ack server push request, request = NotifySubscriberRequest, requestId = 32
20:36:29.127 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:36:29.128 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@17f72262[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:36:29.128 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751373387985_127.0.0.1_5932
20:36:29.134 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751373387985_127.0.0.1_5932]Ignore complete event,isRunning:false,isAbandon=false
20:36:29.138 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@432aa5a3[Running, pool size = 9, active threads = 0, queued tasks = 0, completed tasks = 9]
20:36:29.209 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:36:29.215 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:36:29.254 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:36:29.254 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:36:36.993 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:36:38.104 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cc7d0f95-9f3e-40cd-bbc0-24fda96a88cf_config-0
20:36:38.215 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 60 ms to scan 1 urls, producing 3 keys and 6 values 
20:36:38.266 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
20:36:38.279 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
20:36:38.293 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
20:36:38.308 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
20:36:38.323 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
20:36:38.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc7d0f95-9f3e-40cd-bbc0-24fda96a88cf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:36:38.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc7d0f95-9f3e-40cd-bbc0-24fda96a88cf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000229813b68d8
20:36:38.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc7d0f95-9f3e-40cd-bbc0-24fda96a88cf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000229813b6af8
20:36:38.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc7d0f95-9f3e-40cd-bbc0-24fda96a88cf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:36:38.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc7d0f95-9f3e-40cd-bbc0-24fda96a88cf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:36:38.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc7d0f95-9f3e-40cd-bbc0-24fda96a88cf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:36:39.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc7d0f95-9f3e-40cd-bbc0-24fda96a88cf_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751373399304_127.0.0.1_5969
20:36:39.569 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc7d0f95-9f3e-40cd-bbc0-24fda96a88cf_config-0] Notify connected event to listeners.
20:36:39.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc7d0f95-9f3e-40cd-bbc0-24fda96a88cf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:36:39.572 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc7d0f95-9f3e-40cd-bbc0-24fda96a88cf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000229814f0ad8
20:36:39.762 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:36:44.752 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:36:44.753 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:36:44.753 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:36:45.009 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:36:45.894 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:36:45.896 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:36:45.897 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:36:55.935 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:37:00.038 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4d8985e9-fe1f-4781-93f1-b21bd75b2ca3
20:37:00.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d8985e9-fe1f-4781-93f1-b21bd75b2ca3] RpcClient init label, labels = {module=naming, source=sdk}
20:37:00.042 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d8985e9-fe1f-4781-93f1-b21bd75b2ca3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:37:00.042 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d8985e9-fe1f-4781-93f1-b21bd75b2ca3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:37:00.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d8985e9-fe1f-4781-93f1-b21bd75b2ca3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:37:00.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d8985e9-fe1f-4781-93f1-b21bd75b2ca3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:37:00.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d8985e9-fe1f-4781-93f1-b21bd75b2ca3] Success to connect to server [localhost:8848] on start up, connectionId = 1751373420055_127.0.0.1_6029
20:37:00.186 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d8985e9-fe1f-4781-93f1-b21bd75b2ca3] Notify connected event to listeners.
20:37:00.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d8985e9-fe1f-4781-93f1-b21bd75b2ca3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:37:00.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d8985e9-fe1f-4781-93f1-b21bd75b2ca3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000229814f0ad8
20:37:00.302 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:37:00.360 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:37:00.555 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.416 seconds (JVM running for 25.835)
20:37:00.586 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:37:00.587 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:37:00.590 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:37:00.760 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d8985e9-fe1f-4781-93f1-b21bd75b2ca3] Receive server push request, request = NotifySubscriberRequest, requestId = 34
20:37:00.794 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d8985e9-fe1f-4781-93f1-b21bd75b2ca3] Ack server push request, request = NotifySubscriberRequest, requestId = 34
20:37:10.273 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:37:12.483 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
20:37:12.483 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:38:14.024 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:38:14.029 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:38:14.363 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:38:14.363 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3c88ee5b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:38:14.363 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751373420055_127.0.0.1_6029
20:38:14.367 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751373420055_127.0.0.1_6029]Ignore complete event,isRunning:false,isAbandon=false
20:38:14.369 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1c863d99[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 23]
20:38:14.542 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:38:14.546 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:38:14.556 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:38:14.556 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:38:14.560 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:38:14.561 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:38:23.549 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:38:24.708 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 83e47d8d-5510-4cc5-a6b3-a509041a3146_config-0
20:38:24.818 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 54 ms to scan 1 urls, producing 3 keys and 6 values 
20:38:24.873 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
20:38:24.884 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
20:38:24.898 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
20:38:24.910 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
20:38:24.925 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
20:38:24.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e47d8d-5510-4cc5-a6b3-a509041a3146_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:38:24.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e47d8d-5510-4cc5-a6b3-a509041a3146_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000230cc39e8d8
20:38:24.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e47d8d-5510-4cc5-a6b3-a509041a3146_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000230cc39eaf8
20:38:24.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e47d8d-5510-4cc5-a6b3-a509041a3146_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:38:24.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e47d8d-5510-4cc5-a6b3-a509041a3146_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:38:24.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e47d8d-5510-4cc5-a6b3-a509041a3146_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:38:26.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e47d8d-5510-4cc5-a6b3-a509041a3146_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751373505997_127.0.0.1_6196
20:38:26.261 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e47d8d-5510-4cc5-a6b3-a509041a3146_config-0] Notify connected event to listeners.
20:38:26.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e47d8d-5510-4cc5-a6b3-a509041a3146_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:38:26.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83e47d8d-5510-4cc5-a6b3-a509041a3146_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000230cc518ad8
20:38:26.467 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:38:31.889 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:38:31.890 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:38:31.890 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:38:32.164 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:38:33.084 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:38:33.086 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:38:33.087 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:38:43.800 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:38:48.155 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c523b8bf-e748-4a4d-9e8b-5fe85d589d3a
20:38:48.156 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c523b8bf-e748-4a4d-9e8b-5fe85d589d3a] RpcClient init label, labels = {module=naming, source=sdk}
20:38:48.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c523b8bf-e748-4a4d-9e8b-5fe85d589d3a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:38:48.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c523b8bf-e748-4a4d-9e8b-5fe85d589d3a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:38:48.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c523b8bf-e748-4a4d-9e8b-5fe85d589d3a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:38:48.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c523b8bf-e748-4a4d-9e8b-5fe85d589d3a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:38:48.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c523b8bf-e748-4a4d-9e8b-5fe85d589d3a] Success to connect to server [localhost:8848] on start up, connectionId = 1751373528174_127.0.0.1_6251
20:38:48.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c523b8bf-e748-4a4d-9e8b-5fe85d589d3a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:38:48.294 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c523b8bf-e748-4a4d-9e8b-5fe85d589d3a] Notify connected event to listeners.
20:38:48.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c523b8bf-e748-4a4d-9e8b-5fe85d589d3a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000230cc518ad8
20:38:48.396 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:38:48.448 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:38:48.633 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.043 seconds (JVM running for 27.495)
20:38:48.659 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:38:48.661 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:38:48.662 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:38:48.878 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c523b8bf-e748-4a4d-9e8b-5fe85d589d3a] Receive server push request, request = NotifySubscriberRequest, requestId = 37
20:38:48.915 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c523b8bf-e748-4a4d-9e8b-5fe85d589d3a] Ack server push request, request = NotifySubscriberRequest, requestId = 37
20:38:49.274 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:38:59.449 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
20:38:59.450 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
20:38:59.450 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:38:59.455 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
20:38:59.469 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
20:38:59.469 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:58:15.696 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:58:15.701 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:58:16.048 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:58:16.052 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3520545e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:58:16.053 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751373528174_127.0.0.1_6251
20:58:16.059 [nacos-grpc-client-executor-243] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751373528174_127.0.0.1_6251]Ignore complete event,isRunning:false,isAbandon=false
20:58:16.058 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@193d6126[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 243]
20:58:16.237 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:58:16.238 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:58:16.243 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:58:16.243 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:58:16.246 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:58:16.247 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:58:24.640 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:58:25.725 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9ad9afe7-439f-477d-b1fe-4f239d0e4749_config-0
20:58:25.839 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
20:58:25.895 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
20:58:25.907 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
20:58:25.921 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
20:58:25.936 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
20:58:25.948 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
20:58:25.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad9afe7-439f-477d-b1fe-4f239d0e4749_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:58:25.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad9afe7-439f-477d-b1fe-4f239d0e4749_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001728139dd70
20:58:25.954 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad9afe7-439f-477d-b1fe-4f239d0e4749_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001728139df90
20:58:25.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad9afe7-439f-477d-b1fe-4f239d0e4749_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:58:25.956 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad9afe7-439f-477d-b1fe-4f239d0e4749_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:58:25.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad9afe7-439f-477d-b1fe-4f239d0e4749_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:58:27.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad9afe7-439f-477d-b1fe-4f239d0e4749_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751374706962_127.0.0.1_8295
20:58:27.228 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad9afe7-439f-477d-b1fe-4f239d0e4749_config-0] Notify connected event to listeners.
20:58:27.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad9afe7-439f-477d-b1fe-4f239d0e4749_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:58:27.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad9afe7-439f-477d-b1fe-4f239d0e4749_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017281517b78
20:58:27.427 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:58:32.794 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:58:32.795 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:58:32.795 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:58:33.072 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:58:33.975 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:58:33.978 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:58:33.978 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:58:44.655 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:58:48.985 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3f7cb761-46ff-497e-a394-7bf7f352177c
20:58:48.986 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f7cb761-46ff-497e-a394-7bf7f352177c] RpcClient init label, labels = {module=naming, source=sdk}
20:58:48.989 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f7cb761-46ff-497e-a394-7bf7f352177c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:58:48.989 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f7cb761-46ff-497e-a394-7bf7f352177c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:58:48.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f7cb761-46ff-497e-a394-7bf7f352177c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:58:48.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f7cb761-46ff-497e-a394-7bf7f352177c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:58:49.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f7cb761-46ff-497e-a394-7bf7f352177c] Success to connect to server [localhost:8848] on start up, connectionId = 1751374729005_127.0.0.1_8345
20:58:49.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f7cb761-46ff-497e-a394-7bf7f352177c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:58:49.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f7cb761-46ff-497e-a394-7bf7f352177c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017281517b78
20:58:49.134 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f7cb761-46ff-497e-a394-7bf7f352177c] Notify connected event to listeners.
20:58:49.227 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:58:49.301 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:58:49.510 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.79 seconds (JVM running for 27.252)
20:58:49.529 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:58:49.530 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:58:49.530 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:58:49.801 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f7cb761-46ff-497e-a394-7bf7f352177c] Receive server push request, request = NotifySubscriberRequest, requestId = 40
20:58:49.826 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f7cb761-46ff-497e-a394-7bf7f352177c] Ack server push request, request = NotifySubscriberRequest, requestId = 40
20:58:50.031 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:58:54.880 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
20:58:54.881 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
21:00:07.026 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
21:00:07.036 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
21:00:07.359 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
21:00:07.360 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2ec87086[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
21:00:07.360 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751374729005_127.0.0.1_8345
21:00:07.365 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751374729005_127.0.0.1_8345]Ignore complete event,isRunning:false,isAbandon=false
21:00:07.369 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@51ed9d72[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 26]
21:00:07.546 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
21:00:07.553 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
21:00:07.568 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
21:00:07.568 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
21:00:07.571 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
21:00:07.571 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
21:00:16.708 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:00:17.805 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a9ee77fa-542c-4935-979b-1a98520b18af_config-0
21:00:17.907 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
21:00:17.960 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
21:00:17.972 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
21:00:17.984 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
21:00:17.997 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
21:00:18.013 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
21:00:18.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9ee77fa-542c-4935-979b-1a98520b18af_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
21:00:18.019 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9ee77fa-542c-4935-979b-1a98520b18af_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000246e139e8d8
21:00:18.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9ee77fa-542c-4935-979b-1a98520b18af_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000246e139eaf8
21:00:18.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9ee77fa-542c-4935-979b-1a98520b18af_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
21:00:18.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9ee77fa-542c-4935-979b-1a98520b18af_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
21:00:18.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9ee77fa-542c-4935-979b-1a98520b18af_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
21:00:19.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9ee77fa-542c-4935-979b-1a98520b18af_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751374818978_127.0.0.1_8527
21:00:19.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9ee77fa-542c-4935-979b-1a98520b18af_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
21:00:19.232 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9ee77fa-542c-4935-979b-1a98520b18af_config-0] Notify connected event to listeners.
21:00:19.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9ee77fa-542c-4935-979b-1a98520b18af_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000246e1518668
21:00:19.460 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
21:00:24.472 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
21:00:24.473 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
21:00:24.474 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
21:00:24.705 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
21:00:25.550 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
21:00:25.552 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
21:00:25.553 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
21:00:35.489 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
21:00:39.414 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f2f0dc0c-9dc0-4843-816a-8fa06ff16450
21:00:39.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2f0dc0c-9dc0-4843-816a-8fa06ff16450] RpcClient init label, labels = {module=naming, source=sdk}
21:00:39.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2f0dc0c-9dc0-4843-816a-8fa06ff16450] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
21:00:39.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2f0dc0c-9dc0-4843-816a-8fa06ff16450] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
21:00:39.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2f0dc0c-9dc0-4843-816a-8fa06ff16450] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
21:00:39.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2f0dc0c-9dc0-4843-816a-8fa06ff16450] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
21:00:39.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2f0dc0c-9dc0-4843-816a-8fa06ff16450] Success to connect to server [localhost:8848] on start up, connectionId = 1751374839433_127.0.0.1_8571
21:00:39.563 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2f0dc0c-9dc0-4843-816a-8fa06ff16450] Notify connected event to listeners.
21:00:39.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2f0dc0c-9dc0-4843-816a-8fa06ff16450] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
21:00:39.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2f0dc0c-9dc0-4843-816a-8fa06ff16450] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000246e1518668
21:00:39.646 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
21:00:39.695 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
21:00:39.883 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.038 seconds (JVM running for 25.395)
21:00:39.904 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
21:00:39.905 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
21:00:39.905 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
21:00:40.195 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2f0dc0c-9dc0-4843-816a-8fa06ff16450] Receive server push request, request = NotifySubscriberRequest, requestId = 43
21:00:40.223 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2f0dc0c-9dc0-4843-816a-8fa06ff16450] Ack server push request, request = NotifySubscriberRequest, requestId = 43
21:00:40.532 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:00:44.211 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
21:00:44.212 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
21:00:44.214 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
21:00:44.220 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
21:00:44.229 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
21:00:44.232 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
21:00:44.232 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
21:00:44.233 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
21:00:44.235 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
21:00:44.237 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
21:00:44.250 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
21:00:44.251 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
21:00:44.252 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
21:00:44.253 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
21:04:32.400 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
21:04:32.410 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
21:04:32.760 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
21:04:32.761 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@17ec622e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
21:04:32.761 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751374839433_127.0.0.1_8571
21:04:32.765 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751374839433_127.0.0.1_8571]Ignore complete event,isRunning:false,isAbandon=false
21:04:32.773 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1a986d98[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 57]
21:04:32.959 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
21:04:32.959 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
21:04:32.966 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
21:04:32.966 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
21:04:32.970 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
21:04:32.973 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
21:04:41.572 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:04:42.663 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0
21:04:42.764 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
21:04:42.819 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
21:04:42.832 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
21:04:42.845 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
21:04:42.861 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
21:04:42.878 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
21:04:42.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
21:04:42.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000135e539f1c0
21:04:42.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000135e539f3e0
21:04:42.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
21:04:42.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
21:04:42.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
21:04:44.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751375084133_127.0.0.1_9017
21:04:44.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
21:04:44.417 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] Notify connected event to listeners.
21:04:44.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000135e5518fb0
21:04:44.620 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
21:04:49.719 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
21:04:49.720 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
21:04:49.720 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
21:04:49.960 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
21:04:50.877 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
21:04:50.880 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
21:04:50.881 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
21:05:01.967 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
21:05:08.237 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d5d819d6-69bb-4eef-aa4b-1618d53ced37
21:05:08.238 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] RpcClient init label, labels = {module=naming, source=sdk}
21:05:08.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
21:05:08.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
21:05:08.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
21:05:08.242 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
21:05:08.385 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Success to connect to server [localhost:8848] on start up, connectionId = 1751375108256_127.0.0.1_9094
21:05:08.386 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
21:05:08.386 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Notify connected event to listeners.
21:05:08.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000135e5518fb0
21:05:08.467 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
21:05:08.515 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
21:05:08.679 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 28.0 seconds (JVM running for 30.588)
21:05:08.700 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
21:05:08.701 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
21:05:08.701 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
21:05:08.985 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Receive server push request, request = NotifySubscriberRequest, requestId = 45
21:05:09.005 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Ack server push request, request = NotifySubscriberRequest, requestId = 45
21:05:09.339 [RMI TCP Connection(8)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:05:14.597 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
21:05:14.597 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
21:05:14.598 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
21:05:14.599 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
21:05:14.603 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
21:05:14.615 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
21:05:14.615 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
21:05:14.616 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
21:05:14.618 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
21:05:14.618 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
21:05:14.651 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
21:05:14.652 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
21:05:14.653 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
21:05:14.654 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
21:14:00.884 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Server healthy check fail, currentConnection = 1751375108256_127.0.0.1_9094
21:14:00.883 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] Server healthy check fail, currentConnection = 1751375084133_127.0.0.1_9017
21:14:00.887 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:14:00.887 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:14:01.028 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] Success to connect a server [localhost:8848], connectionId = 1751375640912_127.0.0.1_10003
21:14:01.030 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Success to connect a server [localhost:8848], connectionId = 1751375640914_127.0.0.1_10002
21:14:01.031 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751375084133_127.0.0.1_9017
21:14:01.033 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Abandon prev connection, server is localhost:8848, connectionId is 1751375108256_127.0.0.1_9094
21:14:01.034 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751375084133_127.0.0.1_9017
21:14:01.036 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751375108256_127.0.0.1_9094
21:14:01.044 [nacos-grpc-client-executor-87] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751375084133_127.0.0.1_9017]Ignore complete event,isRunning:false,isAbandon=true
21:14:01.044 [nacos-grpc-client-executor-81] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751375108256_127.0.0.1_9094]Ignore complete event,isRunning:false,isAbandon=true
21:14:01.140 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] Notify disconnected event to listeners
21:14:01.143 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Notify disconnected event to listeners
21:14:01.148 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c637be35-fbb6-4af0-bdc8-8c19e619702d_config-0] Notify connected event to listeners.
21:14:01.150 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Server check success, currentServer is localhost:8848 
21:14:01.159 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Notify connected event to listeners.
21:14:01.696 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
21:14:01.702 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
21:14:02.036 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
21:14:02.036 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@28c9c8f2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
21:14:02.037 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751375640914_127.0.0.1_10002
21:14:02.041 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2ddc6651[Running, pool size = 11, active threads = 1, queued tasks = 0, completed tasks = 85]
21:14:02.041 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d819d6-69bb-4eef-aa4b-1618d53ced37] Notify disconnected event to listeners
21:14:02.214 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
21:14:02.215 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
21:14:02.218 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
21:14:02.218 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
21:14:02.222 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
21:14:02.225 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
21:14:11.080 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:14:12.244 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 11cdc524-52a7-4d29-8428-62fbad1812a0_config-0
21:14:12.343 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
21:14:12.396 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
21:14:12.409 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
21:14:12.424 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
21:14:12.436 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
21:14:12.453 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
21:14:12.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
21:14:12.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023e1139eaf8
21:14:12.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000023e1139ed18
21:14:12.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
21:14:12.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
21:14:12.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
21:14:13.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751375653576_127.0.0.1_10041
21:14:13.842 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] Notify connected event to listeners.
21:14:13.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
21:14:13.845 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023e11518ad8
21:14:14.075 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
21:14:19.819 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
21:14:19.821 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
21:14:19.821 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
21:14:20.065 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
21:14:20.975 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
21:14:20.977 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
21:14:20.978 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
21:14:31.544 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
21:14:35.676 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f
21:14:35.676 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] RpcClient init label, labels = {module=naming, source=sdk}
21:14:35.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
21:14:35.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
21:14:35.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
21:14:35.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
21:14:35.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Success to connect to server [localhost:8848] on start up, connectionId = 1751375675691_127.0.0.1_10101
21:14:35.822 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Notify connected event to listeners.
21:14:35.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
21:14:35.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023e11518ad8
21:14:35.922 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
21:14:35.984 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
21:14:36.150 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.043 seconds (JVM running for 27.491)
21:14:36.178 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
21:14:36.180 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
21:14:36.181 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
21:14:36.329 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:14:36.416 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Receive server push request, request = NotifySubscriberRequest, requestId = 49
21:14:36.481 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Ack server push request, request = NotifySubscriberRequest, requestId = 49
21:14:52.363 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Receive server push request, request = NotifySubscriberRequest, requestId = 53
21:14:52.364 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Ack server push request, request = NotifySubscriberRequest, requestId = 53
21:14:53.714 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
21:14:53.714 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
21:14:53.774 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
21:14:53.774 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
21:22:03.143 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Server healthy check fail, currentConnection = 1751375675691_127.0.0.1_10101
21:22:03.143 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] Server healthy check fail, currentConnection = 1751375653576_127.0.0.1_10041
21:22:06.170 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:22:06.170 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:23:31.692 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Success to connect a server [localhost:8848], connectionId = 1751376211546_127.0.0.1_11103
21:23:31.692 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] Success to connect a server [localhost:8848], connectionId = 1751376211545_127.0.0.1_11102
21:23:31.693 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Abandon prev connection, server is localhost:8848, connectionId is 1751375675691_127.0.0.1_10101
21:23:31.693 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751375653576_127.0.0.1_10041
21:23:31.695 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751375675691_127.0.0.1_10101
21:23:31.695 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751375653576_127.0.0.1_10041
21:23:31.697 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] Notify disconnected event to listeners
21:23:31.698 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Notify disconnected event to listeners
21:23:31.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11cdc524-52a7-4d29-8428-62fbad1812a0_config-0] Notify connected event to listeners.
21:23:31.708 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4e01c8-b52b-4e6f-b7c7-11b2f60eab8f] Notify connected event to listeners.
21:23:32.441 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
21:23:32.448 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
21:23:32.790 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
21:23:32.791 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@12e6478e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
21:23:32.791 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751376211546_127.0.0.1_11103
21:23:32.792 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3068cc40[Running, pool size = 9, active threads = 0, queued tasks = 0, completed tasks = 49]
21:23:32.971 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
21:23:32.974 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
21:23:32.984 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
21:23:32.985 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
21:23:32.987 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
21:23:32.990 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
21:23:32.993 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
21:23:32.993 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
