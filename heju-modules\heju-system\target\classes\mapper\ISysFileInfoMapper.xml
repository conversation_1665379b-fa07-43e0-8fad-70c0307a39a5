<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.heju.system.file.mapper.SysFileInfoMapper">
    <select id="selectAdminList" resultType="com.heju.system.file.domain.dto.SysFileInfoDto">
        SELECT
        a.*,
        b.`name` AS classifyName,
        c.`name` AS positionName,
        c.`id` AS positionId,
        f.user_name AS createName
        FROM sys_file_info a
        LEFT JOIN sys_file_classify b ON a.classify_id = b.id
        LEFT JOIN sys_user f ON f.id = a.create_by
        LEFT JOIN sys_file_position c ON a.position_id = c.id
        where a.del_flag=0
        AND a.position_id IS NOT NULL
        <if test="query.positionId != null and query.positionId != ''">
            AND a.position_id = #{query.positionId}
        </if>
        <if test="query.classifyId != null and query.classifyId != ''">
            AND a.classify_id = #{query.classifyId}
        </if>
        <if test="query.name != null and query.name.trim() != ''">
            AND a.name LIKE CONCAT('%', #{query.name}, '%')
        </if>
        ORDER BY a.create_time DESC
    </select>


<!--    <select id="selectByQuery" resultType="com.heju.system.file.domain.dto.SysFileInfoDto">-->
<!--        SELECT-->
<!--        a.*,-->
<!--        b.`name` AS classifyName,-->
<!--        c.`name` AS positionName,-->
<!--        c.`id` AS positionId,-->
<!--        d.operate_type,-->
<!--        e.is_view,-->
<!--        e.is_download,-->
<!--        f.user_name AS createName-->
<!--        FROM sys_file_info a-->
<!--        LEFT JOIN sys_file_classify b ON a.classify_id = b.id-->
<!--        LEFT JOIN sys_user f ON f.id = a.create_by-->
<!--        LEFT JOIN sys_file_position c ON a.position_id = c.id-->
<!--        LEFT JOIN sys_file_role_merge d ON d.file_id = a.id-->
<!--        LEFT JOIN sys_file_borrow_record e ON e.file_id = a.id-->
<!--        WHERE a.del_flag = 0-->
<!--        &lt;!&ndash; 权限控制：当前用户创建 OR 角色权限 OR 借阅中 &ndash;&gt;-->
<!--        <if test="query.createBy != null">-->
<!--            AND (-->
<!--            a.create_by = #{query.createBy}-->
<!--            <if test="query.roleIds != null and query.roleIds.length > 0">-->
<!--                OR d.role_id IN-->
<!--                <foreach collection="query.roleIds" item="roleId" open="(" separator="," close=")">-->
<!--                    #{roleId}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            OR (e.borrow_user_id = #{query.createBy} AND NOW() BETWEEN e.start_time AND e.end_time)-->
<!--            )-->
<!--        </if>-->

<!--        &lt;!&ndash; 动态查询条件 &ndash;&gt;-->
<!--        <if test="query.positionId != null and query.positionId != ''">-->
<!--            AND a.position_id = #{query.positionId}-->
<!--        </if>-->
<!--        <if test="query.classifyId != null and query.classifyId != ''">-->
<!--            AND a.classify_id = #{query.classifyId}-->
<!--        </if>-->
<!--        <if test="query.name != null and query.name.trim() != ''">-->
<!--            AND a.name LIKE CONCAT('%', #{query.name}, '%')-->
<!--        </if>-->
<!--    </select>-->

    <select id="selectInfoById" resultType="com.heju.system.file.domain.dto.SysFileInfoDto">
        SELECT
            a.*,
            f.user_name AS createName
        FROM sys_file_info a
                 LEFT JOIN sys_user f ON f.id = a.create_by
        WHERE a.id = #{id}
    </select>

    <select id="selectExpiredFiles" resultType="com.heju.system.file.domain.dto.SysFileInfoDto">
        SELECT
            a.*
        FROM sys_file_info a
            WHERE delete_time IS NOT NULL
            AND del_flag = 1
            AND delete_time &lt; DATE_SUB(NOW(), INTERVAL 30 DAY)
    </select>

</mapper>

