10:04:05.210 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:04:06.013 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 44af8c09-1fa2-4100-8f5e-3d67f492a1e8_config-0
10:04:06.084 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 32 ms to scan 1 urls, producing 3 keys and 6 values 
10:04:06.115 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
10:04:06.132 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 3 keys and 10 values 
10:04:06.143 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 4 ms to scan 1 urls, producing 1 keys and 5 values 
10:04:06.160 [main] INFO  o.r.Reflections - [scan,232] - <PERSON>flections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
10:04:06.173 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
10:04:06.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44af8c09-1fa2-4100-8f5e-3d67f492a1e8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:04:06.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44af8c09-1fa2-4100-8f5e-3d67f492a1e8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002b301397268
10:04:06.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44af8c09-1fa2-4100-8f5e-3d67f492a1e8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002b301397488
10:04:06.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44af8c09-1fa2-4100-8f5e-3d67f492a1e8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:04:06.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44af8c09-1fa2-4100-8f5e-3d67f492a1e8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:04:06.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44af8c09-1fa2-4100-8f5e-3d67f492a1e8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:07.672 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44af8c09-1fa2-4100-8f5e-3d67f492a1e8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752372247455_127.0.0.1_8169
10:04:07.672 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44af8c09-1fa2-4100-8f5e-3d67f492a1e8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:07.672 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44af8c09-1fa2-4100-8f5e-3d67f492a1e8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002b30150f500
10:04:07.688 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44af8c09-1fa2-4100-8f5e-3d67f492a1e8_config-0] Notify connected event to listeners.
10:04:08.034 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:04:10.424 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
10:04:10.424 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:04:10.425 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:04:10.552 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:04:12.069 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:04:13.602 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1a5b268a-4eff-4f8f-86cc-5447a59e860b
10:04:13.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] RpcClient init label, labels = {module=naming, source=sdk}
10:04:13.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:04:13.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:04:13.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:04:13.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:13.729 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Success to connect to server [localhost:8848] on start up, connectionId = 1752372253616_127.0.0.1_8175
10:04:13.729 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:13.729 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Notify connected event to listeners.
10:04:13.729 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002b30150f500
10:04:13.801 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
10:04:13.842 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
10:04:14.006 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 9.532 seconds (JVM running for 17.179)
10:04:14.020 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
10:04:14.021 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
10:04:14.023 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
10:04:14.594 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 1
10:04:14.610 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 1
10:11:40.049 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:11:46.098 [nacos-grpc-client-executor-105] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 10
10:11:46.103 [nacos-grpc-client-executor-105] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 10
10:13:28.322 [nacos-grpc-client-executor-127] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 11
10:13:28.323 [nacos-grpc-client-executor-127] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 11
10:20:12.808 [nacos-grpc-client-executor-210] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 16
10:20:12.828 [nacos-grpc-client-executor-210] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 16
10:20:29.182 [nacos-grpc-client-executor-215] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 19
10:20:29.201 [nacos-grpc-client-executor-215] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 19
10:47:03.776 [nacos-grpc-client-executor-535] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 22
10:47:03.806 [nacos-grpc-client-executor-535] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 22
10:47:29.239 [nacos-grpc-client-executor-540] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 24
10:47:29.256 [nacos-grpc-client-executor-540] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 24
13:48:39.548 [nacos-grpc-client-executor-2713] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 27
13:48:39.556 [nacos-grpc-client-executor-2713] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 27
13:48:57.931 [nacos-grpc-client-executor-2716] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 30
13:48:57.946 [nacos-grpc-client-executor-2716] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 30
14:20:40.232 [nacos-grpc-client-executor-3097] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 32
14:20:40.246 [nacos-grpc-client-executor-3097] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 32
14:20:58.487 [nacos-grpc-client-executor-3101] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 35
14:20:58.501 [nacos-grpc-client-executor-3101] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 35
14:22:07.441 [nacos-grpc-client-executor-3114] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 37
14:22:07.453 [nacos-grpc-client-executor-3114] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 37
14:22:24.180 [nacos-grpc-client-executor-3118] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 40
14:22:24.197 [nacos-grpc-client-executor-3118] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 40
14:23:44.715 [nacos-grpc-client-executor-3134] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 42
14:23:44.733 [nacos-grpc-client-executor-3134] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 42
14:24:11.379 [nacos-grpc-client-executor-3139] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Receive server push request, request = NotifySubscriberRequest, requestId = 45
14:24:11.395 [nacos-grpc-client-executor-3139] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a5b268a-4eff-4f8f-86cc-5447a59e860b] Ack server push request, request = NotifySubscriberRequest, requestId = 45
19:20:01.511 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:20:01.511 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:20:01.872 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:20:01.872 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1d11e047[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:20:01.878 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752372253616_127.0.0.1_8175
19:20:01.880 [nacos-grpc-client-executor-6686] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752372253616_127.0.0.1_8175]Ignore complete event,isRunning:false,isAbandon=false
19:20:01.885 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@767473d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6687]
19:22:24.650 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:22:26.364 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5f0bcaf6-cacf-46f2-9ded-e3c7fa3761d6_config-0
19:22:26.593 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 102 ms to scan 1 urls, producing 3 keys and 6 values 
19:22:26.669 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 4 keys and 9 values 
19:22:26.686 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
19:22:26.708 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 5 values 
19:22:26.732 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
19:22:26.750 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
19:22:26.757 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0bcaf6-cacf-46f2-9ded-e3c7fa3761d6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:22:26.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0bcaf6-cacf-46f2-9ded-e3c7fa3761d6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000024dc33b2f18
19:22:26.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0bcaf6-cacf-46f2-9ded-e3c7fa3761d6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000024dc33b3138
19:22:26.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0bcaf6-cacf-46f2-9ded-e3c7fa3761d6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:22:26.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0bcaf6-cacf-46f2-9ded-e3c7fa3761d6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:22:26.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0bcaf6-cacf-46f2-9ded-e3c7fa3761d6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:29.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0bcaf6-cacf-46f2-9ded-e3c7fa3761d6_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752405748901_127.0.0.1_2853
19:22:29.233 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0bcaf6-cacf-46f2-9ded-e3c7fa3761d6_config-0] Notify connected event to listeners.
19:22:29.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0bcaf6-cacf-46f2-9ded-e3c7fa3761d6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:22:29.234 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f0bcaf6-cacf-46f2-9ded-e3c7fa3761d6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000024dc34ecd90
19:22:29.457 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:22:34.660 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
19:22:34.661 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:22:34.661 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:22:34.946 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:22:39.886 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:22:43.347 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 594041da-9124-4918-826b-92be105e68ca
19:22:43.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [594041da-9124-4918-826b-92be105e68ca] RpcClient init label, labels = {module=naming, source=sdk}
19:22:43.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [594041da-9124-4918-826b-92be105e68ca] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:22:43.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [594041da-9124-4918-826b-92be105e68ca] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:22:43.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [594041da-9124-4918-826b-92be105e68ca] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:22:43.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [594041da-9124-4918-826b-92be105e68ca] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:43.524 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [594041da-9124-4918-826b-92be105e68ca] Success to connect to server [localhost:8848] on start up, connectionId = 1752405763386_127.0.0.1_3024
19:22:43.525 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [594041da-9124-4918-826b-92be105e68ca] Notify connected event to listeners.
19:22:43.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [594041da-9124-4918-826b-92be105e68ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:22:43.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [594041da-9124-4918-826b-92be105e68ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000024dc34ecd90
19:22:43.702 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
19:22:43.845 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
19:22:44.128 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [594041da-9124-4918-826b-92be105e68ca] Receive server push request, request = NotifySubscriberRequest, requestId = 5
19:22:44.164 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [594041da-9124-4918-826b-92be105e68ca] Ack server push request, request = NotifySubscriberRequest, requestId = 5
19:22:44.555 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 21.922 seconds (JVM running for 26.368)
19:22:44.586 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
19:22:44.590 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
19:22:44.622 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
19:34:22.133 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:34:22.137 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:34:22.461 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:34:22.461 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@526153a8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:34:22.461 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752405763386_127.0.0.1_3024
19:34:22.463 [nacos-grpc-client-executor-149] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752405763386_127.0.0.1_3024]Ignore complete event,isRunning:false,isAbandon=false
19:34:22.465 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@446cd48c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 150]
