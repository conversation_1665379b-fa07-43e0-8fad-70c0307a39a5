package com.heju.system.file.service;

import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.file.domain.dto.SysFileInfoDto;
import com.heju.system.file.domain.dto.SysFileRecordDto;
import com.heju.system.file.domain.query.SysFileRecordQuery;
import org.springframework.web.bind.annotation.PathVariable;

import java.io.Serializable;
import java.util.List;

/**
 * 文件操作记录管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysFileRecordService extends IBaseService<SysFileRecordQuery, SysFileRecordDto> {

    /**
     * 文件操作记录列表
     * @param fileId
     * @return
     */
    List<SysFileRecordDto> selectByFileId(@PathVariable Serializable fileId);
}
