package com.heju.tenant.api.tenant.domain.model;

import com.github.pagehelper.Page;
import com.heju.tenant.api.tenant.domain.dto.TeTenantDto;
import com.heju.tenant.api.tenant.domain.po.TeTenantPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:53+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class TeTenantConverterImpl implements TeTenantConverter {

    @Override
    public TeTenantDto mapperDto(TeTenantPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        TeTenantDto teTenantDto = new TeTenantDto();

        teTenantDto.setId( arg0.getId() );
        teTenantDto.setSourceName( arg0.getSourceName() );
        teTenantDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            teTenantDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        teTenantDto.setStatus( arg0.getStatus() );
        teTenantDto.setSort( arg0.getSort() );
        teTenantDto.setRemark( arg0.getRemark() );
        teTenantDto.setCreateBy( arg0.getCreateBy() );
        teTenantDto.setCreateTime( arg0.getCreateTime() );
        teTenantDto.setUpdateBy( arg0.getUpdateBy() );
        teTenantDto.setUpdateTime( arg0.getUpdateTime() );
        teTenantDto.setDelFlag( arg0.getDelFlag() );
        teTenantDto.setCreateName( arg0.getCreateName() );
        teTenantDto.setUpdateName( arg0.getUpdateName() );
        teTenantDto.setStrategyId( arg0.getStrategyId() );
        teTenantDto.setName( arg0.getName() );
        teTenantDto.setSystemName( arg0.getSystemName() );
        teTenantDto.setNick( arg0.getNick() );
        teTenantDto.setPhone( arg0.getPhone() );
        teTenantDto.setLogo( arg0.getLogo() );
        teTenantDto.setNameFrequency( arg0.getNameFrequency() );
        teTenantDto.setIsLessor( arg0.getIsLessor() );
        teTenantDto.setIsDefault( arg0.getIsDefault() );

        return teTenantDto;
    }

    @Override
    public List<TeTenantDto> mapperDto(Collection<TeTenantPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<TeTenantDto> list = new ArrayList<TeTenantDto>( arg0.size() );
        for ( TeTenantPo teTenantPo : arg0 ) {
            list.add( mapperDto( teTenantPo ) );
        }

        return list;
    }

    @Override
    public Page<TeTenantDto> mapperPageDto(Collection<TeTenantPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<TeTenantDto> page = new Page<TeTenantDto>();
        for ( TeTenantPo teTenantPo : arg0 ) {
            page.add( mapperDto( teTenantPo ) );
        }

        return page;
    }

    @Override
    public TeTenantPo mapperPo(TeTenantDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        TeTenantPo teTenantPo = new TeTenantPo();

        teTenantPo.setId( arg0.getId() );
        teTenantPo.setSourceName( arg0.getSourceName() );
        teTenantPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            teTenantPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        teTenantPo.setStatus( arg0.getStatus() );
        teTenantPo.setSort( arg0.getSort() );
        teTenantPo.setRemark( arg0.getRemark() );
        teTenantPo.setCreateBy( arg0.getCreateBy() );
        teTenantPo.setCreateTime( arg0.getCreateTime() );
        teTenantPo.setUpdateBy( arg0.getUpdateBy() );
        teTenantPo.setUpdateTime( arg0.getUpdateTime() );
        teTenantPo.setDelFlag( arg0.getDelFlag() );
        teTenantPo.setCreateName( arg0.getCreateName() );
        teTenantPo.setUpdateName( arg0.getUpdateName() );
        teTenantPo.setStrategyId( arg0.getStrategyId() );
        teTenantPo.setName( arg0.getName() );
        teTenantPo.setSystemName( arg0.getSystemName() );
        teTenantPo.setNick( arg0.getNick() );
        teTenantPo.setPhone( arg0.getPhone() );
        teTenantPo.setLogo( arg0.getLogo() );
        teTenantPo.setNameFrequency( arg0.getNameFrequency() );
        teTenantPo.setIsLessor( arg0.getIsLessor() );
        teTenantPo.setIsDefault( arg0.getIsDefault() );

        return teTenantPo;
    }

    @Override
    public List<TeTenantPo> mapperPo(Collection<TeTenantDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<TeTenantPo> list = new ArrayList<TeTenantPo>( arg0.size() );
        for ( TeTenantDto teTenantDto : arg0 ) {
            list.add( mapperPo( teTenantDto ) );
        }

        return list;
    }

    @Override
    public Page<TeTenantPo> mapperPagePo(Collection<TeTenantDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<TeTenantPo> page = new Page<TeTenantPo>();
        for ( TeTenantDto teTenantDto : arg0 ) {
            page.add( mapperPo( teTenantDto ) );
        }

        return page;
    }
}
