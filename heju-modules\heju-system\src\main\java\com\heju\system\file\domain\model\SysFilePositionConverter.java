package com.heju.system.file.domain.model;

import com.heju.common.core.web.entity.model.TreeConverter;
import com.heju.system.file.domain.dto.SysFilePositionDto;
import com.heju.system.file.domain.po.SysFilePositionPo;
import com.heju.system.file.domain.query.SysFilePositionQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 文件存储位置 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysFilePositionConverter extends TreeConverter<SysFilePositionQuery, SysFilePositionDto, SysFilePositionPo> {
}
