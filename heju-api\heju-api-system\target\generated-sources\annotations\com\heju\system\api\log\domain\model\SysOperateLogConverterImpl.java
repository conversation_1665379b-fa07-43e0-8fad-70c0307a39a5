package com.heju.system.api.log.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.api.log.domain.dto.SysOperateLogDto;
import com.heju.system.api.log.domain.po.SysOperateLogPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:51+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysOperateLogConverterImpl implements SysOperateLogConverter {

    @Override
    public SysOperateLogDto mapperDto(SysOperateLogPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysOperateLogDto sysOperateLogDto = new SysOperateLogDto();

        sysOperateLogDto.setId( arg0.getId() );
        sysOperateLogDto.setSourceName( arg0.getSourceName() );
        sysOperateLogDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysOperateLogDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysOperateLogDto.setName( arg0.getName() );
        sysOperateLogDto.setSort( arg0.getSort() );
        sysOperateLogDto.setRemark( arg0.getRemark() );
        sysOperateLogDto.setCreateBy( arg0.getCreateBy() );
        sysOperateLogDto.setCreateTime( arg0.getCreateTime() );
        sysOperateLogDto.setUpdateBy( arg0.getUpdateBy() );
        sysOperateLogDto.setUpdateTime( arg0.getUpdateTime() );
        sysOperateLogDto.setDelFlag( arg0.getDelFlag() );
        sysOperateLogDto.setCreateName( arg0.getCreateName() );
        sysOperateLogDto.setUpdateName( arg0.getUpdateName() );
        sysOperateLogDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysOperateLogDto.setTitle( arg0.getTitle() );
        sysOperateLogDto.setBusinessType( arg0.getBusinessType() );
        sysOperateLogDto.setMethod( arg0.getMethod() );
        sysOperateLogDto.setRequestMethod( arg0.getRequestMethod() );
        sysOperateLogDto.setOperateType( arg0.getOperateType() );
        sysOperateLogDto.setUserId( arg0.getUserId() );
        sysOperateLogDto.setUserName( arg0.getUserName() );
        sysOperateLogDto.setUserNick( arg0.getUserNick() );
        sysOperateLogDto.setUrl( arg0.getUrl() );
        sysOperateLogDto.setIp( arg0.getIp() );
        sysOperateLogDto.setParam( arg0.getParam() );
        sysOperateLogDto.setLocation( arg0.getLocation() );
        sysOperateLogDto.setJsonResult( arg0.getJsonResult() );
        sysOperateLogDto.setStatus( arg0.getStatus() );
        sysOperateLogDto.setErrorMsg( arg0.getErrorMsg() );
        sysOperateLogDto.setOperateTime( arg0.getOperateTime() );
        sysOperateLogDto.setCostTime( arg0.getCostTime() );

        return sysOperateLogDto;
    }

    @Override
    public List<SysOperateLogDto> mapperDto(Collection<SysOperateLogPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysOperateLogDto> list = new ArrayList<SysOperateLogDto>( arg0.size() );
        for ( SysOperateLogPo sysOperateLogPo : arg0 ) {
            list.add( mapperDto( sysOperateLogPo ) );
        }

        return list;
    }

    @Override
    public Page<SysOperateLogDto> mapperPageDto(Collection<SysOperateLogPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysOperateLogDto> page = new Page<SysOperateLogDto>();
        for ( SysOperateLogPo sysOperateLogPo : arg0 ) {
            page.add( mapperDto( sysOperateLogPo ) );
        }

        return page;
    }

    @Override
    public SysOperateLogPo mapperPo(SysOperateLogDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysOperateLogPo sysOperateLogPo = new SysOperateLogPo();

        sysOperateLogPo.setId( arg0.getId() );
        sysOperateLogPo.setSourceName( arg0.getSourceName() );
        sysOperateLogPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysOperateLogPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysOperateLogPo.setName( arg0.getName() );
        sysOperateLogPo.setSort( arg0.getSort() );
        sysOperateLogPo.setRemark( arg0.getRemark() );
        sysOperateLogPo.setCreateBy( arg0.getCreateBy() );
        sysOperateLogPo.setCreateTime( arg0.getCreateTime() );
        sysOperateLogPo.setUpdateBy( arg0.getUpdateBy() );
        sysOperateLogPo.setUpdateTime( arg0.getUpdateTime() );
        sysOperateLogPo.setDelFlag( arg0.getDelFlag() );
        sysOperateLogPo.setCreateName( arg0.getCreateName() );
        sysOperateLogPo.setUpdateName( arg0.getUpdateName() );
        sysOperateLogPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysOperateLogPo.setTitle( arg0.getTitle() );
        sysOperateLogPo.setBusinessType( arg0.getBusinessType() );
        sysOperateLogPo.setMethod( arg0.getMethod() );
        sysOperateLogPo.setRequestMethod( arg0.getRequestMethod() );
        sysOperateLogPo.setOperateType( arg0.getOperateType() );
        sysOperateLogPo.setUserId( arg0.getUserId() );
        sysOperateLogPo.setUserName( arg0.getUserName() );
        sysOperateLogPo.setUserNick( arg0.getUserNick() );
        sysOperateLogPo.setUrl( arg0.getUrl() );
        sysOperateLogPo.setIp( arg0.getIp() );
        sysOperateLogPo.setParam( arg0.getParam() );
        sysOperateLogPo.setLocation( arg0.getLocation() );
        sysOperateLogPo.setJsonResult( arg0.getJsonResult() );
        sysOperateLogPo.setStatus( arg0.getStatus() );
        sysOperateLogPo.setErrorMsg( arg0.getErrorMsg() );
        sysOperateLogPo.setOperateTime( arg0.getOperateTime() );
        sysOperateLogPo.setCostTime( arg0.getCostTime() );

        return sysOperateLogPo;
    }

    @Override
    public List<SysOperateLogPo> mapperPo(Collection<SysOperateLogDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysOperateLogPo> list = new ArrayList<SysOperateLogPo>( arg0.size() );
        for ( SysOperateLogDto sysOperateLogDto : arg0 ) {
            list.add( mapperPo( sysOperateLogDto ) );
        }

        return list;
    }

    @Override
    public Page<SysOperateLogPo> mapperPagePo(Collection<SysOperateLogDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysOperateLogPo> page = new Page<SysOperateLogPo>();
        for ( SysOperateLogDto sysOperateLogDto : arg0 ) {
            page.add( mapperPo( sysOperateLogDto ) );
        }

        return page;
    }
}
