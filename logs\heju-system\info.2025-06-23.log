14:26:59.305 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:27:02.422 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 25cced7c-0297-419e-97f7-5c1af6019436_config-0
14:27:02.804 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 130 ms to scan 1 urls, producing 3 keys and 6 values 
14:27:03.045 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 60 ms to scan 1 urls, producing 4 keys and 9 values 
14:27:03.075 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 3 keys and 10 values 
14:27:03.110 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 1 keys and 5 values 
14:27:03.145 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 24 ms to scan 1 urls, producing 1 keys and 7 values 
14:27:03.172 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 2 keys and 8 values 
14:27:03.180 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:27:03.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002700139cfb8
14:27:03.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002700139d1d8
14:27:03.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:27:03.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:27:03.234 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:27:05.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:27:05.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:27:05.930 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:27:05.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:05.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000270014aaef8
14:27:06.056 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:06.283 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:06.608 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:07.030 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:07.551 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:08.191 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:08.447 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:27:08.968 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:09.789 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:10.709 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:11.891 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:13.194 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:14.511 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:15.934 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:17.466 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:17.916 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:27:17.955 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:27:17.956 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:27:18.412 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:27:19.094 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:19.876 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:27:19.878 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:27:19.879 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:27:20.806 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:22.638 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:26.332 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:28.559 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:30.749 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
14:27:33.214 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1750660053054_127.0.0.1_2871
14:27:33.215 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25cced7c-0297-419e-97f7-5c1af6019436_config-0] Notify connected event to listeners.
14:27:34.717 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:27:39.880 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7fbc672b-4bd8-49ee-8b5d-63ad2cc55905
14:27:39.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fbc672b-4bd8-49ee-8b5d-63ad2cc55905] RpcClient init label, labels = {module=naming, source=sdk}
14:27:39.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fbc672b-4bd8-49ee-8b5d-63ad2cc55905] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:27:39.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fbc672b-4bd8-49ee-8b5d-63ad2cc55905] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:27:39.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fbc672b-4bd8-49ee-8b5d-63ad2cc55905] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:27:39.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fbc672b-4bd8-49ee-8b5d-63ad2cc55905] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:27:40.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fbc672b-4bd8-49ee-8b5d-63ad2cc55905] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750660059897_127.0.0.1_2876
14:27:40.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fbc672b-4bd8-49ee-8b5d-63ad2cc55905] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:40.037 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fbc672b-4bd8-49ee-8b5d-63ad2cc55905] Notify connected event to listeners.
14:27:40.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fbc672b-4bd8-49ee-8b5d-63ad2cc55905] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000270014aaef8
14:27:40.150 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:27:40.237 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system 192.168.2.43:9600 register finished
14:27:40.437 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 43.273 seconds (JVM running for 51.72)
14:27:40.455 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:27:40.455 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:27:40.455 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:27:41.037 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fbc672b-4bd8-49ee-8b5d-63ad2cc55905] Receive server push request, request = NotifySubscriberRequest, requestId = 1
14:27:41.070 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fbc672b-4bd8-49ee-8b5d-63ad2cc55905] Ack server push request, request = NotifySubscriberRequest, requestId = 1
14:32:27.961 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:34:58.170 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,57] - 用户连接1, 当前在线人数为1
14:35:14.057 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,84] - 用户消息:1,报文:to:b:hello
14:35:14.058 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,126] - 发送消息到:hello, 报文:b
14:35:40.291 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,84] - 用户消息:1,报文:everyone!!!
14:35:40.291 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,126] - 发送消息到:null, 报文:everyone!!!
14:35:46.518 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,74] - 用户退出1, 当前在线人数为0
14:36:37.431 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,57] - 用户连接b, 当前在线人数为1
14:36:49.967 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,84] - 用户消息:b,报文:to:1:nihao 1
14:36:49.967 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,126] - 发送消息到:nihao 1, 报文:1
14:37:05.826 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,74] - 用户退出b, 当前在线人数为0
14:39:01.694 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,126] - 发送消息到:null, 报文:hello
15:29:18.089 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,57] - 用户连接1, 当前在线人数为1
15:29:27.205 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,84] - 用户消息:1,报文:to:b:hello
15:29:27.205 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,126] - 发送消息到:hello, 报文:b
15:29:29.064 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,74] - 用户退出1, 当前在线人数为0
15:36:26.237 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:36:26.247 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:36:26.707 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:36:26.707 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@29e7d5cf[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:36:26.707 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750660059897_127.0.0.1_2876
15:36:26.710 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@dcd7620[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 842]
15:36:26.759 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:36:26.764 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:36:26.782 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:36:26.782 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:36:33.514 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:36:34.555 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9291638c-6ea4-417b-9050-d58de9b8a13b_config-0
15:36:34.653 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
15:36:34.709 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
15:36:34.717 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
15:36:34.733 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
15:36:34.754 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 7 values 
15:36:34.762 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
15:36:34.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9291638c-6ea4-417b-9050-d58de9b8a13b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:36:34.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9291638c-6ea4-417b-9050-d58de9b8a13b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001d9473b6480
15:36:34.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9291638c-6ea4-417b-9050-d58de9b8a13b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001d9473b66a0
15:36:34.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9291638c-6ea4-417b-9050-d58de9b8a13b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:36:34.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9291638c-6ea4-417b-9050-d58de9b8a13b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:36:34.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9291638c-6ea4-417b-9050-d58de9b8a13b_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:36:35.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9291638c-6ea4-417b-9050-d58de9b8a13b_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750664195698_127.0.0.1_12203
15:36:35.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9291638c-6ea4-417b-9050-d58de9b8a13b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:36:35.950 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9291638c-6ea4-417b-9050-d58de9b8a13b_config-0] Notify connected event to listeners.
15:36:35.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9291638c-6ea4-417b-9050-d58de9b8a13b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001d9474f0228
15:36:36.222 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:36:41.087 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:36:41.087 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:36:41.087 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:36:41.316 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:36:42.182 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:36:42.183 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:36:42.185 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:36:51.599 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:36:58.429 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e515c015-d0d8-44d2-90dc-5fdb6df718f9
15:36:58.430 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e515c015-d0d8-44d2-90dc-5fdb6df718f9] RpcClient init label, labels = {module=naming, source=sdk}
15:36:58.433 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e515c015-d0d8-44d2-90dc-5fdb6df718f9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:36:58.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e515c015-d0d8-44d2-90dc-5fdb6df718f9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:36:58.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e515c015-d0d8-44d2-90dc-5fdb6df718f9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:36:58.436 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e515c015-d0d8-44d2-90dc-5fdb6df718f9] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:36:58.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e515c015-d0d8-44d2-90dc-5fdb6df718f9] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750664218453_127.0.0.1_12266
15:36:58.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e515c015-d0d8-44d2-90dc-5fdb6df718f9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:36:58.575 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e515c015-d0d8-44d2-90dc-5fdb6df718f9] Notify connected event to listeners.
15:36:58.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e515c015-d0d8-44d2-90dc-5fdb6df718f9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001d9474f0228
15:36:58.666 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:36:58.735 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system 192.168.2.43:9600 register finished
15:36:59.066 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.454 seconds (JVM running for 27.902)
15:36:59.102 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:36:59.102 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:36:59.102 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:36:59.222 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e515c015-d0d8-44d2-90dc-5fdb6df718f9] Receive server push request, request = NotifySubscriberRequest, requestId = 2
15:36:59.248 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e515c015-d0d8-44d2-90dc-5fdb6df718f9] Ack server push request, request = NotifySubscriberRequest, requestId = 2
15:45:14.336 [http-nio-9600-exec-4] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:50:44.554 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,57] - 用户连接99|1, 当前在线人数为1
15:50:54.750 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,57] - 用户连接99|1, 当前在线人数为1
15:50:57.113 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,74] - 用户退出99|1, 当前在线人数为0
15:51:07.663 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,57] - 用户连接99|1, 当前在线人数为1
15:51:09.478 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,84] - 用户消息:99|1,报文:to:b:hello
15:51:09.482 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,126] - 发送消息到:hello, 报文:b
15:51:21.314 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,84] - 用户消息:99|1,报文:everyone!
15:51:21.322 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,126] - 发送消息到:null, 报文:everyone!
16:01:39.915 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,74] - 用户退出99|1, 当前在线人数为0
16:01:39.915 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,74] - 用户退出99|1, 当前在线人数为0
16:01:39.947 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:01:39.966 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:01:40.283 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:01:40.283 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@23528b19[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:01:40.283 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750664218453_127.0.0.1_12266
16:01:40.284 [nacos-grpc-client-executor-306] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750664218453_127.0.0.1_12266]Ignore complete event,isRunning:false,isAbandon=false
16:01:40.287 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@41cd8022[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 307]
16:01:40.329 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:01:40.338 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:01:40.346 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:01:40.346 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:02:05.800 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:02:06.697 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 624d476d-dd42-4567-b815-874e79a9c25d_config-0
16:02:06.786 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
16:02:06.842 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 4 keys and 9 values 
16:02:06.862 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
16:02:06.883 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
16:02:06.898 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
16:02:06.910 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
16:02:06.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [624d476d-dd42-4567-b815-874e79a9c25d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:02:06.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [624d476d-dd42-4567-b815-874e79a9c25d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000027a5f39ad28
16:02:06.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [624d476d-dd42-4567-b815-874e79a9c25d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$471/0x0000027a5f39af48
16:02:06.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [624d476d-dd42-4567-b815-874e79a9c25d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:02:06.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [624d476d-dd42-4567-b815-874e79a9c25d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:02:06.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [624d476d-dd42-4567-b815-874e79a9c25d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:02:08.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [624d476d-dd42-4567-b815-874e79a9c25d_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750665727855_127.0.0.1_14800
16:02:08.063 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [624d476d-dd42-4567-b815-874e79a9c25d_config-0] Notify connected event to listeners.
16:02:08.064 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [624d476d-dd42-4567-b815-874e79a9c25d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:02:08.065 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [624d476d-dd42-4567-b815-874e79a9c25d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$485/0x0000027a5f4d7cb0
16:02:08.231 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:02:13.072 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:02:13.073 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:02:13.073 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:02:13.398 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:02:14.252 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:02:14.254 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:02:14.254 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:02:23.015 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:02:26.898 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1a188997-7d41-4b45-99f1-f80aa0805982
16:02:26.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a188997-7d41-4b45-99f1-f80aa0805982] RpcClient init label, labels = {module=naming, source=sdk}
16:02:26.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a188997-7d41-4b45-99f1-f80aa0805982] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:02:26.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a188997-7d41-4b45-99f1-f80aa0805982] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:02:26.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a188997-7d41-4b45-99f1-f80aa0805982] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:02:26.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a188997-7d41-4b45-99f1-f80aa0805982] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:02:27.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a188997-7d41-4b45-99f1-f80aa0805982] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750665746914_127.0.0.1_14816
16:02:27.031 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a188997-7d41-4b45-99f1-f80aa0805982] Notify connected event to listeners.
16:02:27.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a188997-7d41-4b45-99f1-f80aa0805982] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:02:27.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a188997-7d41-4b45-99f1-f80aa0805982] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$485/0x0000027a5f4d7cb0
16:02:27.110 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:02:27.154 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system 192.168.2.43:9600 register finished
16:02:27.330 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 22.424 seconds (JVM running for 23.562)
16:02:27.351 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:02:27.352 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:02:27.352 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:02:27.600 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a188997-7d41-4b45-99f1-f80aa0805982] Receive server push request, request = NotifySubscriberRequest, requestId = 3
16:02:27.608 [RMI TCP Connection(2)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:02:27.628 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a188997-7d41-4b45-99f1-f80aa0805982] Ack server push request, request = NotifySubscriberRequest, requestId = 3
16:10:55.260 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:10:55.273 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:10:55.609 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:10:55.610 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4f6221ea[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:10:55.610 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750665746914_127.0.0.1_14816
16:10:55.615 [nacos-grpc-client-executor-116] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750665746914_127.0.0.1_14816]Ignore complete event,isRunning:false,isAbandon=false
16:10:55.618 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5a18e097[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 117]
16:10:55.815 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:10:55.826 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:10:55.842 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:10:55.842 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:11:02.706 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:11:03.736 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ac050f22-2fa5-441f-afa3-f4979effca1b_config-0
16:11:03.842 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 52 ms to scan 1 urls, producing 3 keys and 6 values 
16:11:03.893 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
16:11:03.905 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
16:11:03.917 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
16:11:03.938 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
16:11:03.948 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
16:11:03.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac050f22-2fa5-441f-afa3-f4979effca1b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:11:03.954 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac050f22-2fa5-441f-afa3-f4979effca1b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000026f3e3b6d38
16:11:03.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac050f22-2fa5-441f-afa3-f4979effca1b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000026f3e3b6f58
16:11:03.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac050f22-2fa5-441f-afa3-f4979effca1b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:11:03.956 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac050f22-2fa5-441f-afa3-f4979effca1b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:11:03.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac050f22-2fa5-441f-afa3-f4979effca1b_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:11:05.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac050f22-2fa5-441f-afa3-f4979effca1b_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750666264912_127.0.0.1_1407
16:11:05.179 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac050f22-2fa5-441f-afa3-f4979effca1b_config-0] Notify connected event to listeners.
16:11:05.180 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac050f22-2fa5-441f-afa3-f4979effca1b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:11:05.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac050f22-2fa5-441f-afa3-f4979effca1b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000026f3e4f0ad8
16:11:05.387 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:11:10.184 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:11:10.185 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:11:10.185 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:11:10.402 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:11:11.340 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:11:11.342 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:11:11.342 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:11:20.314 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:11:25.046 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 232918de-56e9-435a-9f1b-e28566c89efa
16:11:25.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [232918de-56e9-435a-9f1b-e28566c89efa] RpcClient init label, labels = {module=naming, source=sdk}
16:11:25.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [232918de-56e9-435a-9f1b-e28566c89efa] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:11:25.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [232918de-56e9-435a-9f1b-e28566c89efa] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:11:25.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [232918de-56e9-435a-9f1b-e28566c89efa] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:11:25.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [232918de-56e9-435a-9f1b-e28566c89efa] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:11:25.195 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [232918de-56e9-435a-9f1b-e28566c89efa] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750666285066_127.0.0.1_1427
16:11:25.198 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [232918de-56e9-435a-9f1b-e28566c89efa] Notify connected event to listeners.
16:11:25.198 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [232918de-56e9-435a-9f1b-e28566c89efa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:11:25.198 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [232918de-56e9-435a-9f1b-e28566c89efa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000026f3e4f0ad8
16:11:25.314 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:11:25.390 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system 192.168.2.43:9600 register finished
16:11:25.749 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.889 seconds (JVM running for 25.385)
16:11:25.783 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [232918de-56e9-435a-9f1b-e28566c89efa] Receive server push request, request = NotifySubscriberRequest, requestId = 4
16:11:25.791 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:11:25.791 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:11:25.795 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:11:25.814 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [232918de-56e9-435a-9f1b-e28566c89efa] Ack server push request, request = NotifySubscriberRequest, requestId = 4
16:16:06.866 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:16:06.879 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:16:07.226 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:16:07.226 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@739d1f97[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:16:07.226 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750666285066_127.0.0.1_1427
16:16:07.238 [nacos-grpc-client-executor-65] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750666285066_127.0.0.1_1427]Ignore complete event,isRunning:false,isAbandon=false
16:16:07.243 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@470083ef[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 66]
16:16:07.312 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:16:07.316 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:16:07.327 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:16:07.327 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:16:14.360 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:16:15.318 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c6f58f8c-e15f-4ec4-a1a3-0542c66df2c4_config-0
16:16:15.401 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
16:16:15.463 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
16:16:15.479 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
16:16:15.495 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
16:16:15.505 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
16:16:15.522 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
16:16:15.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6f58f8c-e15f-4ec4-a1a3-0542c66df2c4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:16:15.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6f58f8c-e15f-4ec4-a1a3-0542c66df2c4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000270183b6af8
16:16:15.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6f58f8c-e15f-4ec4-a1a3-0542c66df2c4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000270183b6d18
16:16:15.531 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6f58f8c-e15f-4ec4-a1a3-0542c66df2c4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:16:15.532 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6f58f8c-e15f-4ec4-a1a3-0542c66df2c4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:16:15.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6f58f8c-e15f-4ec4-a1a3-0542c66df2c4_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:16:16.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6f58f8c-e15f-4ec4-a1a3-0542c66df2c4_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750666576668_127.0.0.1_1833
16:16:16.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6f58f8c-e15f-4ec4-a1a3-0542c66df2c4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:16:16.947 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6f58f8c-e15f-4ec4-a1a3-0542c66df2c4_config-0] Notify connected event to listeners.
16:16:16.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c6f58f8c-e15f-4ec4-a1a3-0542c66df2c4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000270184f0ad8
16:16:17.167 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:16:21.882 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:16:21.885 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:16:21.885 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:16:22.112 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:16:23.312 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:16:23.313 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:16:23.315 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:16:32.189 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:16:38.246 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9647fe1e-9083-4372-bde9-840ca5383bb7
16:16:38.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9647fe1e-9083-4372-bde9-840ca5383bb7] RpcClient init label, labels = {module=naming, source=sdk}
16:16:38.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9647fe1e-9083-4372-bde9-840ca5383bb7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:16:38.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9647fe1e-9083-4372-bde9-840ca5383bb7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:16:38.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9647fe1e-9083-4372-bde9-840ca5383bb7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:16:38.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9647fe1e-9083-4372-bde9-840ca5383bb7] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:16:38.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9647fe1e-9083-4372-bde9-840ca5383bb7] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750666598268_127.0.0.1_1857
16:16:38.409 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9647fe1e-9083-4372-bde9-840ca5383bb7] Notify connected event to listeners.
16:16:38.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9647fe1e-9083-4372-bde9-840ca5383bb7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:16:38.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9647fe1e-9083-4372-bde9-840ca5383bb7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000270184f0ad8
16:16:38.501 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:16:38.568 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system 192.168.2.43:9600 register finished
16:16:38.946 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.481 seconds (JVM running for 26.916)
16:16:38.985 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:16:38.985 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:16:38.991 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:16:38.994 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9647fe1e-9083-4372-bde9-840ca5383bb7] Receive server push request, request = NotifySubscriberRequest, requestId = 5
16:16:39.031 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9647fe1e-9083-4372-bde9-840ca5383bb7] Ack server push request, request = NotifySubscriberRequest, requestId = 5
16:18:43.718 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:21:09.049 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接99|1, 当前在线人数为1
16:21:35.645 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出99|1, 当前在线人数为0
16:33:51.662 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接99|1, 当前在线人数为1
17:08:42.118 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接lwl|ww, 当前在线人数为2
17:09:32.976 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为3
17:09:35.123 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为2
17:09:37.483 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为3
17:10:37.611 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为2
17:10:40.563 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为3
17:11:00.932 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为2
17:11:03.651 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为3
17:11:08.251 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为2
17:11:20.805 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为3
17:17:38.553 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为2
17:17:45.242 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为3
17:18:05.667 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为2
17:18:07.996 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为3
17:18:17.428 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为2
17:18:17.997 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接lwl|ww, 当前在线人数为2
17:18:19.516 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为3
17:18:43.255 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:18:43.255 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:18:44.254 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:18:44.256 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:18:55.268 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出99|1, 当前在线人数为2
17:18:55.268 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出lwl|ww, 当前在线人数为1
17:18:59.354 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:18:59.354 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:19:00.357 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:19:00.357 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:19:01.372 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:19:01.373 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:19:02.355 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:19:02.356 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:19:03.366 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:19:03.366 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:19:04.371 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:19:04.371 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:19:05.355 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:19:05.356 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:19:06.360 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:19:06.362 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:19:07.355 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:19:07.356 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:19:17.990 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为0
17:19:20.196 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为1
17:20:05.157 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:20:05.157 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:20:15.153 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:20:15.154 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:20:25.162 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:20:25.164 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:20:35.152 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:20:35.152 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:20:45.159 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:20:45.159 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:20:55.159 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:20:55.160 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:21:05.161 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:21:05.161 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:21:15.160 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:21:15.160 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:21:42.255 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:21:42.256 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:21:45.154 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:21:45.156 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:21:47.313 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为0
17:21:49.770 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为1
17:21:54.798 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:21:54.800 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:22:05.149 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:22:05.149 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:22:15.163 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:22:15.163 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:22:25.148 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:22:25.149 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:22:35.150 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:22:35.153 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:22:36.010 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为0
17:22:38.591 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为1
17:22:45.150 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:22:45.151 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:22:55.153 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:22:55.153 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:23:18.778 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:23:18.783 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:23:25.161 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:23:25.161 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:23:35.150 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:23:35.150 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:23:45.150 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:23:45.150 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:23:46.967 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为0
17:23:49.774 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为1
17:23:55.157 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:23:55.157 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:24:04.796 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:24:04.797 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:24:15.151 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:24:15.151 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:24:25.155 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:24:25.156 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:24:35.150 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:24:35.150 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:24:45.163 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:24:45.163 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:24:55.157 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:24:55.157 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:25:05.149 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:25:05.149 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:25:06.923 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为0
17:25:15.156 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:25:15.156 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:25:25.148 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:25:25.149 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:25:35.147 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:25:35.147 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:25:42.723 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为1
17:25:45.156 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:25:45.156 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:25:55.154 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:25:55.154 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:26:05.150 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:26:05.150 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:26:15.153 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:26:15.153 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:26:25.149 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:26:25.149 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:26:35.154 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:26:35.154 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:26:47.149 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:26:47.151 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:27:47.154 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:27:47.154 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:28:06.162 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:28:06.164 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:28:15.153 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:28:15.153 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:28:25.146 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:28:25.146 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:28:34.795 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:28:34.795 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:28:36.089 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为0
17:28:45.154 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:28:45.156 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:28:55.154 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:28:55.155 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:29:02.703 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为1
17:29:05.153 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:29:05.153 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:29:08.607 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为0
17:29:11.004 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接121|222, 当前在线人数为1
17:29:15.152 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:29:15.154 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:29:25.148 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:29:25.148 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:29:35.153 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:29:35.153 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:29:47.166 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:29:47.167 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:29:48.228 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出121|222, 当前在线人数为0
17:29:48.771 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接undefined|undefined, 当前在线人数为1
17:29:49.327 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接undefined|undefined, 当前在线人数为1
17:30:28.690 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:30:28.690 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:30:33.011 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出undefined|undefined, 当前在线人数为0
17:30:34.041 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接undefined|undefined, 当前在线人数为1
17:30:34.797 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,106] - 用户消息:lwl|ww,报文:PING
17:30:34.798 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:PING
17:30:54.003 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出undefined|undefined, 当前在线人数为0
17:30:54.931 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接undefined|undefined, 当前在线人数为1
17:31:14.285 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出lwl|ww, 当前在线人数为1
17:31:20.744 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出undefined|undefined, 当前在线人数为0
17:31:21.554 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接undefined|undefined, 当前在线人数为1
17:43:01.243 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,79] - 用户连接lwl|ww, 当前在线人数为2
17:43:08.136 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出undefined|undefined, 当前在线人数为1
17:43:08.138 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出lwl|ww, 当前在线人数为0
17:43:08.138 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,96] - 用户退出undefined|undefined, 当前在线人数为0
17:43:08.227 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:43:08.233 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:43:08.554 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:43:08.554 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6bbfdda2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:43:08.554 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750666598268_127.0.0.1_1857
17:43:08.563 [nacos-grpc-client-executor-1047] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750666598268_127.0.0.1_1857]Ignore complete event,isRunning:false,isAbandon=false
17:43:08.567 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@61e190f9[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1048]
17:43:08.644 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:43:08.653 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:43:08.676 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:43:08.676 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:43:16.788 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:43:17.752 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d84b63be-501f-438f-8cfa-f32a7bb7bd89_config-0
17:43:17.854 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
17:43:17.920 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 4 keys and 9 values 
17:43:17.946 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
17:43:17.968 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
17:43:17.986 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
17:43:18.002 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
17:43:18.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d84b63be-501f-438f-8cfa-f32a7bb7bd89_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:43:18.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d84b63be-501f-438f-8cfa-f32a7bb7bd89_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001f4523b7410
17:43:18.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d84b63be-501f-438f-8cfa-f32a7bb7bd89_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001f4523b7630
17:43:18.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d84b63be-501f-438f-8cfa-f32a7bb7bd89_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:43:18.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d84b63be-501f-438f-8cfa-f32a7bb7bd89_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:43:18.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d84b63be-501f-438f-8cfa-f32a7bb7bd89_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:43:19.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d84b63be-501f-438f-8cfa-f32a7bb7bd89_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750671799009_127.0.0.1_12749
17:43:19.259 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d84b63be-501f-438f-8cfa-f32a7bb7bd89_config-0] Notify connected event to listeners.
17:43:19.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d84b63be-501f-438f-8cfa-f32a7bb7bd89_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:43:19.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d84b63be-501f-438f-8cfa-f32a7bb7bd89_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f4524f0fb0
17:43:19.438 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:43:24.286 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:43:24.286 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:43:24.286 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:43:24.536 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:43:25.401 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:43:25.402 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:43:25.402 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:43:33.958 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:43:38.235 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 40405f50-acbf-4468-8e68-3b6ab334bb2d
17:43:38.235 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40405f50-acbf-4468-8e68-3b6ab334bb2d] RpcClient init label, labels = {module=naming, source=sdk}
17:43:38.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40405f50-acbf-4468-8e68-3b6ab334bb2d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:43:38.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40405f50-acbf-4468-8e68-3b6ab334bb2d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:43:38.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40405f50-acbf-4468-8e68-3b6ab334bb2d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:43:38.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40405f50-acbf-4468-8e68-3b6ab334bb2d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:43:38.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40405f50-acbf-4468-8e68-3b6ab334bb2d] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750671818252_127.0.0.1_12771
17:43:38.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40405f50-acbf-4468-8e68-3b6ab334bb2d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:43:38.376 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40405f50-acbf-4468-8e68-3b6ab334bb2d] Notify connected event to listeners.
17:43:38.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40405f50-acbf-4468-8e68-3b6ab334bb2d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f4524f0fb0
17:43:38.448 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:43:38.497 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system 192.168.2.43:9600 register finished
17:43:38.653 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.824 seconds (JVM running for 25.272)
17:43:38.669 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:43:38.669 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:43:38.669 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:43:38.732 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:43:38.837 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接lwl|ww, 当前在线人数为1
17:43:38.955 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40405f50-acbf-4468-8e68-3b6ab334bb2d] Receive server push request, request = NotifySubscriberRequest, requestId = 6
17:43:38.984 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [40405f50-acbf-4468-8e68-3b6ab334bb2d] Ack server push request, request = NotifySubscriberRequest, requestId = 6
17:43:40.811 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
17:43:40.819 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
17:43:50.180 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:43:50.180 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,146] - 发送消息到:lwl|ww, 报文:PING
17:43:59.911 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:43:59.911 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,146] - 发送消息到:lwl|ww, 报文:PING
17:44:10.168 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:44:10.168 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,146] - 发送消息到:lwl|ww, 报文:PING
17:44:19.915 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:44:19.915 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,146] - 发送消息到:lwl|ww, 报文:PING
17:44:30.150 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:44:30.150 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,146] - 发送消息到:lwl|ww, 报文:PING
17:44:40.151 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:44:40.151 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,146] - 发送消息到:lwl|ww, 报文:PING
17:44:50.151 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:44:50.151 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,146] - 发送消息到:lwl|ww, 报文:PING
17:45:00.151 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:45:00.151 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,146] - 发送消息到:lwl|ww, 报文:PING
17:45:10.147 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:45:10.150 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,146] - 发送消息到:lwl|ww, 报文:PING
17:45:19.912 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:45:19.912 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,146] - 发送消息到:lwl|ww, 报文:PING
17:45:22.128 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出lwl|ww, 当前在线人数为1
17:48:46.244 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为0
17:48:46.244 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为0
17:48:46.338 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:48:46.347 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:48:46.670 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:48:46.670 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2c506c4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:48:46.670 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750671818252_127.0.0.1_12771
17:48:46.678 [nacos-grpc-client-executor-72] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750671818252_127.0.0.1_12771]Ignore complete event,isRunning:false,isAbandon=false
17:48:46.678 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2a91c2e0[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 73]
17:48:46.751 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:48:46.765 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:48:46.786 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:48:46.786 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:48:54.470 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:48:55.383 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-0673-45c4-b2d0-70ade8696c3c_config-0
17:48:55.468 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
17:48:55.536 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 4 keys and 9 values 
17:48:55.540 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
17:48:55.559 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
17:48:55.568 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
17:48:55.584 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
17:48:55.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-0673-45c4-b2d0-70ade8696c3c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:48:55.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-0673-45c4-b2d0-70ade8696c3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000254cf3b6f80
17:48:55.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-0673-45c4-b2d0-70ade8696c3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000254cf3b71a0
17:48:55.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-0673-45c4-b2d0-70ade8696c3c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:48:55.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-0673-45c4-b2d0-70ade8696c3c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:48:55.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-0673-45c4-b2d0-70ade8696c3c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:48:56.989 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-0673-45c4-b2d0-70ade8696c3c_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750672136662_127.0.0.1_13279
17:48:56.989 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-0673-45c4-b2d0-70ade8696c3c_config-0] Notify connected event to listeners.
17:48:56.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-0673-45c4-b2d0-70ade8696c3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:48:56.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-0673-45c4-b2d0-70ade8696c3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000254cf4f0fb0
17:48:57.186 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:49:01.925 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:49:01.933 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:49:01.933 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:49:02.174 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:49:03.283 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:49:03.283 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:49:03.283 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:49:12.197 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:49:16.201 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e10383be-5684-497b-a62e-f7f0647296a1
17:49:16.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e10383be-5684-497b-a62e-f7f0647296a1] RpcClient init label, labels = {module=naming, source=sdk}
17:49:16.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e10383be-5684-497b-a62e-f7f0647296a1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:49:16.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e10383be-5684-497b-a62e-f7f0647296a1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:49:16.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e10383be-5684-497b-a62e-f7f0647296a1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:49:16.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e10383be-5684-497b-a62e-f7f0647296a1] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:49:16.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e10383be-5684-497b-a62e-f7f0647296a1] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750672156220_127.0.0.1_13316
17:49:16.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e10383be-5684-497b-a62e-f7f0647296a1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:49:16.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e10383be-5684-497b-a62e-f7f0647296a1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000254cf4f0fb0
17:49:16.342 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e10383be-5684-497b-a62e-f7f0647296a1] Notify connected event to listeners.
17:49:16.421 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:49:16.463 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system 192.168.2.43:9600 register finished
17:49:16.618 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.075 seconds (JVM running for 24.457)
17:49:16.641 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:49:16.641 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:49:16.641 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:49:16.948 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e10383be-5684-497b-a62e-f7f0647296a1] Receive server push request, request = NotifySubscriberRequest, requestId = 7
17:49:16.971 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e10383be-5684-497b-a62e-f7f0647296a1] Ack server push request, request = NotifySubscriberRequest, requestId = 7
17:49:18.937 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:49:19.059 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
17:49:19.059 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
17:49:40.337 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
17:49:40.337 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
17:49:40.392 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:49:40.407 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:49:40.740 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:49:40.740 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7d5f713f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:49:40.740 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750672156220_127.0.0.1_13316
17:49:40.744 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750672156220_127.0.0.1_13316]Ignore complete event,isRunning:false,isAbandon=false
17:49:40.748 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6ab439a2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 13]
17:49:40.796 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:49:40.800 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:49:40.811 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:49:40.811 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:49:48.288 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:49:49.217 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 73e0ab29-b09d-467b-8911-38c853b5306e_config-0
17:49:49.300 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
17:49:49.354 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
17:49:49.377 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
17:49:49.392 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
17:49:49.411 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
17:49:49.429 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
17:49:49.436 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e0ab29-b09d-467b-8911-38c853b5306e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:49:49.436 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e0ab29-b09d-467b-8911-38c853b5306e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000258553b6d38
17:49:49.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e0ab29-b09d-467b-8911-38c853b5306e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000258553b6f58
17:49:49.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e0ab29-b09d-467b-8911-38c853b5306e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:49:49.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e0ab29-b09d-467b-8911-38c853b5306e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:49:49.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e0ab29-b09d-467b-8911-38c853b5306e_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:49:50.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e0ab29-b09d-467b-8911-38c853b5306e_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750672190515_127.0.0.1_13369
17:49:50.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e0ab29-b09d-467b-8911-38c853b5306e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:49:50.785 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e0ab29-b09d-467b-8911-38c853b5306e_config-0] Notify connected event to listeners.
17:49:50.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e0ab29-b09d-467b-8911-38c853b5306e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000258554f0668
17:49:51.055 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:49:56.520 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:49:56.520 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:49:56.520 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:49:56.848 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:49:57.745 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:49:57.746 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:49:57.748 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:50:10.992 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:50:15.008 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ca947769-1961-4590-b309-fcb77db00ecd
17:50:15.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca947769-1961-4590-b309-fcb77db00ecd] RpcClient init label, labels = {module=naming, source=sdk}
17:50:15.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca947769-1961-4590-b309-fcb77db00ecd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:50:15.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca947769-1961-4590-b309-fcb77db00ecd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:50:15.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca947769-1961-4590-b309-fcb77db00ecd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:50:15.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca947769-1961-4590-b309-fcb77db00ecd] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:50:15.149 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca947769-1961-4590-b309-fcb77db00ecd] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750672215027_127.0.0.1_13420
17:50:15.150 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca947769-1961-4590-b309-fcb77db00ecd] Notify connected event to listeners.
17:50:15.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca947769-1961-4590-b309-fcb77db00ecd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:50:15.151 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca947769-1961-4590-b309-fcb77db00ecd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000258554f0668
17:50:15.225 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:50:15.279 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system 192.168.2.43:9600 register finished
17:50:15.458 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 28.016 seconds (JVM running for 29.505)
17:50:15.490 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:50:15.490 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:50:15.490 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:50:15.784 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca947769-1961-4590-b309-fcb77db00ecd] Receive server push request, request = NotifySubscriberRequest, requestId = 8
17:50:15.808 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca947769-1961-4590-b309-fcb77db00ecd] Ack server push request, request = NotifySubscriberRequest, requestId = 8
17:50:15.992 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:50:16.113 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
17:50:16.113 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为1
17:50:23.438 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接lwl|ww, 当前在线人数为3
17:50:34.975 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:50:34.975 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
17:50:45.148 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:50:45.150 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
17:50:46.148 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:50:46.150 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
17:50:47.146 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:50:47.146 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
17:50:48.135 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:50:48.135 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
17:50:49.150 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:50:49.150 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
17:50:50.147 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:50:50.147 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
17:50:51.133 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:50:51.133 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
17:50:52.134 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:50:52.134 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
17:50:53.138 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:50:53.138 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
17:51:25.911 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
17:51:26.382 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
17:51:32.534 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
17:51:33.885 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
17:53:15.551 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:53:15.551 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
17:53:16.553 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:53:16.553 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
17:53:17.550 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:53:17.550 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
17:53:18.561 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
17:53:18.561 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
17:54:08.264 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
17:54:08.549 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
17:54:12.408 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
17:54:13.599 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
17:54:22.778 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:{"type":"ping"}
17:54:22.778 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:54:24.776 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:{"type":"ping"}
17:54:24.776 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:54:32.773 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:{"type":"ping"}
17:54:32.773 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:54:32.781 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
17:54:34.784 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:{"type":"ping"}
17:54:34.784 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:54:38.811 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
17:54:44.016 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:{"type":"ping"}
17:54:44.016 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:54:44.795 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
17:54:49.777 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:{"type":"ping"}
17:54:49.777 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:54:50.584 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
17:54:59.813 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:{"type":"ping"}
17:54:59.814 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:55:00.819 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
17:55:01.016 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:{"type":"ping"}
17:55:01.016 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:55:06.801 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
17:55:11.001 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:{"type":"ping"}
17:55:11.003 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:55:11.021 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
17:55:16.051 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接121|222, 当前在线人数为3
17:55:17.821 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:{"type":"ping"}
17:55:17.821 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:55:26.109 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:{"type":"ping"}
17:55:26.109 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:55:27.776 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:{"type":"ping"}
17:55:27.776 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:55:27.776 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为3
17:55:33.786 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为4
17:55:36.146 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:{"type":"ping"}
17:55:36.146 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:55:44.768 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:{"type":"ping"}
17:55:44.768 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:55:46.795 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:{"type":"ping"}
17:55:46.795 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:55:54.767 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:{"type":"ping"}
17:55:54.767 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:55:56.780 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:{"type":"ping"}
17:55:56.780 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:56:04.796 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:{"type":"ping"}
17:56:04.796 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:56:06.767 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:{"type":"ping"}
17:56:06.767 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:{"type":"ping"}
17:56:08.228 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出121|222, 当前在线人数为3
17:56:08.517 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
17:56:09.333 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
17:56:09.386 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
17:56:19.768 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"ping"
17:56:19.768 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"ping"
17:56:20.785 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"ping"
17:56:20.785 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"ping"
17:56:29.773 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"ping"
17:56:29.773 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"ping"
17:56:29.778 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
17:56:30.830 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"ping"
17:56:30.830 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"ping"
17:56:35.769 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接121|222, 当前在线人数为3
17:56:40.766 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"ping"
17:56:40.766 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"ping"
17:56:41.007 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出121|222, 当前在线人数为2
17:56:41.279 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
17:56:42.150 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
17:56:42.158 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
17:56:52.797 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:56:52.797 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:56:52.797 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:56:52.797 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:57:02.775 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:57:02.775 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:57:02.778 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:57:02.778 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:57:03.765 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
17:57:09.852 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
17:57:12.768 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:57:12.768 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:57:20.784 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:57:20.784 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:57:22.776 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:57:22.776 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:57:22.776 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
17:57:28.794 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接121|222, 当前在线人数为3
17:57:30.766 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:57:30.766 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:57:39.850 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
17:57:39.850 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:57:40.867 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:57:40.867 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:57:40.867 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为3
17:57:46.800 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为4
17:57:49.768 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
17:57:49.768 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:57:57.442 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出lwl|ww, 当前在线人数为3
17:57:57.775 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:57:57.775 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:57:59.817 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
17:57:59.817 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:58:07.802 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:58:07.802 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:58:09.840 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
17:58:09.840 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:58:17.762 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:58:17.771 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:58:19.786 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
17:58:19.786 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:58:27.768 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:58:27.768 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:58:28.805 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
17:58:28.806 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:58:30.253 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出121|222, 当前在线人数为2
17:58:31.263 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
17:58:37.807 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:58:37.807 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:58:41.800 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:58:41.800 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:58:47.764 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:58:47.768 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:58:48.867 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
17:58:51.764 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:58:51.767 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:58:54.767 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
17:59:01.776 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:59:01.776 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:59:01.776 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
17:59:05.859 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:59:05.865 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:59:07.817 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
17:59:15.761 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:59:15.762 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:59:15.772 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
17:59:18.759 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:59:18.759 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:59:21.776 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
17:59:28.758 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:59:28.758 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:59:28.764 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
17:59:32.767 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:59:32.767 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:59:34.765 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
17:59:42.787 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:59:42.787 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:59:43.857 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
17:59:45.803 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:59:45.803 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:59:49.772 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
17:59:55.803 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
17:59:55.803 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
17:59:56.772 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:00:00.813 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:00:00.814 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:00:02.801 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:00:10.771 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:00:10.771 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:00:10.771 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:00:13.765 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:00:13.765 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:00:16.782 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:00:23.791 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:00:23.791 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:00:23.794 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:00:27.766 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:00:27.766 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:00:29.788 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:00:37.756 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:00:37.756 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:00:38.761 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:00:40.750 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:00:40.760 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:00:44.772 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:00:50.759 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:00:50.759 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:00:50.759 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:00:55.949 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:00:55.949 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:00:56.876 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:01:05.765 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:01:05.765 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:01:06.768 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:01:07.801 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:01:07.801 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:01:12.767 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:01:17.758 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:01:17.758 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:01:18.752 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:01:23.899 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:01:23.899 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:01:24.760 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:01:32.950 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接lwl|ww, 当前在线人数为3
18:01:33.759 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:01:33.759 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:01:33.759 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
18:01:34.765 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:01:34.765 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:01:35.480 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:01:35.482 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
18:01:36.477 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:01:36.477 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
18:01:37.482 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:01:37.485 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
18:01:38.480 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:01:38.480 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
18:01:39.481 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:01:39.481 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:PING
18:01:39.771 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
18:01:40.692 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
18:01:41.510 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
18:01:50.832 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:01:50.832 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:01:51.900 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:01:51.900 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:01:58.061 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出lwl|ww, 当前在线人数为2
18:02:00.777 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:02:00.777 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:02:01.767 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:02:01.907 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:02:01.907 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:02:07.793 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:02:11.899 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:02:11.899 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:02:11.911 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:02:16.944 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:02:18.790 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:02:18.790 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:02:26.976 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:02:26.976 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:02:28.804 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:02:28.804 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:02:29.755 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:02:35.792 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:02:37.017 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:02:37.018 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:02:46.749 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:02:46.749 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:02:47.757 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:02:47.757 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:02:47.757 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:02:53.772 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:02:56.757 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:02:56.759 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:03:04.769 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:03:04.769 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:03:06.812 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:03:06.812 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:03:06.812 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:03:12.866 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:03:14.753 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:03:14.753 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:03:23.749 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:03:23.750 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:03:24.835 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:03:24.837 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:03:24.838 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:03:30.777 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:03:33.750 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:03:33.750 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:03:41.838 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:03:41.839 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:03:43.751 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:03:43.751 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:03:44.757 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:03:50.766 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:03:51.765 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:03:51.765 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:04:01.759 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:04:01.759 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:04:01.759 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:04:01.759 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:04:01.759 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:04:07.766 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:04:11.776 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:04:11.776 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:04:18.752 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:04:18.752 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:04:21.750 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:04:21.750 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:04:22.287 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:04:27.820 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:04:28.764 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:04:28.764 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:04:38.776 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:04:38.776 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:04:38.777 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:04:38.777 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,149] - 发送消息到:null, 报文:"PING"
18:04:39.788 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:04:40.562 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:04:40.619 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:04:40.635 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:04:40.962 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:04:40.966 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@64e2cc7e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:04:40.966 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750672215027_127.0.0.1_13420
18:04:40.970 [nacos-grpc-client-executor-181] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750672215027_127.0.0.1_13420]Ignore complete event,isRunning:false,isAbandon=false
18:04:40.970 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3a1f9ed4[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 182]
18:04:41.015 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:04:41.015 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:04:41.024 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:04:41.024 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:04:47.910 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:04:48.754 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6f127d9f-ba40-4852-bf47-12e1966378d3_config-0
18:04:48.867 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
18:04:48.937 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 4 keys and 9 values 
18:04:48.964 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 10 values 
18:04:48.983 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 5 values 
18:04:48.999 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
18:04:49.018 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
18:04:49.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f127d9f-ba40-4852-bf47-12e1966378d3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:04:49.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f127d9f-ba40-4852-bf47-12e1966378d3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000025f9d3b6af8
18:04:49.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f127d9f-ba40-4852-bf47-12e1966378d3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000025f9d3b6d18
18:04:49.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f127d9f-ba40-4852-bf47-12e1966378d3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:04:49.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f127d9f-ba40-4852-bf47-12e1966378d3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:04:49.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f127d9f-ba40-4852-bf47-12e1966378d3_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
18:04:50.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f127d9f-ba40-4852-bf47-12e1966378d3_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750673089949_127.0.0.1_1095
18:04:50.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f127d9f-ba40-4852-bf47-12e1966378d3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:04:50.189 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f127d9f-ba40-4852-bf47-12e1966378d3_config-0] Notify connected event to listeners.
18:04:50.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f127d9f-ba40-4852-bf47-12e1966378d3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000025f9d4f0ad8
18:04:50.382 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:04:55.335 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:04:55.335 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:04:55.335 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:04:55.584 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:04:56.496 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:04:56.501 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:04:56.501 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:05:05.398 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:05:09.390 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ec82e564-fbd2-4d64-9446-9a837ee46e0b
18:05:09.390 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec82e564-fbd2-4d64-9446-9a837ee46e0b] RpcClient init label, labels = {module=naming, source=sdk}
18:05:09.390 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec82e564-fbd2-4d64-9446-9a837ee46e0b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:05:09.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec82e564-fbd2-4d64-9446-9a837ee46e0b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:05:09.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec82e564-fbd2-4d64-9446-9a837ee46e0b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:05:09.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec82e564-fbd2-4d64-9446-9a837ee46e0b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
18:05:09.530 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec82e564-fbd2-4d64-9446-9a837ee46e0b] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750673109407_127.0.0.1_1122
18:05:09.530 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec82e564-fbd2-4d64-9446-9a837ee46e0b] Notify connected event to listeners.
18:05:09.530 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec82e564-fbd2-4d64-9446-9a837ee46e0b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:05:09.530 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec82e564-fbd2-4d64-9446-9a837ee46e0b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000025f9d4f0ad8
18:05:09.600 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:05:09.653 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system 192.168.2.43:9600 register finished
18:05:09.833 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 22.74 seconds (JVM running for 24.122)
18:05:09.860 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:05:09.860 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:05:09.860 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:05:10.002 [http-nio-9600-exec-3] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:05:10.109 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec82e564-fbd2-4d64-9446-9a837ee46e0b] Receive server push request, request = NotifySubscriberRequest, requestId = 9
18:05:10.134 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为1
18:05:10.142 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec82e564-fbd2-4d64-9446-9a837ee46e0b] Ack server push request, request = NotifySubscriberRequest, requestId = 9
18:05:12.756 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为1
18:05:12.762 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为1
18:05:20.107 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:05:20.116 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:05:23.750 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:05:23.750 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:05:23.750 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:05:23.753 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:05:30.107 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:05:30.107 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:05:30.115 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为0
18:05:33.772 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:05:33.772 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:05:33.776 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:05:33.776 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:05:33.776 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为0
18:05:35.133 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接121|222, 当前在线人数为1
18:05:39.803 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:05:43.759 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:05:43.759 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:05:43.759 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:05:45.843 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:05:45.843 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:05:49.755 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:05:50.751 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:05:50.751 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:05:55.839 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:05:55.839 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:00.784 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:06:00.784 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:06:00.787 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:00.787 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:01.818 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:06:05.810 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:06:05.810 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:07.754 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:06:10.753 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:06:10.753 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:15.841 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:06:15.847 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:18.741 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:06:18.741 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:20.755 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:06:20.755 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:20.766 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:06:25.757 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:06:25.757 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:26.813 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:06:28.760 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:06:28.760 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:35.749 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:06:35.749 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:37.756 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:06:37.756 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:38.761 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:06:38.761 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:39.757 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:06:45.752 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:06:45.752 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:45.752 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:06:47.748 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:06:47.748 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:55.740 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:06:55.740 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:56.803 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:06:56.807 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:57.766 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:06:57.769 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:06:58.751 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:07:04.757 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:07:05.754 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:07:05.754 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:07:06.849 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:07:06.849 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:07:15.750 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:07:15.750 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:07:15.750 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:07:15.750 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:07:16.749 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:07:16.755 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:07:16.756 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:07:22.752 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:07:25.733 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:07:25.733 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:07:33.738 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:07:33.738 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:07:35.740 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:07:35.740 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:07:36.733 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:07:42.771 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:07:43.746 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:07:43.746 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:07:49.750 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:07:49.750 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:07:53.735 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:07:53.735 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:07:53.735 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:07:53.735 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:07:54.749 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:08:00.750 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:08:03.744 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:08:03.748 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:08:11.755 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:08:11.755 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:08:13.746 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:08:13.746 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:08:13.748 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:08:19.754 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:08:21.753 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:08:21.753 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:08:28.455 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:08:28.455 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:08:30.749 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:08:30.750 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:08:30.757 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:08:30.757 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:08:31.829 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:08:35.166 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:08:35.166 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:08:37.761 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:08:40.845 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:08:40.845 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:08:45.231 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:08:45.233 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:08:48.832 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:08:48.834 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:08:50.736 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:08:50.736 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:08:50.759 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:08:55.183 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:08:55.183 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:08:56.833 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:08:58.766 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:08:58.766 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:05.132 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:09:05.132 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:07.752 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:09:07.754 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:08.736 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:09:08.736 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:08.736 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:09:14.848 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:09:15.248 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:09:15.253 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:17.732 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:09:17.732 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:25.142 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:09:25.143 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:25.801 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:09:25.802 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:27.847 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:09:27.848 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:27.849 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:09:33.788 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:09:35.136 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:09:35.136 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:35.835 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:09:35.835 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:44.814 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:09:44.814 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:45.137 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:09:45.137 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:45.765 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:09:45.765 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:45.770 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:09:51.818 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:09:54.734 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:09:54.734 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:09:55.142 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:09:55.143 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:02.739 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:10:02.739 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:04.738 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:10:04.738 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:04.738 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:10:05.148 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:10:05.148 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:10.741 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:10:12.745 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:10:12.745 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:15.159 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:10:15.159 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:21.739 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:10:21.740 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:22.733 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:10:22.736 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:23.750 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:10:25.141 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:10:25.141 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:29.748 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:10:31.746 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:10:31.746 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:35.130 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:10:35.132 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:40.732 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:10:40.733 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:41.780 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:10:41.781 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:42.732 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:10:45.160 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:10:45.160 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:48.743 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:10:50.794 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:10:50.794 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:55.139 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:10:55.139 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:10:57.672 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接lwl|ww, 当前在线人数为3
18:10:59.734 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:10:59.734 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:11:00.742 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:11:00.742 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:11:00.742 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
18:11:01.869 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出lwl|ww, 当前在线人数为1
18:11:01.869 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:11:01.869 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出121|222, 当前在线人数为0
18:11:01.943 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:11:01.953 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:11:02.274 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:11:02.274 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@558956ae[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:11:02.274 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750673109407_127.0.0.1_1122
18:11:02.281 [nacos-grpc-client-executor-80] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750673109407_127.0.0.1_1122]Ignore complete event,isRunning:false,isAbandon=false
18:11:02.283 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6cd85af7[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 81]
18:11:02.357 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:11:02.365 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:11:02.383 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:11:02.383 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:11:09.690 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:11:10.682 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 05c5a2fd-6513-415a-9337-1d1cd9719272_config-0
18:11:10.781 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
18:11:10.849 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 4 keys and 9 values 
18:11:10.866 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
18:11:10.882 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
18:11:10.900 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
18:11:10.915 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
18:11:10.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [05c5a2fd-6513-415a-9337-1d1cd9719272_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:11:10.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [05c5a2fd-6513-415a-9337-1d1cd9719272_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000017aac3b6af8
18:11:10.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [05c5a2fd-6513-415a-9337-1d1cd9719272_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000017aac3b6d18
18:11:10.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [05c5a2fd-6513-415a-9337-1d1cd9719272_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:11:10.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [05c5a2fd-6513-415a-9337-1d1cd9719272_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:11:10.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [05c5a2fd-6513-415a-9337-1d1cd9719272_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
18:11:12.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [05c5a2fd-6513-415a-9337-1d1cd9719272_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750673471882_127.0.0.1_1889
18:11:12.132 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [05c5a2fd-6513-415a-9337-1d1cd9719272_config-0] Notify connected event to listeners.
18:11:12.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [05c5a2fd-6513-415a-9337-1d1cd9719272_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:11:12.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [05c5a2fd-6513-415a-9337-1d1cd9719272_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000017aac4f0ad8
18:11:12.349 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:11:17.106 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:11:17.106 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:11:17.106 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:11:17.449 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:11:18.529 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:11:18.533 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:11:18.533 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:11:28.131 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:11:32.287 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c53705b7-dead-4e1e-ae13-19e97fc095f7
18:11:32.287 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c53705b7-dead-4e1e-ae13-19e97fc095f7] RpcClient init label, labels = {module=naming, source=sdk}
18:11:32.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c53705b7-dead-4e1e-ae13-19e97fc095f7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:11:32.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c53705b7-dead-4e1e-ae13-19e97fc095f7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:11:32.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c53705b7-dead-4e1e-ae13-19e97fc095f7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:11:32.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c53705b7-dead-4e1e-ae13-19e97fc095f7] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
18:11:32.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c53705b7-dead-4e1e-ae13-19e97fc095f7] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750673492307_127.0.0.1_1940
18:11:32.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c53705b7-dead-4e1e-ae13-19e97fc095f7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:11:32.425 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c53705b7-dead-4e1e-ae13-19e97fc095f7] Notify connected event to listeners.
18:11:32.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c53705b7-dead-4e1e-ae13-19e97fc095f7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000017aac4f0ad8
18:11:32.503 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:11:32.550 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system 192.168.2.43:9600 register finished
18:11:32.705 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.835 seconds (JVM running for 25.196)
18:11:32.717 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:11:32.717 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:11:32.729 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:11:32.775 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:11:32.875 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接lwl|ww, 当前在线人数为1
18:11:33.049 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c53705b7-dead-4e1e-ae13-19e97fc095f7] Receive server push request, request = NotifySubscriberRequest, requestId = 10
18:11:33.074 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c53705b7-dead-4e1e-ae13-19e97fc095f7] Ack server push request, request = NotifySubscriberRequest, requestId = 10
18:11:33.806 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:11:34.769 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:11:34.769 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:11:35.023 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:11:35.067 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接121|222, 当前在线人数为3
18:11:35.765 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:11:35.767 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:11:36.781 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:11:36.781 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:11:37.767 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:11:37.768 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:11:38.766 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:11:38.767 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:11:39.777 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:11:39.778 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:11:40.766 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:11:40.766 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:11:41.765 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:11:41.765 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:11:42.877 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出lwl|ww, 当前在线人数为2
18:11:44.752 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:11:44.752 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:11:45.085 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:11:45.085 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:11:45.781 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:11:45.782 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:11:54.515 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接lwl|ww, 当前在线人数为3
18:11:54.732 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:11:54.734 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:11:55.160 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:121|222,报文:"PING"
18:11:55.160 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:11:55.730 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:11:55.730 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
18:11:55.731 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:11:56.419 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:11:56.419 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:11:57.420 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:11:57.420 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:11:58.421 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:11:58.421 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:11:59.425 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:11:59.426 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:12:00.316 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出121|222, 当前在线人数为1
18:12:00.425 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:12:00.425 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:12:01.018 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出lwl|ww, 当前在线人数为0
18:12:01.767 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为1
18:12:01.768 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:12:05.733 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:12:05.733 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:12:05.736 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:12:11.799 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:12:12.198 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:12:12.198 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:12:12.723 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:12:12.723 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:12:22.133 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:12:22.133 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:12:22.133 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:12:22.732 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:12:22.732 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:12:22.732 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:12:22.732 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:12:22.732 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:12:27.176 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:12:28.743 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:12:32.720 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:12:32.720 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:12:32.732 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:12:37.191 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:12:37.191 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:12:38.736 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:12:39.739 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:12:39.739 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:12:47.735 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:12:47.735 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:12:47.735 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:12:49.782 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:12:49.782 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:12:49.782 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:12:49.782 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:12:50.731 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:12:53.788 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:12:56.734 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:12:59.814 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:12:59.814 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:12:59.814 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:13:04.739 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:13:04.739 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:13:05.772 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:13:07.731 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:13:07.731 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:13:14.730 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:13:14.730 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:13:14.748 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:13:16.720 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:13:16.728 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:13:17.733 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:13:17.733 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:13:17.733 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:13:20.816 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:13:23.800 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:13:26.849 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:13:26.849 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:13:26.849 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:13:31.734 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:13:31.734 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:13:32.743 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:13:34.832 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:13:34.832 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:13:41.726 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:13:41.726 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:13:41.742 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:13:43.731 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:13:43.731 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:13:44.766 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:13:44.766 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:13:44.766 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:13:47.328 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:13:48.599 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:13:48.607 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:13:49.734 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:13:49.791 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:13:50.529 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:14:00.116 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:00.116 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:00.726 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:00.726 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:01.724 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:01.724 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:10.781 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:10.781 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:10.781 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:10.783 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:10.783 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:14:10.783 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:14:11.804 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:11.804 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:16.416 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
18:14:16.416 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
18:14:21.738 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:21.738 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:21.738 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
18:14:26.735 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:26.735 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:26.735 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:26.735 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:27.749 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
18:14:36.583 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:36.583 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:36.587 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:36.587 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:37.815 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:14:37.815 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:14:38.775 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:38.775 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:42.971 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:14:43.848 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:14:48.731 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:48.731 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:48.733 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:14:53.722 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:53.722 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:54.715 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:14:54.715 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:14:54.734 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:15:02.223 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:15:03.007 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:03.007 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:03.728 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:15:04.728 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:04.728 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:04.728 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:15:05.190 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:15:05.717 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:05.717 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:10.742 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:15:12.726 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:12.726 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:15.565 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:15.565 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:15.718 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:15.718 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:15.739 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:15:21.721 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:21.721 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:21.742 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:15:22.768 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:22.768 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:22.769 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:15:25.732 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:25.734 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:25.734 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:15:28.504 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:15:30.785 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:15:31.779 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:31.779 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:31.781 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:15:32.737 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:32.737 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:37.741 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:15:38.518 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:38.518 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:41.713 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:41.713 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:42.720 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:42.720 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:43.715 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:15:48.757 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:48.757 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:48.757 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:48.759 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:15:48.759 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:49.732 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:15:50.821 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:50.821 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:51.731 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:15:54.736 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:15:57.732 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:15:58.740 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:15:58.740 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:15:58.740 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:16:00.717 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:00.717 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:04.742 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:16:04.865 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:04.865 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:07.823 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:07.823 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:10.791 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:10.791 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:10.796 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:16:15.359 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:15.359 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:15.359 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:16:15.721 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:15.721 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:16.733 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:16:17.749 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:17.749 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:17.749 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:16:20.526 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:16:23.743 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:16:25.730 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:25.730 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:25.730 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:16:27.715 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:27.715 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:30.764 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:30.764 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:31.732 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:16:33.761 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:33.761 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:37.726 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:37.726 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:38.727 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:16:40.719 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:40.719 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:41.717 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:16:42.715 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:42.716 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:43.752 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:43.752 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:43.769 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:16:44.665 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接lwl|ww, 当前在线人数为2
18:16:44.735 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
18:16:47.032 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:16:47.032 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:16:47.734 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
18:16:48.026 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:16:48.026 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:16:49.026 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:16:49.028 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:16:49.732 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
18:16:50.027 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:16:50.027 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:16:51.027 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:16:51.027 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:16:52.028 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:16:52.028 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:16:52.746 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:52.746 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:52.747 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
18:16:53.028 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:16:53.030 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:16:54.026 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:16:54.029 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:16:55.026 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:16:55.026 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:16:55.719 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:55.719 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:56.039 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:16:56.039 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:16:57.033 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:16:57.034 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:16:58.032 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:16:58.032 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:16:58.816 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:16:58.816 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:16:58.826 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
18:16:59.031 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:16:59.031 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:00.030 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:00.032 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:00.711 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:00.711 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:01.026 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:01.027 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:02.031 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:02.031 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:03.025 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:03.025 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:04.028 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:04.030 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:05.025 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:05.025 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:05.721 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:05.721 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:05.721 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
18:17:06.043 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:06.043 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:07.025 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:07.028 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:08.032 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:08.032 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:08.739 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:08.740 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:09.029 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:09.029 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:09.733 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:09.733 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
18:17:09.733 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:10.032 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:10.034 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:10.715 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:10.715 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:10.715 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
18:17:11.039 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:11.039 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:11.831 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
18:17:12.040 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:12.040 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:12.764 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
18:17:13.024 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:13.024 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:13.265 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
18:17:14.029 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:14.030 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:15.025 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:15.025 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:16.034 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:16.034 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:16.126 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
18:17:16.126 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为2
18:17:16.571 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
18:17:17.032 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:17.032 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:17.551 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为3
18:17:18.032 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:18.032 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:19.027 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:lwl|ww,报文:PING
18:17:19.027 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:lwl|ww, 报文:PONG
18:17:19.498 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出lwl|ww, 当前在线人数为2
18:17:19.816 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:19.816 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:20.832 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:17:22.782 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:22.782 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:24.984 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:17:25.049 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:17:25.514 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:17:26.065 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:17:26.723 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:17:32.724 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:32.724 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:32.724 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:17:36.415 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:36.415 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:36.810 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:36.810 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:37.735 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:37.735 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:38.775 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:17:46.714 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:46.720 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:46.720 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:46.720 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:46.720 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:17:46.720 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:17:47.720 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:47.721 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:48.790 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:17:49.716 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:49.716 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:17:52.730 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:17:52.730 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:17:54.742 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:17:59.748 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:17:59.748 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:00.714 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:18:03.740 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:03.740 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:03.740 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:03.740 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:05.716 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:05.716 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:06.730 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:18:13.715 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:13.715 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:13.715 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:13.715 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:14.713 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:18:14.713 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:18:15.714 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:15.714 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:16.747 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:18:17.715 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:17.715 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:20.735 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:18:20.735 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:18:22.732 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:18:27.750 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:27.750 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:28.725 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:18:31.715 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:31.715 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:31.715 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:31.715 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:33.713 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:33.713 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:34.715 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:18:41.715 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:41.715 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:41.715 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:41.715 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:41.731 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:18:41.731 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:18:43.722 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:43.722 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:43.722 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:18:45.713 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:45.713 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:46.774 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:18:47.167 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:18:49.715 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:18:55.707 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:55.707 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:55.720 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:18:57.808 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:57.808 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:18:57.808 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:18:57.808 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:00.714 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:00.714 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:01.733 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:19:07.705 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:07.705 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:07.715 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:07.715 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:07.723 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:19:07.723 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:19:10.707 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:10.707 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:11.711 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:19:12.708 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:12.708 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:12.750 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:19:13.723 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:19:17.717 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:19:22.796 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:22.796 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:22.796 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:22.796 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:22.798 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:19:24.710 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:24.710 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:28.714 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:28.714 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:28.728 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:19:32.829 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:32.829 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:32.830 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:19:34.715 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:34.715 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:34.718 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:19:38.598 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:19:38.768 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:38.768 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:38.768 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:19:39.710 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:39.714 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:40.724 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:19:44.722 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:19:49.721 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:49.721 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:49.727 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:49.728 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:49.730 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:19:51.714 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:51.716 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:55.702 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:55.702 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:55.733 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:19:59.708 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:19:59.709 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:19:59.710 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:20:01.717 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:01.717 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:01.722 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:20:05.714 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:05.714 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:05.714 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:20:05.716 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:20:06.704 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:06.704 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:07.724 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:20:11.712 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:20:15.733 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:15.734 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:16.737 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:16.737 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:16.738 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:20:18.716 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:18.716 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:22.715 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:22.715 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:22.730 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:20:25.732 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:25.733 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:25.733 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:20:28.714 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:28.714 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:28.730 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:20:30.747 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:20:32.714 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:32.719 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:32.719 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:20:33.748 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:33.748 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:34.711 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:20:38.781 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:20:40.789 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:40.789 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:43.713 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:43.713 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:43.713 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:20:45.722 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:45.730 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:49.732 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:49.732 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:49.732 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:20:50.747 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:50.755 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:50.765 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:20:55.774 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:55.775 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:55.775 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:20:55.783 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:20:59.714 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:20:59.714 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:20:59.714 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:21:00.781 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:00.793 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:01.733 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:21:05.750 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:21:06.714 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:06.714 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:10.715 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:10.715 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:10.715 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:21:12.698 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:12.698 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:16.708 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:16.708 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:16.708 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:16.708 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:16.715 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:21:16.715 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:21:22.707 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:22.715 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:22.715 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:21:22.723 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:21:26.698 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:26.698 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:27.700 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:27.700 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:21:27.709 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:28.748 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:21:33.698 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:33.698 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:33.714 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:21:37.698 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:37.698 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:37.698 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:21:39.714 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:39.714 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:43.697 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:43.697 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:43.697 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:21:43.705 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:21:44.715 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:44.715 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:49.698 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:49.698 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:49.705 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:21:50.698 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:21:54.709 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:54.709 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:54.709 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:21:54.709 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:21:55.698 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:21:56.717 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:22:00.698 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:00.698 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:01.711 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:22:04.727 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:04.727 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:04.727 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:22:07.705 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:07.705 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:10.701 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:10.701 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:10.701 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:22:10.709 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:22:12.697 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:12.697 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:16.719 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:22:17.700 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:17.700 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:18.721 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:22:21.705 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:21.705 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:22.698 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:22.698 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:22.713 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:22:24.713 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:22:27.727 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:27.727 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:28.760 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:22:31.723 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:31.723 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:31.724 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:22:35.764 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:35.764 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:37.713 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:37.713 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:37.715 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:22:37.723 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:22:39.705 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:39.705 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:43.713 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:22:45.748 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:45.748 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:45.748 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:22:48.794 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:48.795 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:49.698 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:49.698 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:49.716 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:22:51.718 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:22:54.764 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:54.764 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:55.714 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:22:58.755 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:22:58.755 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:22:58.755 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:23:02.697 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:23:02.697 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:23:04.709 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:23:04.709 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:23:04.714 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:23:04.721 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:23:06.698 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:23:06.708 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:23:10.708 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:23:12.699 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:23:12.699 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:23:12.699 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:23:15.706 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:23:15.707 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:23:16.705 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:23:16.706 [http-nio-9600-exec-9] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:23:17.805 [http-nio-9600-exec-10] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:23:18.697 [http-nio-9600-exec-1] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:23:21.798 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:23:21.798 [http-nio-9600-exec-5] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:23:23.723 [http-nio-9600-exec-2] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:23:25.788 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:23:25.788 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:23:25.788 [http-nio-9600-exec-3] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:23:29.699 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:23:29.699 [http-nio-9600-exec-4] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:23:31.724 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [onMessage,99] - 用户消息:undefined|undefined,报文:"PING"
18:23:31.724 [http-nio-9600-exec-6] INFO  c.h.s.w.WebSocketServer - [sendInfo,148] - 发送消息到:null, 报文:"PING"
18:23:31.732 [http-nio-9600-exec-7] INFO  c.h.s.w.WebSocketServer - [onOpen,71] - 用户连接undefined|undefined, 当前在线人数为2
18:23:32.697 [http-nio-9600-exec-8] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:23:33.668 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:23:33.668 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:23:33.668 [SpringApplicationShutdownHook] INFO  c.h.s.w.WebSocketServer - [onClose,88] - 用户退出undefined|undefined, 当前在线人数为1
18:23:33.744 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:23:33.756 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:23:34.087 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:23:34.087 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2f1450d7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:23:34.087 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750673492307_127.0.0.1_1940
18:23:34.087 [nacos-grpc-client-executor-153] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750673492307_127.0.0.1_1940]Ignore complete event,isRunning:false,isAbandon=false
18:23:34.100 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4657e986[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 154]
18:23:34.132 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:23:34.132 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:23:34.154 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:23:34.154 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
