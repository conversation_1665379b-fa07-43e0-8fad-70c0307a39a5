package com.heju.system.phone.service;

import com.heju.system.phone.domain.query.SysPhoneNumberInfoQuery;
import com.heju.system.phone.domain.dto.SysPhoneNumberInfoDto;
import com.heju.common.web.entity.service.IBaseService;

import java.util.List;

/**
 * 手机号管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysPhoneNumberInfoService extends IBaseService<SysPhoneNumberInfoQuery, SysPhoneNumberInfoDto> {

    /**
     * 手机号信息列表查询
     *
     * @param query 数据查询对象
     * @return 数据对象集合
     */
    List<SysPhoneNumberInfoDto> selectListScope(SysPhoneNumberInfoQuery query);

    /**
     * 查询电话号码
     */
    List<String> selectPhoneList(SysPhoneNumberInfoQuery query);
}
