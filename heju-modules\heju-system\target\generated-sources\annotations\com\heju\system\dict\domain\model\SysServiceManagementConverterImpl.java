package com.heju.system.dict.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.dict.domain.dto.SysServiceManagementDto;
import com.heju.system.dict.domain.po.SysServiceManagementPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:36+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysServiceManagementConverterImpl implements SysServiceManagementConverter {

    @Override
    public Page<SysServiceManagementDto> mapperPageDto(Collection<SysServiceManagementPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysServiceManagementDto> page = new Page<SysServiceManagementDto>();
        for ( SysServiceManagementPo sysServiceManagementPo : arg0 ) {
            page.add( mapperDto( sysServiceManagementPo ) );
        }

        return page;
    }

    @Override
    public Page<SysServiceManagementPo> mapperPagePo(Collection<SysServiceManagementDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysServiceManagementPo> page = new Page<SysServiceManagementPo>();
        for ( SysServiceManagementDto sysServiceManagementDto : arg0 ) {
            page.add( mapperPo( sysServiceManagementDto ) );
        }

        return page;
    }

    @Override
    public SysServiceManagementDto mapperDto(SysServiceManagementPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysServiceManagementDto sysServiceManagementDto = new SysServiceManagementDto();

        sysServiceManagementDto.setId( arg0.getId() );
        sysServiceManagementDto.setSourceName( arg0.getSourceName() );
        sysServiceManagementDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysServiceManagementDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysServiceManagementDto.setName( arg0.getName() );
        sysServiceManagementDto.setSort( arg0.getSort() );
        sysServiceManagementDto.setRemark( arg0.getRemark() );
        sysServiceManagementDto.setCreateBy( arg0.getCreateBy() );
        sysServiceManagementDto.setCreateTime( arg0.getCreateTime() );
        sysServiceManagementDto.setUpdateBy( arg0.getUpdateBy() );
        sysServiceManagementDto.setUpdateTime( arg0.getUpdateTime() );
        sysServiceManagementDto.setDelFlag( arg0.getDelFlag() );
        sysServiceManagementDto.setCreateName( arg0.getCreateName() );
        sysServiceManagementDto.setUpdateName( arg0.getUpdateName() );
        sysServiceManagementDto.setParentId( arg0.getParentId() );
        sysServiceManagementDto.setParentName( arg0.getParentName() );
        sysServiceManagementDto.setAncestors( arg0.getAncestors() );
        sysServiceManagementDto.setLevel( arg0.getLevel() );
        sysServiceManagementDto.setDefaultNode( arg0.getDefaultNode() );
        sysServiceManagementDto.setOldAncestors( arg0.getOldAncestors() );
        sysServiceManagementDto.setOldLevel( arg0.getOldLevel() );
        sysServiceManagementDto.setCode( arg0.getCode() );
        sysServiceManagementDto.setStatus( arg0.getStatus() );
        sysServiceManagementDto.setIsRecommend( arg0.getIsRecommend() );
        sysServiceManagementDto.setServicesIcon( arg0.getServicesIcon() );

        return sysServiceManagementDto;
    }

    @Override
    public List<SysServiceManagementDto> mapperDto(Collection<SysServiceManagementPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysServiceManagementDto> list = new ArrayList<SysServiceManagementDto>( arg0.size() );
        for ( SysServiceManagementPo sysServiceManagementPo : arg0 ) {
            list.add( mapperDto( sysServiceManagementPo ) );
        }

        return list;
    }

    @Override
    public SysServiceManagementPo mapperPo(SysServiceManagementDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysServiceManagementPo sysServiceManagementPo = new SysServiceManagementPo();

        sysServiceManagementPo.setId( arg0.getId() );
        sysServiceManagementPo.setSourceName( arg0.getSourceName() );
        sysServiceManagementPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysServiceManagementPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysServiceManagementPo.setName( arg0.getName() );
        sysServiceManagementPo.setSort( arg0.getSort() );
        sysServiceManagementPo.setRemark( arg0.getRemark() );
        sysServiceManagementPo.setCreateBy( arg0.getCreateBy() );
        sysServiceManagementPo.setCreateTime( arg0.getCreateTime() );
        sysServiceManagementPo.setUpdateBy( arg0.getUpdateBy() );
        sysServiceManagementPo.setUpdateTime( arg0.getUpdateTime() );
        sysServiceManagementPo.setDelFlag( arg0.getDelFlag() );
        sysServiceManagementPo.setCreateName( arg0.getCreateName() );
        sysServiceManagementPo.setUpdateName( arg0.getUpdateName() );
        sysServiceManagementPo.setParentId( arg0.getParentId() );
        sysServiceManagementPo.setParentName( arg0.getParentName() );
        sysServiceManagementPo.setAncestors( arg0.getAncestors() );
        sysServiceManagementPo.setLevel( arg0.getLevel() );
        sysServiceManagementPo.setDefaultNode( arg0.getDefaultNode() );
        sysServiceManagementPo.setOldAncestors( arg0.getOldAncestors() );
        sysServiceManagementPo.setOldLevel( arg0.getOldLevel() );
        sysServiceManagementPo.setCode( arg0.getCode() );
        sysServiceManagementPo.setStatus( arg0.getStatus() );
        sysServiceManagementPo.setIsRecommend( arg0.getIsRecommend() );
        sysServiceManagementPo.setServicesIcon( arg0.getServicesIcon() );

        return sysServiceManagementPo;
    }

    @Override
    public List<SysServiceManagementPo> mapperPo(Collection<SysServiceManagementDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysServiceManagementPo> list = new ArrayList<SysServiceManagementPo>( arg0.size() );
        for ( SysServiceManagementDto sysServiceManagementDto : arg0 ) {
            list.add( mapperPo( sysServiceManagementDto ) );
        }

        return list;
    }
}
