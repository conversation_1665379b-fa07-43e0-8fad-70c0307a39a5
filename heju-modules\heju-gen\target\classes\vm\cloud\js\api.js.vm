import request from '@/utils/request'

#set($IdName = $pkColumn.javaField)
#if($api.list)
/** 查询${functionName}列表 */
export function list${BusinessName}Api(query) {
    return request({
        url: '/${moduleName}/${businessName}/list',
        method: 'get',
        params: query
    })
}

#if($table.tree || $table.subTree)
/** 查询${functionName}列表（排除节点） */
export function list${BusinessName}ExNodesApi(${IdName}) {
    return request({
        url: '/${moduleName}/${businessName}/list/exclude',
        method: 'get',
        params: { ${IdName}: ${IdName} }
    })
}

#end
/** 查询${functionName}选择框列表 */
export function option${BusinessName}Api() {
    return request({
        url: '/${moduleName}/${businessName}/option',
        method: 'get'
    })
}

#end
/** 查询${functionName}详细 */
export function get${BusinessName}Api(${IdName}) {
    return request({
        url: '/${moduleName}/${businessName}/' + ${IdName},
        method: 'get'
    })
}

/** 新增${functionName} */
export function add${BusinessName}Api(data) {
    return request({
        url: '/${moduleName}/${businessName}',
        method: 'post',
        data: data
    })
}

/** 修改${functionName} */
export function edit${BusinessName}Api(data) {
    return request({
        url: '/${moduleName}/${businessName}',
        method: 'put',
        data: data
    })
}

#if($api.editStatus || $api.editStatusForce)
#set($StatusName = $statusColumn.javaField)
/** 修改${functionName}状态 */
export function editStatus${BusinessName}Api(${IdName}, ${StatusName}) {
    return request({
        url: '/${moduleName}/${businessName}/status',
        method: 'put',
        data: {${IdName}: ${IdName}, ${StatusName}: ${StatusName}}
    })
}

#end
#if($api.batchRemove)
/** 删除${functionName} */
export function del${BusinessName}Api(ids) {
    return request({
        url: '/${moduleName}/${businessName}/batch/' + ids,
        method: 'delete'
    })
}

#end
#if($api.batchRemoveForce)
/** 强制删除${functionName} */
export function delForce${BusinessName}Api(ids) {
    return request({
        url: '/${moduleName}/${businessName}/batch/force/' + ids,
        method: 'delete'
    })
}

#end