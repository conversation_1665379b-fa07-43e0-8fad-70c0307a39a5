package com.heju.system.entity.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.entity.domain.dto.SysEntitySaicChangeRecordDto;
import com.heju.system.entity.domain.po.SysEntitySaicChangeRecordPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysEntitySaicChangeRecordConverterImpl implements SysEntitySaicChangeRecordConverter {

    @Override
    public SysEntitySaicChangeRecordDto mapperDto(SysEntitySaicChangeRecordPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntitySaicChangeRecordDto sysEntitySaicChangeRecordDto = new SysEntitySaicChangeRecordDto();

        sysEntitySaicChangeRecordDto.setId( arg0.getId() );
        sysEntitySaicChangeRecordDto.setSourceName( arg0.getSourceName() );
        sysEntitySaicChangeRecordDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntitySaicChangeRecordDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntitySaicChangeRecordDto.setName( arg0.getName() );
        sysEntitySaicChangeRecordDto.setStatus( arg0.getStatus() );
        sysEntitySaicChangeRecordDto.setSort( arg0.getSort() );
        sysEntitySaicChangeRecordDto.setRemark( arg0.getRemark() );
        sysEntitySaicChangeRecordDto.setCreateBy( arg0.getCreateBy() );
        sysEntitySaicChangeRecordDto.setCreateTime( arg0.getCreateTime() );
        sysEntitySaicChangeRecordDto.setUpdateBy( arg0.getUpdateBy() );
        sysEntitySaicChangeRecordDto.setUpdateTime( arg0.getUpdateTime() );
        sysEntitySaicChangeRecordDto.setDelFlag( arg0.getDelFlag() );
        sysEntitySaicChangeRecordDto.setCreateName( arg0.getCreateName() );
        sysEntitySaicChangeRecordDto.setUpdateName( arg0.getUpdateName() );
        sysEntitySaicChangeRecordDto.setChangeDate( arg0.getChangeDate() );
        sysEntitySaicChangeRecordDto.setChangeType( arg0.getChangeType() );
        sysEntitySaicChangeRecordDto.setChangeBefore( arg0.getChangeBefore() );
        sysEntitySaicChangeRecordDto.setChangeAfter( arg0.getChangeAfter() );
        sysEntitySaicChangeRecordDto.setEntityId( arg0.getEntityId() );

        return sysEntitySaicChangeRecordDto;
    }

    @Override
    public List<SysEntitySaicChangeRecordDto> mapperDto(Collection<SysEntitySaicChangeRecordPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntitySaicChangeRecordDto> list = new ArrayList<SysEntitySaicChangeRecordDto>( arg0.size() );
        for ( SysEntitySaicChangeRecordPo sysEntitySaicChangeRecordPo : arg0 ) {
            list.add( mapperDto( sysEntitySaicChangeRecordPo ) );
        }

        return list;
    }

    @Override
    public Page<SysEntitySaicChangeRecordDto> mapperPageDto(Collection<SysEntitySaicChangeRecordPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntitySaicChangeRecordDto> page = new Page<SysEntitySaicChangeRecordDto>();
        for ( SysEntitySaicChangeRecordPo sysEntitySaicChangeRecordPo : arg0 ) {
            page.add( mapperDto( sysEntitySaicChangeRecordPo ) );
        }

        return page;
    }

    @Override
    public SysEntitySaicChangeRecordPo mapperPo(SysEntitySaicChangeRecordDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntitySaicChangeRecordPo sysEntitySaicChangeRecordPo = new SysEntitySaicChangeRecordPo();

        sysEntitySaicChangeRecordPo.setId( arg0.getId() );
        sysEntitySaicChangeRecordPo.setSourceName( arg0.getSourceName() );
        sysEntitySaicChangeRecordPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntitySaicChangeRecordPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntitySaicChangeRecordPo.setName( arg0.getName() );
        sysEntitySaicChangeRecordPo.setStatus( arg0.getStatus() );
        sysEntitySaicChangeRecordPo.setSort( arg0.getSort() );
        sysEntitySaicChangeRecordPo.setRemark( arg0.getRemark() );
        sysEntitySaicChangeRecordPo.setCreateBy( arg0.getCreateBy() );
        sysEntitySaicChangeRecordPo.setCreateTime( arg0.getCreateTime() );
        sysEntitySaicChangeRecordPo.setUpdateBy( arg0.getUpdateBy() );
        sysEntitySaicChangeRecordPo.setUpdateTime( arg0.getUpdateTime() );
        sysEntitySaicChangeRecordPo.setDelFlag( arg0.getDelFlag() );
        sysEntitySaicChangeRecordPo.setCreateName( arg0.getCreateName() );
        sysEntitySaicChangeRecordPo.setUpdateName( arg0.getUpdateName() );
        sysEntitySaicChangeRecordPo.setChangeDate( arg0.getChangeDate() );
        sysEntitySaicChangeRecordPo.setChangeType( arg0.getChangeType() );
        sysEntitySaicChangeRecordPo.setChangeBefore( arg0.getChangeBefore() );
        sysEntitySaicChangeRecordPo.setChangeAfter( arg0.getChangeAfter() );
        sysEntitySaicChangeRecordPo.setEntityId( arg0.getEntityId() );

        return sysEntitySaicChangeRecordPo;
    }

    @Override
    public List<SysEntitySaicChangeRecordPo> mapperPo(Collection<SysEntitySaicChangeRecordDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntitySaicChangeRecordPo> list = new ArrayList<SysEntitySaicChangeRecordPo>( arg0.size() );
        for ( SysEntitySaicChangeRecordDto sysEntitySaicChangeRecordDto : arg0 ) {
            list.add( mapperPo( sysEntitySaicChangeRecordDto ) );
        }

        return list;
    }

    @Override
    public Page<SysEntitySaicChangeRecordPo> mapperPagePo(Collection<SysEntitySaicChangeRecordDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntitySaicChangeRecordPo> page = new Page<SysEntitySaicChangeRecordPo>();
        for ( SysEntitySaicChangeRecordDto sysEntitySaicChangeRecordDto : arg0 ) {
            page.add( mapperPo( sysEntitySaicChangeRecordDto ) );
        }

        return page;
    }
}
