09:12:09.546 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:12:10.289 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6602e3ac-9995-4850-9062-e15a6a4db494_config-0
09:12:10.377 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 37 ms to scan 1 urls, producing 3 keys and 6 values 
09:12:10.421 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:12:10.437 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 3 keys and 10 values 
09:12:10.453 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 5 values 
09:12:10.465 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:12:10.485 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
09:12:10.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6602e3ac-9995-4850-9062-e15a6a4db494_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:12:10.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6602e3ac-9995-4850-9062-e15a6a4db494_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000029e813b7220
09:12:10.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6602e3ac-9995-4850-9062-e15a6a4db494_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000029e813b7440
09:12:10.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6602e3ac-9995-4850-9062-e15a6a4db494_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:12:10.507 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6602e3ac-9995-4850-9062-e15a6a4db494_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:12:10.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6602e3ac-9995-4850-9062-e15a6a4db494_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:11.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6602e3ac-9995-4850-9062-e15a6a4db494_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753319531470_127.0.0.1_12501
09:12:11.704 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6602e3ac-9995-4850-9062-e15a6a4db494_config-0] Notify connected event to listeners.
09:12:11.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6602e3ac-9995-4850-9062-e15a6a4db494_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:11.705 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6602e3ac-9995-4850-9062-e15a6a4db494_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000029e814f0fb0
09:12:11.869 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:12:15.587 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:12:15.588 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:12:15.588 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:12:15.781 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:12:18.335 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:12:22.713 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e53d8fa1-15bc-4818-bd73-524c1cf176ee
09:12:22.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] RpcClient init label, labels = {module=naming, source=sdk}
09:12:22.717 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:12:22.717 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:12:22.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:12:22.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:22.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Success to connect to server [localhost:8848] on start up, connectionId = 1753319542730_127.0.0.1_12622
09:12:22.859 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Notify connected event to listeners.
09:12:22.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:22.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000029e814f0fb0
09:12:22.945 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:12:22.999 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:12:23.216 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 14.271 seconds (JVM running for 15.866)
09:12:23.236 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:12:23.237 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:12:23.241 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:12:23.784 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:12:23.792 [RMI TCP Connection(12)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:12:23.804 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Ack server push request, request = NotifySubscriberRequest, requestId = 1
10:37:20.882 [nacos-grpc-client-executor-1038] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Receive server push request, request = NotifySubscriberRequest, requestId = 16
10:37:20.882 [nacos-grpc-client-executor-1038] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Ack server push request, request = NotifySubscriberRequest, requestId = 16
11:49:17.955 [nacos-grpc-client-executor-1919] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Receive server push request, request = NotifySubscriberRequest, requestId = 17
11:49:17.980 [nacos-grpc-client-executor-1919] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Ack server push request, request = NotifySubscriberRequest, requestId = 17
11:49:45.304 [nacos-grpc-client-executor-1926] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Receive server push request, request = NotifySubscriberRequest, requestId = 21
11:49:45.323 [nacos-grpc-client-executor-1926] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Ack server push request, request = NotifySubscriberRequest, requestId = 21
11:51:27.481 [nacos-grpc-client-executor-1947] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Receive server push request, request = NotifySubscriberRequest, requestId = 26
11:51:27.503 [nacos-grpc-client-executor-1947] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Ack server push request, request = NotifySubscriberRequest, requestId = 26
11:51:53.056 [nacos-grpc-client-executor-1952] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Receive server push request, request = NotifySubscriberRequest, requestId = 30
11:51:53.076 [nacos-grpc-client-executor-1952] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Ack server push request, request = NotifySubscriberRequest, requestId = 30
13:28:55.932 [nacos-grpc-client-executor-3116] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Receive server push request, request = NotifySubscriberRequest, requestId = 35
13:28:55.950 [nacos-grpc-client-executor-3116] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Ack server push request, request = NotifySubscriberRequest, requestId = 35
13:29:39.240 [nacos-grpc-client-executor-3125] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Receive server push request, request = NotifySubscriberRequest, requestId = 39
13:29:39.249 [nacos-grpc-client-executor-3125] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Ack server push request, request = NotifySubscriberRequest, requestId = 39
13:31:40.252 [nacos-grpc-client-executor-3149] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Receive server push request, request = NotifySubscriberRequest, requestId = 44
13:31:40.271 [nacos-grpc-client-executor-3149] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Ack server push request, request = NotifySubscriberRequest, requestId = 44
13:31:58.819 [nacos-grpc-client-executor-3153] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Receive server push request, request = NotifySubscriberRequest, requestId = 48
13:31:58.835 [nacos-grpc-client-executor-3153] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Ack server push request, request = NotifySubscriberRequest, requestId = 48
13:37:01.866 [nacos-grpc-client-executor-3215] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Receive server push request, request = NotifySubscriberRequest, requestId = 53
13:37:01.888 [nacos-grpc-client-executor-3215] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Ack server push request, request = NotifySubscriberRequest, requestId = 53
13:37:18.886 [nacos-grpc-client-executor-3218] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Receive server push request, request = NotifySubscriberRequest, requestId = 57
13:37:18.900 [nacos-grpc-client-executor-3218] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Ack server push request, request = NotifySubscriberRequest, requestId = 57
13:40:17.535 [nacos-grpc-client-executor-3254] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Receive server push request, request = NotifySubscriberRequest, requestId = 63
13:40:17.552 [nacos-grpc-client-executor-3254] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Ack server push request, request = NotifySubscriberRequest, requestId = 63
13:40:32.374 [nacos-grpc-client-executor-3257] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Receive server push request, request = NotifySubscriberRequest, requestId = 67
13:40:32.386 [nacos-grpc-client-executor-3257] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e53d8fa1-15bc-4818-bd73-524c1cf176ee] Ack server push request, request = NotifySubscriberRequest, requestId = 67
13:41:02.332 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:41:02.338 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:41:02.665 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:41:02.665 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@dccfcde[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:41:02.665 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753319542730_127.0.0.1_12622
13:41:02.665 [nacos-grpc-client-executor-3265] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753319542730_127.0.0.1_12622]Ignore complete event,isRunning:false,isAbandon=false
13:41:02.665 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3d2c0531[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3266]
13:41:41.654 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:41:43.511 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e08b0272-cde4-4ccb-a0df-e518ad817bf6_config-0
13:41:43.674 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 90 ms to scan 1 urls, producing 3 keys and 6 values 
13:41:43.753 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 4 keys and 9 values 
13:41:43.778 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 10 values 
13:41:43.801 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
13:41:43.822 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
13:41:43.840 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
13:41:43.849 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e08b0272-cde4-4ccb-a0df-e518ad817bf6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:41:43.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e08b0272-cde4-4ccb-a0df-e518ad817bf6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000023ecf3b2ad8
13:41:43.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e08b0272-cde4-4ccb-a0df-e518ad817bf6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000023ecf3b2cf8
13:41:43.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e08b0272-cde4-4ccb-a0df-e518ad817bf6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:41:43.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e08b0272-cde4-4ccb-a0df-e518ad817bf6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:41:43.874 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e08b0272-cde4-4ccb-a0df-e518ad817bf6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:41:46.137 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e08b0272-cde4-4ccb-a0df-e518ad817bf6_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753335705776_127.0.0.1_1438
13:41:46.138 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e08b0272-cde4-4ccb-a0df-e518ad817bf6_config-0] Notify connected event to listeners.
13:41:46.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e08b0272-cde4-4ccb-a0df-e518ad817bf6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:41:46.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e08b0272-cde4-4ccb-a0df-e518ad817bf6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000023ecf4ee9a8
13:41:46.396 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:41:52.272 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
13:41:52.273 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:41:52.273 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:41:52.709 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:41:56.181 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:42:01.523 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8f3e4be7-f945-4a6b-8e69-b8a5bda1ec60
13:42:01.524 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f3e4be7-f945-4a6b-8e69-b8a5bda1ec60] RpcClient init label, labels = {module=naming, source=sdk}
13:42:01.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f3e4be7-f945-4a6b-8e69-b8a5bda1ec60] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:42:01.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f3e4be7-f945-4a6b-8e69-b8a5bda1ec60] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:42:01.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f3e4be7-f945-4a6b-8e69-b8a5bda1ec60] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:42:01.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f3e4be7-f945-4a6b-8e69-b8a5bda1ec60] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:42:01.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f3e4be7-f945-4a6b-8e69-b8a5bda1ec60] Success to connect to server [localhost:8848] on start up, connectionId = 1753335721546_127.0.0.1_1493
13:42:01.665 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f3e4be7-f945-4a6b-8e69-b8a5bda1ec60] Notify connected event to listeners.
13:42:01.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f3e4be7-f945-4a6b-8e69-b8a5bda1ec60] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:42:01.666 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f3e4be7-f945-4a6b-8e69-b8a5bda1ec60] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000023ecf4ee9a8
13:42:01.744 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
13:42:01.792 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
13:42:02.031 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 22.61 seconds (JVM running for 26.865)
13:42:02.047 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
13:42:02.048 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
13:42:02.052 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
13:42:02.216 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f3e4be7-f945-4a6b-8e69-b8a5bda1ec60] Receive server push request, request = NotifySubscriberRequest, requestId = 4
13:42:02.238 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f3e4be7-f945-4a6b-8e69-b8a5bda1ec60] Ack server push request, request = NotifySubscriberRequest, requestId = 4
20:42:17.782 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:42:17.787 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:42:18.124 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:42:18.124 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5ebc4d03[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:42:18.124 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753335721546_127.0.0.1_1493
20:42:18.126 [nacos-grpc-client-executor-5047] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753335721546_127.0.0.1_1493]Ignore complete event,isRunning:false,isAbandon=false
20:42:18.130 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@d7d481c[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 5048]
