09:49:47.720 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:49:48.686 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e8f1a9bc-13f3-45fe-b0a4-d95c1dc039ff_config-0
09:49:48.768 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 42 ms to scan 1 urls, producing 3 keys and 6 values 
09:49:48.797 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:49:48.808 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:49:48.821 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:49:48.840 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 1 keys and 7 values 
09:49:48.854 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:49:48.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8f1a9bc-13f3-45fe-b0a4-d95c1dc039ff_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:49:48.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8f1a9bc-13f3-45fe-b0a4-d95c1dc039ff_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001da3639fd80
09:49:48.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8f1a9bc-13f3-45fe-b0a4-d95c1dc039ff_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001da363a0000
09:49:48.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8f1a9bc-13f3-45fe-b0a4-d95c1dc039ff_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:49:48.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8f1a9bc-13f3-45fe-b0a4-d95c1dc039ff_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:49:48.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8f1a9bc-13f3-45fe-b0a4-d95c1dc039ff_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:49:50.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8f1a9bc-13f3-45fe-b0a4-d95c1dc039ff_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752025789785_127.0.0.1_8065
09:49:50.005 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8f1a9bc-13f3-45fe-b0a4-d95c1dc039ff_config-0] Notify connected event to listeners.
09:49:50.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8f1a9bc-13f3-45fe-b0a4-d95c1dc039ff_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:49:50.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8f1a9bc-13f3-45fe-b0a4-d95c1dc039ff_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001da3651a0a0
09:49:50.188 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:49:54.017 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:49:54.018 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:49:54.019 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:49:54.233 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:49:55.442 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:49:55.444 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:49:55.444 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:49:59.036 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:50:02.787 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 98e524a7-7251-43a1-b1b8-8a036358e68a
09:50:02.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98e524a7-7251-43a1-b1b8-8a036358e68a] RpcClient init label, labels = {module=naming, source=sdk}
09:50:02.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98e524a7-7251-43a1-b1b8-8a036358e68a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:50:02.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98e524a7-7251-43a1-b1b8-8a036358e68a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:50:02.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98e524a7-7251-43a1-b1b8-8a036358e68a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:50:02.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98e524a7-7251-43a1-b1b8-8a036358e68a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:50:02.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98e524a7-7251-43a1-b1b8-8a036358e68a] Success to connect to server [localhost:8848] on start up, connectionId = 1752025802807_127.0.0.1_8116
09:50:02.935 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98e524a7-7251-43a1-b1b8-8a036358e68a] Notify connected event to listeners.
09:50:02.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98e524a7-7251-43a1-b1b8-8a036358e68a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:50:02.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98e524a7-7251-43a1-b1b8-8a036358e68a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001da3651a0a0
09:50:02.993 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:50:03.041 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
09:50:03.207 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 16.355 seconds (JVM running for 17.853)
09:50:03.224 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:50:03.227 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:50:03.228 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:50:03.481 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98e524a7-7251-43a1-b1b8-8a036358e68a] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:50:03.502 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98e524a7-7251-43a1-b1b8-8a036358e68a] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:50:03.530 [RMI TCP Connection(1)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:56:53.758 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:56:53.770 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:56:54.106 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:56:54.107 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@68819da4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:56:54.107 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752025802807_127.0.0.1_8116
09:56:54.109 [nacos-grpc-client-executor-94] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752025802807_127.0.0.1_8116]Ignore complete event,isRunning:false,isAbandon=false
09:56:54.111 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7b251da7[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 95]
09:56:54.249 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:56:54.251 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:56:54.258 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:56:54.258 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:57:40.519 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:57:41.878 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 07ac2735-8910-4d4b-8ca4-8e150635e528_config-0
09:57:41.994 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 64 ms to scan 1 urls, producing 3 keys and 6 values 
09:57:42.035 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:57:42.056 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
09:57:42.085 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 1 keys and 5 values 
09:57:42.111 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 7 values 
09:57:42.130 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
09:57:42.137 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07ac2735-8910-4d4b-8ca4-8e150635e528_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:57:42.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07ac2735-8910-4d4b-8ca4-8e150635e528_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000298e83b7268
09:57:42.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07ac2735-8910-4d4b-8ca4-8e150635e528_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000298e83b7488
09:57:42.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07ac2735-8910-4d4b-8ca4-8e150635e528_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:57:42.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07ac2735-8910-4d4b-8ca4-8e150635e528_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:57:42.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07ac2735-8910-4d4b-8ca4-8e150635e528_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:57:44.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07ac2735-8910-4d4b-8ca4-8e150635e528_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752026263913_127.0.0.1_8958
09:57:44.395 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07ac2735-8910-4d4b-8ca4-8e150635e528_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:57:44.396 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07ac2735-8910-4d4b-8ca4-8e150635e528_config-0] Notify connected event to listeners.
09:57:44.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07ac2735-8910-4d4b-8ca4-8e150635e528_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000298e84f0fb0
09:57:44.667 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:57:52.419 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:57:52.420 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:57:52.421 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:57:52.816 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:57:54.560 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:57:54.563 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:57:54.564 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:57:59.828 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:58:05.683 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bf864067-11aa-4320-88a2-b6ce15c80d97
09:58:05.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf864067-11aa-4320-88a2-b6ce15c80d97] RpcClient init label, labels = {module=naming, source=sdk}
09:58:05.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf864067-11aa-4320-88a2-b6ce15c80d97] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:58:05.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf864067-11aa-4320-88a2-b6ce15c80d97] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:58:05.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf864067-11aa-4320-88a2-b6ce15c80d97] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:58:05.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf864067-11aa-4320-88a2-b6ce15c80d97] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:58:05.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf864067-11aa-4320-88a2-b6ce15c80d97] Success to connect to server [localhost:8848] on start up, connectionId = 1752026285702_127.0.0.1_9110
09:58:05.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf864067-11aa-4320-88a2-b6ce15c80d97] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:58:05.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf864067-11aa-4320-88a2-b6ce15c80d97] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000298e84f0fb0
09:58:05.834 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf864067-11aa-4320-88a2-b6ce15c80d97] Notify connected event to listeners.
09:58:05.892 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:58:05.939 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
09:58:06.144 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 26.953 seconds (JVM running for 29.207)
09:58:06.166 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:58:06.187 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:58:06.187 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:58:06.478 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf864067-11aa-4320-88a2-b6ce15c80d97] Receive server push request, request = NotifySubscriberRequest, requestId = 26
09:58:06.495 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf864067-11aa-4320-88a2-b6ce15c80d97] Ack server push request, request = NotifySubscriberRequest, requestId = 26
10:00:27.226 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:00:35.102 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf864067-11aa-4320-88a2-b6ce15c80d97] Receive server push request, request = NotifySubscriberRequest, requestId = 32
10:00:35.104 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf864067-11aa-4320-88a2-b6ce15c80d97] Ack server push request, request = NotifySubscriberRequest, requestId = 32
15:14:48.128 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:14:48.133 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:14:48.484 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:14:48.484 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4f8942ac[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:14:48.484 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752026285702_127.0.0.1_9110
15:14:48.489 [nacos-grpc-client-executor-3808] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752026285702_127.0.0.1_9110]Ignore complete event,isRunning:false,isAbandon=false
15:14:48.501 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3653755a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3809]
15:14:48.734 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:14:48.744 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:14:48.780 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:14:48.780 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:32:29.581 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:32:30.635 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 41af5232-d0b9-4d73-ae96-387666acf356_config-0
15:32:30.701 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
15:32:30.735 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
15:32:30.746 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
15:32:30.758 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
15:32:30.771 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
15:32:30.782 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
15:32:30.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41af5232-d0b9-4d73-ae96-387666acf356_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:32:30.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41af5232-d0b9-4d73-ae96-387666acf356_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000025c1f3ceff8
15:32:30.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41af5232-d0b9-4d73-ae96-387666acf356_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000025c1f3cf218
15:32:30.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41af5232-d0b9-4d73-ae96-387666acf356_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:32:30.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41af5232-d0b9-4d73-ae96-387666acf356_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:32:30.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41af5232-d0b9-4d73-ae96-387666acf356_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:32:31.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41af5232-d0b9-4d73-ae96-387666acf356_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752046351629_127.0.0.1_8669
15:32:31.861 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41af5232-d0b9-4d73-ae96-387666acf356_config-0] Notify connected event to listeners.
15:32:31.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41af5232-d0b9-4d73-ae96-387666acf356_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:32:31.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41af5232-d0b9-4d73-ae96-387666acf356_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025c1f508ad8
15:32:32.011 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:32:36.251 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
15:32:36.252 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:32:36.252 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:32:36.460 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:32:37.234 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:32:37.235 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:32:37.235 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:32:40.054 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:32:43.271 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8e1da9c3-c97e-4b9d-8374-2abcde16b7d7
15:32:43.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e1da9c3-c97e-4b9d-8374-2abcde16b7d7] RpcClient init label, labels = {module=naming, source=sdk}
15:32:43.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e1da9c3-c97e-4b9d-8374-2abcde16b7d7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:32:43.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e1da9c3-c97e-4b9d-8374-2abcde16b7d7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:32:43.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e1da9c3-c97e-4b9d-8374-2abcde16b7d7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:32:43.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e1da9c3-c97e-4b9d-8374-2abcde16b7d7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:32:43.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e1da9c3-c97e-4b9d-8374-2abcde16b7d7] Success to connect to server [localhost:8848] on start up, connectionId = 1752046363286_127.0.0.1_8736
15:32:43.410 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e1da9c3-c97e-4b9d-8374-2abcde16b7d7] Notify connected event to listeners.
15:32:43.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e1da9c3-c97e-4b9d-8374-2abcde16b7d7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:32:43.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e1da9c3-c97e-4b9d-8374-2abcde16b7d7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025c1f508ad8
15:32:43.453 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
15:32:43.487 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
15:32:43.619 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 14.749 seconds (JVM running for 16.089)
15:32:43.633 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
15:32:43.636 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
15:32:43.637 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
15:32:43.986 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e1da9c3-c97e-4b9d-8374-2abcde16b7d7] Receive server push request, request = NotifySubscriberRequest, requestId = 60
15:32:44.008 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e1da9c3-c97e-4b9d-8374-2abcde16b7d7] Ack server push request, request = NotifySubscriberRequest, requestId = 60
15:32:44.178 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:28:58.497 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:28:58.521 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:28:58.921 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:28:58.922 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@57b7249b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:28:58.923 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752046363286_127.0.0.1_8736
16:28:58.938 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7f36bbac[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 683]
16:28:59.184 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e1da9c3-c97e-4b9d-8374-2abcde16b7d7] Notify disconnected event to listeners
16:28:59.337 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:28:59.351 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:28:59.398 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:28:59.399 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:38:11.447 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:38:12.500 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9384cbe2-52df-4775-8f13-394897525621_config-0
17:38:12.572 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
17:38:12.619 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 4 keys and 9 values 
17:38:12.635 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
17:38:12.651 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
17:38:12.666 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
17:38:12.682 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
17:38:12.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9384cbe2-52df-4775-8f13-394897525621_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:38:12.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9384cbe2-52df-4775-8f13-394897525621_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001278139fd80
17:38:12.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9384cbe2-52df-4775-8f13-394897525621_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000127813a0000
17:38:12.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9384cbe2-52df-4775-8f13-394897525621_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:38:12.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9384cbe2-52df-4775-8f13-394897525621_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:38:12.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9384cbe2-52df-4775-8f13-394897525621_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:38:13.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9384cbe2-52df-4775-8f13-394897525621_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752053893589_127.0.0.1_11533
17:38:13.808 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9384cbe2-52df-4775-8f13-394897525621_config-0] Notify connected event to listeners.
17:38:13.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9384cbe2-52df-4775-8f13-394897525621_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:38:13.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9384cbe2-52df-4775-8f13-394897525621_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001278151a0a0
17:38:13.937 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:38:17.094 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
17:38:17.094 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:38:17.094 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:38:17.522 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:38:19.088 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:38:19.088 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:38:19.088 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:38:23.115 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:38:27.142 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5a766ee0-55f8-4f40-887a-e1a8877b92f9
17:38:27.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a766ee0-55f8-4f40-887a-e1a8877b92f9] RpcClient init label, labels = {module=naming, source=sdk}
17:38:27.151 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a766ee0-55f8-4f40-887a-e1a8877b92f9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:38:27.151 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a766ee0-55f8-4f40-887a-e1a8877b92f9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:38:27.151 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a766ee0-55f8-4f40-887a-e1a8877b92f9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:38:27.151 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a766ee0-55f8-4f40-887a-e1a8877b92f9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:38:27.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a766ee0-55f8-4f40-887a-e1a8877b92f9] Success to connect to server [localhost:8848] on start up, connectionId = 1752053907162_127.0.0.1_11576
17:38:27.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a766ee0-55f8-4f40-887a-e1a8877b92f9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:38:27.285 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a766ee0-55f8-4f40-887a-e1a8877b92f9] Notify connected event to listeners.
17:38:27.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a766ee0-55f8-4f40-887a-e1a8877b92f9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001278151a0a0
17:38:27.333 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
17:38:27.380 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
17:38:27.586 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 17.293 seconds (JVM running for 18.959)
17:38:27.602 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
17:38:27.613 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
17:38:27.613 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
17:38:27.729 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:38:27.808 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a766ee0-55f8-4f40-887a-e1a8877b92f9] Receive server push request, request = NotifySubscriberRequest, requestId = 81
17:38:27.824 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a766ee0-55f8-4f40-887a-e1a8877b92f9] Ack server push request, request = NotifySubscriberRequest, requestId = 81
19:28:57.658 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:28:57.662 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:28:57.999 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:28:57.999 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6984d025[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:28:58.000 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752053907162_127.0.0.1_11576
19:28:58.002 [nacos-grpc-client-executor-1331] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752053907162_127.0.0.1_11576]Ignore complete event,isRunning:false,isAbandon=false
19:28:58.007 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@347800c4[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 1332]
19:28:58.181 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:28:58.187 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:28:58.209 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:28:58.210 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
