10:22:21.154 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:22:22.281 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 222da35e-bb3f-4858-9125-54921ce9bf76_config-0
10:22:22.375 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 50 ms to scan 1 urls, producing 3 keys and 6 values 
10:22:22.425 [main] INFO  o.r.Reflections - [scan,232] - <PERSON>flections took 27 ms to scan 1 urls, producing 4 keys and 9 values 
10:22:22.445 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 3 keys and 10 values 
10:22:22.461 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 5 values 
10:22:22.477 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 7 values 
10:22:22.497 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
10:22:22.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [222da35e-bb3f-4858-9125-54921ce9bf76_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:22:22.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [222da35e-bb3f-4858-9125-54921ce9bf76_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001e2a13b8200
10:22:22.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [222da35e-bb3f-4858-9125-54921ce9bf76_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001e2a13b8420
10:22:22.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [222da35e-bb3f-4858-9125-54921ce9bf76_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:22:22.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [222da35e-bb3f-4858-9125-54921ce9bf76_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:22:22.516 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [222da35e-bb3f-4858-9125-54921ce9bf76_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:22:23.849 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [222da35e-bb3f-4858-9125-54921ce9bf76_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754187743582_127.0.0.1_1424
10:22:23.850 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [222da35e-bb3f-4858-9125-54921ce9bf76_config-0] Notify connected event to listeners.
10:22:23.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [222da35e-bb3f-4858-9125-54921ce9bf76_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:22:23.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [222da35e-bb3f-4858-9125-54921ce9bf76_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001e2a14f26e0
10:22:24.059 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:22:30.815 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
10:22:30.817 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:22:30.817 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:22:31.134 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:22:32.412 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:22:32.412 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:22:32.412 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:22:36.765 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:22:40.908 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c2d8c9a0-7d07-464a-b33d-ea9ea98a6989
10:22:40.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] RpcClient init label, labels = {module=naming, source=sdk}
10:22:40.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:22:40.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:22:40.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:22:40.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:22:41.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Success to connect to server [localhost:8848] on start up, connectionId = 1754187760929_127.0.0.1_1748
10:22:41.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:22:41.049 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Notify connected event to listeners.
10:22:41.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001e2a14f26e0
10:22:41.107 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
10:22:41.146 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
10:22:41.333 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 21.402 seconds (JVM running for 24.285)
10:22:41.351 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
10:22:41.353 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
10:22:41.353 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
10:22:41.583 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 6
10:22:41.598 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 6
10:23:55.278 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:24:01.817 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 14
10:24:01.817 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 14
10:53:47.105 [nacos-grpc-client-executor-389] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 15
10:53:47.120 [nacos-grpc-client-executor-389] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 15
10:54:04.736 [nacos-grpc-client-executor-393] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 18
10:54:04.750 [nacos-grpc-client-executor-393] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 18
11:55:22.949 [nacos-grpc-client-executor-1129] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 22
11:55:22.969 [nacos-grpc-client-executor-1129] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 22
11:55:49.223 [nacos-grpc-client-executor-1134] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 25
11:55:49.238 [nacos-grpc-client-executor-1134] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 25
12:43:57.332 [nacos-grpc-client-executor-1710] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 29
12:43:57.351 [nacos-grpc-client-executor-1710] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 29
12:44:17.628 [nacos-grpc-client-executor-1715] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 33
12:44:17.638 [nacos-grpc-client-executor-1715] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 33
14:11:46.622 [nacos-grpc-client-executor-2766] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 36
14:11:46.637 [nacos-grpc-client-executor-2766] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 36
14:12:15.278 [nacos-grpc-client-executor-2772] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 39
14:12:15.298 [nacos-grpc-client-executor-2772] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 39
14:30:47.699 [nacos-grpc-client-executor-2994] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 43
14:30:47.713 [nacos-grpc-client-executor-2994] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 43
14:31:08.260 [nacos-grpc-client-executor-2998] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 46
14:31:08.276 [nacos-grpc-client-executor-2998] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 46
14:47:48.462 [nacos-grpc-client-executor-3199] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 50
14:47:48.477 [nacos-grpc-client-executor-3199] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 50
14:48:12.317 [nacos-grpc-client-executor-3204] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 53
14:48:12.332 [nacos-grpc-client-executor-3204] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 53
15:12:28.680 [nacos-grpc-client-executor-3496] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 57
15:12:28.699 [nacos-grpc-client-executor-3496] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 57
15:12:46.046 [nacos-grpc-client-executor-3500] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 60
15:12:46.049 [nacos-grpc-client-executor-3500] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 60
15:21:48.819 [nacos-grpc-client-executor-3609] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 64
15:21:48.834 [nacos-grpc-client-executor-3609] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 64
15:22:09.543 [nacos-grpc-client-executor-3613] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 67
15:22:09.560 [nacos-grpc-client-executor-3613] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 67
15:29:22.618 [nacos-grpc-client-executor-3699] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 71
15:29:22.633 [nacos-grpc-client-executor-3699] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 71
15:29:50.584 [nacos-grpc-client-executor-3705] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 74
15:29:50.599 [nacos-grpc-client-executor-3705] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 74
15:35:12.250 [nacos-grpc-client-executor-3769] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 78
15:35:12.269 [nacos-grpc-client-executor-3769] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 78
15:35:29.236 [nacos-grpc-client-executor-3772] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 81
15:35:29.250 [nacos-grpc-client-executor-3772] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 81
16:23:19.154 [nacos-grpc-client-executor-4346] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 85
16:23:19.172 [nacos-grpc-client-executor-4346] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 85
16:23:36.785 [nacos-grpc-client-executor-4350] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Receive server push request, request = NotifySubscriberRequest, requestId = 88
16:23:36.802 [nacos-grpc-client-executor-4350] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2d8c9a0-7d07-464a-b33d-ea9ea98a6989] Ack server push request, request = NotifySubscriberRequest, requestId = 88
18:45:57.583 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:45:57.583 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:45:57.906 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:45:57.906 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@23a32657[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:45:57.906 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754187760929_127.0.0.1_1748
18:45:57.908 [nacos-grpc-client-executor-6057] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754187760929_127.0.0.1_1748]Ignore complete event,isRunning:false,isAbandon=false
18:45:57.908 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@fd0a412[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6058]
18:45:58.078 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:45:58.090 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:45:58.092 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:45:58.092 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
