package com.heju.tenant.api.tenant.domain.model;

import com.github.pagehelper.Page;
import com.heju.tenant.api.tenant.domain.dto.TeStrategyDto;
import com.heju.tenant.api.tenant.domain.po.TeStrategyPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:53+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class TeStrategyConverterImpl implements TeStrategyConverter {

    @Override
    public TeStrategyDto mapperDto(TeStrategyPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        TeStrategyDto teStrategyDto = new TeStrategyDto();

        teStrategyDto.setId( arg0.getId() );
        teStrategyDto.setSourceName( arg0.getSourceName() );
        teStrategyDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            teStrategyDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        teStrategyDto.setName( arg0.getName() );
        teStrategyDto.setStatus( arg0.getStatus() );
        teStrategyDto.setSort( arg0.getSort() );
        teStrategyDto.setRemark( arg0.getRemark() );
        teStrategyDto.setCreateBy( arg0.getCreateBy() );
        teStrategyDto.setCreateTime( arg0.getCreateTime() );
        teStrategyDto.setUpdateBy( arg0.getUpdateBy() );
        teStrategyDto.setUpdateTime( arg0.getUpdateTime() );
        teStrategyDto.setDelFlag( arg0.getDelFlag() );
        teStrategyDto.setCreateName( arg0.getCreateName() );
        teStrategyDto.setUpdateName( arg0.getUpdateName() );
        teStrategyDto.setSourceId( arg0.getSourceId() );
        teStrategyDto.setSourceSlave( arg0.getSourceSlave() );
        teStrategyDto.setIsDefault( arg0.getIsDefault() );

        return teStrategyDto;
    }

    @Override
    public List<TeStrategyDto> mapperDto(Collection<TeStrategyPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<TeStrategyDto> list = new ArrayList<TeStrategyDto>( arg0.size() );
        for ( TeStrategyPo teStrategyPo : arg0 ) {
            list.add( mapperDto( teStrategyPo ) );
        }

        return list;
    }

    @Override
    public Page<TeStrategyDto> mapperPageDto(Collection<TeStrategyPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<TeStrategyDto> page = new Page<TeStrategyDto>();
        for ( TeStrategyPo teStrategyPo : arg0 ) {
            page.add( mapperDto( teStrategyPo ) );
        }

        return page;
    }

    @Override
    public TeStrategyPo mapperPo(TeStrategyDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        TeStrategyPo teStrategyPo = new TeStrategyPo();

        teStrategyPo.setId( arg0.getId() );
        teStrategyPo.setSourceName( arg0.getSourceName() );
        teStrategyPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            teStrategyPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        teStrategyPo.setName( arg0.getName() );
        teStrategyPo.setStatus( arg0.getStatus() );
        teStrategyPo.setSort( arg0.getSort() );
        teStrategyPo.setRemark( arg0.getRemark() );
        teStrategyPo.setCreateBy( arg0.getCreateBy() );
        teStrategyPo.setCreateTime( arg0.getCreateTime() );
        teStrategyPo.setUpdateBy( arg0.getUpdateBy() );
        teStrategyPo.setUpdateTime( arg0.getUpdateTime() );
        teStrategyPo.setDelFlag( arg0.getDelFlag() );
        teStrategyPo.setCreateName( arg0.getCreateName() );
        teStrategyPo.setUpdateName( arg0.getUpdateName() );
        teStrategyPo.setSourceId( arg0.getSourceId() );
        teStrategyPo.setSourceSlave( arg0.getSourceSlave() );
        teStrategyPo.setIsDefault( arg0.getIsDefault() );

        return teStrategyPo;
    }

    @Override
    public List<TeStrategyPo> mapperPo(Collection<TeStrategyDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<TeStrategyPo> list = new ArrayList<TeStrategyPo>( arg0.size() );
        for ( TeStrategyDto teStrategyDto : arg0 ) {
            list.add( mapperPo( teStrategyDto ) );
        }

        return list;
    }

    @Override
    public Page<TeStrategyPo> mapperPagePo(Collection<TeStrategyDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<TeStrategyPo> page = new Page<TeStrategyPo>();
        for ( TeStrategyDto teStrategyDto : arg0 ) {
            page.add( mapperPo( teStrategyDto ) );
        }

        return page;
    }
}
