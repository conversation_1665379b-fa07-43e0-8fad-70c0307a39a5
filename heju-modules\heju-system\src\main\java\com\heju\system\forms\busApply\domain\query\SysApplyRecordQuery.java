package com.heju.system.forms.busApply.domain.query;

import com.heju.system.forms.busApply.domain.po.SysApplyRecordPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 行政申请 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysApplyRecordQuery extends SysApplyRecordPo {

    @Serial
    private static final long serialVersionUID = 1L;
}
