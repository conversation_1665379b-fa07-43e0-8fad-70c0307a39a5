package com.heju.system.api.dict.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.api.dict.domain.dto.SysConfigDto;
import com.heju.system.api.dict.domain.po.SysConfigPo;
import com.heju.system.api.dict.domain.query.SysConfigQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 参数配置 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysConfigConverter extends BaseConverter<SysConfigQuery, SysConfigDto, SysConfigPo> {
}
