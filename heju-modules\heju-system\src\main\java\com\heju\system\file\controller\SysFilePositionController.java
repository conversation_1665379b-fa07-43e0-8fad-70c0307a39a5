package com.heju.system.file.controller;

import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.TreeController;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.system.file.domain.dto.SysFilePositionDto;
import com.heju.system.file.domain.query.SysFilePositionQuery;
import com.heju.system.file.service.ISysFilePositionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 文件存储位置管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/position")
public class SysFilePositionController extends TreeController<SysFilePositionQuery, SysFilePositionDto, ISysFilePositionService> {

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "文件存储位置" ;
    }

    /**
     * 查询文件存储位置列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_FILE_POSITION_LIST)
    public AjaxResult list(SysFilePositionQuery filePosition) {
        return super.list(filePosition);
    }

    /**
     * 查询文件存储位置列表（排除节点）
     */
    @GetMapping("/list/exclude")
//    @RequiresPermissions(Auth.SYS_FILE_POSITION_LIST)
    public AjaxResult listExNodes(SysFilePositionQuery filePosition) {
        return super.listExNodes(filePosition);
    }

    /**
     * 查询文件存储位置详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_FILE_POSITION_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 文件存储位置新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_FILE_POSITION_ADD)
    @Log(title = "文件存储位置管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysFilePositionDto filePosition) {
        return super.add(filePosition);
    }

    /**
     * 文件存储位置修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_FILE_POSITION_EDIT)
    @Log(title = "文件存储位置管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysFilePositionDto filePosition) {
        return super.edit(filePosition);
    }

    /**
     * 文件存储位置修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_FILE_POSITION_EDIT, Auth.SYS_FILE_POSITION_ES}, logical = Logical.OR)
    @Log(title = "文件存储位置管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysFilePositionDto filePosition) {
        return super.editStatus(filePosition);
    }

    /**
     * 文件存储位置批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_FILE_POSITION_DEL)
    @Log(title = "文件存储位置管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取文件存储位置选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 前置校验 （强制）增加/修改
     */
    @Override
    protected void AEHandle(BaseConstants.Operate operate, SysFilePositionDto filePosition) {
        if (baseService.checkNameUnique(filePosition.getId(), filePosition.getName()))
            warn(StrUtil.format("{}{}{}失败，文件存储位置名称已存在", operate.getInfo(), getNodeName(), filePosition.getName()));
    }

}
