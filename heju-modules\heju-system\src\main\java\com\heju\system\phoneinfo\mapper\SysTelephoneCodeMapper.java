package com.heju.system.phoneinfo.mapper;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.phoneinfo.domain.dto.SysTelephoneCodeDto;
import com.heju.system.phoneinfo.domain.po.SysTelephoneCodePo;
import com.heju.system.phoneinfo.domain.query.SysTelephoneCodeQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 转发短信管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysTelephoneCodeMapper extends BaseMapper<SysTelephoneCodeQuery, SysTelephoneCodeDto, SysTelephoneCodePo> {
}
