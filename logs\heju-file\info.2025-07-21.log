09:04:14.976 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:04:16.069 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5cc0ce5e-03e6-4b77-9157-22fa33b7c09f_config-0
09:04:16.221 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 71 ms to scan 1 urls, producing 3 keys and 6 values 
09:04:16.296 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 33 ms to scan 1 urls, producing 4 keys and 9 values 
09:04:16.317 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 3 keys and 10 values 
09:04:16.348 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 1 keys and 5 values 
09:04:16.367 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 7 values 
09:04:16.388 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
09:04:16.395 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc0ce5e-03e6-4b77-9157-22fa33b7c09f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:04:16.395 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc0ce5e-03e6-4b77-9157-22fa33b7c09f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000014d4e3af470
09:04:16.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc0ce5e-03e6-4b77-9157-22fa33b7c09f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000014d4e3af690
09:04:16.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc0ce5e-03e6-4b77-9157-22fa33b7c09f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:04:16.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc0ce5e-03e6-4b77-9157-22fa33b7c09f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:04:16.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc0ce5e-03e6-4b77-9157-22fa33b7c09f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:04:18.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc0ce5e-03e6-4b77-9157-22fa33b7c09f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753059858167_127.0.0.1_1642
09:04:18.483 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc0ce5e-03e6-4b77-9157-22fa33b7c09f_config-0] Notify connected event to listeners.
09:04:18.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc0ce5e-03e6-4b77-9157-22fa33b7c09f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:04:18.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc0ce5e-03e6-4b77-9157-22fa33b7c09f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000014d4e4e8fb0
09:04:18.721 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:04:24.089 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:04:24.091 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:04:24.091 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:04:24.478 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:04:27.538 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:04:31.891 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c391622d-5b30-4dc7-a69d-2d8543831412
09:04:31.892 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c391622d-5b30-4dc7-a69d-2d8543831412] RpcClient init label, labels = {module=naming, source=sdk}
09:04:31.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c391622d-5b30-4dc7-a69d-2d8543831412] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:04:31.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c391622d-5b30-4dc7-a69d-2d8543831412] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:04:31.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c391622d-5b30-4dc7-a69d-2d8543831412] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:04:31.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c391622d-5b30-4dc7-a69d-2d8543831412] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:04:32.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c391622d-5b30-4dc7-a69d-2d8543831412] Success to connect to server [localhost:8848] on start up, connectionId = 1753059871909_127.0.0.1_1671
09:04:32.037 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c391622d-5b30-4dc7-a69d-2d8543831412] Notify connected event to listeners.
09:04:32.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c391622d-5b30-4dc7-a69d-2d8543831412] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:04:32.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c391622d-5b30-4dc7-a69d-2d8543831412] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000014d4e4e8fb0
09:04:32.099 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:04:32.144 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:04:32.415 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 18.231 seconds (JVM running for 20.317)
09:04:32.436 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:04:32.437 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:04:32.444 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:04:32.588 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c391622d-5b30-4dc7-a69d-2d8543831412] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:04:32.610 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c391622d-5b30-4dc7-a69d-2d8543831412] Ack server push request, request = NotifySubscriberRequest, requestId = 3
15:36:23.283 [http-nio-9300-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:36:26.110 [nacos-grpc-client-executor-4706] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c391622d-5b30-4dc7-a69d-2d8543831412] Receive server push request, request = NotifySubscriberRequest, requestId = 21
15:36:26.110 [nacos-grpc-client-executor-4706] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c391622d-5b30-4dc7-a69d-2d8543831412] Ack server push request, request = NotifySubscriberRequest, requestId = 21
17:57:14.131 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:57:14.140 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:57:14.482 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:57:14.482 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@582682d3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:57:14.482 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753059871909_127.0.0.1_1671
17:57:14.482 [nacos-grpc-client-executor-6395] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753059871909_127.0.0.1_1671]Ignore complete event,isRunning:false,isAbandon=false
17:57:14.490 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4f475388[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6396]
