package com.heju.system.file.controller;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.file.domain.dto.SysFileBorrowRecordDto;
import com.heju.system.file.domain.po.SysFileBorrowRecordPo;
import com.heju.system.file.domain.query.SysFileBorrowRecordQuery;
import com.heju.system.file.mapper.SysFileBorrowRecordMapper;
import com.heju.system.file.service.ISysFileBorrowRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.sql.Date;
import java.util.List;

/**
 * 文件借阅记录管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/borrow/record")
public class SysFileBorrowRecordController extends BaseController<SysFileBorrowRecordQuery, SysFileBorrowRecordDto, ISysFileBorrowRecordService> {


    @Autowired
    private SysFileBorrowRecordMapper sysFileBorrowRecordMapper;

    @Autowired ISysFileBorrowRecordService service;

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "文件借阅记录" ;
    }

    /**
     * 查询文件借阅记录列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_FILE_BORROW_RECORD_LIST)
    public AjaxResult list(SysFileBorrowRecordQuery fileBorrowRecord) {
        return super.list(fileBorrowRecord);
    }


    /**
     * 查询文件借阅记录列表
     */
    @GetMapping("/overTime/list")
    public AjaxResult overTime(SysFileBorrowRecordQuery fileBorrowRecord) {
        startPage();
        List<SysFileBorrowRecordDto> list = service.overTimeList(fileBorrowRecord);
        return getDataTable(list);
    }

    /**
     * 查询文件借阅记录详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_FILE_BORROW_RECORD_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 文件借阅记录新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_FILE_BORROW_RECORD_ADD)
    @Log(title = "文件借阅记录管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysFileBorrowRecordDto fileBorrowRecord) {
        return super.add(fileBorrowRecord);
    }


    /**
     * 文件借阅记录 - 批量操作(新增、修改)
     */
    @PostMapping("/batch/operation")
    public AjaxResult addBatch(@Validated({V_A.class}) @RequestBody List<SysFileBorrowRecordDto> fileBorrowRecords) {
        return toAjax(service.batchOperation(fileBorrowRecords));
    }


    /**
     * 文件借阅记录修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_FILE_BORROW_RECORD_EDIT)
    @Log(title = "文件借阅记录管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysFileBorrowRecordDto fileBorrowRecord) {
        return super.edit(fileBorrowRecord);
    }

    /**
     * 文件借阅记录修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_FILE_BORROW_RECORD_EDIT, Auth.SYS_FILE_BORROW_RECORD_ES}, logical = Logical.OR)
    @Log(title = "文件借阅记录管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysFileBorrowRecordDto fileBorrowRecord) {
        return super.editStatus(fileBorrowRecord);
    }

    /**
     * 文件借阅记录批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_FILE_BORROW_RECORD_DEL)
    @Log(title = "文件借阅记录管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取文件借阅记录选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 通过文件id生成文件借阅记录
     */
    @PostMapping(value="/file/id")
    @DSTransactional
    public AjaxResult adds(@RequestBody List<SysFileBorrowRecordPo> fileBorrowRecords) {
        return AjaxResult.success(sysFileBorrowRecordMapper.batchInsertBorrowRecords(fileBorrowRecords));
    }

    /**
     * 查阅自己一段时间内的借阅记录并对这段时间内的重复数据进行去重
     */
    @GetMapping(value="/file/time")
    @DSTransactional
    public AjaxResult fileTime(SysFileBorrowRecordQuery fileBorrowRecordQuery) {
        return AjaxResult.success(sysFileBorrowRecordMapper.borrowrecordtime(SecurityUtils.getUserId(),
                fileBorrowRecordQuery.getFileId(),
                (Date) fileBorrowRecordQuery.getStartTime(),
                (Date) fileBorrowRecordQuery.getEndTime()));
    }

}
