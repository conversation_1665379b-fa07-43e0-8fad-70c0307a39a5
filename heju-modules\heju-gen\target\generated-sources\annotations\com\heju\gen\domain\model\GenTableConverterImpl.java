package com.heju.gen.domain.model;

import com.github.pagehelper.Page;
import com.heju.gen.domain.dto.GenTableDto;
import com.heju.gen.domain.po.GenTablePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:45+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class GenTableConverterImpl implements GenTableConverter {

    @Override
    public GenTableDto mapperDto(GenTablePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        GenTableDto genTableDto = new GenTableDto();

        genTableDto.setId( arg0.getId() );
        genTableDto.setSourceName( arg0.getSourceName() );
        genTableDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            genTableDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        genTableDto.setName( arg0.getName() );
        genTableDto.setStatus( arg0.getStatus() );
        genTableDto.setSort( arg0.getSort() );
        genTableDto.setRemark( arg0.getRemark() );
        genTableDto.setCreateBy( arg0.getCreateBy() );
        genTableDto.setCreateTime( arg0.getCreateTime() );
        genTableDto.setUpdateBy( arg0.getUpdateBy() );
        genTableDto.setUpdateTime( arg0.getUpdateTime() );
        genTableDto.setDelFlag( arg0.getDelFlag() );
        genTableDto.setCreateName( arg0.getCreateName() );
        genTableDto.setUpdateName( arg0.getUpdateName() );
        genTableDto.setComment( arg0.getComment() );
        genTableDto.setClassName( arg0.getClassName() );
        genTableDto.setPrefix( arg0.getPrefix() );
        genTableDto.setTplCategory( arg0.getTplCategory() );
        genTableDto.setPackageName( arg0.getPackageName() );
        genTableDto.setModuleName( arg0.getModuleName() );
        genTableDto.setBusinessName( arg0.getBusinessName() );
        genTableDto.setAuthorityName( arg0.getAuthorityName() );
        genTableDto.setFunctionName( arg0.getFunctionName() );
        genTableDto.setFunctionAuthor( arg0.getFunctionAuthor() );
        genTableDto.setGenPath( arg0.getGenPath() );
        genTableDto.setUiPath( arg0.getUiPath() );
        genTableDto.setOptions( arg0.getOptions() );

        return genTableDto;
    }

    @Override
    public List<GenTableDto> mapperDto(Collection<GenTablePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<GenTableDto> list = new ArrayList<GenTableDto>( arg0.size() );
        for ( GenTablePo genTablePo : arg0 ) {
            list.add( mapperDto( genTablePo ) );
        }

        return list;
    }

    @Override
    public Page<GenTableDto> mapperPageDto(Collection<GenTablePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<GenTableDto> page = new Page<GenTableDto>();
        for ( GenTablePo genTablePo : arg0 ) {
            page.add( mapperDto( genTablePo ) );
        }

        return page;
    }

    @Override
    public GenTablePo mapperPo(GenTableDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        GenTablePo genTablePo = new GenTablePo();

        genTablePo.setId( arg0.getId() );
        genTablePo.setSourceName( arg0.getSourceName() );
        genTablePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            genTablePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        genTablePo.setName( arg0.getName() );
        genTablePo.setStatus( arg0.getStatus() );
        genTablePo.setSort( arg0.getSort() );
        genTablePo.setRemark( arg0.getRemark() );
        genTablePo.setCreateBy( arg0.getCreateBy() );
        genTablePo.setCreateTime( arg0.getCreateTime() );
        genTablePo.setUpdateBy( arg0.getUpdateBy() );
        genTablePo.setUpdateTime( arg0.getUpdateTime() );
        genTablePo.setDelFlag( arg0.getDelFlag() );
        genTablePo.setCreateName( arg0.getCreateName() );
        genTablePo.setUpdateName( arg0.getUpdateName() );
        genTablePo.setComment( arg0.getComment() );
        genTablePo.setClassName( arg0.getClassName() );
        genTablePo.setPrefix( arg0.getPrefix() );
        genTablePo.setTplCategory( arg0.getTplCategory() );
        genTablePo.setPackageName( arg0.getPackageName() );
        genTablePo.setModuleName( arg0.getModuleName() );
        genTablePo.setBusinessName( arg0.getBusinessName() );
        genTablePo.setAuthorityName( arg0.getAuthorityName() );
        genTablePo.setFunctionName( arg0.getFunctionName() );
        genTablePo.setFunctionAuthor( arg0.getFunctionAuthor() );
        genTablePo.setGenPath( arg0.getGenPath() );
        genTablePo.setUiPath( arg0.getUiPath() );
        genTablePo.setOptions( arg0.getOptions() );

        return genTablePo;
    }

    @Override
    public List<GenTablePo> mapperPo(Collection<GenTableDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<GenTablePo> list = new ArrayList<GenTablePo>( arg0.size() );
        for ( GenTableDto genTableDto : arg0 ) {
            list.add( mapperPo( genTableDto ) );
        }

        return list;
    }

    @Override
    public Page<GenTablePo> mapperPagePo(Collection<GenTableDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<GenTablePo> page = new Page<GenTablePo>();
        for ( GenTableDto genTableDto : arg0 ) {
            page.add( mapperPo( genTableDto ) );
        }

        return page;
    }
}
