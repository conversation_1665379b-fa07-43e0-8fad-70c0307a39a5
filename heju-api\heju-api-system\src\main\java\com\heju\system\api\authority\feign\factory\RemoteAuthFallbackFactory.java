package com.heju.system.api.authority.feign.factory;

import com.heju.common.core.web.result.R;
import com.heju.system.api.authority.domain.dto.SysTenantMenuDto;
import com.heju.system.api.authority.feign.RemoteAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 权限服务 降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteAuthFallbackFactory implements FallbackFactory<RemoteAuthService> {

    @Override
    public RemoteAuthService create(Throwable throwable) {
        log.error("权限服务调用失败:{}", throwable.getMessage());
        return new RemoteAuthService() {
            @Override
            public R<Long[]> getTenantAuthInner(Long enterpriseId, String sourceName, String source) {
                return R.fail("获取租户权限信息失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> addTenantAuthInner(String authIdsString, Long enterpriseId, String sourceName, String source) {
                return R.fail("新增租户权限信息失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> editTenantAuthInner(Long[] authIds, Long enterpriseId, String sourceName, String source) {
                return R.fail("修改权限信息失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> tenantDeleteMenus(List<Long> menuIdList, String sourceName, String source) {
                return R.fail("删除租户菜单权限失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> tenantUpdateMenus(SysTenantMenuDto sysTenantMenuDto, String sourceName, String source) {
                return R.fail("修改租户菜单权限失败:" + throwable.getMessage());
            }

            @Override
            public List<Long> tenantHasMenuIds(Long tenantId, String sourceName, String source) {
                return new ArrayList<>();
            }

        };
    }
}