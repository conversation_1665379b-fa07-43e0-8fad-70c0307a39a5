package com.heju.system.api.authority.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.api.authority.domain.dto.SysRoleGroupDto;
import com.heju.system.api.authority.domain.po.SysRoleGroupPo;
import com.heju.system.api.authority.domain.query.SysRoleGroupQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 角色组 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysRoleGroupConverter extends BaseConverter<SysRoleGroupQuery, SysRoleGroupDto, SysRoleGroupPo> {
}
