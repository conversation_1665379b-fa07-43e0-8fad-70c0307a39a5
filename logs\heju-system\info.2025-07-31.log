09:43:54.130 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:43:56.785 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0
09:43:57.030 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 105 ms to scan 1 urls, producing 3 keys and 6 values 
09:43:57.163 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 27 ms to scan 1 urls, producing 4 keys and 9 values 
09:43:57.185 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 3 keys and 10 values 
09:43:57.210 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 5 values 
09:43:57.237 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 1 keys and 7 values 
09:43:57.262 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
09:43:57.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:43:57.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000026f923bc730
09:43:57.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000026f923bc950
09:43:57.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:43:57.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:43:57.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:00.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = ********39539_127.0.0.1_6004
09:44:00.033 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:00.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000026f924f4200
09:44:00.124 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] Notify connected event to listeners.
09:44:00.784 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:44:21.591 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:44:21.594 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:44:21.594 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:44:22.133 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:44:24.156 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:44:24.160 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:44:24.160 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:44:38.961 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:44:42.241 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5fe2eb29-a3ce-411b-9e4f-c50dba83fef2
09:44:42.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe2eb29-a3ce-411b-9e4f-c50dba83fef2] RpcClient init label, labels = {module=naming, source=sdk}
09:44:42.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe2eb29-a3ce-411b-9e4f-c50dba83fef2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:44:42.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe2eb29-a3ce-411b-9e4f-c50dba83fef2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:44:42.254 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe2eb29-a3ce-411b-9e4f-c50dba83fef2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:44:42.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe2eb29-a3ce-411b-9e4f-c50dba83fef2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:42.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe2eb29-a3ce-411b-9e4f-c50dba83fef2] Success to connect to server [localhost:8848] on start up, connectionId = ********82261_127.0.0.1_6540
09:44:42.379 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe2eb29-a3ce-411b-9e4f-c50dba83fef2] Notify connected event to listeners.
09:44:42.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe2eb29-a3ce-411b-9e4f-c50dba83fef2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:42.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe2eb29-a3ce-411b-9e4f-c50dba83fef2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000026f924f4200
09:44:42.459 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:44:42.499 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:44:42.640 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 49.897 seconds (JVM running for 57.572)
09:44:42.662 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:44:42.662 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:44:42.662 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:44:42.950 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe2eb29-a3ce-411b-9e4f-c50dba83fef2] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:44:42.966 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe2eb29-a3ce-411b-9e4f-c50dba83fef2] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:44:59.107 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:45:05.530 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:45:05.536 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:20:16.580 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:23:25.170 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] Success to connect a server [localhost:8848], connectionId = 1753928605047_127.0.0.1_10994
10:23:25.172 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] Abandon prev connection, server is localhost:8848, connectionId is ********39539_127.0.0.1_6004
10:23:25.172 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection ********39539_127.0.0.1_6004
10:23:25.172 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] Notify disconnected event to listeners
10:23:25.173 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14cf9cc6-7f46-4211-bad9-5350d388c40f_config-0] Notify connected event to listeners.
11:36:05.134 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:36:05.136 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:36:05.461 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:36:05.461 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7761cb50[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:36:05.461 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection ********82261_127.0.0.1_6540
11:36:05.463 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@f6b69e9[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 1325]
11:36:05.645 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:36:05.657 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:36:05.667 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:36:05.668 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:36:05.669 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:36:05.669 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:36:27.149 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:36:28.146 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 27ff8694-7f3e-4501-a13b-6c430b2aa40f_config-0
11:36:28.237 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
11:36:28.281 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
11:36:28.292 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
11:36:28.304 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
11:36:28.316 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
11:36:28.326 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
11:36:28.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27ff8694-7f3e-4501-a13b-6c430b2aa40f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:36:28.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27ff8694-7f3e-4501-a13b-6c430b2aa40f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001cb243b6d38
11:36:28.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27ff8694-7f3e-4501-a13b-6c430b2aa40f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001cb243b6f58
11:36:28.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27ff8694-7f3e-4501-a13b-6c430b2aa40f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:36:28.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27ff8694-7f3e-4501-a13b-6c430b2aa40f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:36:28.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27ff8694-7f3e-4501-a13b-6c430b2aa40f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:36:29.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27ff8694-7f3e-4501-a13b-6c430b2aa40f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753932989135_127.0.0.1_4756
11:36:29.369 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27ff8694-7f3e-4501-a13b-6c430b2aa40f_config-0] Notify connected event to listeners.
11:36:29.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27ff8694-7f3e-4501-a13b-6c430b2aa40f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:36:29.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27ff8694-7f3e-4501-a13b-6c430b2aa40f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001cb244f0fb0
11:36:29.479 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:36:32.710 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:36:32.710 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:36:32.710 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:36:32.849 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:36:33.447 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:36:33.448 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:36:33.448 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:36:39.665 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:36:42.328 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e1b8bb0e-c689-4562-b4f8-2f14aa023330
11:36:42.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1b8bb0e-c689-4562-b4f8-2f14aa023330] RpcClient init label, labels = {module=naming, source=sdk}
11:36:42.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1b8bb0e-c689-4562-b4f8-2f14aa023330] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:36:42.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1b8bb0e-c689-4562-b4f8-2f14aa023330] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:36:42.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1b8bb0e-c689-4562-b4f8-2f14aa023330] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:36:42.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1b8bb0e-c689-4562-b4f8-2f14aa023330] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:36:42.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1b8bb0e-c689-4562-b4f8-2f14aa023330] Success to connect to server [localhost:8848] on start up, connectionId = 1753933002340_127.0.0.1_4792
11:36:42.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1b8bb0e-c689-4562-b4f8-2f14aa023330] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:36:42.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1b8bb0e-c689-4562-b4f8-2f14aa023330] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001cb244f0fb0
11:36:42.465 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1b8bb0e-c689-4562-b4f8-2f14aa023330] Notify connected event to listeners.
11:36:42.508 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:36:42.533 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:36:42.634 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.184 seconds (JVM running for 18.076)
11:36:42.649 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:36:42.649 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:36:42.650 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:36:43.056 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1b8bb0e-c689-4562-b4f8-2f14aa023330] Receive server push request, request = NotifySubscriberRequest, requestId = 21
11:36:43.070 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1b8bb0e-c689-4562-b4f8-2f14aa023330] Ack server push request, request = NotifySubscriberRequest, requestId = 21
11:36:52.047 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:36:53.175 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
11:36:53.175 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:36:53.178 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
11:36:53.179 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:36:53.180 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
11:36:53.188 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
11:36:53.188 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:36:53.188 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
11:36:53.189 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
11:36:53.189 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:14:03.606 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:14:03.612 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:14:03.951 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:14:03.951 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@355d738b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:14:03.951 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753933002340_127.0.0.1_4792
14:14:03.953 [nacos-grpc-client-executor-1867] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753933002340_127.0.0.1_4792]Ignore complete event,isRunning:false,isAbandon=false
14:14:03.956 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@26ad6bc5[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1868]
14:14:04.115 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:14:04.116 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:14:04.117 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:14:04.117 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:14:04.118 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:14:04.122 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:14:10.186 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:14:10.880 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1e059704-bf31-457d-bf52-866932d3eb57_config-0
14:14:10.970 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
14:14:11.011 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:14:11.022 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
14:14:11.035 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
14:14:11.046 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:14:11.066 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
14:14:11.070 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e059704-bf31-457d-bf52-866932d3eb57_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:14:11.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e059704-bf31-457d-bf52-866932d3eb57_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000235813be480
14:14:11.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e059704-bf31-457d-bf52-866932d3eb57_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000235813be6a0
14:14:11.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e059704-bf31-457d-bf52-866932d3eb57_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:14:11.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e059704-bf31-457d-bf52-866932d3eb57_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:14:11.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e059704-bf31-457d-bf52-866932d3eb57_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:14:11.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e059704-bf31-457d-bf52-866932d3eb57_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753942451718_127.0.0.1_11986
14:14:11.912 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e059704-bf31-457d-bf52-866932d3eb57_config-0] Notify connected event to listeners.
14:14:11.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e059704-bf31-457d-bf52-866932d3eb57_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:14:11.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e059704-bf31-457d-bf52-866932d3eb57_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000235814f8668
14:14:11.996 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:14:14.704 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:14:14.704 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:14:14.704 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:14:14.834 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:14:15.408 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:14:15.409 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:14:15.410 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:14:21.512 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:14:24.298 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1b89e29d-d0b6-4d62-af5d-ff5724f2d372
14:14:24.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b89e29d-d0b6-4d62-af5d-ff5724f2d372] RpcClient init label, labels = {module=naming, source=sdk}
14:14:24.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b89e29d-d0b6-4d62-af5d-ff5724f2d372] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:14:24.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b89e29d-d0b6-4d62-af5d-ff5724f2d372] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:14:24.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b89e29d-d0b6-4d62-af5d-ff5724f2d372] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:14:24.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b89e29d-d0b6-4d62-af5d-ff5724f2d372] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:14:24.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b89e29d-d0b6-4d62-af5d-ff5724f2d372] Success to connect to server [localhost:8848] on start up, connectionId = 1753942464311_127.0.0.1_12039
14:14:24.430 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b89e29d-d0b6-4d62-af5d-ff5724f2d372] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:14:24.430 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b89e29d-d0b6-4d62-af5d-ff5724f2d372] Notify connected event to listeners.
14:14:24.430 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b89e29d-d0b6-4d62-af5d-ff5724f2d372] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000235814f8668
14:14:24.480 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:14:24.507 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:14:24.629 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 14.931 seconds (JVM running for 15.773)
14:14:24.644 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:14:24.644 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:14:24.645 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:14:24.993 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b89e29d-d0b6-4d62-af5d-ff5724f2d372] Receive server push request, request = NotifySubscriberRequest, requestId = 30
14:14:25.003 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:14:25.013 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b89e29d-d0b6-4d62-af5d-ff5724f2d372] Ack server push request, request = NotifySubscriberRequest, requestId = 30
14:15:04.048 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
14:15:04.048 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
14:15:04.048 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:15:04.049 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:15:04.052 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
14:15:04.059 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
14:15:04.059 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:15:04.060 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
14:15:04.060 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
14:15:04.061 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:36:09.869 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:36:09.871 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:36:10.197 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:36:10.197 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@214bdd86[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:36:10.198 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753942464311_127.0.0.1_12039
14:36:10.199 [nacos-grpc-client-executor-274] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753942464311_127.0.0.1_12039]Ignore complete event,isRunning:false,isAbandon=false
14:36:10.203 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@37ae4850[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 275]
14:36:10.356 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:36:10.357 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:36:10.359 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:36:10.360 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:36:10.361 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:36:10.361 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:36:14.299 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:36:14.847 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cb1ecd17-b07b-4d09-bba8-6731ee0e9393_config-0
14:36:14.896 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
14:36:14.923 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
14:36:14.929 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
14:36:14.935 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
14:36:14.943 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
14:36:14.951 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
14:36:14.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1ecd17-b07b-4d09-bba8-6731ee0e9393_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:36:14.954 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1ecd17-b07b-4d09-bba8-6731ee0e9393_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000022e503c0b08
14:36:14.954 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1ecd17-b07b-4d09-bba8-6731ee0e9393_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000022e503c0d28
14:36:14.954 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1ecd17-b07b-4d09-bba8-6731ee0e9393_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:36:14.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1ecd17-b07b-4d09-bba8-6731ee0e9393_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:36:14.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1ecd17-b07b-4d09-bba8-6731ee0e9393_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:36:15.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1ecd17-b07b-4d09-bba8-6731ee0e9393_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753943775474_127.0.0.1_2714
14:36:15.652 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1ecd17-b07b-4d09-bba8-6731ee0e9393_config-0] Notify connected event to listeners.
14:36:15.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1ecd17-b07b-4d09-bba8-6731ee0e9393_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:36:15.654 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1ecd17-b07b-4d09-bba8-6731ee0e9393_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022e504fa958
14:36:15.730 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:36:18.149 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:36:18.149 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:36:18.149 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:36:18.265 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:36:18.776 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:36:18.777 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:36:18.778 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:36:23.943 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:36:26.139 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0106f506-f6e2-47a6-8a60-aa8bd4510957
14:36:26.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0106f506-f6e2-47a6-8a60-aa8bd4510957] RpcClient init label, labels = {module=naming, source=sdk}
14:36:26.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0106f506-f6e2-47a6-8a60-aa8bd4510957] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:36:26.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0106f506-f6e2-47a6-8a60-aa8bd4510957] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:36:26.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0106f506-f6e2-47a6-8a60-aa8bd4510957] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:36:26.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0106f506-f6e2-47a6-8a60-aa8bd4510957] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:36:26.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0106f506-f6e2-47a6-8a60-aa8bd4510957] Success to connect to server [localhost:8848] on start up, connectionId = 1753943786150_127.0.0.1_2728
14:36:26.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0106f506-f6e2-47a6-8a60-aa8bd4510957] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:36:26.261 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0106f506-f6e2-47a6-8a60-aa8bd4510957] Notify connected event to listeners.
14:36:26.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0106f506-f6e2-47a6-8a60-aa8bd4510957] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022e504fa958
14:36:26.313 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:36:26.342 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:36:26.432 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.61 seconds (JVM running for 13.486)
14:36:26.445 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:36:26.446 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:36:26.446 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:36:26.840 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0106f506-f6e2-47a6-8a60-aa8bd4510957] Receive server push request, request = NotifySubscriberRequest, requestId = 35
14:36:26.857 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0106f506-f6e2-47a6-8a60-aa8bd4510957] Ack server push request, request = NotifySubscriberRequest, requestId = 35
14:36:26.997 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:36:36.014 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
14:36:36.015 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:36:36.025 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:36:36.026 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
14:36:36.027 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
14:36:36.033 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
14:36:36.033 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:36:36.033 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:36:36.034 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:36:36.034 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:59:26.626 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:59:26.628 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:59:26.963 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:59:26.963 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7adcb096[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:59:26.964 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753943786150_127.0.0.1_2728
14:59:26.966 [nacos-grpc-client-executor-289] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753943786150_127.0.0.1_2728]Ignore complete event,isRunning:false,isAbandon=false
14:59:26.972 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5e66e08f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 290]
14:59:27.151 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:59:27.151 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
14:59:27.152 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
14:59:27.153 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:59:27.153 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:59:27.153 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:07:30.775 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:07:31.932 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d2d385f4-244a-4431-b3b7-bce5d5a0f4f1_config-0
15:07:32.029 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
15:07:32.092 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
15:07:32.104 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
15:07:32.117 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
15:07:32.129 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
15:07:32.142 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
15:07:32.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d385f4-244a-4431-b3b7-bce5d5a0f4f1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:07:32.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d385f4-244a-4431-b3b7-bce5d5a0f4f1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001ebac3b6af8
15:07:32.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d385f4-244a-4431-b3b7-bce5d5a0f4f1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001ebac3b6d18
15:07:32.149 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d385f4-244a-4431-b3b7-bce5d5a0f4f1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:07:32.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d385f4-244a-4431-b3b7-bce5d5a0f4f1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:07:32.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d385f4-244a-4431-b3b7-bce5d5a0f4f1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:07:34.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d385f4-244a-4431-b3b7-bce5d5a0f4f1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753945653723_127.0.0.1_9334
15:07:34.111 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d385f4-244a-4431-b3b7-bce5d5a0f4f1_config-0] Notify connected event to listeners.
15:07:34.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d385f4-244a-4431-b3b7-bce5d5a0f4f1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:34.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d385f4-244a-4431-b3b7-bce5d5a0f4f1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ebac4f0ad8
15:07:34.337 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:07:39.635 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:07:39.635 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:07:39.635 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:07:39.796 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:07:40.487 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:07:40.488 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:07:40.489 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:07:46.018 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:07:48.487 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 642f17f6-289d-4a77-9c66-4c88e91e1b4b
15:07:48.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [642f17f6-289d-4a77-9c66-4c88e91e1b4b] RpcClient init label, labels = {module=naming, source=sdk}
15:07:48.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [642f17f6-289d-4a77-9c66-4c88e91e1b4b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:07:48.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [642f17f6-289d-4a77-9c66-4c88e91e1b4b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:07:48.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [642f17f6-289d-4a77-9c66-4c88e91e1b4b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:07:48.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [642f17f6-289d-4a77-9c66-4c88e91e1b4b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:07:48.613 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [642f17f6-289d-4a77-9c66-4c88e91e1b4b] Success to connect to server [localhost:8848] on start up, connectionId = 1753945668500_127.0.0.1_9398
15:07:48.613 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [642f17f6-289d-4a77-9c66-4c88e91e1b4b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:48.613 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [642f17f6-289d-4a77-9c66-4c88e91e1b4b] Notify connected event to listeners.
15:07:48.613 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [642f17f6-289d-4a77-9c66-4c88e91e1b4b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ebac4f0ad8
15:07:48.655 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:07:48.680 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:07:48.782 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.132 seconds (JVM running for 21.073)
15:07:48.810 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:07:48.810 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:07:48.811 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:07:49.171 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [642f17f6-289d-4a77-9c66-4c88e91e1b4b] Receive server push request, request = NotifySubscriberRequest, requestId = 46
15:07:49.183 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [642f17f6-289d-4a77-9c66-4c88e91e1b4b] Ack server push request, request = NotifySubscriberRequest, requestId = 46
15:07:55.109 [http-nio-9600-exec-3] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:07:56.219 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
15:07:56.219 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
15:07:56.219 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:07:56.221 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:07:56.222 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
15:07:56.227 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
15:07:56.227 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:07:56.228 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:07:56.229 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:07:56.229 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:32:34.315 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:32:34.318 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:32:34.649 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:32:34.649 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@271b7c3c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:32:34.650 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753945668500_127.0.0.1_9398
16:32:34.652 [nacos-grpc-client-executor-1029] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753945668500_127.0.0.1_9398]Ignore complete event,isRunning:false,isAbandon=false
16:32:34.654 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 1030]
16:32:34.804 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:32:34.804 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:32:34.807 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:32:34.807 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:32:34.809 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:32:34.809 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:32:40.390 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:32:41.119 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3f5ed399-d9f7-4886-a99f-ee94ce557711_config-0
16:32:41.184 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
16:32:41.211 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
16:32:41.217 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
16:32:41.224 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
16:32:41.231 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
16:32:41.239 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
16:32:41.242 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f5ed399-d9f7-4886-a99f-ee94ce557711_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:32:41.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f5ed399-d9f7-4886-a99f-ee94ce557711_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000211e339f410
16:32:41.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f5ed399-d9f7-4886-a99f-ee94ce557711_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000211e339f630
16:32:41.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f5ed399-d9f7-4886-a99f-ee94ce557711_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:32:41.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f5ed399-d9f7-4886-a99f-ee94ce557711_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:32:41.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f5ed399-d9f7-4886-a99f-ee94ce557711_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:32:41.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f5ed399-d9f7-4886-a99f-ee94ce557711_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753950761758_127.0.0.1_2820
16:32:41.959 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f5ed399-d9f7-4886-a99f-ee94ce557711_config-0] Notify connected event to listeners.
16:32:41.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f5ed399-d9f7-4886-a99f-ee94ce557711_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:32:41.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3f5ed399-d9f7-4886-a99f-ee94ce557711_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000211e3518fb0
16:32:42.040 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:32:45.280 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:32:45.280 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:32:45.280 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:32:45.432 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:32:46.093 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:32:46.094 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:32:46.095 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:32:55.667 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:32:59.488 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 91b305cd-9de7-458c-98f0-e06bfa700fdc
16:32:59.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] RpcClient init label, labels = {module=naming, source=sdk}
16:32:59.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:32:59.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:32:59.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:32:59.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:32:59.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Success to connect to server [localhost:8848] on start up, connectionId = 1753950779501_127.0.0.1_2900
16:32:59.621 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Notify connected event to listeners.
16:32:59.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:32:59.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000211e3518fb0
16:32:59.672 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:32:59.702 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:32:59.838 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.975 seconds (JVM running for 20.945)
16:32:59.855 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:32:59.855 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:32:59.856 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:33:00.021 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:33:00.251 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Receive server push request, request = NotifySubscriberRequest, requestId = 51
16:33:00.268 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Ack server push request, request = NotifySubscriberRequest, requestId = 51
16:33:54.690 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:33:54.690 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:33:54.691 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:33:54.691 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:33:54.693 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:33:54.699 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:33:54.699 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:33:54.700 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:33:54.700 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:33:54.701 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:38:33.224 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Server healthy check fail, currentConnection = 1753950779501_127.0.0.1_2900
16:38:33.225 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:38:42.845 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Success to connect a server [localhost:8848], connectionId = 1753951114743_127.0.0.1_3799
16:38:42.847 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Abandon prev connection, server is localhost:8848, connectionId is 1753950779501_127.0.0.1_2900
16:38:42.848 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753950779501_127.0.0.1_2900
16:38:42.854 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753950779501_127.0.0.1_2900]Ignore complete event,isRunning:false,isAbandon=true
16:38:45.051 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Notify disconnected event to listeners
16:38:45.056 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Notify connected event to listeners.
16:38:48.286 [nacos-grpc-client-executor-78] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Receive server push request, request = NotifySubscriberRequest, requestId = 54
16:38:48.287 [nacos-grpc-client-executor-78] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91b305cd-9de7-458c-98f0-e06bfa700fdc] Ack server push request, request = NotifySubscriberRequest, requestId = 54
16:50:27.435 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:50:27.439 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:50:27.747 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:50:27.747 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@54add9c0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:50:27.747 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753951114743_127.0.0.1_3799
16:50:27.748 [nacos-grpc-client-executor-125] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753951114743_127.0.0.1_3799]Ignore complete event,isRunning:false,isAbandon=false
16:50:27.750 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1d3d37b1[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 125]
16:50:27.893 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:50:27.894 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
16:50:27.895 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
16:50:27.895 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:50:27.896 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:50:27.896 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:50:32.901 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:50:33.651 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fe4d6541-9be5-4fb3-bae1-6af56a3636ee_config-0
16:50:33.716 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
16:50:33.754 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
16:50:33.764 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
16:50:33.774 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
16:50:33.783 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
16:50:33.796 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
16:50:33.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe4d6541-9be5-4fb3-bae1-6af56a3636ee_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:50:33.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe4d6541-9be5-4fb3-bae1-6af56a3636ee_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001fa813bdd70
16:50:33.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe4d6541-9be5-4fb3-bae1-6af56a3636ee_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001fa813bdf90
16:50:33.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe4d6541-9be5-4fb3-bae1-6af56a3636ee_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:50:33.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe4d6541-9be5-4fb3-bae1-6af56a3636ee_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:50:33.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe4d6541-9be5-4fb3-bae1-6af56a3636ee_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:50:34.807 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe4d6541-9be5-4fb3-bae1-6af56a3636ee_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753951834575_127.0.0.1_6164
16:50:34.807 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe4d6541-9be5-4fb3-bae1-6af56a3636ee_config-0] Notify connected event to listeners.
16:50:34.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe4d6541-9be5-4fb3-bae1-6af56a3636ee_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:50:34.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe4d6541-9be5-4fb3-bae1-6af56a3636ee_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fa814f7b88
16:50:34.917 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:50:41.206 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:50:41.207 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:50:41.208 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:50:41.534 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:50:42.758 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:50:42.760 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:50:42.760 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:50:52.208 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:50:55.373 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1a7e3786-1cae-4e34-9964-639d343a6bd3
16:50:55.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a7e3786-1cae-4e34-9964-639d343a6bd3] RpcClient init label, labels = {module=naming, source=sdk}
16:50:55.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a7e3786-1cae-4e34-9964-639d343a6bd3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:50:55.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a7e3786-1cae-4e34-9964-639d343a6bd3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:50:55.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a7e3786-1cae-4e34-9964-639d343a6bd3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:50:55.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a7e3786-1cae-4e34-9964-639d343a6bd3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:50:55.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a7e3786-1cae-4e34-9964-639d343a6bd3] Success to connect to server [localhost:8848] on start up, connectionId = 1753951855386_127.0.0.1_6235
16:50:55.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a7e3786-1cae-4e34-9964-639d343a6bd3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:50:55.511 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a7e3786-1cae-4e34-9964-639d343a6bd3] Notify connected event to listeners.
16:50:55.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a7e3786-1cae-4e34-9964-639d343a6bd3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fa814f7b88
16:50:55.562 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:50:55.590 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:50:55.712 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.324 seconds (JVM running for 24.207)
16:50:55.726 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:50:55.726 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:50:55.726 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:50:55.929 [RMI TCP Connection(10)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:50:56.139 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a7e3786-1cae-4e34-9964-639d343a6bd3] Receive server push request, request = NotifySubscriberRequest, requestId = 57
16:50:56.155 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a7e3786-1cae-4e34-9964-639d343a6bd3] Ack server push request, request = NotifySubscriberRequest, requestId = 57
16:51:23.858 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:51:23.859 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:51:23.870 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:51:23.872 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:51:23.878 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:51:23.878 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:51:23.883 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:51:23.883 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
16:51:23.884 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
16:51:23.884 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:54:18.563 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:54:18.566 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:54:18.889 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:54:18.890 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@22325ad7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:54:18.890 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753951855386_127.0.0.1_6235
16:54:18.891 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753951855386_127.0.0.1_6235]Ignore complete event,isRunning:false,isAbandon=false
16:54:18.893 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2a379baf[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 48]
16:54:19.036 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:54:19.036 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:54:19.037 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:54:19.037 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:54:19.038 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:54:19.038 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:54:23.737 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:54:24.272 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 48bef2fe-2fff-4978-93c9-78ea3ea89e13_config-0
16:54:24.325 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
16:54:24.352 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
16:54:24.359 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
16:54:24.366 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
16:54:24.373 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
16:54:24.379 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
16:54:24.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48bef2fe-2fff-4978-93c9-78ea3ea89e13_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:54:24.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48bef2fe-2fff-4978-93c9-78ea3ea89e13_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000020dc93bf1c0
16:54:24.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48bef2fe-2fff-4978-93c9-78ea3ea89e13_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000020dc93bf3e0
16:54:24.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48bef2fe-2fff-4978-93c9-78ea3ea89e13_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:54:24.382 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48bef2fe-2fff-4978-93c9-78ea3ea89e13_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:54:24.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48bef2fe-2fff-4978-93c9-78ea3ea89e13_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:54:25.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48bef2fe-2fff-4978-93c9-78ea3ea89e13_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753952064863_127.0.0.1_6887
16:54:25.050 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48bef2fe-2fff-4978-93c9-78ea3ea89e13_config-0] Notify connected event to listeners.
16:54:25.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48bef2fe-2fff-4978-93c9-78ea3ea89e13_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:54:25.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48bef2fe-2fff-4978-93c9-78ea3ea89e13_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020dc94f8ad8
16:54:25.128 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:54:27.556 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:54:27.557 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:54:27.557 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:54:27.672 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:54:28.812 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:54:28.813 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:54:28.813 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:54:33.723 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:54:36.644 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9bcc2eb5-c443-4130-826c-6dd14e465aa4
16:54:36.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bcc2eb5-c443-4130-826c-6dd14e465aa4] RpcClient init label, labels = {module=naming, source=sdk}
16:54:36.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bcc2eb5-c443-4130-826c-6dd14e465aa4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:54:36.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bcc2eb5-c443-4130-826c-6dd14e465aa4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:54:36.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bcc2eb5-c443-4130-826c-6dd14e465aa4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:54:36.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bcc2eb5-c443-4130-826c-6dd14e465aa4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:54:36.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bcc2eb5-c443-4130-826c-6dd14e465aa4] Success to connect to server [localhost:8848] on start up, connectionId = 1753952076653_127.0.0.1_6945
16:54:36.763 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bcc2eb5-c443-4130-826c-6dd14e465aa4] Notify connected event to listeners.
16:54:36.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bcc2eb5-c443-4130-826c-6dd14e465aa4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:54:36.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bcc2eb5-c443-4130-826c-6dd14e465aa4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020dc94f8ad8
16:54:36.802 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:54:36.823 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:54:36.913 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.644 seconds (JVM running for 14.467)
16:54:36.925 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:54:36.925 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:54:36.926 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:54:37.288 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bcc2eb5-c443-4130-826c-6dd14e465aa4] Receive server push request, request = NotifySubscriberRequest, requestId = 60
16:54:37.303 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bcc2eb5-c443-4130-826c-6dd14e465aa4] Ack server push request, request = NotifySubscriberRequest, requestId = 60
16:54:37.474 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:54:38.543 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:54:38.543 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:54:38.544 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:54:38.545 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:54:38.547 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:54:38.553 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:54:38.553 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:54:38.554 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
16:54:38.555 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
16:54:38.555 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:59:56.570 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:59:56.573 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:59:56.895 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:59:56.895 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@603e5cf6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:59:56.896 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753952076653_127.0.0.1_6945
16:59:56.896 [nacos-grpc-client-executor-74] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753952076653_127.0.0.1_6945]Ignore complete event,isRunning:false,isAbandon=false
16:59:56.898 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1002a5e2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 75]
16:59:57.042 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:59:57.042 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:59:57.043 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:59:57.043 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:59:57.044 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:59:57.044 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:00:01.760 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:00:02.359 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b8646330-bd75-4583-9f71-9e895121e60a_config-0
17:00:02.408 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 6 values 
17:00:02.434 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
17:00:02.440 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
17:00:02.446 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
17:00:02.452 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
17:00:02.460 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
17:00:02.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8646330-bd75-4583-9f71-9e895121e60a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:00:02.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8646330-bd75-4583-9f71-9e895121e60a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000260593cf1c0
17:00:02.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8646330-bd75-4583-9f71-9e895121e60a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000260593cf3e0
17:00:02.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8646330-bd75-4583-9f71-9e895121e60a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:00:02.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8646330-bd75-4583-9f71-9e895121e60a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:00:02.469 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8646330-bd75-4583-9f71-9e895121e60a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:00:03.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8646330-bd75-4583-9f71-9e895121e60a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753952402969_127.0.0.1_8003
17:00:03.153 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8646330-bd75-4583-9f71-9e895121e60a_config-0] Notify connected event to listeners.
17:00:03.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8646330-bd75-4583-9f71-9e895121e60a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:00:03.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8646330-bd75-4583-9f71-9e895121e60a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026059508fb0
17:00:03.247 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:00:05.871 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:00:05.872 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:00:05.872 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:00:05.999 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:00:06.561 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:00:06.562 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:00:06.562 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:00:11.724 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:00:13.798 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f8724bbb-88e3-4235-8526-fb3491943e7b
17:00:13.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8724bbb-88e3-4235-8526-fb3491943e7b] RpcClient init label, labels = {module=naming, source=sdk}
17:00:13.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8724bbb-88e3-4235-8526-fb3491943e7b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:00:13.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8724bbb-88e3-4235-8526-fb3491943e7b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:00:13.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8724bbb-88e3-4235-8526-fb3491943e7b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:00:13.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8724bbb-88e3-4235-8526-fb3491943e7b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:00:13.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8724bbb-88e3-4235-8526-fb3491943e7b] Success to connect to server [localhost:8848] on start up, connectionId = 1753952413807_127.0.0.1_8056
17:00:13.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8724bbb-88e3-4235-8526-fb3491943e7b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:00:13.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8724bbb-88e3-4235-8526-fb3491943e7b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026059508fb0
17:00:13.928 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8724bbb-88e3-4235-8526-fb3491943e7b] Notify connected event to listeners.
17:00:13.965 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:00:13.986 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:00:14.074 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.831 seconds (JVM running for 13.687)
17:00:14.085 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:00:14.085 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:00:14.085 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:00:14.336 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:00:14.471 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8724bbb-88e3-4235-8526-fb3491943e7b] Receive server push request, request = NotifySubscriberRequest, requestId = 64
17:00:14.483 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8724bbb-88e3-4235-8526-fb3491943e7b] Ack server push request, request = NotifySubscriberRequest, requestId = 64
17:00:22.596 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
17:00:22.596 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:00:22.597 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:00:22.599 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
17:00:22.605 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
17:00:22.605 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:00:22.607 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
17:00:22.607 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:00:22.608 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:00:22.608 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:19:26.468 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:19:26.471 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:19:26.799 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:19:26.799 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@60a7ca30[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:19:26.799 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753952413807_127.0.0.1_8056
17:19:26.800 [nacos-grpc-client-executor-246] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753952413807_127.0.0.1_8056]Ignore complete event,isRunning:false,isAbandon=false
17:19:26.802 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6216a507[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 247]
17:19:26.944 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:19:26.944 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
17:19:26.945 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
17:19:26.946 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:19:26.946 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:19:26.946 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:20:30.951 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:20:31.984 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8442e79a-86e9-46e2-a746-e96ca1172658_config-0
17:20:32.077 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
17:20:32.128 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
17:20:32.140 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
17:20:32.152 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
17:20:32.162 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
17:20:32.177 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
17:20:32.180 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:20:32.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000181a639e230
17:20:32.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000181a639e450
17:20:32.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:20:32.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:20:32.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:20:33.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753953633007_127.0.0.1_11966
17:20:33.221 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Notify connected event to listeners.
17:20:33.222 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:20:33.222 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000181a6518228
17:20:33.323 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:20:36.278 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:20:36.279 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:20:36.279 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:20:36.411 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:20:37.013 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:20:37.014 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:20:37.014 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:20:42.693 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:20:45.225 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of edc64e5c-0787-4c3c-9f96-2cf9ef529acb
17:20:45.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edc64e5c-0787-4c3c-9f96-2cf9ef529acb] RpcClient init label, labels = {module=naming, source=sdk}
17:20:45.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edc64e5c-0787-4c3c-9f96-2cf9ef529acb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:20:45.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edc64e5c-0787-4c3c-9f96-2cf9ef529acb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:20:45.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edc64e5c-0787-4c3c-9f96-2cf9ef529acb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:20:45.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edc64e5c-0787-4c3c-9f96-2cf9ef529acb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:20:45.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edc64e5c-0787-4c3c-9f96-2cf9ef529acb] Success to connect to server [localhost:8848] on start up, connectionId = 1753953645235_127.0.0.1_12007
17:20:45.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edc64e5c-0787-4c3c-9f96-2cf9ef529acb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:20:45.345 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edc64e5c-0787-4c3c-9f96-2cf9ef529acb] Notify connected event to listeners.
17:20:45.345 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edc64e5c-0787-4c3c-9f96-2cf9ef529acb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000181a6518228
17:20:45.386 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:20:45.409 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:20:45.507 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.446 seconds (JVM running for 16.616)
17:20:45.518 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:20:45.519 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:20:45.519 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:20:45.580 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:20:45.919 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edc64e5c-0787-4c3c-9f96-2cf9ef529acb] Receive server push request, request = NotifySubscriberRequest, requestId = 66
17:20:45.940 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edc64e5c-0787-4c3c-9f96-2cf9ef529acb] Ack server push request, request = NotifySubscriberRequest, requestId = 66
17:21:39.154 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:21:39.155 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:21:39.157 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
17:21:39.158 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
17:21:39.159 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:21:39.165 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:21:39.165 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:21:39.165 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
17:21:39.166 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
17:21:39.166 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:25:38.765 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Server healthy check fail, currentConnection = 1753953633007_127.0.0.1_11966
17:25:46.651 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:27:52.975 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:27:54.930 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Success to connect a server [localhost:8848], connectionId = 1753954074752_127.0.0.1_13241
17:27:54.931 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1753953633007_127.0.0.1_11966
17:27:54.932 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753953633007_127.0.0.1_11966
17:27:54.938 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Notify disconnected event to listeners
17:27:54.939 [nacos-grpc-client-executor-61] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753953633007_127.0.0.1_11966]Ignore complete event,isRunning:true,isAbandon=true
17:27:54.939 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8442e79a-86e9-46e2-a746-e96ca1172658_config-0] Notify connected event to listeners.
17:41:15.413 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:41:15.416 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:41:15.736 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:41:15.736 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@21281c0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:41:15.736 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753953645235_127.0.0.1_12007
17:41:15.737 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3c0cd667[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 188]
17:41:15.877 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:41:15.878 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
17:41:15.879 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
17:41:15.879 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:41:15.879 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:41:15.880 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:41:20.600 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:41:21.139 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bd0d5265-2588-42ae-a137-78ea8241eb2f_config-0
17:41:21.191 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
17:41:21.217 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
17:41:21.222 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
17:41:21.228 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
17:41:21.233 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
17:41:21.241 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
17:41:21.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0d5265-2588-42ae-a137-78ea8241eb2f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:41:21.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0d5265-2588-42ae-a137-78ea8241eb2f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000256013bf1c0
17:41:21.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0d5265-2588-42ae-a137-78ea8241eb2f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000256013bf3e0
17:41:21.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0d5265-2588-42ae-a137-78ea8241eb2f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:41:21.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0d5265-2588-42ae-a137-78ea8241eb2f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:41:21.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0d5265-2588-42ae-a137-78ea8241eb2f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:41:21.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0d5265-2588-42ae-a137-78ea8241eb2f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753954881728_127.0.0.1_1980
17:41:21.912 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0d5265-2588-42ae-a137-78ea8241eb2f_config-0] Notify connected event to listeners.
17:41:21.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0d5265-2588-42ae-a137-78ea8241eb2f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:41:21.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd0d5265-2588-42ae-a137-78ea8241eb2f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000256014f8fb0
17:41:21.992 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:41:24.412 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:41:24.412 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:41:24.412 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:41:24.528 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:41:25.056 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:41:25.057 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:41:25.057 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:41:29.930 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:41:31.917 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 80fbf31f-68b1-49e5-b461-5969a5110dcd
17:41:31.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80fbf31f-68b1-49e5-b461-5969a5110dcd] RpcClient init label, labels = {module=naming, source=sdk}
17:41:31.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80fbf31f-68b1-49e5-b461-5969a5110dcd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:41:31.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80fbf31f-68b1-49e5-b461-5969a5110dcd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:41:31.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80fbf31f-68b1-49e5-b461-5969a5110dcd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:41:31.920 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80fbf31f-68b1-49e5-b461-5969a5110dcd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:41:32.042 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80fbf31f-68b1-49e5-b461-5969a5110dcd] Success to connect to server [localhost:8848] on start up, connectionId = 1753954891926_127.0.0.1_2039
17:41:32.042 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80fbf31f-68b1-49e5-b461-5969a5110dcd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:41:32.042 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80fbf31f-68b1-49e5-b461-5969a5110dcd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000256014f8fb0
17:41:32.043 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80fbf31f-68b1-49e5-b461-5969a5110dcd] Notify connected event to listeners.
17:41:32.087 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:41:32.110 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:41:32.224 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.079 seconds (JVM running for 12.946)
17:41:32.240 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:41:32.241 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:41:32.241 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:41:32.353 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:41:32.690 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80fbf31f-68b1-49e5-b461-5969a5110dcd] Receive server push request, request = NotifySubscriberRequest, requestId = 69
17:41:32.708 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80fbf31f-68b1-49e5-b461-5969a5110dcd] Ack server push request, request = NotifySubscriberRequest, requestId = 69
17:42:01.974 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:42:01.974 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
17:42:01.974 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:42:01.975 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
17:42:01.976 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:42:01.981 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:42:01.981 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:42:01.981 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
17:42:01.982 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
17:42:01.983 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:46:45.945 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:46:45.950 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:46:46.282 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:46:46.282 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@11388f21[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:46:46.282 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753954891926_127.0.0.1_2039
20:46:46.284 [nacos-grpc-client-executor-2234] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753954891926_127.0.0.1_2039]Ignore complete event,isRunning:false,isAbandon=false
20:46:46.288 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1b069c8[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 2235]
20:46:46.458 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:46:46.459 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
20:46:46.461 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
20:46:46.461 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:46:46.462 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:46:46.463 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
