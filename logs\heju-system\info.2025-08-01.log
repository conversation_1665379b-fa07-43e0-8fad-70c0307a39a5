09:07:12.596 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:07:14.083 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ceefbaa9-1aca-4a70-96f6-a532dc3f7af4_config-0
09:07:14.235 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 57 ms to scan 1 urls, producing 3 keys and 6 values 
09:07:14.323 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 4 keys and 9 values 
09:07:14.341 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 3 keys and 10 values 
09:07:14.359 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 5 values 
09:07:14.373 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:07:14.393 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
09:07:14.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ceefbaa9-1aca-4a70-96f6-a532dc3f7af4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:07:14.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ceefbaa9-1aca-4a70-96f6-a532dc3f7af4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001bfb73bc730
09:07:14.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ceefbaa9-1aca-4a70-96f6-a532dc3f7af4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001bfb73bc950
09:07:14.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ceefbaa9-1aca-4a70-96f6-a532dc3f7af4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:07:14.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ceefbaa9-1aca-4a70-96f6-a532dc3f7af4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:07:14.415 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ceefbaa9-1aca-4a70-96f6-a532dc3f7af4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:07:16.164 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ceefbaa9-1aca-4a70-96f6-a532dc3f7af4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754010435802_127.0.0.1_6230
09:07:16.165 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ceefbaa9-1aca-4a70-96f6-a532dc3f7af4_config-0] Notify connected event to listeners.
09:07:16.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ceefbaa9-1aca-4a70-96f6-a532dc3f7af4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:07:16.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ceefbaa9-1aca-4a70-96f6-a532dc3f7af4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001bfb74f4b18
09:07:16.416 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:07:25.104 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:07:25.104 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:07:25.105 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:07:25.542 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:07:27.333 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:07:27.336 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:07:27.337 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:07:43.409 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:07:47.355 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4b869902-5462-4f6d-8239-86171fa119ef
09:07:47.355 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b869902-5462-4f6d-8239-86171fa119ef] RpcClient init label, labels = {module=naming, source=sdk}
09:07:47.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b869902-5462-4f6d-8239-86171fa119ef] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:07:47.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b869902-5462-4f6d-8239-86171fa119ef] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:07:47.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b869902-5462-4f6d-8239-86171fa119ef] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:07:47.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b869902-5462-4f6d-8239-86171fa119ef] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:07:47.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b869902-5462-4f6d-8239-86171fa119ef] Success to connect to server [localhost:8848] on start up, connectionId = 1754010467365_127.0.0.1_6619
09:07:47.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b869902-5462-4f6d-8239-86171fa119ef] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:07:47.483 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b869902-5462-4f6d-8239-86171fa119ef] Notify connected event to listeners.
09:07:47.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b869902-5462-4f6d-8239-86171fa119ef] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001bfb74f4b18
09:07:47.552 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:07:47.594 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:07:47.753 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 36.468 seconds (JVM running for 40.17)
09:07:47.770 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:07:47.771 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:07:47.772 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:07:48.075 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b869902-5462-4f6d-8239-86171fa119ef] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:07:48.090 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b869902-5462-4f6d-8239-86171fa119ef] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:08:13.006 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:08:15.560 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b869902-5462-4f6d-8239-86171fa119ef] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:08:15.560 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b869902-5462-4f6d-8239-86171fa119ef] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:08:15.761 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:08:15.763 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:08:15.911 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:08:15.912 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
10:01:53.402 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:01:53.409 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:01:53.755 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:01:53.756 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@15a30d64[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:01:53.756 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754010467365_127.0.0.1_6619
10:01:53.760 [nacos-grpc-client-executor-666] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754010467365_127.0.0.1_6619]Ignore complete event,isRunning:false,isAbandon=false
10:01:53.767 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5abeaa35[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 667]
10:01:54.055 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:01:54.073 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:01:54.104 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:01:54.105 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:01:54.110 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:01:54.111 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:01:54.113 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:01:54.114 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:02:22.082 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:02:22.751 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1f1323e9-bd1e-4a27-8662-12dcff9075c0_config-0
10:02:22.798 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 6 values 
10:02:22.833 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 4 keys and 9 values 
10:02:22.848 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
10:02:22.848 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
10:02:22.865 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
10:02:22.879 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
10:02:22.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f1323e9-bd1e-4a27-8662-12dcff9075c0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:02:22.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f1323e9-bd1e-4a27-8662-12dcff9075c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002ac5b3b5d00
10:02:22.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f1323e9-bd1e-4a27-8662-12dcff9075c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002ac5b3b5f20
10:02:22.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f1323e9-bd1e-4a27-8662-12dcff9075c0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:02:22.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f1323e9-bd1e-4a27-8662-12dcff9075c0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:02:22.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f1323e9-bd1e-4a27-8662-12dcff9075c0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:02:23.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f1323e9-bd1e-4a27-8662-12dcff9075c0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754013743389_127.0.0.1_1827
10:02:23.606 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f1323e9-bd1e-4a27-8662-12dcff9075c0_config-0] Notify connected event to listeners.
10:02:23.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f1323e9-bd1e-4a27-8662-12dcff9075c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:02:23.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f1323e9-bd1e-4a27-8662-12dcff9075c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002ac5b4efb88
10:02:23.758 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:02:26.495 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:02:26.495 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:02:26.495 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:02:26.625 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:02:27.214 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:02:27.214 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:02:27.214 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:02:33.495 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:02:36.238 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c120ee83-3e53-4456-a375-fd6b7325f7c6
10:02:36.238 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c120ee83-3e53-4456-a375-fd6b7325f7c6] RpcClient init label, labels = {module=naming, source=sdk}
10:02:36.238 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c120ee83-3e53-4456-a375-fd6b7325f7c6] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:02:36.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c120ee83-3e53-4456-a375-fd6b7325f7c6] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:02:36.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c120ee83-3e53-4456-a375-fd6b7325f7c6] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:02:36.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c120ee83-3e53-4456-a375-fd6b7325f7c6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:02:36.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c120ee83-3e53-4456-a375-fd6b7325f7c6] Success to connect to server [localhost:8848] on start up, connectionId = 1754013756263_127.0.0.1_1839
10:02:36.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c120ee83-3e53-4456-a375-fd6b7325f7c6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:02:36.380 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c120ee83-3e53-4456-a375-fd6b7325f7c6] Notify connected event to listeners.
10:02:36.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c120ee83-3e53-4456-a375-fd6b7325f7c6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002ac5b4efb88
10:02:36.420 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:02:36.462 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:02:36.590 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.046 seconds (JVM running for 16.945)
10:02:36.607 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:02:36.608 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:02:36.608 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:02:36.908 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c120ee83-3e53-4456-a375-fd6b7325f7c6] Receive server push request, request = NotifySubscriberRequest, requestId = 21
10:02:36.915 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c120ee83-3e53-4456-a375-fd6b7325f7c6] Ack server push request, request = NotifySubscriberRequest, requestId = 21
10:02:42.396 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:02:43.512 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:02:43.512 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:06:37.887 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:06:37.888 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:06:38.205 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:06:38.205 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@697b9881[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:06:38.205 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754013756263_127.0.0.1_1839
10:06:38.208 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754013756263_127.0.0.1_1839]Ignore complete event,isRunning:false,isAbandon=false
10:06:38.209 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4f8b5078[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 60]
10:06:38.335 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:06:38.337 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:06:38.343 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:06:38.343 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:06:38.343 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:06:38.343 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:06:43.021 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:06:43.750 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ebbc94b5-4ec9-4528-84f2-1002ade17528_config-0
10:06:43.804 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 6 values 
10:06:43.831 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
10:06:43.842 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
10:06:43.850 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
10:06:43.858 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
10:06:43.870 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
10:06:43.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebbc94b5-4ec9-4528-84f2-1002ade17528_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:06:43.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebbc94b5-4ec9-4528-84f2-1002ade17528_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000248ce39dd70
10:06:43.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebbc94b5-4ec9-4528-84f2-1002ade17528_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000248ce39df90
10:06:43.874 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebbc94b5-4ec9-4528-84f2-1002ade17528_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:06:43.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebbc94b5-4ec9-4528-84f2-1002ade17528_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:06:43.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebbc94b5-4ec9-4528-84f2-1002ade17528_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:06:44.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebbc94b5-4ec9-4528-84f2-1002ade17528_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754014004675_127.0.0.1_3104
10:06:44.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebbc94b5-4ec9-4528-84f2-1002ade17528_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:06:44.889 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebbc94b5-4ec9-4528-84f2-1002ade17528_config-0] Notify connected event to listeners.
10:06:44.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ebbc94b5-4ec9-4528-84f2-1002ade17528_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000248ce518228
10:06:45.016 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:06:54.021 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:06:54.022 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:06:54.022 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:06:54.362 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:06:56.303 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:06:56.305 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:06:56.305 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:07:03.482 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:07:05.939 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 07c2846b-a79d-4133-ad47-737390480f8f
10:07:05.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07c2846b-a79d-4133-ad47-737390480f8f] RpcClient init label, labels = {module=naming, source=sdk}
10:07:05.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07c2846b-a79d-4133-ad47-737390480f8f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:07:05.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07c2846b-a79d-4133-ad47-737390480f8f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:07:05.942 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07c2846b-a79d-4133-ad47-737390480f8f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:07:05.942 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07c2846b-a79d-4133-ad47-737390480f8f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:07:06.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07c2846b-a79d-4133-ad47-737390480f8f] Success to connect to server [localhost:8848] on start up, connectionId = 1754014025945_127.0.0.1_3260
10:07:06.060 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07c2846b-a79d-4133-ad47-737390480f8f] Notify connected event to listeners.
10:07:06.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07c2846b-a79d-4133-ad47-737390480f8f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:07:06.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07c2846b-a79d-4133-ad47-737390480f8f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000248ce518228
10:07:06.101 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:07:06.123 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:07:06.237 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.924 seconds (JVM running for 25.159)
10:07:06.251 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:07:06.251 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:07:06.252 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:07:06.625 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07c2846b-a79d-4133-ad47-737390480f8f] Receive server push request, request = NotifySubscriberRequest, requestId = 33
10:07:06.637 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07c2846b-a79d-4133-ad47-737390480f8f] Ack server push request, request = NotifySubscriberRequest, requestId = 33
10:07:06.772 [RMI TCP Connection(13)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:09:15.287 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:09:15.303 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:09:15.641 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:09:15.641 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@26e3b40[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:09:15.641 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754014025945_127.0.0.1_3260
10:09:15.643 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754014025945_127.0.0.1_3260]Ignore complete event,isRunning:false,isAbandon=false
10:09:15.646 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3cc0601c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 39]
10:09:15.798 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:09:15.798 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:09:15.808 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:09:15.809 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:09:20.377 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:09:20.905 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 74bc946b-1a5c-4e67-b6c6-451aeecc001d_config-0
10:09:20.947 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 6 values 
10:09:20.972 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
10:09:20.983 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:09:20.989 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
10:09:20.996 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
10:09:21.001 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
10:09:21.003 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74bc946b-1a5c-4e67-b6c6-451aeecc001d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:09:21.003 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74bc946b-1a5c-4e67-b6c6-451aeecc001d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f7cc3a5d00
10:09:21.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74bc946b-1a5c-4e67-b6c6-451aeecc001d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001f7cc3a5f20
10:09:21.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74bc946b-1a5c-4e67-b6c6-451aeecc001d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:09:21.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74bc946b-1a5c-4e67-b6c6-451aeecc001d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:09:21.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74bc946b-1a5c-4e67-b6c6-451aeecc001d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:09:21.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74bc946b-1a5c-4e67-b6c6-451aeecc001d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754014161524_127.0.0.1_3758
10:09:21.708 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74bc946b-1a5c-4e67-b6c6-451aeecc001d_config-0] Notify connected event to listeners.
10:09:21.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74bc946b-1a5c-4e67-b6c6-451aeecc001d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:09:21.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74bc946b-1a5c-4e67-b6c6-451aeecc001d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f7cc507b78
10:09:21.789 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:09:24.219 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:09:24.220 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:09:24.220 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:09:24.336 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:09:24.776 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:09:24.777 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:09:24.777 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:09:30.066 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:09:32.659 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bf089ab5-9bdb-401f-adc6-1bc04790b58d
10:09:32.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf089ab5-9bdb-401f-adc6-1bc04790b58d] RpcClient init label, labels = {module=naming, source=sdk}
10:09:32.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf089ab5-9bdb-401f-adc6-1bc04790b58d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:09:32.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf089ab5-9bdb-401f-adc6-1bc04790b58d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:09:32.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf089ab5-9bdb-401f-adc6-1bc04790b58d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:09:32.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf089ab5-9bdb-401f-adc6-1bc04790b58d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:09:32.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf089ab5-9bdb-401f-adc6-1bc04790b58d] Success to connect to server [localhost:8848] on start up, connectionId = 1754014172669_127.0.0.1_3826
10:09:32.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf089ab5-9bdb-401f-adc6-1bc04790b58d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:09:32.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf089ab5-9bdb-401f-adc6-1bc04790b58d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f7cc507b78
10:09:32.785 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf089ab5-9bdb-401f-adc6-1bc04790b58d] Notify connected event to listeners.
10:09:32.827 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:09:32.852 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:09:32.958 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.107 seconds (JVM running for 13.973)
10:09:32.970 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:09:32.971 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:09:32.972 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:09:33.410 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf089ab5-9bdb-401f-adc6-1bc04790b58d] Receive server push request, request = NotifySubscriberRequest, requestId = 42
10:09:33.426 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf089ab5-9bdb-401f-adc6-1bc04790b58d] Ack server push request, request = NotifySubscriberRequest, requestId = 42
10:09:33.501 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:09:47.133 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:09:47.133 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:09:47.127 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
10:09:47.133 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:09:47.135 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:09:47.137 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:09:47.137 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:09:47.137 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
10:09:47.141 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
10:09:47.141 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:14:14.363 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:14:14.373 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:14:14.702 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:14:14.702 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@23198a86[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:14:14.702 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754014172669_127.0.0.1_3826
10:14:14.702 [nacos-grpc-client-executor-70] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754014172669_127.0.0.1_3826]Ignore complete event,isRunning:false,isAbandon=false
10:14:14.702 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@9a09a72[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 71]
10:14:14.834 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:14:14.834 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:14:14.834 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:14:14.834 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:14:14.841 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:14:14.841 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:14:18.844 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:14:19.358 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 92244d93-06db-4c4c-88dc-c72219297557_config-0
10:14:19.408 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
10:14:19.433 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
10:14:19.440 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
10:14:19.446 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
10:14:19.452 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
10:14:19.457 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
10:14:19.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92244d93-06db-4c4c-88dc-c72219297557_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:14:19.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92244d93-06db-4c4c-88dc-c72219297557_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001a2013be8d8
10:14:19.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92244d93-06db-4c4c-88dc-c72219297557_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001a2013beaf8
10:14:19.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92244d93-06db-4c4c-88dc-c72219297557_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:14:19.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92244d93-06db-4c4c-88dc-c72219297557_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:14:19.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92244d93-06db-4c4c-88dc-c72219297557_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:14:20.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92244d93-06db-4c4c-88dc-c72219297557_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754014459951_127.0.0.1_5141
10:14:20.131 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92244d93-06db-4c4c-88dc-c72219297557_config-0] Notify connected event to listeners.
10:14:20.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92244d93-06db-4c4c-88dc-c72219297557_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:14:20.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [92244d93-06db-4c4c-88dc-c72219297557_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a2014f8668
10:14:20.215 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:14:22.590 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:14:22.590 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:14:22.590 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:14:22.700 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:14:23.156 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:14:23.157 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:14:23.157 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:14:27.992 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:14:30.112 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0e3e66eb-75b0-42ce-a95f-5ee39b5353cd
10:14:30.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e3e66eb-75b0-42ce-a95f-5ee39b5353cd] RpcClient init label, labels = {module=naming, source=sdk}
10:14:30.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e3e66eb-75b0-42ce-a95f-5ee39b5353cd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:14:30.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e3e66eb-75b0-42ce-a95f-5ee39b5353cd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:14:30.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e3e66eb-75b0-42ce-a95f-5ee39b5353cd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:14:30.115 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e3e66eb-75b0-42ce-a95f-5ee39b5353cd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:14:30.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e3e66eb-75b0-42ce-a95f-5ee39b5353cd] Success to connect to server [localhost:8848] on start up, connectionId = 1754014470121_127.0.0.1_5207
10:14:30.229 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e3e66eb-75b0-42ce-a95f-5ee39b5353cd] Notify connected event to listeners.
10:14:30.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e3e66eb-75b0-42ce-a95f-5ee39b5353cd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:14:30.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e3e66eb-75b0-42ce-a95f-5ee39b5353cd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a2014f8668
10:14:30.266 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:14:30.285 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:14:30.370 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 11.989 seconds (JVM running for 12.856)
10:14:30.381 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:14:30.381 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:14:30.381 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:14:30.483 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:14:30.774 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e3e66eb-75b0-42ce-a95f-5ee39b5353cd] Receive server push request, request = NotifySubscriberRequest, requestId = 51
10:14:30.789 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e3e66eb-75b0-42ce-a95f-5ee39b5353cd] Ack server push request, request = NotifySubscriberRequest, requestId = 51
10:14:31.425 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:14:31.426 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:14:31.426 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:14:31.428 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:14:31.434 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:14:31.434 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:50:50.898 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:50:50.901 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:50:51.231 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:50:51.231 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@15aaad77[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:50:51.231 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754014470121_127.0.0.1_5207
10:50:51.233 [nacos-grpc-client-executor-448] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754014470121_127.0.0.1_5207]Ignore complete event,isRunning:false,isAbandon=false
10:50:51.236 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4c009c6b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 449]
10:50:51.378 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:50:51.378 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:50:51.380 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:50:51.380 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:50:51.381 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:50:51.382 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:51:15.564 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:51:16.168 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 361e22d7-b953-4ea4-9cdc-fa592e8b23a2_config-0
10:51:16.242 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
10:51:16.268 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 1 ms to scan 1 urls, producing 4 keys and 9 values 
10:51:16.284 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
10:51:16.284 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
10:51:16.284 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
10:51:16.299 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
10:51:16.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [361e22d7-b953-4ea4-9cdc-fa592e8b23a2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:51:16.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [361e22d7-b953-4ea4-9cdc-fa592e8b23a2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001e4553b6af8
10:51:16.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [361e22d7-b953-4ea4-9cdc-fa592e8b23a2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001e4553b6d18
10:51:16.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [361e22d7-b953-4ea4-9cdc-fa592e8b23a2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:51:16.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [361e22d7-b953-4ea4-9cdc-fa592e8b23a2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:51:16.315 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [361e22d7-b953-4ea4-9cdc-fa592e8b23a2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:51:17.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [361e22d7-b953-4ea4-9cdc-fa592e8b23a2_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754016676881_127.0.0.1_10776
10:51:17.080 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [361e22d7-b953-4ea4-9cdc-fa592e8b23a2_config-0] Notify connected event to listeners.
10:51:17.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [361e22d7-b953-4ea4-9cdc-fa592e8b23a2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:51:17.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [361e22d7-b953-4ea4-9cdc-fa592e8b23a2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001e4554f0668
10:51:17.169 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:51:22.081 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:51:22.081 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:51:22.081 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:51:22.261 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:51:22.880 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:51:22.894 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:51:22.894 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:51:29.686 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:51:32.302 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6a8e3981-9ac8-42f3-be20-d2f91bbb0fd0
10:51:32.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8e3981-9ac8-42f3-be20-d2f91bbb0fd0] RpcClient init label, labels = {module=naming, source=sdk}
10:51:32.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8e3981-9ac8-42f3-be20-d2f91bbb0fd0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:51:32.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8e3981-9ac8-42f3-be20-d2f91bbb0fd0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:51:32.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8e3981-9ac8-42f3-be20-d2f91bbb0fd0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:51:32.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8e3981-9ac8-42f3-be20-d2f91bbb0fd0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:51:32.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8e3981-9ac8-42f3-be20-d2f91bbb0fd0] Success to connect to server [localhost:8848] on start up, connectionId = 1754016692318_127.0.0.1_10862
10:51:32.432 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8e3981-9ac8-42f3-be20-d2f91bbb0fd0] Notify connected event to listeners.
10:51:32.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8e3981-9ac8-42f3-be20-d2f91bbb0fd0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:51:32.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8e3981-9ac8-42f3-be20-d2f91bbb0fd0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001e4554f0668
10:51:32.480 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:51:32.495 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:51:32.626 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 17.526 seconds (JVM running for 19.043)
10:51:32.647 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:51:32.647 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:51:32.647 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:51:32.967 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8e3981-9ac8-42f3-be20-d2f91bbb0fd0] Receive server push request, request = NotifySubscriberRequest, requestId = 61
10:51:32.987 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8e3981-9ac8-42f3-be20-d2f91bbb0fd0] Ack server push request, request = NotifySubscriberRequest, requestId = 61
10:51:35.784 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:51:37.024 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:51:37.024 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:27:46.494 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:27:46.498 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:27:46.836 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:27:46.836 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@38b9e577[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:27:46.836 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754016692318_127.0.0.1_10862
11:27:46.840 [nacos-grpc-client-executor-414] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754016692318_127.0.0.1_10862]Ignore complete event,isRunning:false,isAbandon=false
11:27:46.840 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@323d2465[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 415]
11:27:46.984 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:27:46.987 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:27:46.991 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:27:46.991 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:27:46.991 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:27:46.991 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:27:56.805 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:27:58.094 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c3b86b1f-e8b5-4b19-94c9-720a37aa413f_config-0
11:27:58.201 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
11:27:58.261 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
11:27:58.273 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
11:27:58.288 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
11:27:58.302 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
11:27:58.317 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
11:27:58.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3b86b1f-e8b5-4b19-94c9-720a37aa413f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:27:58.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3b86b1f-e8b5-4b19-94c9-720a37aa413f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002020139eaf8
11:27:58.323 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3b86b1f-e8b5-4b19-94c9-720a37aa413f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002020139ed18
11:27:58.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3b86b1f-e8b5-4b19-94c9-720a37aa413f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:27:58.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3b86b1f-e8b5-4b19-94c9-720a37aa413f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:27:58.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3b86b1f-e8b5-4b19-94c9-720a37aa413f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:27:59.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3b86b1f-e8b5-4b19-94c9-720a37aa413f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754018879236_127.0.0.1_4808
11:27:59.461 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3b86b1f-e8b5-4b19-94c9-720a37aa413f_config-0] Notify connected event to listeners.
11:27:59.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3b86b1f-e8b5-4b19-94c9-720a37aa413f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:27:59.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3b86b1f-e8b5-4b19-94c9-720a37aa413f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020201518668
11:27:59.574 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:28:03.716 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:28:03.717 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:28:03.718 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:28:04.023 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:28:04.904 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:28:04.906 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:28:04.907 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:28:14.884 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:28:18.589 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 98f8235d-bdb9-4646-9c6b-7dc585e8dda9
11:28:18.589 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98f8235d-bdb9-4646-9c6b-7dc585e8dda9] RpcClient init label, labels = {module=naming, source=sdk}
11:28:18.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98f8235d-bdb9-4646-9c6b-7dc585e8dda9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:28:18.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98f8235d-bdb9-4646-9c6b-7dc585e8dda9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:28:18.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98f8235d-bdb9-4646-9c6b-7dc585e8dda9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:28:18.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98f8235d-bdb9-4646-9c6b-7dc585e8dda9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:28:18.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98f8235d-bdb9-4646-9c6b-7dc585e8dda9] Success to connect to server [localhost:8848] on start up, connectionId = 1754018898603_127.0.0.1_4950
11:28:18.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98f8235d-bdb9-4646-9c6b-7dc585e8dda9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:28:18.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98f8235d-bdb9-4646-9c6b-7dc585e8dda9] Notify connected event to listeners.
11:28:18.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98f8235d-bdb9-4646-9c6b-7dc585e8dda9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020201518668
11:28:18.773 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:28:18.802 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:28:18.939 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.656 seconds (JVM running for 26.094)
11:28:18.954 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:28:18.954 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:28:18.955 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:28:19.245 [RMI TCP Connection(16)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:28:19.321 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98f8235d-bdb9-4646-9c6b-7dc585e8dda9] Receive server push request, request = NotifySubscriberRequest, requestId = 67
11:28:19.339 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98f8235d-bdb9-4646-9c6b-7dc585e8dda9] Ack server push request, request = NotifySubscriberRequest, requestId = 67
11:28:25.132 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:28:25.132 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:29:45.880 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:29:45.880 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:29:46.217 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:29:46.217 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7f5a883b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:29:46.217 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754018898603_127.0.0.1_4950
11:29:46.218 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754018898603_127.0.0.1_4950]Ignore complete event,isRunning:false,isAbandon=false
11:29:46.220 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@25cd8f01[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 28]
11:29:46.359 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:29:46.361 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:29:46.365 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:29:46.365 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:29:46.365 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:29:46.365 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:29:50.185 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:29:50.713 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 494b307d-4c24-40eb-8e5d-d784c484dbbd_config-0
11:29:50.764 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
11:29:50.792 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
11:29:50.799 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:29:50.805 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
11:29:50.811 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
11:29:50.818 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
11:29:50.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [494b307d-4c24-40eb-8e5d-d784c484dbbd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:29:50.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [494b307d-4c24-40eb-8e5d-d784c484dbbd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000278323be8d8
11:29:50.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [494b307d-4c24-40eb-8e5d-d784c484dbbd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000278323beaf8
11:29:50.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [494b307d-4c24-40eb-8e5d-d784c484dbbd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:29:50.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [494b307d-4c24-40eb-8e5d-d784c484dbbd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:29:50.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [494b307d-4c24-40eb-8e5d-d784c484dbbd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:29:51.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [494b307d-4c24-40eb-8e5d-d784c484dbbd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754018991306_127.0.0.1_5511
11:29:51.488 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [494b307d-4c24-40eb-8e5d-d784c484dbbd_config-0] Notify connected event to listeners.
11:29:51.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [494b307d-4c24-40eb-8e5d-d784c484dbbd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:29:51.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [494b307d-4c24-40eb-8e5d-d784c484dbbd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000278324f8668
11:29:51.564 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:29:53.940 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:29:53.940 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:29:53.940 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:29:54.051 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:29:54.520 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:29:54.521 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:29:54.521 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:29:59.494 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:30:01.496 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-3cb8-4f3d-bd43-2a70888748ed
11:30:01.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3cb8-4f3d-bd43-2a70888748ed] RpcClient init label, labels = {module=naming, source=sdk}
11:30:01.497 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3cb8-4f3d-bd43-2a70888748ed] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:30:01.498 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3cb8-4f3d-bd43-2a70888748ed] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:30:01.498 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3cb8-4f3d-bd43-2a70888748ed] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:30:01.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3cb8-4f3d-bd43-2a70888748ed] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:30:01.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3cb8-4f3d-bd43-2a70888748ed] Success to connect to server [localhost:8848] on start up, connectionId = 1754019001505_127.0.0.1_5597
11:30:01.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3cb8-4f3d-bd43-2a70888748ed] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:30:01.618 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3cb8-4f3d-bd43-2a70888748ed] Notify connected event to listeners.
11:30:01.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3cb8-4f3d-bd43-2a70888748ed] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000278324f8668
11:30:01.659 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:30:01.682 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:30:01.767 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.047 seconds (JVM running for 12.831)
11:30:01.777 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:30:01.777 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:30:01.777 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:30:01.847 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:30:02.222 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3cb8-4f3d-bd43-2a70888748ed] Receive server push request, request = NotifySubscriberRequest, requestId = 76
11:30:02.237 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-3cb8-4f3d-bd43-2a70888748ed] Ack server push request, request = NotifySubscriberRequest, requestId = 76
11:30:05.350 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:30:05.351 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:32:07.428 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:32:07.432 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:32:07.764 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:32:07.764 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@8a00e90[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:32:07.764 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754019001505_127.0.0.1_5597
11:32:07.766 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754019001505_127.0.0.1_5597]Ignore complete event,isRunning:false,isAbandon=false
11:32:07.768 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6606f0a8[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 37]
11:32:07.912 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:32:07.916 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:32:07.919 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:32:07.920 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:32:07.921 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:32:07.921 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:32:12.635 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:32:13.202 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 51e47d01-543a-477b-beab-78c277035f78_config-0
11:32:13.252 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
11:32:13.280 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
11:32:13.287 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:32:13.294 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:32:13.301 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
11:32:13.307 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
11:32:13.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51e47d01-543a-477b-beab-78c277035f78_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:32:13.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51e47d01-543a-477b-beab-78c277035f78_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001c75b3bdb10
11:32:13.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51e47d01-543a-477b-beab-78c277035f78_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001c75b3bdd30
11:32:13.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51e47d01-543a-477b-beab-78c277035f78_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:32:13.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51e47d01-543a-477b-beab-78c277035f78_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:32:13.316 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51e47d01-543a-477b-beab-78c277035f78_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:32:14.003 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51e47d01-543a-477b-beab-78c277035f78_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754019133820_127.0.0.1_6242
11:32:14.004 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51e47d01-543a-477b-beab-78c277035f78_config-0] Notify connected event to listeners.
11:32:14.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51e47d01-543a-477b-beab-78c277035f78_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:32:14.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51e47d01-543a-477b-beab-78c277035f78_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001c75b4f7cb0
11:32:14.081 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:32:17.275 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:32:17.276 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:32:17.276 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:32:17.455 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:32:18.137 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:32:18.138 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:32:18.139 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:32:24.239 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:32:27.756 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cefaaf7b-0fd2-4955-a164-cf216a0d4ed5
11:32:27.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cefaaf7b-0fd2-4955-a164-cf216a0d4ed5] RpcClient init label, labels = {module=naming, source=sdk}
11:32:27.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cefaaf7b-0fd2-4955-a164-cf216a0d4ed5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:32:27.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cefaaf7b-0fd2-4955-a164-cf216a0d4ed5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:32:27.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cefaaf7b-0fd2-4955-a164-cf216a0d4ed5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:32:27.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cefaaf7b-0fd2-4955-a164-cf216a0d4ed5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:32:27.905 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cefaaf7b-0fd2-4955-a164-cf216a0d4ed5] Success to connect to server [localhost:8848] on start up, connectionId = 1754019147778_127.0.0.1_6378
11:32:27.906 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cefaaf7b-0fd2-4955-a164-cf216a0d4ed5] Notify connected event to listeners.
11:32:27.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cefaaf7b-0fd2-4955-a164-cf216a0d4ed5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:32:27.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cefaaf7b-0fd2-4955-a164-cf216a0d4ed5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001c75b4f7cb0
11:32:27.976 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:32:28.024 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:32:28.279 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.099 seconds (JVM running for 16.901)
11:32:28.304 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:32:28.306 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:32:28.307 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:32:28.455 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:32:28.515 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cefaaf7b-0fd2-4955-a164-cf216a0d4ed5] Receive server push request, request = NotifySubscriberRequest, requestId = 86
11:32:28.539 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cefaaf7b-0fd2-4955-a164-cf216a0d4ed5] Ack server push request, request = NotifySubscriberRequest, requestId = 86
11:32:33.126 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:32:33.126 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:35:04.263 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:35:04.263 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:35:04.585 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:35:04.585 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6583bba8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:35:04.585 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754019147778_127.0.0.1_6378
11:35:04.588 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754019147778_127.0.0.1_6378]Ignore complete event,isRunning:false,isAbandon=false
11:35:04.591 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@37aee336[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 46]
11:35:04.730 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:35:04.733 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:35:04.734 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:35:04.734 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:35:04.734 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:35:04.734 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:35:09.540 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:35:10.049 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e0456f04-b15e-4dc6-b174-856c7431ef79_config-0
11:35:10.102 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
11:35:10.128 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
11:35:10.134 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:35:10.141 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:35:10.147 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
11:35:10.154 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
11:35:10.156 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:35:10.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001ca183beaf8
11:35:10.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001ca183bed18
11:35:10.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:35:10.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:35:10.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:35:10.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754019310642_127.0.0.1_7107
11:35:10.823 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Notify connected event to listeners.
11:35:10.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:35:10.825 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001ca184f8ad8
11:35:10.896 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:35:13.328 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:35:13.328 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:35:13.328 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:35:13.434 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:35:14.051 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:35:14.052 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:35:14.052 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:35:22.660 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:35:24.661 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c
11:35:24.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] RpcClient init label, labels = {module=naming, source=sdk}
11:35:24.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:35:24.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:35:24.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:35:24.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:35:24.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Success to connect to server [localhost:8848] on start up, connectionId = 1754019324668_127.0.0.1_7196
11:35:24.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:35:24.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001ca184f8ad8
11:35:24.781 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Notify connected event to listeners.
11:35:24.821 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:35:24.842 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:35:24.916 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.828 seconds (JVM running for 16.674)
11:35:24.935 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:35:24.935 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:35:24.935 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:35:25.147 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:35:25.318 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Receive server push request, request = NotifySubscriberRequest, requestId = 93
11:35:25.335 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Ack server push request, request = NotifySubscriberRequest, requestId = 93
11:35:30.813 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:35:30.813 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:08:43.072 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Server healthy check fail, currentConnection = 1754019310642_127.0.0.1_7107
12:08:43.072 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
12:11:41.763 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Success to connect a server [localhost:8848], connectionId = 1754021501650_127.0.0.1_13066
12:11:41.763 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1754019310642_127.0.0.1_7107
12:11:41.763 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754019310642_127.0.0.1_7107
12:11:41.763 [nacos-grpc-client-executor-237] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754019310642_127.0.0.1_7107]Ignore complete event,isRunning:false,isAbandon=true
12:11:41.769 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Notify disconnected event to listeners
12:11:41.769 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Notify connected event to listeners.
12:16:35.185 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Server healthy check fail, currentConnection = 1754019324668_127.0.0.1_7196
12:16:35.185 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Server healthy check fail, currentConnection = 1754021501650_127.0.0.1_13066
12:16:35.185 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Try to reconnect to a new server, server is  not appointed, will choose a random server.
12:16:35.185 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
12:16:35.311 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Success to connect a server [localhost:8848], connectionId = 1754021795195_127.0.0.1_13633
12:16:35.311 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Success to connect a server [localhost:8848], connectionId = 1754021795195_127.0.0.1_13634
12:16:35.311 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Abandon prev connection, server is localhost:8848, connectionId is 1754019324668_127.0.0.1_7196
12:16:35.311 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754019324668_127.0.0.1_7196
12:16:35.311 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1754021501650_127.0.0.1_13066
12:16:35.311 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754021501650_127.0.0.1_13066
12:16:35.311 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Notify disconnected event to listeners
12:16:35.312 [nacos-grpc-client-executor-248] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754021501650_127.0.0.1_13066]Ignore complete event,isRunning:true,isAbandon=true
12:16:35.311 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Notify disconnected event to listeners
12:16:35.312 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0456f04-b15e-4dc6-b174-856c7431ef79_config-0] Notify connected event to listeners.
12:16:35.316 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Notify connected event to listeners.
12:19:39.846 [nacos-grpc-client-executor-250] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Receive server push request, request = NotifySubscriberRequest, requestId = 103
12:19:39.860 [nacos-grpc-client-executor-250] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca0cb110-3675-4b60-b16f-fdbc5cf7ed9c] Ack server push request, request = NotifySubscriberRequest, requestId = 103
12:32:52.880 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:32:52.883 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:32:53.203 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:32:53.203 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6f46d0e2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:32:53.203 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754021795195_127.0.0.1_13633
12:32:53.203 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3a444aa[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 410]
12:32:53.383 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:32:53.389 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:32:53.392 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:32:53.392 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:32:53.393 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:32:53.393 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:32:59.718 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:33:00.993 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 73e70b6f-e1e8-4ebb-84b6-ebe197d9ba40_config-0
12:33:01.091 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
12:33:01.151 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
12:33:01.172 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
12:33:01.189 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
12:33:01.208 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
12:33:01.220 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
12:33:01.224 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e70b6f-e1e8-4ebb-84b6-ebe197d9ba40_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:33:01.224 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e70b6f-e1e8-4ebb-84b6-ebe197d9ba40_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b14b39dd70
12:33:01.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e70b6f-e1e8-4ebb-84b6-ebe197d9ba40_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001b14b39df90
12:33:01.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e70b6f-e1e8-4ebb-84b6-ebe197d9ba40_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:33:01.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e70b6f-e1e8-4ebb-84b6-ebe197d9ba40_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:33:01.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e70b6f-e1e8-4ebb-84b6-ebe197d9ba40_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:33:02.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e70b6f-e1e8-4ebb-84b6-ebe197d9ba40_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754022782282_127.0.0.1_2346
12:33:02.531 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e70b6f-e1e8-4ebb-84b6-ebe197d9ba40_config-0] Notify connected event to listeners.
12:33:02.531 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e70b6f-e1e8-4ebb-84b6-ebe197d9ba40_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:33:02.532 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73e70b6f-e1e8-4ebb-84b6-ebe197d9ba40_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b14b517b88
12:33:02.672 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:33:08.580 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:33:08.580 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:33:08.584 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:33:08.816 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:33:09.517 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:33:09.518 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:33:09.519 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:33:16.048 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:33:19.521 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d330214a-cd1f-4bf9-9979-4349672959bc
12:33:19.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330214a-cd1f-4bf9-9979-4349672959bc] RpcClient init label, labels = {module=naming, source=sdk}
12:33:19.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330214a-cd1f-4bf9-9979-4349672959bc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:33:19.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330214a-cd1f-4bf9-9979-4349672959bc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:33:19.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330214a-cd1f-4bf9-9979-4349672959bc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:33:19.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330214a-cd1f-4bf9-9979-4349672959bc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:33:19.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330214a-cd1f-4bf9-9979-4349672959bc] Success to connect to server [localhost:8848] on start up, connectionId = 1754022799527_127.0.0.1_2437
12:33:19.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330214a-cd1f-4bf9-9979-4349672959bc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:33:19.665 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330214a-cd1f-4bf9-9979-4349672959bc] Notify connected event to listeners.
12:33:19.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330214a-cd1f-4bf9-9979-4349672959bc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b14b517b88
12:33:19.740 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:33:19.784 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:33:19.992 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.223 seconds (JVM running for 22.531)
12:33:20.016 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:33:20.017 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:33:20.017 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:33:20.153 [RMI TCP Connection(10)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:33:20.264 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330214a-cd1f-4bf9-9979-4349672959bc] Receive server push request, request = NotifySubscriberRequest, requestId = 114
12:33:20.285 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330214a-cd1f-4bf9-9979-4349672959bc] Ack server push request, request = NotifySubscriberRequest, requestId = 114
12:33:30.016 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:33:30.016 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:33:30.022 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:33:30.022 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:33:30.026 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
12:33:30.032 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:33:30.033 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:33:30.033 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
12:33:30.034 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
12:33:30.034 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:15:00.287 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:15:00.287 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:15:00.633 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:15:00.633 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4e0cd81[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:15:00.633 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754022799527_127.0.0.1_2437
14:15:00.637 [nacos-grpc-client-executor-1230] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754022799527_127.0.0.1_2437]Ignore complete event,isRunning:false,isAbandon=false
14:15:00.637 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@17b949d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1231]
14:15:00.785 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:15:00.785 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
14:15:00.786 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
14:15:00.786 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:15:00.786 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:15:00.786 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:15:06.023 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:15:06.596 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of eab68f36-02d7-4e5e-9072-8fc0d1370d99_config-0
14:15:06.647 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 6 values 
14:15:06.678 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 4 keys and 9 values 
14:15:06.692 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
14:15:06.696 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 1 ms to scan 1 urls, producing 1 keys and 5 values 
14:15:06.696 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
14:15:06.713 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
14:15:06.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab68f36-02d7-4e5e-9072-8fc0d1370d99_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:15:06.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab68f36-02d7-4e5e-9072-8fc0d1370d99_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000197c53cdaf0
14:15:06.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab68f36-02d7-4e5e-9072-8fc0d1370d99_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000197c53cdd10
14:15:06.717 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab68f36-02d7-4e5e-9072-8fc0d1370d99_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:15:06.717 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab68f36-02d7-4e5e-9072-8fc0d1370d99_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:15:06.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab68f36-02d7-4e5e-9072-8fc0d1370d99_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:15:07.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab68f36-02d7-4e5e-9072-8fc0d1370d99_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754028907278_127.0.0.1_2887
14:15:07.473 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab68f36-02d7-4e5e-9072-8fc0d1370d99_config-0] Notify connected event to listeners.
14:15:07.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab68f36-02d7-4e5e-9072-8fc0d1370d99_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:15:07.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eab68f36-02d7-4e5e-9072-8fc0d1370d99_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000197c5507cb0
14:15:07.565 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:15:10.366 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:15:10.367 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:15:10.368 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:15:10.508 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:15:11.736 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:15:11.738 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:15:11.738 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:15:17.476 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:15:20.437 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2cc95f46-c1bf-49db-8c02-b70c72956406
14:15:20.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cc95f46-c1bf-49db-8c02-b70c72956406] RpcClient init label, labels = {module=naming, source=sdk}
14:15:20.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cc95f46-c1bf-49db-8c02-b70c72956406] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:15:20.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cc95f46-c1bf-49db-8c02-b70c72956406] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:15:20.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cc95f46-c1bf-49db-8c02-b70c72956406] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:15:20.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cc95f46-c1bf-49db-8c02-b70c72956406] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:15:20.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cc95f46-c1bf-49db-8c02-b70c72956406] Success to connect to server [localhost:8848] on start up, connectionId = 1754028920450_127.0.0.1_2946
14:15:20.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cc95f46-c1bf-49db-8c02-b70c72956406] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:15:20.565 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cc95f46-c1bf-49db-8c02-b70c72956406] Notify connected event to listeners.
14:15:20.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cc95f46-c1bf-49db-8c02-b70c72956406] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000197c5507cb0
14:15:20.615 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:15:20.641 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:15:20.770 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.271 seconds (JVM running for 16.2)
14:15:20.785 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:15:20.785 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:15:20.786 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:15:21.142 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cc95f46-c1bf-49db-8c02-b70c72956406] Receive server push request, request = NotifySubscriberRequest, requestId = 123
14:15:21.157 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2cc95f46-c1bf-49db-8c02-b70c72956406] Ack server push request, request = NotifySubscriberRequest, requestId = 123
14:15:21.233 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:16:30.174 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:16:30.174 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:48:41.553 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:48:41.559 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:48:41.882 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:48:41.882 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4b4b413f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:48:41.882 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754028920450_127.0.0.1_2946
14:48:41.885 [nacos-grpc-client-executor-411] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754028920450_127.0.0.1_2946]Ignore complete event,isRunning:false,isAbandon=false
14:48:41.885 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2c213c73[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 412]
14:48:42.018 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:48:42.021 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:48:42.023 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:48:42.023 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:48:42.023 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:48:42.023 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:48:47.736 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:48:48.261 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 027dadcf-1c15-42ca-b2ef-4a094fd064ec_config-0
14:48:48.312 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
14:48:48.339 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
14:48:48.345 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
14:48:48.351 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
14:48:48.357 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
14:48:48.363 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
14:48:48.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [027dadcf-1c15-42ca-b2ef-4a094fd064ec_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:48:48.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [027dadcf-1c15-42ca-b2ef-4a094fd064ec_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000020eca39ef80
14:48:48.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [027dadcf-1c15-42ca-b2ef-4a094fd064ec_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000020eca39f1a0
14:48:48.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [027dadcf-1c15-42ca-b2ef-4a094fd064ec_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:48:48.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [027dadcf-1c15-42ca-b2ef-4a094fd064ec_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:48:48.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [027dadcf-1c15-42ca-b2ef-4a094fd064ec_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:48:49.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [027dadcf-1c15-42ca-b2ef-4a094fd064ec_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754030928869_127.0.0.1_10602
14:48:49.053 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [027dadcf-1c15-42ca-b2ef-4a094fd064ec_config-0] Notify connected event to listeners.
14:48:49.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [027dadcf-1c15-42ca-b2ef-4a094fd064ec_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:48:49.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [027dadcf-1c15-42ca-b2ef-4a094fd064ec_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020eca518fb0
14:48:49.139 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:48:51.610 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:48:51.610 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:48:51.610 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:48:51.733 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:48:52.259 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:48:52.261 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:48:52.261 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:48:57.334 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:48:59.607 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b0fe2659-4de7-4c3a-966b-6ec6b17f27eb
14:48:59.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0fe2659-4de7-4c3a-966b-6ec6b17f27eb] RpcClient init label, labels = {module=naming, source=sdk}
14:48:59.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0fe2659-4de7-4c3a-966b-6ec6b17f27eb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:48:59.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0fe2659-4de7-4c3a-966b-6ec6b17f27eb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:48:59.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0fe2659-4de7-4c3a-966b-6ec6b17f27eb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:48:59.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0fe2659-4de7-4c3a-966b-6ec6b17f27eb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:48:59.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0fe2659-4de7-4c3a-966b-6ec6b17f27eb] Success to connect to server [localhost:8848] on start up, connectionId = 1754030939620_127.0.0.1_10649
14:48:59.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0fe2659-4de7-4c3a-966b-6ec6b17f27eb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:48:59.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0fe2659-4de7-4c3a-966b-6ec6b17f27eb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020eca518fb0
14:48:59.730 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0fe2659-4de7-4c3a-966b-6ec6b17f27eb] Notify connected event to listeners.
14:48:59.770 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:48:59.793 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:48:59.878 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.674 seconds (JVM running for 13.667)
14:48:59.891 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:48:59.891 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:48:59.891 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:49:00.282 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:49:00.323 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0fe2659-4de7-4c3a-966b-6ec6b17f27eb] Receive server push request, request = NotifySubscriberRequest, requestId = 133
14:49:00.339 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0fe2659-4de7-4c3a-966b-6ec6b17f27eb] Ack server push request, request = NotifySubscriberRequest, requestId = 133
14:49:04.817 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:49:04.817 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:09:30.948 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:09:30.948 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:09:31.281 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:09:31.281 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4803fba3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:09:31.281 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754030939620_127.0.0.1_10649
15:09:31.281 [nacos-grpc-client-executor-260] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754030939620_127.0.0.1_10649]Ignore complete event,isRunning:false,isAbandon=false
15:09:31.281 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7ddbfb40[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 261]
15:09:31.417 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:09:31.417 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:09:31.417 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:09:31.417 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:09:31.417 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:09:31.417 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:09:36.178 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:09:36.723 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7d3e9255-1edc-4ae9-81bb-8fc337c30cb0_config-0
15:09:36.772 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 6 values 
15:09:36.797 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
15:09:36.802 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
15:09:36.808 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
15:09:36.814 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
15:09:36.820 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
15:09:36.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d3e9255-1edc-4ae9-81bb-8fc337c30cb0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:09:36.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d3e9255-1edc-4ae9-81bb-8fc337c30cb0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000242203a5af0
15:09:36.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d3e9255-1edc-4ae9-81bb-8fc337c30cb0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000242203a5d10
15:09:36.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d3e9255-1edc-4ae9-81bb-8fc337c30cb0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:09:36.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d3e9255-1edc-4ae9-81bb-8fc337c30cb0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:09:36.830 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d3e9255-1edc-4ae9-81bb-8fc337c30cb0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:09:37.524 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d3e9255-1edc-4ae9-81bb-8fc337c30cb0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754032177302_127.0.0.1_14623
15:09:37.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d3e9255-1edc-4ae9-81bb-8fc337c30cb0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:09:37.525 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d3e9255-1edc-4ae9-81bb-8fc337c30cb0_config-0] Notify connected event to listeners.
15:09:37.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d3e9255-1edc-4ae9-81bb-8fc337c30cb0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024220507b88
15:09:37.605 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:09:39.934 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:09:39.934 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:09:39.934 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:09:40.049 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:09:40.868 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:09:40.870 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:09:40.870 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:09:46.390 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:09:48.451 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e5cc5072-3f1d-42c1-980f-ed518b25c7ff
15:09:48.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5cc5072-3f1d-42c1-980f-ed518b25c7ff] RpcClient init label, labels = {module=naming, source=sdk}
15:09:48.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5cc5072-3f1d-42c1-980f-ed518b25c7ff] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:09:48.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5cc5072-3f1d-42c1-980f-ed518b25c7ff] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:09:48.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5cc5072-3f1d-42c1-980f-ed518b25c7ff] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:09:48.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5cc5072-3f1d-42c1-980f-ed518b25c7ff] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:09:48.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5cc5072-3f1d-42c1-980f-ed518b25c7ff] Success to connect to server [localhost:8848] on start up, connectionId = 1754032188460_127.0.0.1_14679
15:09:48.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5cc5072-3f1d-42c1-980f-ed518b25c7ff] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:09:48.578 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5cc5072-3f1d-42c1-980f-ed518b25c7ff] Notify connected event to listeners.
15:09:48.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5cc5072-3f1d-42c1-980f-ed518b25c7ff] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024220507b88
15:09:48.609 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:09:48.625 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:09:48.720 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.062 seconds (JVM running for 13.906)
15:09:48.720 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:09:48.720 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:09:48.720 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:09:49.079 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:09:49.197 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5cc5072-3f1d-42c1-980f-ed518b25c7ff] Receive server push request, request = NotifySubscriberRequest, requestId = 138
15:09:49.215 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5cc5072-3f1d-42c1-980f-ed518b25c7ff] Ack server push request, request = NotifySubscriberRequest, requestId = 138
15:09:52.507 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:09:52.507 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:19:54.948 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:19:54.948 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:19:55.287 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:19:55.287 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@39491d9d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:19:55.288 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754032188460_127.0.0.1_14679
15:19:55.289 [nacos-grpc-client-executor-133] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754032188460_127.0.0.1_14679]Ignore complete event,isRunning:false,isAbandon=false
15:19:55.291 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3b6fe9b9[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 134]
15:19:55.418 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:19:55.422 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:19:55.423 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:19:55.423 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:19:55.423 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:19:55.423 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:20:00.269 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:20:00.790 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8bd5c1aa-0dae-41c4-ae5b-da505ddb6f27_config-0
15:20:00.841 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 6 values 
15:20:00.866 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
15:20:00.872 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
15:20:00.878 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
15:20:00.883 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
15:20:00.892 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
15:20:00.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bd5c1aa-0dae-41c4-ae5b-da505ddb6f27_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:20:00.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bd5c1aa-0dae-41c4-ae5b-da505ddb6f27_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000016bcf3be480
15:20:00.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bd5c1aa-0dae-41c4-ae5b-da505ddb6f27_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000016bcf3be6a0
15:20:00.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bd5c1aa-0dae-41c4-ae5b-da505ddb6f27_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:20:00.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bd5c1aa-0dae-41c4-ae5b-da505ddb6f27_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:20:00.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bd5c1aa-0dae-41c4-ae5b-da505ddb6f27_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:20:01.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bd5c1aa-0dae-41c4-ae5b-da505ddb6f27_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754032801393_127.0.0.1_2414
15:20:01.575 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bd5c1aa-0dae-41c4-ae5b-da505ddb6f27_config-0] Notify connected event to listeners.
15:20:01.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bd5c1aa-0dae-41c4-ae5b-da505ddb6f27_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:20:01.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bd5c1aa-0dae-41c4-ae5b-da505ddb6f27_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000016bcf4f8228
15:20:01.649 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:20:04.000 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:20:04.000 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:20:04.000 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:20:04.105 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:20:04.686 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:20:04.687 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:20:04.687 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:20:09.776 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:20:11.823 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-e23e-4b60-af26-63b08e699851
15:20:11.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-e23e-4b60-af26-63b08e699851] RpcClient init label, labels = {module=naming, source=sdk}
15:20:11.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-e23e-4b60-af26-63b08e699851] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:20:11.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-e23e-4b60-af26-63b08e699851] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:20:11.825 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-e23e-4b60-af26-63b08e699851] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:20:11.825 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-e23e-4b60-af26-63b08e699851] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:20:11.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-e23e-4b60-af26-63b08e699851] Success to connect to server [localhost:8848] on start up, connectionId = 1754032811832_127.0.0.1_2512
15:20:11.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-e23e-4b60-af26-63b08e699851] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:20:11.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-e23e-4b60-af26-63b08e699851] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000016bcf4f8228
15:20:11.952 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-e23e-4b60-af26-63b08e699851] Notify connected event to listeners.
15:20:11.988 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:20:12.007 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:20:12.104 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.295 seconds (JVM running for 13.123)
15:20:12.115 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:20:12.115 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:20:12.115 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:20:12.505 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:20:12.515 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-e23e-4b60-af26-63b08e699851] Receive server push request, request = NotifySubscriberRequest, requestId = 151
15:20:12.530 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-e23e-4b60-af26-63b08e699851] Ack server push request, request = NotifySubscriberRequest, requestId = 151
15:20:23.039 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:20:23.040 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:03:51.871 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:03:51.871 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:03:52.222 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:03:52.222 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5f2c1eb5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:03:52.222 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754032811832_127.0.0.1_2512
16:03:52.222 [nacos-grpc-client-executor-535] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754032811832_127.0.0.1_2512]Ignore complete event,isRunning:false,isAbandon=false
16:03:52.222 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@425ab43e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 536]
16:03:52.398 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:03:52.412 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:03:52.428 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:03:52.428 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:03:52.428 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:03:52.428 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:03:57.332 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:03:57.866 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 56c3d7c6-80f6-4d9e-bb51-d9cdf7b5e083_config-0
16:03:57.919 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 6 values 
16:03:57.947 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
16:03:57.953 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
16:03:57.959 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
16:03:57.969 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
16:03:57.975 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
16:03:57.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c3d7c6-80f6-4d9e-bb51-d9cdf7b5e083_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:03:57.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c3d7c6-80f6-4d9e-bb51-d9cdf7b5e083_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000222683a5f80
16:03:57.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c3d7c6-80f6-4d9e-bb51-d9cdf7b5e083_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000222683a61a0
16:03:57.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c3d7c6-80f6-4d9e-bb51-d9cdf7b5e083_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:03:57.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c3d7c6-80f6-4d9e-bb51-d9cdf7b5e083_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:03:57.986 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c3d7c6-80f6-4d9e-bb51-d9cdf7b5e083_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:03:58.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c3d7c6-80f6-4d9e-bb51-d9cdf7b5e083_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754035438681_127.0.0.1_12049
16:03:58.924 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c3d7c6-80f6-4d9e-bb51-d9cdf7b5e083_config-0] Notify connected event to listeners.
16:03:58.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c3d7c6-80f6-4d9e-bb51-d9cdf7b5e083_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:03:58.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [56c3d7c6-80f6-4d9e-bb51-d9cdf7b5e083_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022268508228
16:03:59.041 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:04:01.520 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:04:01.520 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:04:01.520 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:04:01.634 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:04:02.277 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:04:02.277 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:04:02.277 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:04:07.664 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:04:09.748 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7b21a9b1-b8ef-4e1a-b7fb-53870cc5efe6
16:04:09.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b21a9b1-b8ef-4e1a-b7fb-53870cc5efe6] RpcClient init label, labels = {module=naming, source=sdk}
16:04:09.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b21a9b1-b8ef-4e1a-b7fb-53870cc5efe6] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:04:09.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b21a9b1-b8ef-4e1a-b7fb-53870cc5efe6] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:04:09.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b21a9b1-b8ef-4e1a-b7fb-53870cc5efe6] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:04:09.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b21a9b1-b8ef-4e1a-b7fb-53870cc5efe6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:04:09.874 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b21a9b1-b8ef-4e1a-b7fb-53870cc5efe6] Success to connect to server [localhost:8848] on start up, connectionId = 1754035449758_127.0.0.1_12116
16:04:09.874 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b21a9b1-b8ef-4e1a-b7fb-53870cc5efe6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:04:09.874 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b21a9b1-b8ef-4e1a-b7fb-53870cc5efe6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022268508228
16:04:09.874 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b21a9b1-b8ef-4e1a-b7fb-53870cc5efe6] Notify connected event to listeners.
16:04:09.915 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:04:09.937 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:04:10.019 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.192 seconds (JVM running for 13.992)
16:04:10.030 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:04:10.030 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:04:10.031 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:04:10.168 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:04:10.457 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b21a9b1-b8ef-4e1a-b7fb-53870cc5efe6] Receive server push request, request = NotifySubscriberRequest, requestId = 159
16:04:10.474 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b21a9b1-b8ef-4e1a-b7fb-53870cc5efe6] Ack server push request, request = NotifySubscriberRequest, requestId = 159
16:04:25.089 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:04:25.089 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:08:58.960 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:08:58.963 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:08:59.278 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:08:59.278 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@417edc4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:08:59.278 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754035449758_127.0.0.1_12116
16:08:59.280 [nacos-grpc-client-executor-70] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754035449758_127.0.0.1_12116]Ignore complete event,isRunning:false,isAbandon=false
16:08:59.282 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4ef9e9da[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 71]
16:08:59.414 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:08:59.416 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:08:59.423 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:08:59.424 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:08:59.424 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:08:59.424 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:09:04.536 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:09:05.082 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f4bd923f-1ed1-4c1d-a1fa-a7c2163220ba_config-0
16:09:05.136 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
16:09:05.165 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
16:09:05.173 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
16:09:05.181 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
16:09:05.189 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
16:09:05.195 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
16:09:05.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4bd923f-1ed1-4c1d-a1fa-a7c2163220ba_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:09:05.198 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4bd923f-1ed1-4c1d-a1fa-a7c2163220ba_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001cda83beaf8
16:09:05.198 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4bd923f-1ed1-4c1d-a1fa-a7c2163220ba_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001cda83bed18
16:09:05.198 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4bd923f-1ed1-4c1d-a1fa-a7c2163220ba_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:09:05.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4bd923f-1ed1-4c1d-a1fa-a7c2163220ba_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:09:05.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4bd923f-1ed1-4c1d-a1fa-a7c2163220ba_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:09:05.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4bd923f-1ed1-4c1d-a1fa-a7c2163220ba_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754035745769_127.0.0.1_13650
16:09:05.970 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4bd923f-1ed1-4c1d-a1fa-a7c2163220ba_config-0] Notify connected event to listeners.
16:09:05.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4bd923f-1ed1-4c1d-a1fa-a7c2163220ba_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:09:05.971 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4bd923f-1ed1-4c1d-a1fa-a7c2163220ba_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001cda84f8668
16:09:06.068 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:09:09.541 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:09:09.542 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:09:09.542 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:09:09.706 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:09:10.593 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:09:10.593 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:09:10.594 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:09:20.098 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:09:22.860 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9c8b562a-7a0a-47bc-bc55-e765e5595e41
16:09:22.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8b562a-7a0a-47bc-bc55-e765e5595e41] RpcClient init label, labels = {module=naming, source=sdk}
16:09:22.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8b562a-7a0a-47bc-bc55-e765e5595e41] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:09:22.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8b562a-7a0a-47bc-bc55-e765e5595e41] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:09:22.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8b562a-7a0a-47bc-bc55-e765e5595e41] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:09:22.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8b562a-7a0a-47bc-bc55-e765e5595e41] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:09:22.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8b562a-7a0a-47bc-bc55-e765e5595e41] Success to connect to server [localhost:8848] on start up, connectionId = 1754035762871_127.0.0.1_13740
16:09:22.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8b562a-7a0a-47bc-bc55-e765e5595e41] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:09:22.983 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8b562a-7a0a-47bc-bc55-e765e5595e41] Notify connected event to listeners.
16:09:22.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8b562a-7a0a-47bc-bc55-e765e5595e41] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001cda84f8668
16:09:23.028 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:09:23.052 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:09:23.161 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.128 seconds (JVM running for 19.951)
16:09:23.174 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:09:23.174 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:09:23.174 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:09:23.466 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:09:23.577 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8b562a-7a0a-47bc-bc55-e765e5595e41] Receive server push request, request = NotifySubscriberRequest, requestId = 167
16:09:23.593 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8b562a-7a0a-47bc-bc55-e765e5595e41] Ack server push request, request = NotifySubscriberRequest, requestId = 167
16:09:28.753 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:09:28.754 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:39:39.817 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:39:39.817 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:39:40.163 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:39:40.165 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@727a29c5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:39:40.165 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754035762871_127.0.0.1_13740
16:39:40.171 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8b562a-7a0a-47bc-bc55-e765e5595e41] Notify disconnected event to listeners
16:39:40.169 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@70ebcb40[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 316]
16:39:40.317 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:39:40.317 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:39:40.317 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:39:40.317 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:39:40.317 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:39:40.317 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:39:49.095 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:39:50.125 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 873eb9a1-0eba-4655-8a7b-a23de0879882_config-0
16:39:50.212 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
16:39:50.264 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
16:39:50.279 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
16:39:50.299 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
16:39:50.313 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
16:39:50.322 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
16:39:50.326 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [873eb9a1-0eba-4655-8a7b-a23de0879882_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:39:50.326 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [873eb9a1-0eba-4655-8a7b-a23de0879882_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000220cb3b6480
16:39:50.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [873eb9a1-0eba-4655-8a7b-a23de0879882_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000220cb3b66a0
16:39:50.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [873eb9a1-0eba-4655-8a7b-a23de0879882_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:39:50.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [873eb9a1-0eba-4655-8a7b-a23de0879882_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:39:50.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [873eb9a1-0eba-4655-8a7b-a23de0879882_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:39:51.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [873eb9a1-0eba-4655-8a7b-a23de0879882_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754037591125_127.0.0.1_4895
16:39:51.350 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [873eb9a1-0eba-4655-8a7b-a23de0879882_config-0] Notify connected event to listeners.
16:39:51.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [873eb9a1-0eba-4655-8a7b-a23de0879882_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:51.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [873eb9a1-0eba-4655-8a7b-a23de0879882_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000220cb4f08c8
16:39:51.450 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:39:54.268 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:39:54.268 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:39:54.268 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:39:54.402 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:39:55.051 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:39:55.053 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:39:55.053 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:40:01.148 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:40:03.761 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0cbff855-**************-16e2fcf7e365
16:40:03.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cbff855-**************-16e2fcf7e365] RpcClient init label, labels = {module=naming, source=sdk}
16:40:03.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cbff855-**************-16e2fcf7e365] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:40:03.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cbff855-**************-16e2fcf7e365] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:40:03.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cbff855-**************-16e2fcf7e365] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:40:03.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cbff855-**************-16e2fcf7e365] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:40:03.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cbff855-**************-16e2fcf7e365] Success to connect to server [localhost:8848] on start up, connectionId = 1754037603772_127.0.0.1_4935
16:40:03.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cbff855-**************-16e2fcf7e365] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:40:03.885 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cbff855-**************-16e2fcf7e365] Notify connected event to listeners.
16:40:03.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cbff855-**************-16e2fcf7e365] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000220cb4f08c8
16:40:03.933 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:40:03.954 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:40:04.053 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.022 seconds (JVM running for 17.941)
16:40:04.064 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:40:04.071 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:40:04.071 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:40:04.465 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cbff855-**************-16e2fcf7e365] Receive server push request, request = NotifySubscriberRequest, requestId = 176
16:40:04.487 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cbff855-**************-16e2fcf7e365] Ack server push request, request = NotifySubscriberRequest, requestId = 176
16:40:11.739 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:40:13.104 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:40:13.104 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:40:13.113 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:40:13.117 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:40:13.121 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:40:13.121 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:41:01.245 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:41:01.247 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:41:01.594 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:41:01.597 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@743649ca[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:41:01.597 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754037603772_127.0.0.1_4935
16:41:01.597 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754037603772_127.0.0.1_4935]Ignore complete event,isRunning:false,isAbandon=false
16:41:01.603 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@259421f9[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 21]
16:41:01.749 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:41:01.749 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:41:01.749 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:41:01.749 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:41:01.749 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:41:01.749 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:41:06.879 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:41:07.414 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c73a7fe6-7c91-48c9-a193-4aa0f97ba071_config-0
16:41:07.473 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 3 keys and 6 values 
16:41:07.501 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
16:41:07.508 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
16:41:07.516 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
16:41:07.522 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
16:41:07.529 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
16:41:07.532 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73a7fe6-7c91-48c9-a193-4aa0f97ba071_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:41:07.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73a7fe6-7c91-48c9-a193-4aa0f97ba071_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023bb139eaf8
16:41:07.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73a7fe6-7c91-48c9-a193-4aa0f97ba071_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000023bb139ed18
16:41:07.537 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73a7fe6-7c91-48c9-a193-4aa0f97ba071_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:41:07.538 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73a7fe6-7c91-48c9-a193-4aa0f97ba071_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:41:07.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73a7fe6-7c91-48c9-a193-4aa0f97ba071_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:41:08.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73a7fe6-7c91-48c9-a193-4aa0f97ba071_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754037668079_127.0.0.1_5189
16:41:08.265 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73a7fe6-7c91-48c9-a193-4aa0f97ba071_config-0] Notify connected event to listeners.
16:41:08.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73a7fe6-7c91-48c9-a193-4aa0f97ba071_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:41:08.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c73a7fe6-7c91-48c9-a193-4aa0f97ba071_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023bb1518668
16:41:08.342 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:41:10.874 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:41:10.874 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:41:10.874 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:41:11.007 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:41:11.596 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:41:11.597 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:41:11.598 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:41:17.435 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:41:19.930 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d94e65a2-9f97-4c44-9754-9a0593246d47
16:41:19.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d94e65a2-9f97-4c44-9754-9a0593246d47] RpcClient init label, labels = {module=naming, source=sdk}
16:41:19.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d94e65a2-9f97-4c44-9754-9a0593246d47] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:41:19.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d94e65a2-9f97-4c44-9754-9a0593246d47] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:41:19.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d94e65a2-9f97-4c44-9754-9a0593246d47] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:41:19.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d94e65a2-9f97-4c44-9754-9a0593246d47] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:41:20.052 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d94e65a2-9f97-4c44-9754-9a0593246d47] Success to connect to server [localhost:8848] on start up, connectionId = 1754037679942_127.0.0.1_5265
16:41:20.053 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d94e65a2-9f97-4c44-9754-9a0593246d47] Notify connected event to listeners.
16:41:20.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d94e65a2-9f97-4c44-9754-9a0593246d47] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:41:20.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d94e65a2-9f97-4c44-9754-9a0593246d47] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023bb1518668
16:41:20.091 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:41:20.115 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:41:20.224 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.809 seconds (JVM running for 14.732)
16:41:20.238 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:41:20.239 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:41:20.239 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:41:20.477 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:41:20.595 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d94e65a2-9f97-4c44-9754-9a0593246d47] Receive server push request, request = NotifySubscriberRequest, requestId = 186
16:41:20.606 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d94e65a2-9f97-4c44-9754-9a0593246d47] Ack server push request, request = NotifySubscriberRequest, requestId = 186
16:41:31.269 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:41:31.269 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:41:31.280 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:41:31.282 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:41:31.282 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:41:31.287 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:41:31.287 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:41:31.288 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:41:31.288 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:41:31.289 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:29:18.592 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:29:18.597 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:29:18.922 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:29:18.922 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@412f1cb7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:29:18.922 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754037679942_127.0.0.1_5265
17:29:18.926 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5e7376d6[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 589]
17:29:19.067 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:29:19.068 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
17:29:19.069 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
17:29:19.069 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:29:19.069 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:29:19.070 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:29:24.044 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:29:24.590 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 241c46d0-e7a8-4c37-b30b-62c7158f672c_config-0
17:29:24.641 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
17:29:24.668 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
17:29:24.674 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
17:29:24.681 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
17:29:24.687 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
17:29:24.693 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
17:29:24.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [241c46d0-e7a8-4c37-b30b-62c7158f672c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:29:24.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [241c46d0-e7a8-4c37-b30b-62c7158f672c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000022a013bdf80
17:29:24.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [241c46d0-e7a8-4c37-b30b-62c7158f672c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000022a013be1a0
17:29:24.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [241c46d0-e7a8-4c37-b30b-62c7158f672c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:29:24.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [241c46d0-e7a8-4c37-b30b-62c7158f672c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:29:24.701 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [241c46d0-e7a8-4c37-b30b-62c7158f672c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:29:25.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [241c46d0-e7a8-4c37-b30b-62c7158f672c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754040565306_127.0.0.1_14327
17:29:25.490 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [241c46d0-e7a8-4c37-b30b-62c7158f672c_config-0] Notify connected event to listeners.
17:29:25.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [241c46d0-e7a8-4c37-b30b-62c7158f672c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:29:25.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [241c46d0-e7a8-4c37-b30b-62c7158f672c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022a014f8668
17:29:25.566 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:29:28.033 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:29:28.033 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:29:28.033 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:29:28.146 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:29:28.890 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:29:28.891 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:29:28.892 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:29:34.563 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:29:36.800 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-9a33-4bd5-9de2-78adcc4fe605
17:29:36.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9a33-4bd5-9de2-78adcc4fe605] RpcClient init label, labels = {module=naming, source=sdk}
17:29:36.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9a33-4bd5-9de2-78adcc4fe605] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:29:36.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9a33-4bd5-9de2-78adcc4fe605] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:29:36.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9a33-4bd5-9de2-78adcc4fe605] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:29:36.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9a33-4bd5-9de2-78adcc4fe605] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:29:36.936 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9a33-4bd5-9de2-78adcc4fe605] Success to connect to server [localhost:8848] on start up, connectionId = 1754040576812_127.0.0.1_14343
17:29:36.936 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9a33-4bd5-9de2-78adcc4fe605] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:29:36.936 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9a33-4bd5-9de2-78adcc4fe605] Notify connected event to listeners.
17:29:36.937 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9a33-4bd5-9de2-78adcc4fe605] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022a014f8668
17:29:36.983 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:29:37.009 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:29:37.094 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.537 seconds (JVM running for 14.418)
17:29:37.104 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:29:37.104 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:29:37.105 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:29:37.515 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9a33-4bd5-9de2-78adcc4fe605] Receive server push request, request = NotifySubscriberRequest, requestId = 196
17:29:37.538 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9a33-4bd5-9de2-78adcc4fe605] Ack server push request, request = NotifySubscriberRequest, requestId = 196
17:29:37.548 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:29:45.040 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:29:45.041 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
17:29:45.041 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:29:45.043 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:29:45.048 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:29:45.048 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:31:57.835 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:31:57.838 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:31:58.162 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:31:58.162 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4649bb37[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:31:58.162 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754040576812_127.0.0.1_14343
17:31:58.165 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754040576812_127.0.0.1_14343]Ignore complete event,isRunning:false,isAbandon=false
17:31:58.169 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@226dd334[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 39]
17:31:58.321 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:31:58.322 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
17:31:58.323 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
17:31:58.323 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:31:58.323 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:31:58.324 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:32:03.402 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:32:03.978 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4f3ea347-086b-4110-912d-96d2f9e62a1a_config-0
17:32:04.037 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 3 keys and 6 values 
17:32:04.069 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
17:32:04.079 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
17:32:04.088 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
17:32:04.095 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
17:32:04.101 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
17:32:04.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f3ea347-086b-4110-912d-96d2f9e62a1a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:32:04.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f3ea347-086b-4110-912d-96d2f9e62a1a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001e96139e8d8
17:32:04.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f3ea347-086b-4110-912d-96d2f9e62a1a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001e96139eaf8
17:32:04.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f3ea347-086b-4110-912d-96d2f9e62a1a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:32:04.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f3ea347-086b-4110-912d-96d2f9e62a1a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:32:04.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f3ea347-086b-4110-912d-96d2f9e62a1a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:32:04.807 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f3ea347-086b-4110-912d-96d2f9e62a1a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754040724623_127.0.0.1_14532
17:32:04.808 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f3ea347-086b-4110-912d-96d2f9e62a1a_config-0] Notify connected event to listeners.
17:32:04.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f3ea347-086b-4110-912d-96d2f9e62a1a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:32:04.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f3ea347-086b-4110-912d-96d2f9e62a1a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e961518440
17:32:04.898 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:32:07.623 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:32:07.624 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:32:07.624 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:32:07.781 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:32:08.917 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:32:08.918 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:32:08.919 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:32:14.558 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:32:17.032 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0ebb1f54-8603-4a37-8557-d2436c837c7e
17:32:17.033 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebb1f54-8603-4a37-8557-d2436c837c7e] RpcClient init label, labels = {module=naming, source=sdk}
17:32:17.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebb1f54-8603-4a37-8557-d2436c837c7e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:32:17.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebb1f54-8603-4a37-8557-d2436c837c7e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:32:17.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebb1f54-8603-4a37-8557-d2436c837c7e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:32:17.036 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebb1f54-8603-4a37-8557-d2436c837c7e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:32:17.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebb1f54-8603-4a37-8557-d2436c837c7e] Success to connect to server [localhost:8848] on start up, connectionId = 1754040737045_127.0.0.1_14549
17:32:17.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebb1f54-8603-4a37-8557-d2436c837c7e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:32:17.159 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebb1f54-8603-4a37-8557-d2436c837c7e] Notify connected event to listeners.
17:32:17.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebb1f54-8603-4a37-8557-d2436c837c7e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e961518440
17:32:17.202 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:32:17.227 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:32:17.338 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 14.454 seconds (JVM running for 15.346)
17:32:17.351 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:32:17.351 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:32:17.352 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:32:17.494 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:32:17.758 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebb1f54-8603-4a37-8557-d2436c837c7e] Receive server push request, request = NotifySubscriberRequest, requestId = 202
17:32:17.777 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebb1f54-8603-4a37-8557-d2436c837c7e] Ack server push request, request = NotifySubscriberRequest, requestId = 202
17:32:33.975 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
17:32:33.975 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:32:34.007 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:32:34.023 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
17:32:34.023 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
17:32:34.023 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:33:56.879 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:33:56.882 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:33:57.211 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:33:57.211 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6c6af0d6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:33:57.212 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754040737045_127.0.0.1_14549
17:33:57.214 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754040737045_127.0.0.1_14549]Ignore complete event,isRunning:false,isAbandon=false
17:33:57.219 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4bfe8c35[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 31]
17:33:57.372 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:33:57.372 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:33:57.374 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:33:57.374 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:33:57.374 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:33:57.375 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:34:02.338 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:34:02.913 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2312f63d-6631-4150-bc0a-3e7683d4e90a_config-0
17:34:02.970 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
17:34:03.007 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
17:34:03.013 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
17:34:03.020 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
17:34:03.030 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
17:34:03.041 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
17:34:03.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2312f63d-6631-4150-bc0a-3e7683d4e90a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:34:03.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2312f63d-6631-4150-bc0a-3e7683d4e90a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f3cf39eaf8
17:34:03.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2312f63d-6631-4150-bc0a-3e7683d4e90a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001f3cf39ed18
17:34:03.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2312f63d-6631-4150-bc0a-3e7683d4e90a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:34:03.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2312f63d-6631-4150-bc0a-3e7683d4e90a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:34:03.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2312f63d-6631-4150-bc0a-3e7683d4e90a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:34:03.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2312f63d-6631-4150-bc0a-3e7683d4e90a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754040843586_127.0.0.1_14725
17:34:03.766 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2312f63d-6631-4150-bc0a-3e7683d4e90a_config-0] Notify connected event to listeners.
17:34:03.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2312f63d-6631-4150-bc0a-3e7683d4e90a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:34:03.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2312f63d-6631-4150-bc0a-3e7683d4e90a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f3cf518668
17:34:03.846 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:34:06.312 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:34:06.312 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:34:06.312 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:34:06.423 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:34:06.934 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:34:06.935 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:34:06.935 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:34:12.242 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:34:14.534 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8f296e6a-0bd4-488c-911e-70d70064e623
17:34:14.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f296e6a-0bd4-488c-911e-70d70064e623] RpcClient init label, labels = {module=naming, source=sdk}
17:34:14.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f296e6a-0bd4-488c-911e-70d70064e623] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:34:14.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f296e6a-0bd4-488c-911e-70d70064e623] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:34:14.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f296e6a-0bd4-488c-911e-70d70064e623] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:34:14.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f296e6a-0bd4-488c-911e-70d70064e623] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:34:14.653 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f296e6a-0bd4-488c-911e-70d70064e623] Success to connect to server [localhost:8848] on start up, connectionId = 1754040854543_127.0.0.1_14746
17:34:14.653 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f296e6a-0bd4-488c-911e-70d70064e623] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:34:14.653 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f296e6a-0bd4-488c-911e-70d70064e623] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f3cf518668
17:34:14.653 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f296e6a-0bd4-488c-911e-70d70064e623] Notify connected event to listeners.
17:34:14.696 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:34:14.720 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:34:14.816 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.979 seconds (JVM running for 13.878)
17:34:14.827 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:34:14.827 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:34:14.827 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:34:15.220 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f296e6a-0bd4-488c-911e-70d70064e623] Receive server push request, request = NotifySubscriberRequest, requestId = 213
17:34:15.234 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f296e6a-0bd4-488c-911e-70d70064e623] Ack server push request, request = NotifySubscriberRequest, requestId = 213
17:34:15.372 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:34:23.455 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
17:34:23.455 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:34:23.456 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:34:23.456 [http-nio-9600-exec-8] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
17:34:23.457 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
17:34:23.458 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
17:34:23.463 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
17:34:23.463 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:34:23.463 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:34:23.464 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:34:23.464 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:34:23.464 [http-nio-9600-exec-8] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
17:34:23.465 [http-nio-9600-exec-8] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
17:34:23.465 [http-nio-9600-exec-8] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:34:23.543 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-6} inited
17:34:23.543 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
17:34:23.543 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
17:34:23.545 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:56:19.276 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:56:19.278 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:56:19.615 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:56:19.616 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2b4ad731[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:56:19.616 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754040854543_127.0.0.1_14746
17:56:19.618 [nacos-grpc-client-executor-275] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754040854543_127.0.0.1_14746]Ignore complete event,isRunning:false,isAbandon=false
17:56:19.620 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5323966c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 276]
17:56:19.760 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:56:19.761 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-6} closing ...
17:56:19.762 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-6} closed
17:56:19.763 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:56:19.764 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:56:19.764 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:56:23.715 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:56:24.233 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9efd2c30-ec46-4e27-b3e3-0db83dd51139_config-0
17:56:24.283 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 
17:56:24.309 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
17:56:24.315 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
17:56:24.321 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
17:56:24.328 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
17:56:24.333 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
17:56:24.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9efd2c30-ec46-4e27-b3e3-0db83dd51139_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:56:24.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9efd2c30-ec46-4e27-b3e3-0db83dd51139_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001648d39eaf8
17:56:24.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9efd2c30-ec46-4e27-b3e3-0db83dd51139_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001648d39ed18
17:56:24.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9efd2c30-ec46-4e27-b3e3-0db83dd51139_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:56:24.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9efd2c30-ec46-4e27-b3e3-0db83dd51139_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:56:24.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9efd2c30-ec46-4e27-b3e3-0db83dd51139_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:56:24.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9efd2c30-ec46-4e27-b3e3-0db83dd51139_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754042184796_127.0.0.1_2502
17:56:24.973 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9efd2c30-ec46-4e27-b3e3-0db83dd51139_config-0] Notify connected event to listeners.
17:56:24.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9efd2c30-ec46-4e27-b3e3-0db83dd51139_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:56:24.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9efd2c30-ec46-4e27-b3e3-0db83dd51139_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001648d518668
17:56:25.049 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:56:27.385 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:56:27.385 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:56:27.386 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:56:27.497 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:56:28.047 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:56:28.048 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:56:28.048 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:56:32.948 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:56:34.957 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 645c2a1d-49a6-4388-b08d-2292f557d126
17:56:34.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [645c2a1d-49a6-4388-b08d-2292f557d126] RpcClient init label, labels = {module=naming, source=sdk}
17:56:34.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [645c2a1d-49a6-4388-b08d-2292f557d126] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:56:34.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [645c2a1d-49a6-4388-b08d-2292f557d126] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:56:34.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [645c2a1d-49a6-4388-b08d-2292f557d126] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:56:34.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [645c2a1d-49a6-4388-b08d-2292f557d126] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:56:35.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [645c2a1d-49a6-4388-b08d-2292f557d126] Success to connect to server [localhost:8848] on start up, connectionId = 1754042194967_127.0.0.1_2515
17:56:35.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [645c2a1d-49a6-4388-b08d-2292f557d126] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:56:35.078 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [645c2a1d-49a6-4388-b08d-2292f557d126] Notify connected event to listeners.
17:56:35.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [645c2a1d-49a6-4388-b08d-2292f557d126] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001648d518668
17:56:35.115 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:56:35.136 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:56:35.220 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 11.946 seconds (JVM running for 12.727)
17:56:35.231 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:56:35.231 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:56:35.231 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:56:35.490 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:56:35.660 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [645c2a1d-49a6-4388-b08d-2292f557d126] Receive server push request, request = NotifySubscriberRequest, requestId = 222
17:56:35.681 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [645c2a1d-49a6-4388-b08d-2292f557d126] Ack server push request, request = NotifySubscriberRequest, requestId = 222
17:57:55.170 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
17:57:55.170 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:57:55.186 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:57:55.187 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
17:57:55.187 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
17:57:55.187 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:23:28.526 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:23:28.530 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:23:28.868 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:23:28.868 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1d4c8f62[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:23:28.869 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754042194967_127.0.0.1_2515
18:23:28.870 [nacos-grpc-client-executor-290] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754042194967_127.0.0.1_2515]Ignore complete event,isRunning:false,isAbandon=false
18:23:28.872 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6382d45d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 291]
18:23:29.012 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:23:29.012 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:23:29.013 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:23:29.013 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:23:29.014 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:23:29.014 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:23:33.887 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:23:34.426 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f5492161-a60e-4568-b124-30b1257df581_config-0
18:23:34.479 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
18:23:34.504 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
18:23:34.510 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
18:23:34.516 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
18:23:34.523 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
18:23:34.532 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
18:23:34.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5492161-a60e-4568-b124-30b1257df581_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:23:34.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5492161-a60e-4568-b124-30b1257df581_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001bf933beaf8
18:23:34.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5492161-a60e-4568-b124-30b1257df581_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001bf933bed18
18:23:34.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5492161-a60e-4568-b124-30b1257df581_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:23:34.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5492161-a60e-4568-b124-30b1257df581_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:23:34.541 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5492161-a60e-4568-b124-30b1257df581_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:23:35.218 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5492161-a60e-4568-b124-30b1257df581_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754043815044_127.0.0.1_5452
18:23:35.219 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5492161-a60e-4568-b124-30b1257df581_config-0] Notify connected event to listeners.
18:23:35.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5492161-a60e-4568-b124-30b1257df581_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:23:35.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5492161-a60e-4568-b124-30b1257df581_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001bf934f8fb0
18:23:35.297 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:23:37.757 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:23:37.757 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:23:37.757 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:23:37.866 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:23:38.502 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:23:38.503 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:23:38.504 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:23:44.081 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:23:46.274 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e1d1c40b-1757-4677-acf6-301ebf1c4136
18:23:46.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1d1c40b-1757-4677-acf6-301ebf1c4136] RpcClient init label, labels = {module=naming, source=sdk}
18:23:46.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1d1c40b-1757-4677-acf6-301ebf1c4136] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:23:46.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1d1c40b-1757-4677-acf6-301ebf1c4136] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:23:46.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1d1c40b-1757-4677-acf6-301ebf1c4136] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:23:46.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1d1c40b-1757-4677-acf6-301ebf1c4136] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:23:46.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1d1c40b-1757-4677-acf6-301ebf1c4136] Success to connect to server [localhost:8848] on start up, connectionId = 1754043826287_127.0.0.1_5487
18:23:46.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1d1c40b-1757-4677-acf6-301ebf1c4136] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:23:46.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1d1c40b-1757-4677-acf6-301ebf1c4136] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001bf934f8fb0
18:23:46.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1d1c40b-1757-4677-acf6-301ebf1c4136] Notify connected event to listeners.
18:23:46.446 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:23:46.471 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:23:46.584 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.167 seconds (JVM running for 13.971)
18:23:46.596 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:23:46.596 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:23:46.596 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:23:46.998 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1d1c40b-1757-4677-acf6-301ebf1c4136] Receive server push request, request = NotifySubscriberRequest, requestId = 232
18:23:47.017 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1d1c40b-1757-4677-acf6-301ebf1c4136] Ack server push request, request = NotifySubscriberRequest, requestId = 232
18:23:47.144 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:24:00.356 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1d1c40b-1757-4677-acf6-301ebf1c4136] Receive server push request, request = NotifySubscriberRequest, requestId = 233
18:24:00.357 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1d1c40b-1757-4677-acf6-301ebf1c4136] Ack server push request, request = NotifySubscriberRequest, requestId = 233
18:24:01.107 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:24:01.108 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:24:01.110 [http-nio-9600-exec-8] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
18:24:01.113 [http-nio-9600-exec-8] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:24:01.123 [http-nio-9600-exec-8] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:24:01.124 [http-nio-9600-exec-8] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:29:20.486 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:29:20.489 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:29:20.818 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:29:20.818 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7d05f1d1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:29:20.818 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754043826287_127.0.0.1_5487
18:29:20.820 [nacos-grpc-client-executor-83] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754043826287_127.0.0.1_5487]Ignore complete event,isRunning:false,isAbandon=false
18:29:20.821 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@39f5ab11[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 84]
18:29:20.962 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:29:20.962 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
18:29:20.963 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
18:29:20.963 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:29:20.964 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:29:20.965 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:29:24.587 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:29:25.119 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 53b50ea3-17e2-43e1-bccc-1cd64ad2719c_config-0
18:29:25.163 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
18:29:25.189 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
18:29:25.196 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
18:29:25.202 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
18:29:25.209 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
18:29:25.214 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
18:29:25.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53b50ea3-17e2-43e1-bccc-1cd64ad2719c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:29:25.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53b50ea3-17e2-43e1-bccc-1cd64ad2719c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000281d53beaf8
18:29:25.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53b50ea3-17e2-43e1-bccc-1cd64ad2719c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000281d53bed18
18:29:25.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53b50ea3-17e2-43e1-bccc-1cd64ad2719c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:29:25.218 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53b50ea3-17e2-43e1-bccc-1cd64ad2719c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:29:25.223 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53b50ea3-17e2-43e1-bccc-1cd64ad2719c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:29:25.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53b50ea3-17e2-43e1-bccc-1cd64ad2719c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754044165714_127.0.0.1_6050
18:29:25.897 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53b50ea3-17e2-43e1-bccc-1cd64ad2719c_config-0] Notify connected event to listeners.
18:29:25.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53b50ea3-17e2-43e1-bccc-1cd64ad2719c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:29:25.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53b50ea3-17e2-43e1-bccc-1cd64ad2719c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000281d54f8ad8
18:29:25.972 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:29:28.308 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:29:28.309 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:29:28.309 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:29:28.420 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:29:28.887 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:29:28.888 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:29:28.889 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:29:33.825 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:29:35.724 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a2491a34-0e0d-45b3-994c-ae46d3d4d6ca
18:29:35.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2491a34-0e0d-45b3-994c-ae46d3d4d6ca] RpcClient init label, labels = {module=naming, source=sdk}
18:29:35.725 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2491a34-0e0d-45b3-994c-ae46d3d4d6ca] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:29:35.726 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2491a34-0e0d-45b3-994c-ae46d3d4d6ca] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:29:35.726 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2491a34-0e0d-45b3-994c-ae46d3d4d6ca] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:29:35.726 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2491a34-0e0d-45b3-994c-ae46d3d4d6ca] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:29:35.849 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2491a34-0e0d-45b3-994c-ae46d3d4d6ca] Success to connect to server [localhost:8848] on start up, connectionId = 1754044175733_127.0.0.1_6072
18:29:35.849 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2491a34-0e0d-45b3-994c-ae46d3d4d6ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:29:35.849 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2491a34-0e0d-45b3-994c-ae46d3d4d6ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000281d54f8ad8
18:29:35.849 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2491a34-0e0d-45b3-994c-ae46d3d4d6ca] Notify connected event to listeners.
18:29:35.888 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:29:35.912 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:29:35.995 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 11.867 seconds (JVM running for 12.635)
18:29:36.005 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:29:36.006 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:29:36.006 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:29:36.293 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:29:36.408 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2491a34-0e0d-45b3-994c-ae46d3d4d6ca] Receive server push request, request = NotifySubscriberRequest, requestId = 240
18:29:36.422 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2491a34-0e0d-45b3-994c-ae46d3d4d6ca] Ack server push request, request = NotifySubscriberRequest, requestId = 240
18:29:42.593 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:29:42.593 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:31:38.723 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:31:38.727 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:31:39.062 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:31:39.063 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3d1a40eb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:31:39.063 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754044175733_127.0.0.1_6072
18:31:39.064 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754044175733_127.0.0.1_6072]Ignore complete event,isRunning:false,isAbandon=false
18:31:39.065 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@47b36023[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 41]
18:31:39.208 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:31:39.210 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:31:39.214 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:31:39.214 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:31:39.215 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:31:39.215 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:31:43.027 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:31:43.547 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0465fd8f-399a-4abb-8f2a-5a873a8b095e_config-0
18:31:43.598 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
18:31:43.624 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
18:31:43.630 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
18:31:43.636 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
18:31:43.643 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
18:31:43.649 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
18:31:43.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465fd8f-399a-4abb-8f2a-5a873a8b095e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:31:43.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465fd8f-399a-4abb-8f2a-5a873a8b095e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000024f013ceaf8
18:31:43.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465fd8f-399a-4abb-8f2a-5a873a8b095e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000024f013ced18
18:31:43.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465fd8f-399a-4abb-8f2a-5a873a8b095e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:31:43.653 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465fd8f-399a-4abb-8f2a-5a873a8b095e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:31:43.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465fd8f-399a-4abb-8f2a-5a873a8b095e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:31:44.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465fd8f-399a-4abb-8f2a-5a873a8b095e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754044304154_127.0.0.1_6299
18:31:44.335 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465fd8f-399a-4abb-8f2a-5a873a8b095e_config-0] Notify connected event to listeners.
18:31:44.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465fd8f-399a-4abb-8f2a-5a873a8b095e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:31:44.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0465fd8f-399a-4abb-8f2a-5a873a8b095e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024f01508ad8
18:31:44.426 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:31:46.879 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:31:46.880 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:31:46.880 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:31:47.034 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:31:47.645 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:31:47.646 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:31:47.646 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:31:52.864 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:31:55.331 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ddf8e54b-3919-45ce-bd34-a45ab0c0383b
18:31:55.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf8e54b-3919-45ce-bd34-a45ab0c0383b] RpcClient init label, labels = {module=naming, source=sdk}
18:31:55.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf8e54b-3919-45ce-bd34-a45ab0c0383b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:31:55.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf8e54b-3919-45ce-bd34-a45ab0c0383b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:31:55.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf8e54b-3919-45ce-bd34-a45ab0c0383b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:31:55.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf8e54b-3919-45ce-bd34-a45ab0c0383b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:31:55.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf8e54b-3919-45ce-bd34-a45ab0c0383b] Success to connect to server [localhost:8848] on start up, connectionId = 1754044315342_127.0.0.1_6363
18:31:55.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf8e54b-3919-45ce-bd34-a45ab0c0383b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:31:55.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf8e54b-3919-45ce-bd34-a45ab0c0383b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024f01508ad8
18:31:55.460 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf8e54b-3919-45ce-bd34-a45ab0c0383b] Notify connected event to listeners.
18:31:55.509 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:31:55.534 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:31:55.650 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.097 seconds (JVM running for 13.899)
18:31:55.664 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:31:55.664 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:31:55.665 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:31:56.082 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf8e54b-3919-45ce-bd34-a45ab0c0383b] Receive server push request, request = NotifySubscriberRequest, requestId = 250
18:31:56.093 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ddf8e54b-3919-45ce-bd34-a45ab0c0383b] Ack server push request, request = NotifySubscriberRequest, requestId = 250
18:31:56.205 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:31:59.370 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:31:59.371 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:40:34.409 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:40:34.412 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:40:34.744 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:40:34.744 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5401617b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:40:34.744 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754044315342_127.0.0.1_6363
18:40:34.746 [nacos-grpc-client-executor-112] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754044315342_127.0.0.1_6363]Ignore complete event,isRunning:false,isAbandon=false
18:40:34.748 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2deb2453[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 113]
18:40:34.887 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:40:34.889 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:40:34.894 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:40:34.894 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:40:34.895 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:40:34.895 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:40:38.912 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:40:39.465 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f0237caa-28f9-424d-86e0-f4f7b995ffe7_config-0
18:40:39.518 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
18:40:39.547 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
18:40:39.554 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
18:40:39.561 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
18:40:39.567 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
18:40:39.574 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
18:40:39.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0237caa-28f9-424d-86e0-f4f7b995ffe7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:40:39.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0237caa-28f9-424d-86e0-f4f7b995ffe7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001fb9d3be8d8
18:40:39.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0237caa-28f9-424d-86e0-f4f7b995ffe7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001fb9d3beaf8
18:40:39.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0237caa-28f9-424d-86e0-f4f7b995ffe7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:40:39.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0237caa-28f9-424d-86e0-f4f7b995ffe7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:40:39.583 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0237caa-28f9-424d-86e0-f4f7b995ffe7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:40:40.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0237caa-28f9-424d-86e0-f4f7b995ffe7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754044840113_127.0.0.1_7692
18:40:40.298 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0237caa-28f9-424d-86e0-f4f7b995ffe7_config-0] Notify connected event to listeners.
18:40:40.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0237caa-28f9-424d-86e0-f4f7b995ffe7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:40:40.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0237caa-28f9-424d-86e0-f4f7b995ffe7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fb9d4f8b60
18:40:40.376 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:40:42.956 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:40:42.956 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:40:42.956 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:40:43.114 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:40:43.790 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:40:43.791 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:40:43.792 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:40:50.203 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:40:53.200 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1eb768f3-a770-4c44-bf6c-756a7e6f1dcc
18:40:53.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb768f3-a770-4c44-bf6c-756a7e6f1dcc] RpcClient init label, labels = {module=naming, source=sdk}
18:40:53.202 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb768f3-a770-4c44-bf6c-756a7e6f1dcc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:40:53.202 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb768f3-a770-4c44-bf6c-756a7e6f1dcc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:40:53.203 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb768f3-a770-4c44-bf6c-756a7e6f1dcc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:40:53.204 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb768f3-a770-4c44-bf6c-756a7e6f1dcc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:40:53.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb768f3-a770-4c44-bf6c-756a7e6f1dcc] Success to connect to server [localhost:8848] on start up, connectionId = 1754044853213_127.0.0.1_7785
18:40:53.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb768f3-a770-4c44-bf6c-756a7e6f1dcc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:40:53.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb768f3-a770-4c44-bf6c-756a7e6f1dcc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fb9d4f8b60
18:40:53.327 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb768f3-a770-4c44-bf6c-756a7e6f1dcc] Notify connected event to listeners.
18:40:53.367 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:40:53.390 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:40:53.496 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.053 seconds (JVM running for 15.867)
18:40:53.509 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:40:53.510 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:40:53.510 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:40:53.727 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:40:53.859 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb768f3-a770-4c44-bf6c-756a7e6f1dcc] Receive server push request, request = NotifySubscriberRequest, requestId = 258
18:40:53.873 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb768f3-a770-4c44-bf6c-756a7e6f1dcc] Ack server push request, request = NotifySubscriberRequest, requestId = 258
18:41:07.414 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:41:07.415 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:41:45.527 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:41:45.530 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:41:45.865 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:41:45.865 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@63813ba6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:41:45.867 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754044853213_127.0.0.1_7785
18:41:45.869 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754044853213_127.0.0.1_7785]Ignore complete event,isRunning:false,isAbandon=false
18:41:45.870 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3920b772[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 24]
18:41:46.014 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:41:46.018 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:41:46.025 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:41:46.025 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:41:46.026 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:41:46.026 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
