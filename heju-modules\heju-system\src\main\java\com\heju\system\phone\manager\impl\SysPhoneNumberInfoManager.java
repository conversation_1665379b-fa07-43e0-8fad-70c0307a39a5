package com.heju.system.phone.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.heju.system.phone.domain.po.SysPhoneNumberInfoPo;
import com.heju.system.phone.domain.dto.SysPhoneNumberInfoDto;
import com.heju.system.phone.domain.query.SysPhoneNumberInfoQuery;
import com.heju.system.phone.domain.model.SysPhoneNumberInfoConverter;
import com.heju.system.phone.mapper.SysPhoneNumberInfoMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.phone.manager.ISysPhoneNumberInfoManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 手机号管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysPhoneNumberInfoManager extends BaseManagerImpl<SysPhoneNumberInfoQuery, SysPhoneNumberInfoDto, SysPhoneNumberInfoPo, SysPhoneNumberInfoMapper, SysPhoneNumberInfoConverter> implements ISysPhoneNumberInfoManager {

    @Autowired
    private SysPhoneNumberInfoMapper mapper;

    /**
     * 查询数据对象列表
     *
     * @param query 数据查询对象
     * @return 数据对象集合
     */
    @Override
    public List<SysPhoneNumberInfoDto> selectList(SysPhoneNumberInfoQuery query) {
        LambdaQueryWrapper<SysPhoneNumberInfoPo> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(query.getPhoneNumber())) {
            wrapper.and(wrapper1 -> wrapper1
                    .like(SysPhoneNumberInfoPo::getSimOne, query.getPhoneNumber())
                    .or()
                    .like(SysPhoneNumberInfoPo::getSimTwo, query.getPhoneNumber())
            );
        }
        if (StringUtils.hasText(query.getPhoneModel())) {
            wrapper.like(SysPhoneNumberInfoPo::getPhoneModel, query.getPhoneModel());
        }
        if (query.getCustody() != null) {
            wrapper.eq(SysPhoneNumberInfoPo::getCustody, query.getCustody());
        }
        List<SysPhoneNumberInfoPo> poList = baseMapper.selectList(wrapper);
        return subMerge(mapperDto(poList));
    }

}
