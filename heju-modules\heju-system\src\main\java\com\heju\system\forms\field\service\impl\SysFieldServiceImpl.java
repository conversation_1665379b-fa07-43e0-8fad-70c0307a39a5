package com.heju.system.forms.field.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.merge.SysFieldRoleMerge;
import com.heju.system.forms.field.domain.po.SysFieldPo;
import com.heju.system.forms.field.domain.query.SysFieldQuery;
import com.heju.system.forms.field.manager.ISysFieldManager;
import com.heju.system.forms.field.mapper.merge.SysFieldRoleMergeMapper;
import com.heju.system.forms.field.service.ISysFieldService;
import com.heju.system.forms.option.domain.dto.SysOptionDto;
import com.heju.system.forms.option.service.ISysOptionService;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.service.ISysSheetService;
import com.heju.system.utils.FieldTypeConstants;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 字段管理管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysFieldServiceImpl extends BaseServiceImpl<SysFieldQuery, SysFieldDto, ISysFieldManager> implements ISysFieldService {


    @Resource
    private ISysSheetService sheetService;

    @Resource
    private SysFieldRoleMergeMapper fieldRoleMergeMapper;

    @Resource
    private ISysOptionService optionService;
    /**
     * 查询字段管理对象列表 | 数据权限
     *
     * @param field 字段管理对象
     * @return 字段管理对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysFieldMapper"})
    public List<SysFieldDto> selectListScope(SysFieldQuery field) {
        List<SysFieldDto> sysFieldDos = baseManager.selectList(field);
        List<SysFieldDto> collect = sysFieldDos.stream().filter(sysFieldDto -> sysFieldDto.getFieldType().equals(FieldTypeConstants.QUOTE)).toList();
        if(!collect.isEmpty()) {
            Set<Long> quoteSheetIds = collect.stream().map(SysFieldDto::getQuoteSheetId).collect(Collectors.toSet());
            Set<Long> quoteSheetFieldIds = collect.stream().map(SysFieldDto::getQuoteSheetFieldId).collect(Collectors.toSet());
            List<SysSheetDto> quoteSheets = sheetService.selectListByIds(quoteSheetIds);
            List<SysFieldDto> quoteSheetFields = baseManager.selectListByIds(quoteSheetFieldIds);
            Map<Long, String> relationSheetMap = quoteSheets.stream().collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getName, (v1, v2) -> v1));
            Map<Long, SysFieldDto> relationSheetFieldMap = quoteSheetFields.stream().collect(Collectors.toMap(SysFieldDto::getId, sysFieldDto->sysFieldDto));
            for (SysFieldDto sysFieldDto : sysFieldDos) {
                if(sysFieldDto.getFieldType().equals(FieldTypeConstants.QUOTE)){
                    sysFieldDto.setQuoteSheetFieldName(relationSheetFieldMap.get(sysFieldDto.getQuoteSheetFieldId()).getName());
                    sysFieldDto.setQuoteFieldType(relationSheetFieldMap.get(sysFieldDto.getQuoteSheetFieldId()).getFieldType());
                    sysFieldDto.setQuoteSheetName(relationSheetMap.get(sysFieldDto.getQuoteSheetId()));
                }
            }
        }
        return sysFieldDos;
    }


    /**
     * 新增数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int insert(SysFieldDto dto) {
        startHandle(OperateConstants.ServiceType.ADD, null, dto);
        if(dto.getFieldType().equals(FieldTypeConstants.RELATION)){
            SysFieldDto fieldDto = baseManager.selectPrimary(dto.getSheetId());
            dto.setRelationSheetFieldId(fieldDto.getId());
        }
        if((dto.getFieldType().equals(FieldTypeConstants.SELECT_MULTI)||
                dto.getFieldType().equals(FieldTypeConstants.SELECT_SINGLE)) &&
                dto.getOptionId()==null){
            SysOptionDto option=new SysOptionDto();
            option.setOptionValueList(dto.getOptionValueList());
            option.setOptionType(2);
            option.setApiName(dto.getApiName());
            option.setName(dto.getName());
            optionService.insert(option);
            dto.setOptionId(option.getId());
        }
        int row = baseManager.insert(dto);
        endHandle(OperateConstants.ServiceType.ADD, row, null, dto);
        if(row>0) {
            SysSheetDto sysSheetDto = sheetService.selectById(dto.getSheetId());
            dto.setSheetApiName(sysSheetDto.getApiName());
            if(dto.getQuoteSheetFieldId()!=null){
                SysSheetDto sysQuoteSheetDto = sheetService.selectById(dto.getQuoteSheetId());
                dto.setQuoteSheetApiName(sysQuoteSheetDto.getApiName());
            }
            baseManager.insertFieldToSql(dto);

            //绑定角色
            bindFieldRoleMerge(dto.getRoleIds(),dto.getId(),dto.getSheetId());
        }
        return row;
    }

    /**
     * 修改数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int update(SysFieldDto dto) {
        SysFieldDto originDto = baseManager.selectById(dto.getId());
        startHandle(OperateConstants.ServiceType.EDIT, originDto, dto);
        int row = baseManager.update(dto);
        endHandle(OperateConstants.ServiceType.EDIT, row, originDto, dto);
        if(row>0) {
            SysSheetDto sysSheetDto = sheetService.selectById(dto.getSheetId());
            dto.setSheetApiName(sysSheetDto.getApiName());
            baseManager.updateSheetColumn(dto);
            //删除现有绑定角色
            fieldRoleMergeMapper.delete( Wrappers.<SysFieldRoleMerge>query().lambda()
                    .eq(SysFieldRoleMerge::getFieldId, dto.getId()));
            //重新绑定角色
            bindFieldRoleMerge(dto.getRoleIds(),dto.getId(),dto.getSheetId());
        }

        return row;
    }

    @Override
    public boolean checkConfigCodeUnique(Long Id, String apiName,Long sheetId) {
        return ObjectUtil.isNotNull(baseManager.checkConfigCodeUnique(ObjectUtil.isNull(Id) ? BaseConstants.NONE_ID : Id, apiName,sheetId));
    }

    @Override
    public List<SysFieldPo> selectQuote(List<Long> ids) {
        return baseManager.selectQuote(ids);
    }

    @Override
    public List<SysFieldPo> selectRelation(List<Long> ids) {
        return baseManager.selectRelation(ids);
    }

    @Override
    public List<SysFieldDto> relationList(SysFieldQuery field) {
        List<SysFieldDto> sysFieldDos = baseManager.selectList(field);
        if(!sysFieldDos.isEmpty()) {
            //当前所有表id
            Set<Long> sheetIds = sysFieldDos.stream().map(SysFieldDto::getSheetId).collect(Collectors.toSet());
            //所有关联表id
            Set<Long> sheetRelationIds = sysFieldDos.stream().map(SysFieldDto::getRelationSheetId).collect(Collectors.toSet());
            sheetIds.addAll(sheetRelationIds);
            List<SysSheetDto> relationSheetDos = sheetService.selectListByIds(sheetIds);
            Map<Long, String> relationSheetMap = relationSheetDos.stream().collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getName, (v1, v2) -> v1));
            //所有关联字段id
            Set<Long> fieldRelationIds = sysFieldDos.stream().map(SysFieldDto::getRelationSheetFieldId).collect(Collectors.toSet());
            List<SysFieldDto> relationFieldDos = baseManager.selectListByIds(fieldRelationIds);
            Map<Long, String> relationFieldMap = relationFieldDos.stream().collect(Collectors.toMap(SysFieldDto::getId, SysFieldDto::getName, (v1, v2) -> v1));
            for (SysFieldDto sysFieldDto : sysFieldDos) {
                sysFieldDto.setSheetName(relationSheetMap.get(sysFieldDto.getSheetId()));
                sysFieldDto.setRelationSheetName(relationSheetMap.get(sysFieldDto.getRelationSheetId()));
                sysFieldDto.setRelationSheetFieldName(relationFieldMap.get(sysFieldDto.getRelationSheetFieldId()));
            }
        }
        return sysFieldDos;
    }

    @Override
    public AjaxResult option(SysFieldQuery field) {
        List<SysFieldDto> sysFieldDos = baseManager.selectList(field);
        if(field.getSelectType()==2){
            sysFieldDos = sysFieldDos.stream().filter(sysFieldDto -> !sysFieldDto.getFieldType().equals(FieldTypeConstants.QUOTE)).collect(Collectors.toList());
        }
        return AjaxResult.success(sysFieldDos);
    }

    /**
     * 绑定字段和角色关系
     * @param roleIds 角色列表
     * @param fieldId 字段id
     */
    public void bindFieldRoleMerge( List<Long> roleIds,Long fieldId,Long sheetId){
        List<SysFieldRoleMerge> fieldRoleMerges=new ArrayList<>();
        roleIds.forEach( roleId ->{
            SysFieldRoleMerge sysFieldRoleMerge=new SysFieldRoleMerge();
            sysFieldRoleMerge.setFieldId(fieldId);
            sysFieldRoleMerge.setRoleId(roleId);
            sysFieldRoleMerge.setSheetId(sheetId);
            fieldRoleMerges.add(sysFieldRoleMerge);
        });
        fieldRoleMergeMapper.insertBatch(fieldRoleMerges);
    }

    /**
     * 根据Id删除数据对象（批量）
     *
     * @param idList Id集合
     * @return 结果
     */
    @Override
    @DSTransactional
    public int deleteByIds(Collection<? extends Serializable> idList) {
        List<SysFieldDto> originList = selectListByIds(idList);
        startBatchHandle(OperateConstants.ServiceType.BATCH_DELETE, originList, null);
        int rows = baseManager.deleteByIds(idList);
        //删除数据库字段
        originList=originList.stream().filter(sysFieldDto ->!sysFieldDto.getFieldType().equals(FieldTypeConstants.QUOTE)).collect(Collectors.toList());
        Set<Long> sheetIds = originList.stream().map(SysFieldDto::getSheetId).collect(Collectors.toSet());
        List<SysSheetDto> sysSheets = sheetService.selectListByIds(sheetIds);
        baseManager.deleteSheetField(originList,sysSheets.get(NumberUtil.Zero).getApiName());
        endBatchHandle(OperateConstants.ServiceType.BATCH_DELETE, rows, originList, null);
        return rows;
    }

    @Override
    public AjaxResult searchQuote(SysFieldQuery query) {
        List<SysFieldDto> sysFieldDos = baseManager.selectList(query);
        Set<Long> optionSheetIds = sysFieldDos.stream().map(SysFieldDto::getSheetId).collect(Collectors.toSet());
        if(!optionSheetIds.isEmpty()) {
            List<SysSheetDto> sysSheetDos = sheetService.selectListByIds(optionSheetIds);
            Map<Long, String> collect = sysSheetDos.stream().collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getName, (v1, v2) -> v1));
            for (SysFieldDto sysFieldDto : sysFieldDos) {
                sysFieldDto.setSheetName(collect.get(sysFieldDto.getSheetId()));
            }
        }
        return AjaxResult.success(sysFieldDos);
    }

    @Override
    public List<SysFieldDto> selectBySheetIds(List<Long> sheetIds) {
        return baseManager.selectBySheetIds(sheetIds);
    }

    @Override
    public List<SysFieldDto> selectByApiNames(List<String> apiName,Long sheetId) {
        return baseManager.selectByApiNames(apiName,sheetId);
    }

    @Override
    public List<SysFieldDto> selectQuoteByIds(List<Long> ids) {
        return baseManager.selectQuoteByApiNames(ids);
    }

    @Override
    public List<SysFieldDto> selectReferencingByIds(List<Long> ids) {
        return baseManager.selectReferencingByIds(ids);
    }

    @Override
    public List<SysFieldDto> selectReferencedByIds(List<Long> ids) {
        return baseManager.selectReferencedByIds(ids);
    }

    /**
     * 根据Id查询单条数据对象
     *
     * @param id Id
     * @return 数据对象
     */
    @Override
    public SysFieldDto selectById(Serializable id) {
        SysFieldDto dto = baseManager.selectById(id);
        List<SysFieldRoleMerge> fieldRoleMerges = fieldRoleMergeMapper.selectList(Wrappers.<SysFieldRoleMerge>query().lambda()
                .eq(SysFieldRoleMerge::getFieldId, dto.getId()));
        List<Long> roleIds = fieldRoleMerges.stream().map(SysFieldRoleMerge::getRoleId).collect(Collectors.toList());
        dto.setRoleIds(roleIds);
        return subCorrelates(dto);
    }
}
