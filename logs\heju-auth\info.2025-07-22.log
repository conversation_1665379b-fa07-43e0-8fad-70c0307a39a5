09:18:21.125 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:18:21.880 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0
09:18:21.984 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 49 ms to scan 1 urls, producing 3 keys and 6 values 
09:18:22.034 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 4 keys and 9 values 
09:18:22.047 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:18:22.062 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 5 values 
09:18:22.075 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:18:22.090 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:18:22.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:22.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000014f153b4fb8
09:18:22.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000014f153b51d8
09:18:22.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:22.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:22.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:23.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753147103365_127.0.0.1_13152
09:18:23.665 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Notify connected event to listeners.
09:18:23.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:23.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000014f154ed418
09:18:23.876 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:18:26.875 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:18:26.875 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:18:26.875 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:18:27.034 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:18:28.818 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:18:30.570 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9cc2c3e3-a03b-48b8-8690-91f39f3e2a04
09:18:30.571 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] RpcClient init label, labels = {module=naming, source=sdk}
09:18:30.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:30.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:30.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:30.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:30.745 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Success to connect to server [localhost:8848] on start up, connectionId = 1753147110620_127.0.0.1_13243
09:18:30.745 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Notify connected event to listeners.
09:18:30.745 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:30.745 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000014f154ed418
09:18:30.837 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:18:30.967 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:18:31.414 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 11.039 seconds (JVM running for 16.673)
09:18:31.456 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:18:31.457 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:18:31.472 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:18:31.817 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:18:31.819 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:23:39.343 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:23:45.424 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:23:45.425 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:23:49.404 [nacos-grpc-client-executor-78] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:23:49.405 [nacos-grpc-client-executor-78] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Ack server push request, request = NotifySubscriberRequest, requestId = 12
20:39:57.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:39:57.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:39:57.534 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:57.547 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:57.769 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:57.782 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:58.080 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:58.095 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:58.488 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:58.529 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:59.008 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:59.076 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:59.638 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:39:59.716 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:00.372 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:00.451 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:01.187 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:01.294 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:02.097 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:02.206 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:03.110 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:03.217 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:04.254 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:04.350 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:05.475 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:05.558 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:06.781 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:06.875 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:08.197 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:08.289 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:09.703 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c4feae4-3b62-4eb8-a67e-246f31bc4f9d_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:09.794 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:40:09.908 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:40:10.228 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:40:10.555 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:40:10.556 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@57fbade7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:40:10.556 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753147110620_127.0.0.1_13243
20:40:10.556 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Client is shutdown, stop reconnect to server
20:40:10.556 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@43175573[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 8326]
20:40:10.556 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc2c3e3-a03b-48b8-8690-91f39f3e2a04] Notify disconnected event to listeners
