CREATE TRIGGER `updateDept` AFTER UPDATE ON `sys_company` FOR EACH ROW BEGIN
    UPDATE sys_dept SET company_name = new.`name` WHERE company_id=old.id;
END;;
delimiter ;

CREATE TRIGGER `updatePost` AFTER UPDATE ON `sys_company` FOR EACH ROW BEGIN
    UPDATE sys_post SET company_name = new.`name` WHERE company_id=old.id;
END;;
delimiter ;


CREATE TRIGGER `deptUpdatePost` AFTER UPDATE ON `sys_dept` FOR EACH ROW BEGIN
    UPDATE sys_post SET dept_name = new.`name` WHERE dept_id=old.id;
    UPDATE sys_post SET company_id=new.`company_id`,company_name=new.company_name WHERE dept_id=old.id;
END;;
delimiter ;

CREATE TRIGGER `updateName` AFTER UPDATE ON `sys_entity` FOR EACH ROW BEGIN
    UPDATE sys_entity_examine SET entity_name = new.`name` WHERE entity_id=old.id;
END;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;


