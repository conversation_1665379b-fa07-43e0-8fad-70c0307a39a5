09:04:11.252 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:04:12.114 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6ed87318-bd55-4a15-8833-811848ebbf7f_config-0
09:04:12.235 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 58 ms to scan 1 urls, producing 3 keys and 6 values 
09:04:12.279 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:04:12.284 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 3 ms to scan 1 urls, producing 3 keys and 10 values 
09:04:12.307 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:04:12.326 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 7 values 
09:04:12.342 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:04:12.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ed87318-bd55-4a15-8833-811848ebbf7f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:04:12.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ed87318-bd55-4a15-8833-811848ebbf7f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001f7493b8638
09:04:12.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ed87318-bd55-4a15-8833-811848ebbf7f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001f7493b8858
09:04:12.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ed87318-bd55-4a15-8833-811848ebbf7f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:04:12.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ed87318-bd55-4a15-8833-811848ebbf7f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:04:12.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ed87318-bd55-4a15-8833-811848ebbf7f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:04:13.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ed87318-bd55-4a15-8833-811848ebbf7f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753059853592_127.0.0.1_1634
09:04:13.871 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ed87318-bd55-4a15-8833-811848ebbf7f_config-0] Notify connected event to listeners.
09:04:13.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ed87318-bd55-4a15-8833-811848ebbf7f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:04:13.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ed87318-bd55-4a15-8833-811848ebbf7f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001f7494f0228
09:04:14.076 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:04:21.682 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:04:24.152 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:04:25.377 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 670c6b83-b30a-41fc-90be-ccd320028fdb_config-0
09:04:25.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [670c6b83-b30a-41fc-90be-ccd320028fdb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:04:25.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [670c6b83-b30a-41fc-90be-ccd320028fdb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001f7493b8638
09:04:25.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [670c6b83-b30a-41fc-90be-ccd320028fdb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001f7493b8858
09:04:25.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [670c6b83-b30a-41fc-90be-ccd320028fdb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:04:25.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [670c6b83-b30a-41fc-90be-ccd320028fdb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:04:25.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [670c6b83-b30a-41fc-90be-ccd320028fdb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:04:25.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [670c6b83-b30a-41fc-90be-ccd320028fdb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753059865397_127.0.0.1_1656
09:04:25.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [670c6b83-b30a-41fc-90be-ccd320028fdb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:04:25.521 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [670c6b83-b30a-41fc-90be-ccd320028fdb_config-0] Notify connected event to listeners.
09:04:25.522 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [670c6b83-b30a-41fc-90be-ccd320028fdb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001f7494f0228
09:04:25.755 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3795bd62-8642-42e5-a3f4-4233f2dad77a
09:04:25.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] RpcClient init label, labels = {module=naming, source=sdk}
09:04:25.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:04:25.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:04:25.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:04:25.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:04:25.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Success to connect to server [localhost:8848] on start up, connectionId = 1753059865781_127.0.0.1_1658
09:04:25.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:04:25.904 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Notify connected event to listeners.
09:04:25.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001f7494f0228
09:04:26.960 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:04:26.961 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:04:27.111 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.27:8081 register finished
09:04:27.171 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 16.766 seconds (JVM running for 21.434)
09:04:27.183 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:04:27.186 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:04:27.187 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:04:27.637 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:04:27.638 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:04:56.860 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:04:56.860 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:04:56.870 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:04:56.870 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:04:56.879 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:04:56.880 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:04:56.890 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:04:56.890 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:04:56.900 [nacos-grpc-client-executor-57] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:04:56.900 [nacos-grpc-client-executor-57] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Ack server push request, request = NotifySubscriberRequest, requestId = 12
10:29:19.828 [nacos-grpc-client-executor-1823] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Receive server push request, request = NotifySubscriberRequest, requestId = 19
10:29:19.844 [nacos-grpc-client-executor-1823] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3795bd62-8642-42e5-a3f4-4233f2dad77a] Ack server push request, request = NotifySubscriberRequest, requestId = 19
17:57:13.942 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:57:13.942 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:57:14.286 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:57:14.286 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6f864674[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:57:14.286 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753059865781_127.0.0.1_1658
17:57:14.289 [nacos-grpc-client-executor-10977] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753059865781_127.0.0.1_1658]Ignore complete event,isRunning:false,isAbandon=false
17:57:14.300 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4d6c7c3d[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 10978]
