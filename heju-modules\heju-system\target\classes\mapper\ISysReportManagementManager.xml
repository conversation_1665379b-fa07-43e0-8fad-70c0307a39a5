<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.heju.system.third.mapper.SysThirdAuthMapper">
    <select id="getTenantCpmList" resultType="com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeDto">
        SELECT
            a.name,
            a.id companyId,
            b.third_auth_id thirdAuthId,
            c.name thirdAuthName,
            b.id
        FROM
            sys_company a
                INNER JOIN sys_company_third_merge sctm on a.id = sctm.company_id
                LEFT JOIN sys_company_third_auth_merge b ON a.id = b.company_id and b.third_id = #{thirdId}
                LEFT JOIN sys_third_auth c ON b.third_auth_id = c.id
        where sctm.third_id = #{thirdId}
    </select>

    <insert id="addTenantCpmAuth"  >
        INSERT INTO sys_company_third_auth_merge (company_id, third_auth_id,third_id )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.companyId},
            #{item.thirdAuthId},
            #{item.thirdId}
            )
        </foreach>
    </insert>

    <delete id="delTenantCpmAuth" >
        delete from sys_company_third_auth_merge
        <where>

                    <foreach collection="list" open="" close="" separator="or" item="item">
                        (company_id = #{item.companyId} and third_auth_id =#{item.thirdAuthId})
                    </foreach>
        </where>

    </delete>

    <select id="getTenantCpmListByAuthId" parameterType="com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeAddDto"
            resultType="com.heju.system.third.domain.dto.SysCompanyThirdAuthMergeDto">
        SELECT
            id, company_id, third_auth_id
        FROM
            sys_company_third_auth_merge
        where third_auth_id = #{thirdAuthId}
    </select>

    <select id="thirdAuthList" resultType="com.heju.system.third.domain.dto.SysThirdAuthDto">
        select id,name,third_id,status,del_flag from sys_third_auth
        <where>
            <if test="thirdId != '' and thirdId != null ">
                third_id = #{thirdId}
            </if>
        </where>
    </select>

</mapper>

