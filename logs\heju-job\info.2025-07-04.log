09:24:00.937 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:24:03.383 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0
09:24:03.522 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 74 ms to scan 1 urls, producing 3 keys and 6 values 
09:24:03.588 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 27 ms to scan 1 urls, producing 4 keys and 9 values 
09:24:03.639 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 40 ms to scan 1 urls, producing 3 keys and 10 values 
09:24:03.667 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 1 keys and 5 values 
09:24:03.701 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 22 ms to scan 1 urls, producing 1 keys and 7 values 
09:24:03.722 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
09:24:03.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:24:03.734 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002135639cfb8
09:24:03.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002135639d1d8
09:24:03.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:24:03.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:24:03.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:06.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:06.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:06.852 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:06.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:06.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000213564aaef8
09:24:07.050 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:07.296 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:07.637 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:08.081 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:08.612 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:09.252 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:09.386 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:24:10.031 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:10.872 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:11.854 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:13.579 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:15.206 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:17.337 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:19.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:20.927 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:21.820 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
09:24:21.820 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:24:21.821 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:24:22.113 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:24:22.570 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:23.609 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:24:23.611 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:24:23.611 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:24:24.198 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:24:24.216 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:24:24.216 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:24:24.249 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751592264201'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

09:24:24.249 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
09:24:24.249 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:24:24.249 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:24.249 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@1221d607
09:24:26.030 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:27.927 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:29.203 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:24:29.942 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:32.044 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:34.266 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:34.842 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f
09:24:34.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] RpcClient init label, labels = {module=naming, source=sdk}
09:24:34.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:34.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:34.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:34.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:34.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:34.879 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:34.891 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:34.891 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:34.891 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000213564aaef8
09:24:35.010 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:35.226 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:35.554 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:35.974 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:36.221 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:24:36.223 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2bf3ec4a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:24:36.223 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3594b4b4[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:24:36.223 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8ab21-6feb-4d3e-b9e3-d2e5be9ff58f] Client is shutdown, stop reconnect to server
09:24:36.226 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4e7c321c-dab3-4811-9fef-72e87d5c210e
09:24:36.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e7c321c-dab3-4811-9fef-72e87d5c210e] RpcClient init label, labels = {module=naming, source=sdk}
09:24:36.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e7c321c-dab3-4811-9fef-72e87d5c210e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:36.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e7c321c-dab3-4811-9fef-72e87d5c210e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:36.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e7c321c-dab3-4811-9fef-72e87d5c210e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:36.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e7c321c-dab3-4811-9fef-72e87d5c210e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:36.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e7c321c-dab3-4811-9fef-72e87d5c210e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:36.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e7c321c-dab3-4811-9fef-72e87d5c210e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:36.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e7c321c-dab3-4811-9fef-72e87d5c210e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:36.252 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e7c321c-dab3-4811-9fef-72e87d5c210e] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:36.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e7c321c-dab3-4811-9fef-72e87d5c210e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000213564aaef8
09:24:36.367 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e7c321c-dab3-4811-9fef-72e87d5c210e] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:36.480 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5fa3a96-4e06-4451-9143-b6ad3c0e1585_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:36.589 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e7c321c-dab3-4811-9fef-72e87d5c210e] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:36.630 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751592264201 shutting down.
09:24:36.630 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751592264201 paused.
09:24:36.630 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751592264201 shutdown complete.
09:24:36.630 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:24:36.637 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:24:36.653 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:24:36.658 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:25:21.761 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:25:23.104 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 98b10fe8-8374-4622-9ab7-313c74caaa7b_config-0
09:25:23.204 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 51 ms to scan 1 urls, producing 3 keys and 6 values 
09:25:23.238 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:25:23.255 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
09:25:23.275 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 5 values 
09:25:23.289 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:25:23.301 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:25:23.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98b10fe8-8374-4622-9ab7-313c74caaa7b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:25:23.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98b10fe8-8374-4622-9ab7-313c74caaa7b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000029c8139f268
09:25:23.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98b10fe8-8374-4622-9ab7-313c74caaa7b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000029c8139f488
09:25:23.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98b10fe8-8374-4622-9ab7-313c74caaa7b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:25:23.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98b10fe8-8374-4622-9ab7-313c74caaa7b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:25:23.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98b10fe8-8374-4622-9ab7-313c74caaa7b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
