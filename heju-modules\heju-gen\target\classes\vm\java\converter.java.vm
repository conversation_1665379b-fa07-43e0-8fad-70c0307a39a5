package ${packageName}.domain.model;

#if($table.base)
#set($Entity="BaseConverter")
#elseif($table.tree)
#set($Entity="TreeConverter")
#end
import com.heju.common.core.web.entity.model.${Entity};
import ${packageName}.domain.dto.${ClassName}Dto;
import ${packageName}.domain.po.${ClassName}Po;
import ${packageName}.domain.query.${ClassName}Query;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * ${functionName} 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ${ClassName}Converter extends ${Entity}<${ClassName}Query, ${ClassName}Dto, ${ClassName}Po> {
}
