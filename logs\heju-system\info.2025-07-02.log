09:20:22.226 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:20:23.321 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0
09:20:23.461 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 76 ms to scan 1 urls, producing 3 keys and 6 values 
09:20:23.546 [main] INFO  o.r.Reflections - [scan,232] - <PERSON>flections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
09:20:23.552 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 3 keys and 10 values 
09:20:23.566 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
09:20:23.582 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 1 keys and 7 values 
09:20:23.598 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
09:20:23.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:20:23.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000020cce39c730
09:20:23.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000020cce39c950
09:20:23.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:20:23.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:20:23.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:20:26.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:20:26.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:20:26.597 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:20:26.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:20:26.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000020cce4aa840
09:20:27.052 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:27.304 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:27.683 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:28.149 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:28.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:29.335 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:29.650 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:20:30.091 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:30.926 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:31.853 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:32.894 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:34.406 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:35.909 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:37.456 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:39.068 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:40.854 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:42.653 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:44.612 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:46.753 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:48.880 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:49.233 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:20:49.235 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:20:49.236 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:20:49.988 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:20:51.106 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:52.867 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:20:52.872 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:20:52.873 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:20:53.441 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:55.979 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:58.842 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:01.582 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:04.552 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:07.550 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:10.551 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:13.900 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 28 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:17.108 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 29 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:20.477 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 30 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:23.912 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 31 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:27.441 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 32 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:30.986 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 33 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:34.617 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 34 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:38.324 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 35 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:38.341 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:21:42.336 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 36 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:46.294 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 37 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:50.360 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 38 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:52.948 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d914a5d7-f3f5-4037-9c48-1c5bcb8eb308
09:21:52.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] RpcClient init label, labels = {module=naming, source=sdk}
09:21:52.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:52.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:52.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:52.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:53.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:53.242 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:53.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:53.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000020cce4aa840
09:21:53.293 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:21:53.449 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:53.666 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:21:53.704 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:54.037 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:54.285 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf1129-f648-48c4-8f87-3d51b295f0ce_config-0] Fail to connect server, after trying 39 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:54.463 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:54.655 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:21:54.656 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@485996f7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:21:54.657 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@35984172[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:21:54.658 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d914a5d7-f3f5-4037-9c48-1c5bcb8eb308] Client is shutdown, stop reconnect to server
09:21:54.670 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ded5fe16-4907-4f07-9c52-8cd01c13f736
09:21:54.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ded5fe16-4907-4f07-9c52-8cd01c13f736] RpcClient init label, labels = {module=naming, source=sdk}
09:21:54.672 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ded5fe16-4907-4f07-9c52-8cd01c13f736] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:54.672 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ded5fe16-4907-4f07-9c52-8cd01c13f736] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:54.672 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ded5fe16-4907-4f07-9c52-8cd01c13f736] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:54.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ded5fe16-4907-4f07-9c52-8cd01c13f736] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:54.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ded5fe16-4907-4f07-9c52-8cd01c13f736] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:54.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ded5fe16-4907-4f07-9c52-8cd01c13f736] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:54.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ded5fe16-4907-4f07-9c52-8cd01c13f736] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:54.737 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ded5fe16-4907-4f07-9c52-8cd01c13f736] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:21:54.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ded5fe16-4907-4f07-9c52-8cd01c13f736] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000020cce4aa840
09:21:54.887 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ded5fe16-4907-4f07-9c52-8cd01c13f736] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:55.108 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ded5fe16-4907-4f07-9c52-8cd01c13f736] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:55.231 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:21:55.246 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:21:55.293 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:21:55.293 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:21:55.355 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9600"]
09:21:55.355 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:21:55.429 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ded5fe16-4907-4f07-9c52-8cd01c13f736] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:55.439 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9600"]
09:21:55.452 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9600"]
09:21:55.862 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ded5fe16-4907-4f07-9c52-8cd01c13f736] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:10.599 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:23:13.002 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0
09:23:13.148 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 66 ms to scan 1 urls, producing 3 keys and 6 values 
09:23:13.228 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:23:13.242 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
09:23:13.258 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
09:23:13.291 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
09:23:13.307 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
09:23:13.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:23:13.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002c1a13b68d8
09:23:13.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002c1a13b6af8
09:23:13.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:23:13.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:23:13.323 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:15.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751419394821_127.0.0.1_3564
09:23:15.150 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] Notify connected event to listeners.
09:23:15.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:15.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002c1a14f0668
09:23:15.421 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:23:20.642 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:23:20.642 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:23:20.642 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:23:20.845 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:23:21.558 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:23:21.558 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:23:21.558 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:23:28.424 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:23:34.304 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5682c839-3845-41e7-8509-d4e6358dc4af
09:23:34.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] RpcClient init label, labels = {module=naming, source=sdk}
09:23:34.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:23:34.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:23:34.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:23:34.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:34.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Success to connect to server [localhost:8848] on start up, connectionId = 1751419414332_127.0.0.1_3586
09:23:34.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:34.481 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Notify connected event to listeners.
09:23:34.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002c1a14f0668
09:23:34.625 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:23:34.709 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:23:35.110 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.425 seconds (JVM running for 30.065)
09:23:35.120 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:23:35.155 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:23:35.162 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:23:35.162 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:23:35.167 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:42:53.488 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] Server healthy check fail, currentConnection = 1751419394821_127.0.0.1_3564
09:42:53.490 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:42:58.874 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Server healthy check fail, currentConnection = 1751419414332_127.0.0.1_3586
09:42:58.875 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:42:59.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Success to connect a server [localhost:8848], connectionId = 1751420579255_127.0.0.1_5604
09:42:59.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Abandon prev connection, server is localhost:8848, connectionId is 1751419414332_127.0.0.1_3586
09:42:59.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751419414332_127.0.0.1_3586
09:42:59.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Notify disconnected event to listeners
09:42:59.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Notify connected event to listeners.
09:42:59.536 [nacos-grpc-client-executor-243] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751419414332_127.0.0.1_3586]Ignore complete event,isRunning:true,isAbandon=true
09:42:59.735 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] Success to connect a server [localhost:8848], connectionId = 1751420579474_127.0.0.1_5609
09:42:59.745 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751419394821_127.0.0.1_3564
09:42:59.747 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751419394821_127.0.0.1_3564
09:42:59.747 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] Notify disconnected event to listeners
09:42:59.749 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c423e7d2-3c2e-4dda-9c8c-61841efd3b36_config-0] Notify connected event to listeners.
09:43:02.480 [nacos-grpc-client-executor-246] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Receive server push request, request = NotifySubscriberRequest, requestId = 21
09:43:02.482 [nacos-grpc-client-executor-246] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5682c839-3845-41e7-8509-d4e6358dc4af] Ack server push request, request = NotifySubscriberRequest, requestId = 21
10:36:16.448 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:37:14.143 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:37:14.144 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:40:37.911 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:40:37.918 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:40:38.265 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:40:38.265 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@23ddce18[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:40:38.267 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751420579255_127.0.0.1_5604
13:40:38.267 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@51f48f2d[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 3086]
13:40:38.464 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:40:38.464 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:40:38.481 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:40:38.481 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:40:38.494 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:40:38.496 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:15:02.142 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:15:03.214 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b463d0cd-cc57-4698-af9e-5ef554ed558e_config-0
14:15:03.324 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 54 ms to scan 1 urls, producing 3 keys and 6 values 
14:15:03.378 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
14:15:03.391 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:15:03.408 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:15:03.424 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:15:03.435 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:15:03.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b463d0cd-cc57-4698-af9e-5ef554ed558e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:15:03.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b463d0cd-cc57-4698-af9e-5ef554ed558e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000187683b71c0
14:15:03.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b463d0cd-cc57-4698-af9e-5ef554ed558e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000187683b73e0
14:15:03.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b463d0cd-cc57-4698-af9e-5ef554ed558e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:15:03.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b463d0cd-cc57-4698-af9e-5ef554ed558e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:15:03.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b463d0cd-cc57-4698-af9e-5ef554ed558e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:15:04.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b463d0cd-cc57-4698-af9e-5ef554ed558e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751436904462_127.0.0.1_9454
14:15:04.784 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b463d0cd-cc57-4698-af9e-5ef554ed558e_config-0] Notify connected event to listeners.
14:15:04.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b463d0cd-cc57-4698-af9e-5ef554ed558e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:15:04.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b463d0cd-cc57-4698-af9e-5ef554ed558e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000187684f0ad8
14:15:04.945 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:15:10.358 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:15:10.359 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:15:10.359 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:15:10.690 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:15:11.842 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:15:11.844 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:15:11.845 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:15:21.985 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:15:25.927 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dd56344c-1568-4d45-be72-9f5e1a6bfaac
14:15:25.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd56344c-1568-4d45-be72-9f5e1a6bfaac] RpcClient init label, labels = {module=naming, source=sdk}
14:15:25.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd56344c-1568-4d45-be72-9f5e1a6bfaac] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:15:25.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd56344c-1568-4d45-be72-9f5e1a6bfaac] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:15:25.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd56344c-1568-4d45-be72-9f5e1a6bfaac] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:15:25.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd56344c-1568-4d45-be72-9f5e1a6bfaac] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:15:26.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd56344c-1568-4d45-be72-9f5e1a6bfaac] Success to connect to server [localhost:8848] on start up, connectionId = 1751436925946_127.0.0.1_9503
14:15:26.074 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd56344c-1568-4d45-be72-9f5e1a6bfaac] Notify connected event to listeners.
14:15:26.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd56344c-1568-4d45-be72-9f5e1a6bfaac] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:15:26.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd56344c-1568-4d45-be72-9f5e1a6bfaac] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000187684f0ad8
14:15:26.165 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:15:26.215 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:15:26.385 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.125 seconds (JVM running for 26.556)
14:15:26.429 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:15:26.429 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:15:26.430 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:15:26.629 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd56344c-1568-4d45-be72-9f5e1a6bfaac] Receive server push request, request = NotifySubscriberRequest, requestId = 79
14:15:26.651 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd56344c-1568-4d45-be72-9f5e1a6bfaac] Ack server push request, request = NotifySubscriberRequest, requestId = 79
14:20:06.298 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:20:06.306 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:20:06.642 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:20:06.642 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@24b46e8e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:20:06.643 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751436925946_127.0.0.1_9503
14:20:06.646 [nacos-grpc-client-executor-62] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751436925946_127.0.0.1_9503]Ignore complete event,isRunning:false,isAbandon=false
14:20:06.647 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@8de24a3[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 63]
14:20:06.700 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:20:06.706 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:20:06.715 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:20:06.716 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:25:46.937 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:25:48.007 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c1bce400-970b-4223-bf5e-f70103f716fc_config-0
14:25:48.125 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
14:25:48.175 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:25:48.187 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:25:48.201 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:25:48.215 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:25:48.228 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:25:48.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1bce400-970b-4223-bf5e-f70103f716fc_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:25:48.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1bce400-970b-4223-bf5e-f70103f716fc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000223313cd7b8
14:25:48.234 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1bce400-970b-4223-bf5e-f70103f716fc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000223313cd9d8
14:25:48.235 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1bce400-970b-4223-bf5e-f70103f716fc_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:25:48.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1bce400-970b-4223-bf5e-f70103f716fc_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:25:48.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1bce400-970b-4223-bf5e-f70103f716fc_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:25:49.558 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1bce400-970b-4223-bf5e-f70103f716fc_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751437549295_127.0.0.1_10230
14:25:49.560 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1bce400-970b-4223-bf5e-f70103f716fc_config-0] Notify connected event to listeners.
14:25:49.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1bce400-970b-4223-bf5e-f70103f716fc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:25:49.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1bce400-970b-4223-bf5e-f70103f716fc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022331507b88
14:25:49.781 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:25:54.899 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:25:54.900 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:25:54.900 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:25:55.179 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:25:56.047 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:25:56.049 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:25:56.050 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:26:06.294 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:26:10.295 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d8c59e06-e10c-410f-ae27-6ed9f59c39e4
14:26:10.296 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c59e06-e10c-410f-ae27-6ed9f59c39e4] RpcClient init label, labels = {module=naming, source=sdk}
14:26:10.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c59e06-e10c-410f-ae27-6ed9f59c39e4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:26:10.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c59e06-e10c-410f-ae27-6ed9f59c39e4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:26:10.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c59e06-e10c-410f-ae27-6ed9f59c39e4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:26:10.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c59e06-e10c-410f-ae27-6ed9f59c39e4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:26:10.427 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c59e06-e10c-410f-ae27-6ed9f59c39e4] Success to connect to server [localhost:8848] on start up, connectionId = 1751437570311_127.0.0.1_10258
14:26:10.428 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c59e06-e10c-410f-ae27-6ed9f59c39e4] Notify connected event to listeners.
14:26:10.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c59e06-e10c-410f-ae27-6ed9f59c39e4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:26:10.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c59e06-e10c-410f-ae27-6ed9f59c39e4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022331507b88
14:26:10.555 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:26:10.605 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:26:10.814 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.71 seconds (JVM running for 26.125)
14:26:10.835 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:26:10.837 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:26:10.838 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:26:11.071 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c59e06-e10c-410f-ae27-6ed9f59c39e4] Receive server push request, request = NotifySubscriberRequest, requestId = 82
14:26:11.101 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c59e06-e10c-410f-ae27-6ed9f59c39e4] Ack server push request, request = NotifySubscriberRequest, requestId = 82
14:26:11.405 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:34:46.996 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:34:47.009 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:34:47.346 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:34:47.346 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5fc5f50f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:34:47.346 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751437570311_127.0.0.1_10258
14:34:47.346 [nacos-grpc-client-executor-117] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751437570311_127.0.0.1_10258]Ignore complete event,isRunning:false,isAbandon=false
14:34:47.355 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7b50c69a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 118]
14:34:47.514 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:34:47.514 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:34:47.532 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:34:47.532 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:34:55.302 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:34:56.546 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7fb96eed-d410-4a1d-ac8a-64ff850a219a_config-0
14:34:56.648 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
14:34:56.693 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 4 keys and 9 values 
14:34:56.711 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
14:34:56.721 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
14:34:56.737 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
14:34:56.740 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
14:34:56.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb96eed-d410-4a1d-ac8a-64ff850a219a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:34:56.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb96eed-d410-4a1d-ac8a-64ff850a219a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000229093b71c0
14:34:56.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb96eed-d410-4a1d-ac8a-64ff850a219a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000229093b73e0
14:34:56.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb96eed-d410-4a1d-ac8a-64ff850a219a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:34:56.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb96eed-d410-4a1d-ac8a-64ff850a219a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:34:56.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb96eed-d410-4a1d-ac8a-64ff850a219a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:34:57.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb96eed-d410-4a1d-ac8a-64ff850a219a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751438097671_127.0.0.1_10980
14:34:57.939 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb96eed-d410-4a1d-ac8a-64ff850a219a_config-0] Notify connected event to listeners.
14:34:57.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb96eed-d410-4a1d-ac8a-64ff850a219a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:57.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb96eed-d410-4a1d-ac8a-64ff850a219a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000229094f0fb0
14:34:58.090 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:35:03.214 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:35:03.214 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:35:03.214 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:35:03.461 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:35:04.304 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:35:04.304 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:35:04.304 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:35:14.528 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:35:19.068 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b1f1339d-b007-4b24-b189-c66b6115cb6a
14:35:19.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1f1339d-b007-4b24-b189-c66b6115cb6a] RpcClient init label, labels = {module=naming, source=sdk}
14:35:19.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1f1339d-b007-4b24-b189-c66b6115cb6a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:35:19.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1f1339d-b007-4b24-b189-c66b6115cb6a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:35:19.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1f1339d-b007-4b24-b189-c66b6115cb6a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:35:19.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1f1339d-b007-4b24-b189-c66b6115cb6a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:35:19.213 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1f1339d-b007-4b24-b189-c66b6115cb6a] Success to connect to server [localhost:8848] on start up, connectionId = 1751438119085_127.0.0.1_11018
14:35:19.214 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1f1339d-b007-4b24-b189-c66b6115cb6a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:35:19.214 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1f1339d-b007-4b24-b189-c66b6115cb6a] Notify connected event to listeners.
14:35:19.214 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1f1339d-b007-4b24-b189-c66b6115cb6a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000229094f0fb0
14:35:19.295 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:35:19.337 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:35:19.543 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.127 seconds (JVM running for 26.587)
14:35:19.575 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:35:19.575 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:35:19.575 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:35:19.774 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1f1339d-b007-4b24-b189-c66b6115cb6a] Receive server push request, request = NotifySubscriberRequest, requestId = 84
14:35:19.790 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1f1339d-b007-4b24-b189-c66b6115cb6a] Ack server push request, request = NotifySubscriberRequest, requestId = 84
14:35:31.394 [http-nio-9600-exec-4] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:40:53.790 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:40:53.800 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:40:54.140 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:40:54.140 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@af1c497[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:40:54.140 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751438119085_127.0.0.1_11018
14:40:54.143 [nacos-grpc-client-executor-77] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751438119085_127.0.0.1_11018]Ignore complete event,isRunning:false,isAbandon=false
14:40:54.146 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6044ed99[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 78]
14:40:54.187 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:40:54.191 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:40:54.202 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:40:54.202 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:41:01.537 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:41:02.753 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bb69d46d-bb8c-4f09-8027-a57afdbf158b_config-0
14:41:02.855 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
14:41:02.908 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:41:02.921 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:41:02.934 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:41:02.947 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
14:41:02.964 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
14:41:02.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb69d46d-bb8c-4f09-8027-a57afdbf158b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:41:02.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb69d46d-bb8c-4f09-8027-a57afdbf158b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001f7113b6af8
14:41:02.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb69d46d-bb8c-4f09-8027-a57afdbf158b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001f7113b6d18
14:41:02.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb69d46d-bb8c-4f09-8027-a57afdbf158b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:41:02.971 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb69d46d-bb8c-4f09-8027-a57afdbf158b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:41:02.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb69d46d-bb8c-4f09-8027-a57afdbf158b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:41:04.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb69d46d-bb8c-4f09-8027-a57afdbf158b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751438464014_127.0.0.1_11501
14:41:04.300 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb69d46d-bb8c-4f09-8027-a57afdbf158b_config-0] Notify connected event to listeners.
14:41:04.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb69d46d-bb8c-4f09-8027-a57afdbf158b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:41:04.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb69d46d-bb8c-4f09-8027-a57afdbf158b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f7114f0ad8
14:41:04.485 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:41:09.666 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:41:09.667 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:41:09.667 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:41:09.917 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:41:10.839 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:41:10.841 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:41:10.842 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:41:21.089 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:41:25.329 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2fce5a4d-c7f5-4c11-9cc0-82efa3c5a999
14:41:25.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fce5a4d-c7f5-4c11-9cc0-82efa3c5a999] RpcClient init label, labels = {module=naming, source=sdk}
14:41:25.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fce5a4d-c7f5-4c11-9cc0-82efa3c5a999] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:41:25.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fce5a4d-c7f5-4c11-9cc0-82efa3c5a999] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:41:25.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fce5a4d-c7f5-4c11-9cc0-82efa3c5a999] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:41:25.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fce5a4d-c7f5-4c11-9cc0-82efa3c5a999] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:41:25.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fce5a4d-c7f5-4c11-9cc0-82efa3c5a999] Success to connect to server [localhost:8848] on start up, connectionId = 1751438485352_127.0.0.1_11524
14:41:25.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fce5a4d-c7f5-4c11-9cc0-82efa3c5a999] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:41:25.476 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fce5a4d-c7f5-4c11-9cc0-82efa3c5a999] Notify connected event to listeners.
14:41:25.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fce5a4d-c7f5-4c11-9cc0-82efa3c5a999] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f7114f0ad8
14:41:25.561 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:41:25.614 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:41:25.799 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.191 seconds (JVM running for 26.608)
14:41:25.827 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:41:25.828 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:41:25.829 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:41:26.123 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fce5a4d-c7f5-4c11-9cc0-82efa3c5a999] Receive server push request, request = NotifySubscriberRequest, requestId = 86
14:41:26.148 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fce5a4d-c7f5-4c11-9cc0-82efa3c5a999] Ack server push request, request = NotifySubscriberRequest, requestId = 86
14:41:31.150 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:13:07.304 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:13:07.304 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:13:07.648 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:13:07.648 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@15b67999[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:13:07.648 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751438485352_127.0.0.1_11524
15:13:07.654 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@306b68b9[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 389]
15:13:07.658 [nacos-grpc-client-executor-389] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751438485352_127.0.0.1_11524]Ignore complete event,isRunning:false,isAbandon=false
15:13:07.704 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:13:07.709 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:13:07.717 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:13:07.717 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:35:27.700 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:35:29.890 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c0e8c417-95c0-4f24-ad0f-0ab5828cb5bf_config-0
17:35:30.071 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 83 ms to scan 1 urls, producing 3 keys and 6 values 
17:35:30.146 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
17:35:30.168 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
17:35:30.181 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
17:35:30.198 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
17:35:30.223 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
17:35:30.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e8c417-95c0-4f24-ad0f-0ab5828cb5bf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:35:30.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e8c417-95c0-4f24-ad0f-0ab5828cb5bf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000212443b5d00
17:35:30.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e8c417-95c0-4f24-ad0f-0ab5828cb5bf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000212443b5f20
17:35:30.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e8c417-95c0-4f24-ad0f-0ab5828cb5bf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:35:30.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e8c417-95c0-4f24-ad0f-0ab5828cb5bf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:35:30.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e8c417-95c0-4f24-ad0f-0ab5828cb5bf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:35:31.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e8c417-95c0-4f24-ad0f-0ab5828cb5bf_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751448931671_127.0.0.1_13031
17:35:31.985 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e8c417-95c0-4f24-ad0f-0ab5828cb5bf_config-0] Notify connected event to listeners.
17:35:31.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e8c417-95c0-4f24-ad0f-0ab5828cb5bf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:31.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e8c417-95c0-4f24-ad0f-0ab5828cb5bf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000212444efcb0
17:35:32.222 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:35:38.686 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:35:38.686 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:35:38.688 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:35:38.994 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:35:40.062 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:35:40.062 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:35:40.062 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:35:53.570 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:36:01.940 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5e79581e-83c4-432e-82a4-602b9c5550fa
17:36:01.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e79581e-83c4-432e-82a4-602b9c5550fa] RpcClient init label, labels = {module=naming, source=sdk}
17:36:01.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e79581e-83c4-432e-82a4-602b9c5550fa] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:36:01.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e79581e-83c4-432e-82a4-602b9c5550fa] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:36:01.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e79581e-83c4-432e-82a4-602b9c5550fa] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:36:01.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e79581e-83c4-432e-82a4-602b9c5550fa] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:36:02.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e79581e-83c4-432e-82a4-602b9c5550fa] Success to connect to server [localhost:8848] on start up, connectionId = 1751448961963_127.0.0.1_13105
17:36:02.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e79581e-83c4-432e-82a4-602b9c5550fa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:36:02.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e79581e-83c4-432e-82a4-602b9c5550fa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000212444efcb0
17:36:02.092 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e79581e-83c4-432e-82a4-602b9c5550fa] Notify connected event to listeners.
17:36:02.192 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:36:02.279 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:36:02.649 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 37.112 seconds (JVM running for 40.411)
17:36:02.691 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:36:02.691 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:36:02.691 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:36:02.742 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e79581e-83c4-432e-82a4-602b9c5550fa] Receive server push request, request = NotifySubscriberRequest, requestId = 98
17:36:02.771 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e79581e-83c4-432e-82a4-602b9c5550fa] Ack server push request, request = NotifySubscriberRequest, requestId = 98
17:36:26.882 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:36:28.824 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:36:28.825 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:44:53.976 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:44:53.976 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:44:54.342 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:44:54.342 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@58baca5a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:44:54.342 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751448961963_127.0.0.1_13105
17:44:54.342 [nacos-grpc-client-executor-114] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751448961963_127.0.0.1_13105]Ignore complete event,isRunning:false,isAbandon=false
17:44:54.350 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@41c9aec4[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 115]
17:44:54.528 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:44:54.528 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:44:54.542 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:44:54.542 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:44:54.548 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:44:54.548 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:44:59.455 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:45:00.626 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1eb14772-d255-4125-b013-93b8cce142b8_config-0
17:45:00.722 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
17:45:00.774 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
17:45:00.789 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
17:45:00.808 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
17:45:00.825 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
17:45:00.837 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
17:45:00.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:45:00.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001edc33b6480
17:45:00.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001edc33b66a0
17:45:00.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:45:00.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:45:00.857 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:45:02.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751449501831_127.0.0.1_13919
17:45:02.104 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] Notify connected event to listeners.
17:45:02.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:45:02.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001edc34f0228
17:45:02.324 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:45:07.642 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:45:07.650 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:45:07.650 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:45:07.932 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:45:08.849 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:45:08.852 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:45:08.852 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:45:19.381 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:45:23.671 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3d20bbb6-d534-41c5-bc38-1093249d495a
17:45:23.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] RpcClient init label, labels = {module=naming, source=sdk}
17:45:23.677 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:45:23.677 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:45:23.677 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:45:23.677 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:45:23.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Success to connect to server [localhost:8848] on start up, connectionId = 1751449523691_127.0.0.1_13940
17:45:23.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:45:23.817 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Notify connected event to listeners.
17:45:23.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001edc34f0228
17:45:23.917 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:45:23.977 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:45:24.172 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.563 seconds (JVM running for 27.113)
17:45:24.224 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:45:24.225 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:45:24.225 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:45:24.373 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Receive server push request, request = NotifySubscriberRequest, requestId = 104
17:45:24.397 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Ack server push request, request = NotifySubscriberRequest, requestId = 104
17:45:43.905 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:45:45.956 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:45:45.956 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:54:41.210 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Server healthy check fail, currentConnection = 1751449523691_127.0.0.1_13940
17:54:42.892 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] Server healthy check fail, currentConnection = 1751449501831_127.0.0.1_13919
17:54:42.899 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:54:45.387 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:55:00.061 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Success to connect a server [localhost:8848], connectionId = 1751450098445_127.0.0.1_14686
17:55:00.061 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] Success to connect a server [localhost:8848], connectionId = 1751450098239_127.0.0.1_14685
17:55:00.062 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Abandon prev connection, server is localhost:8848, connectionId is 1751449523691_127.0.0.1_13940
17:55:00.063 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751449523691_127.0.0.1_13940
17:55:00.063 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751449501831_127.0.0.1_13919
17:55:00.066 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751449501831_127.0.0.1_13919
17:55:00.989 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] Notify disconnected event to listeners
17:55:00.067 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Notify disconnected event to listeners
17:55:00.992 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eb14772-d255-4125-b013-93b8cce142b8_config-0] Notify connected event to listeners.
17:55:00.995 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Notify connected event to listeners.
17:56:15.624 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Server healthy check fail, currentConnection = 1751450098445_127.0.0.1_14686
17:56:15.626 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Try to reconnect to a new server, server is  not appointed, will choose a random server.
18:17:16.190 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Success to connect a server [localhost:8848], connectionId = 1751450183240_127.0.0.1_14778
18:17:16.190 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Abandon prev connection, server is localhost:8848, connectionId is 1751450098445_127.0.0.1_14686
18:17:16.190 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751450098445_127.0.0.1_14686
18:17:16.194 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Notify disconnected event to listeners
18:17:16.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Notify connected event to listeners.
18:17:16.217 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Server check success, currentServer is localhost:8848 
18:26:04.087 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Server check success, currentServer is localhost:8848 
18:26:07.628 [nacos-grpc-client-executor-118] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Receive server push request, request = NotifySubscriberRequest, requestId = 109
18:26:07.651 [nacos-grpc-client-executor-118] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d20bbb6-d534-41c5-bc38-1093249d495a] Ack server push request, request = NotifySubscriberRequest, requestId = 109
18:29:23.037 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:29:23.048 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:29:23.386 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:29:23.386 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1b45ee7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:29:23.386 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751450183240_127.0.0.1_14778
18:29:23.388 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6176a62f[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 160]
18:29:23.559 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:29:23.559 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:29:23.584 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:29:23.584 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:29:23.587 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:29:23.587 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:29:31.517 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:29:32.688 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ee40f36d-737a-4be8-b232-54c77e0224d1_config-0
18:29:32.800 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 63 ms to scan 1 urls, producing 3 keys and 6 values 
18:29:32.852 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
18:29:32.867 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
18:29:32.878 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
18:29:32.886 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
18:29:32.907 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
18:29:32.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee40f36d-737a-4be8-b232-54c77e0224d1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:29:32.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee40f36d-737a-4be8-b232-54c77e0224d1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000231263b6af8
18:29:32.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee40f36d-737a-4be8-b232-54c77e0224d1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000231263b6d18
18:29:32.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee40f36d-737a-4be8-b232-54c77e0224d1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:29:32.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee40f36d-737a-4be8-b232-54c77e0224d1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:29:32.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee40f36d-737a-4be8-b232-54c77e0224d1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:29:34.315 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee40f36d-737a-4be8-b232-54c77e0224d1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751452174006_127.0.0.1_4990
18:29:34.317 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee40f36d-737a-4be8-b232-54c77e0224d1_config-0] Notify connected event to listeners.
18:29:34.318 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee40f36d-737a-4be8-b232-54c77e0224d1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:29:34.318 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee40f36d-737a-4be8-b232-54c77e0224d1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000231264f0ad8
18:29:34.501 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:29:40.151 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:29:40.152 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:29:40.152 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:29:40.435 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:29:41.351 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:29:41.351 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:29:41.351 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:29:52.200 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:29:56.545 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1f3ac649-d63a-4377-848c-fbf5835a5259
18:29:56.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3ac649-d63a-4377-848c-fbf5835a5259] RpcClient init label, labels = {module=naming, source=sdk}
18:29:56.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3ac649-d63a-4377-848c-fbf5835a5259] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:29:56.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3ac649-d63a-4377-848c-fbf5835a5259] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:29:56.550 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3ac649-d63a-4377-848c-fbf5835a5259] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:29:56.551 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3ac649-d63a-4377-848c-fbf5835a5259] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:29:56.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3ac649-d63a-4377-848c-fbf5835a5259] Success to connect to server [localhost:8848] on start up, connectionId = 1751452196559_127.0.0.1_5023
18:29:56.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3ac649-d63a-4377-848c-fbf5835a5259] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:29:56.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3ac649-d63a-4377-848c-fbf5835a5259] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000231264f0ad8
18:29:56.681 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3ac649-d63a-4377-848c-fbf5835a5259] Notify connected event to listeners.
18:29:56.795 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:29:56.862 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:29:57.051 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.415 seconds (JVM running for 27.942)
18:29:57.098 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:29:57.101 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:29:57.101 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:29:57.312 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3ac649-d63a-4377-848c-fbf5835a5259] Receive server push request, request = NotifySubscriberRequest, requestId = 112
18:29:57.340 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3ac649-d63a-4377-848c-fbf5835a5259] Ack server push request, request = NotifySubscriberRequest, requestId = 112
18:30:01.410 [http-nio-9600-exec-6] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:30:03.421 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:30:03.421 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:08:56.684 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:08:56.690 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:08:57.027 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:08:57.028 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4d2ec356[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:08:57.028 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751452196559_127.0.0.1_5023
19:08:57.034 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@36973bae[Running, pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 479]
19:08:57.035 [nacos-grpc-client-executor-479] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751452196559_127.0.0.1_5023]Ignore complete event,isRunning:false,isAbandon=false
19:08:57.232 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:08:57.237 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:08:57.251 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:08:57.251 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:08:57.251 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:08:57.251 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:09:05.575 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:09:06.858 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0ff5b422-6002-4c21-8df7-117ac603dbca_config-0
19:09:06.957 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
19:09:07.007 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
19:09:07.021 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
19:09:07.036 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
19:09:07.052 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
19:09:07.069 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
19:09:07.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ff5b422-6002-4c21-8df7-117ac603dbca_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:09:07.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ff5b422-6002-4c21-8df7-117ac603dbca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000020c913beaf8
19:09:07.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ff5b422-6002-4c21-8df7-117ac603dbca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000020c913bed18
19:09:07.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ff5b422-6002-4c21-8df7-117ac603dbca_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:09:07.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ff5b422-6002-4c21-8df7-117ac603dbca_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:09:07.086 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ff5b422-6002-4c21-8df7-117ac603dbca_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:09:08.546 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ff5b422-6002-4c21-8df7-117ac603dbca_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751454548245_127.0.0.1_9365
19:09:08.548 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ff5b422-6002-4c21-8df7-117ac603dbca_config-0] Notify connected event to listeners.
19:09:08.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ff5b422-6002-4c21-8df7-117ac603dbca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:09:08.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ff5b422-6002-4c21-8df7-117ac603dbca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020c914f8ad8
19:09:08.772 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:09:14.126 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:09:14.127 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:09:14.127 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:09:14.513 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:09:15.494 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:09:15.496 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:09:15.498 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:09:26.949 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:09:33.277 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8ecf44c8-30cd-4c07-9404-db9bab4539d2
19:09:33.277 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf44c8-30cd-4c07-9404-db9bab4539d2] RpcClient init label, labels = {module=naming, source=sdk}
19:09:33.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf44c8-30cd-4c07-9404-db9bab4539d2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:09:33.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf44c8-30cd-4c07-9404-db9bab4539d2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:09:33.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf44c8-30cd-4c07-9404-db9bab4539d2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:09:33.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf44c8-30cd-4c07-9404-db9bab4539d2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:09:33.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf44c8-30cd-4c07-9404-db9bab4539d2] Success to connect to server [localhost:8848] on start up, connectionId = 1751454573294_127.0.0.1_9422
19:09:33.414 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf44c8-30cd-4c07-9404-db9bab4539d2] Notify connected event to listeners.
19:09:33.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf44c8-30cd-4c07-9404-db9bab4539d2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:09:33.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf44c8-30cd-4c07-9404-db9bab4539d2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020c914f8ad8
19:09:33.519 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:09:33.569 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:09:33.764 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 29.205 seconds (JVM running for 30.8)
19:09:33.811 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:09:33.811 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:09:33.813 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:09:34.007 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf44c8-30cd-4c07-9404-db9bab4539d2] Receive server push request, request = NotifySubscriberRequest, requestId = 115
19:09:34.033 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf44c8-30cd-4c07-9404-db9bab4539d2] Ack server push request, request = NotifySubscriberRequest, requestId = 115
19:09:34.109 [RMI TCP Connection(12)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:09:53.996 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf44c8-30cd-4c07-9404-db9bab4539d2] Receive server push request, request = NotifySubscriberRequest, requestId = 117
19:09:53.998 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf44c8-30cd-4c07-9404-db9bab4539d2] Ack server push request, request = NotifySubscriberRequest, requestId = 117
19:09:59.732 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:09:59.732 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
19:09:59.734 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:09:59.734 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
19:09:59.734 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-6} inited
19:09:59.734 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
19:09:59.739 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:09:59.759 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:09:59.761 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:09:59.761 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
19:09:59.765 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
19:09:59.765 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:09:59.769 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
19:09:59.771 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
19:09:59.772 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:09:59.772 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-6} closing ...
19:09:59.775 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-6} closed
19:09:59.776 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:13:53.907 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:13:53.915 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:13:54.251 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:13:54.252 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@25d13bdc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:13:54.252 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751454573294_127.0.0.1_9422
19:13:54.254 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751454573294_127.0.0.1_9422]Ignore complete event,isRunning:false,isAbandon=false
19:13:54.260 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@cdc08cf[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 65]
19:13:54.432 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:13:54.432 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
19:13:54.435 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
19:13:54.435 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:13:54.436 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:13:54.437 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:14:02.600 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:14:03.863 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 64df7ea9-2eee-49ad-807d-c77258fc0176_config-0
19:14:03.986 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 57 ms to scan 1 urls, producing 3 keys and 6 values 
19:14:04.045 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
19:14:04.058 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
19:14:04.070 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
19:14:04.083 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
19:14:04.098 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
19:14:04.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64df7ea9-2eee-49ad-807d-c77258fc0176_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:14:04.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64df7ea9-2eee-49ad-807d-c77258fc0176_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001eac33cdd70
19:14:04.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64df7ea9-2eee-49ad-807d-c77258fc0176_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001eac33cdf90
19:14:04.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64df7ea9-2eee-49ad-807d-c77258fc0176_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:14:04.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64df7ea9-2eee-49ad-807d-c77258fc0176_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:14:04.115 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64df7ea9-2eee-49ad-807d-c77258fc0176_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:14:05.515 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64df7ea9-2eee-49ad-807d-c77258fc0176_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751454845218_127.0.0.1_10208
19:14:05.518 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64df7ea9-2eee-49ad-807d-c77258fc0176_config-0] Notify connected event to listeners.
19:14:05.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64df7ea9-2eee-49ad-807d-c77258fc0176_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:14:05.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64df7ea9-2eee-49ad-807d-c77258fc0176_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001eac3507b88
19:14:05.718 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:14:11.040 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:14:11.041 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:14:11.041 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:14:11.297 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:14:12.348 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:14:12.351 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:14:12.351 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:14:24.058 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:14:28.885 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 30d4b0f9-e3a5-47b1-8368-9a14d760e577
19:14:28.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30d4b0f9-e3a5-47b1-8368-9a14d760e577] RpcClient init label, labels = {module=naming, source=sdk}
19:14:28.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30d4b0f9-e3a5-47b1-8368-9a14d760e577] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:14:28.890 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30d4b0f9-e3a5-47b1-8368-9a14d760e577] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:14:28.890 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30d4b0f9-e3a5-47b1-8368-9a14d760e577] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:14:28.891 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30d4b0f9-e3a5-47b1-8368-9a14d760e577] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:14:29.033 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30d4b0f9-e3a5-47b1-8368-9a14d760e577] Success to connect to server [localhost:8848] on start up, connectionId = 1751454868904_127.0.0.1_10283
19:14:29.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30d4b0f9-e3a5-47b1-8368-9a14d760e577] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:14:29.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30d4b0f9-e3a5-47b1-8368-9a14d760e577] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001eac3507b88
19:14:29.034 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30d4b0f9-e3a5-47b1-8368-9a14d760e577] Notify connected event to listeners.
19:14:29.132 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:14:29.180 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:14:29.366 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 27.656 seconds (JVM running for 29.259)
19:14:29.389 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:14:29.390 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:14:29.391 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:14:29.613 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30d4b0f9-e3a5-47b1-8368-9a14d760e577] Receive server push request, request = NotifySubscriberRequest, requestId = 124
19:14:29.636 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [30d4b0f9-e3a5-47b1-8368-9a14d760e577] Ack server push request, request = NotifySubscriberRequest, requestId = 124
19:14:29.853 [RMI TCP Connection(14)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:14:42.554 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-6} inited
19:14:42.554 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:14:42.564 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
19:14:42.564 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:14:42.568 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
19:14:42.569 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-6} closing ...
19:14:42.571 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
19:14:42.573 [http-nio-9600-exec-9] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-7} inited
19:14:42.581 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-6} closed
19:14:42.582 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:14:42.582 [http-nio-9600-exec-9] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
19:14:42.584 [http-nio-9600-exec-9] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
19:14:42.584 [http-nio-9600-exec-9] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:14:42.584 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-7} closing ...
19:14:42.586 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-7} closed
19:14:42.586 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:14:42.587 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
19:14:42.588 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
19:14:42.588 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:14:42.589 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
19:14:42.590 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
19:14:42.591 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:22:31.476 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:22:31.495 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:22:31.835 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:22:31.836 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@75415d1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:22:31.836 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751454868904_127.0.0.1_10283
19:22:31.840 [nacos-grpc-client-executor-106] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751454868904_127.0.0.1_10283]Ignore complete event,isRunning:false,isAbandon=false
19:22:31.842 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@683fdda7[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 107]
19:22:32.047 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:22:32.048 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:22:32.051 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:22:32.052 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:22:32.053 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:22:32.055 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:22:39.678 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:22:40.729 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6fbf4921-af48-4404-8be2-625236d07e68_config-0
19:22:40.817 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
19:22:40.870 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
19:22:40.881 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
19:22:40.893 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
19:22:40.906 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
19:22:40.925 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
19:22:40.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fbf4921-af48-4404-8be2-625236d07e68_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:22:40.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fbf4921-af48-4404-8be2-625236d07e68_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001cb1539e8d8
19:22:40.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fbf4921-af48-4404-8be2-625236d07e68_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001cb1539eaf8
19:22:40.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fbf4921-af48-4404-8be2-625236d07e68_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:22:40.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fbf4921-af48-4404-8be2-625236d07e68_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:22:40.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fbf4921-af48-4404-8be2-625236d07e68_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:42.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fbf4921-af48-4404-8be2-625236d07e68_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751455361921_127.0.0.1_11054
19:22:42.176 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fbf4921-af48-4404-8be2-625236d07e68_config-0] Notify connected event to listeners.
19:22:42.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fbf4921-af48-4404-8be2-625236d07e68_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:22:42.180 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fbf4921-af48-4404-8be2-625236d07e68_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001cb15518228
19:22:42.370 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:22:47.247 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:22:47.248 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:22:47.248 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:22:47.480 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:22:48.307 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:22:48.309 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:22:48.310 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:22:57.970 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:23:01.985 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 57c77656-18d8-4c27-a101-c1007c718bc9
19:23:01.986 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57c77656-18d8-4c27-a101-c1007c718bc9] RpcClient init label, labels = {module=naming, source=sdk}
19:23:01.987 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57c77656-18d8-4c27-a101-c1007c718bc9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:23:01.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57c77656-18d8-4c27-a101-c1007c718bc9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:23:01.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57c77656-18d8-4c27-a101-c1007c718bc9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:23:01.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57c77656-18d8-4c27-a101-c1007c718bc9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:23:02.124 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57c77656-18d8-4c27-a101-c1007c718bc9] Success to connect to server [localhost:8848] on start up, connectionId = 1751455382002_127.0.0.1_11121
19:23:02.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57c77656-18d8-4c27-a101-c1007c718bc9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:23:02.125 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57c77656-18d8-4c27-a101-c1007c718bc9] Notify connected event to listeners.
19:23:02.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57c77656-18d8-4c27-a101-c1007c718bc9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001cb15518228
19:23:02.218 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:23:02.272 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:23:02.453 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.627 seconds (JVM running for 25.147)
19:23:02.477 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:23:02.478 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:23:02.479 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:23:02.750 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57c77656-18d8-4c27-a101-c1007c718bc9] Receive server push request, request = NotifySubscriberRequest, requestId = 128
19:23:02.772 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57c77656-18d8-4c27-a101-c1007c718bc9] Ack server push request, request = NotifySubscriberRequest, requestId = 128
19:23:02.848 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:24:42.055 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:24:42.060 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:24:42.389 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:24:42.390 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@50f18bf[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:24:42.390 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751455382002_127.0.0.1_11121
19:24:42.395 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751455382002_127.0.0.1_11121]Ignore complete event,isRunning:false,isAbandon=false
19:24:42.397 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@509b7a5[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 29]
19:24:42.571 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:24:42.574 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:24:42.584 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:24:42.584 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:24:50.019 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:24:51.074 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e250312d-f50e-48c9-af14-791604940291_config-0
19:24:51.179 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
19:24:51.234 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
19:24:51.245 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
19:24:51.259 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
19:24:51.273 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
19:24:51.294 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
19:24:51.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e250312d-f50e-48c9-af14-791604940291_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:24:51.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e250312d-f50e-48c9-af14-791604940291_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002df0139e480
19:24:51.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e250312d-f50e-48c9-af14-791604940291_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002df0139e6a0
19:24:51.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e250312d-f50e-48c9-af14-791604940291_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:24:51.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e250312d-f50e-48c9-af14-791604940291_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:24:51.316 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e250312d-f50e-48c9-af14-791604940291_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:24:52.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e250312d-f50e-48c9-af14-791604940291_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751455492310_127.0.0.1_11329
19:24:52.564 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e250312d-f50e-48c9-af14-791604940291_config-0] Notify connected event to listeners.
19:24:52.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e250312d-f50e-48c9-af14-791604940291_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:24:52.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e250312d-f50e-48c9-af14-791604940291_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002df01518228
19:24:52.763 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:24:57.827 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:24:57.828 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:24:57.828 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:24:58.086 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:24:58.935 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:24:58.938 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:24:58.939 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:25:09.412 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:25:13.643 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6086dfb9-f192-4bdf-b275-9420ad9d63ba
19:25:13.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6086dfb9-f192-4bdf-b275-9420ad9d63ba] RpcClient init label, labels = {module=naming, source=sdk}
19:25:13.647 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6086dfb9-f192-4bdf-b275-9420ad9d63ba] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:25:13.647 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6086dfb9-f192-4bdf-b275-9420ad9d63ba] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:25:13.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6086dfb9-f192-4bdf-b275-9420ad9d63ba] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:25:13.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6086dfb9-f192-4bdf-b275-9420ad9d63ba] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:25:13.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6086dfb9-f192-4bdf-b275-9420ad9d63ba] Success to connect to server [localhost:8848] on start up, connectionId = 1751455513662_127.0.0.1_11384
19:25:13.789 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6086dfb9-f192-4bdf-b275-9420ad9d63ba] Notify connected event to listeners.
19:25:13.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6086dfb9-f192-4bdf-b275-9420ad9d63ba] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:25:13.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6086dfb9-f192-4bdf-b275-9420ad9d63ba] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002df01518228
19:25:13.878 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:25:13.928 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:25:14.122 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.943 seconds (JVM running for 26.407)
19:25:14.156 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:25:14.157 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:25:14.157 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:25:14.380 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:25:14.469 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6086dfb9-f192-4bdf-b275-9420ad9d63ba] Receive server push request, request = NotifySubscriberRequest, requestId = 134
19:25:14.505 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6086dfb9-f192-4bdf-b275-9420ad9d63ba] Ack server push request, request = NotifySubscriberRequest, requestId = 134
19:39:05.916 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:39:05.920 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:39:06.248 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:39:06.248 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2fd1a2a0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:39:06.248 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751455513662_127.0.0.1_11384
19:39:06.251 [nacos-grpc-client-executor-177] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751455513662_127.0.0.1_11384]Ignore complete event,isRunning:false,isAbandon=false
19:39:06.255 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@50b33401[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 178]
19:39:06.416 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:39:06.421 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:39:06.436 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:39:06.437 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:39:15.083 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:39:16.131 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cbee129a-d388-4cb3-95c1-ee41221258cd_config-0
19:39:16.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
19:39:16.282 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
19:39:16.295 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
19:39:16.310 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
19:39:16.323 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
19:39:16.341 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
19:39:16.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbee129a-d388-4cb3-95c1-ee41221258cd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:39:16.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbee129a-d388-4cb3-95c1-ee41221258cd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000220b139eaf8
19:39:16.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbee129a-d388-4cb3-95c1-ee41221258cd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000220b139ed18
19:39:16.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbee129a-d388-4cb3-95c1-ee41221258cd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:39:16.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbee129a-d388-4cb3-95c1-ee41221258cd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:39:16.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbee129a-d388-4cb3-95c1-ee41221258cd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:39:17.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbee129a-d388-4cb3-95c1-ee41221258cd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751456357382_127.0.0.1_12627
19:39:17.638 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbee129a-d388-4cb3-95c1-ee41221258cd_config-0] Notify connected event to listeners.
19:39:17.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbee129a-d388-4cb3-95c1-ee41221258cd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:39:17.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbee129a-d388-4cb3-95c1-ee41221258cd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000220b1518668
19:39:17.834 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:39:22.931 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:39:22.932 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:39:22.932 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:39:23.164 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:39:24.048 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:39:24.051 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:39:24.052 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:39:34.603 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:39:38.857 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4c3952c0-5a52-4208-9247-dbbba901e294
19:39:38.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c3952c0-5a52-4208-9247-dbbba901e294] RpcClient init label, labels = {module=naming, source=sdk}
19:39:38.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c3952c0-5a52-4208-9247-dbbba901e294] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:39:38.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c3952c0-5a52-4208-9247-dbbba901e294] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:39:38.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c3952c0-5a52-4208-9247-dbbba901e294] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:39:38.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c3952c0-5a52-4208-9247-dbbba901e294] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:39:38.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c3952c0-5a52-4208-9247-dbbba901e294] Success to connect to server [localhost:8848] on start up, connectionId = 1751456378873_127.0.0.1_12672
19:39:39.000 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c3952c0-5a52-4208-9247-dbbba901e294] Notify connected event to listeners.
19:39:39.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c3952c0-5a52-4208-9247-dbbba901e294] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:39:39.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c3952c0-5a52-4208-9247-dbbba901e294] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000220b1518668
19:39:39.078 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:39:39.123 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:39:39.294 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.067 seconds (JVM running for 26.553)
19:39:39.323 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:39:39.324 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:39:39.325 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:39:39.594 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c3952c0-5a52-4208-9247-dbbba901e294] Receive server push request, request = NotifySubscriberRequest, requestId = 138
19:39:39.621 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c3952c0-5a52-4208-9247-dbbba901e294] Ack server push request, request = NotifySubscriberRequest, requestId = 138
19:39:39.621 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:41:37.093 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:41:37.095 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:28:35.212 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:28:35.214 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:28:35.543 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:28:35.543 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5e30316a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:28:35.543 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751456378873_127.0.0.1_12672
20:28:35.547 [nacos-grpc-client-executor-586] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751456378873_127.0.0.1_12672]Ignore complete event,isRunning:false,isAbandon=false
20:28:35.549 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1b6248f8[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 587]
20:28:35.711 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:28:35.711 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:28:35.726 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:28:35.726 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:28:35.726 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:28:35.726 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:29:01.207 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:29:02.315 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 753b6aff-a556-4173-aa37-5adec2c756ab_config-0
20:29:02.424 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
20:29:02.482 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
20:29:02.499 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
20:29:02.509 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
20:29:02.525 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
20:29:02.538 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
20:29:02.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [753b6aff-a556-4173-aa37-5adec2c756ab_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:29:02.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [753b6aff-a556-4173-aa37-5adec2c756ab_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002063538b188
20:29:02.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [753b6aff-a556-4173-aa37-5adec2c756ab_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$471/0x000002063538b3a8
20:29:02.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [753b6aff-a556-4173-aa37-5adec2c756ab_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:29:02.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [753b6aff-a556-4173-aa37-5adec2c756ab_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:29:02.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [753b6aff-a556-4173-aa37-5adec2c756ab_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:29:03.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [753b6aff-a556-4173-aa37-5adec2c756ab_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751459343574_127.0.0.1_4948
20:29:03.820 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [753b6aff-a556-4173-aa37-5adec2c756ab_config-0] Notify connected event to listeners.
20:29:03.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [753b6aff-a556-4173-aa37-5adec2c756ab_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:29:03.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [753b6aff-a556-4173-aa37-5adec2c756ab_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$485/0x00000206354e4910
20:29:03.960 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:29:04.556 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:29:05.709 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 84b0d21c-20b5-40ff-a965-683fe87bdd6c_config-0
20:29:05.809 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
20:29:05.874 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
20:29:05.884 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
20:29:05.898 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
20:29:05.909 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
20:29:05.925 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
20:29:05.927 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b0d21c-20b5-40ff-a965-683fe87bdd6c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:29:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b0d21c-20b5-40ff-a965-683fe87bdd6c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000142013b68d8
20:29:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b0d21c-20b5-40ff-a965-683fe87bdd6c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000142013b6af8
20:29:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b0d21c-20b5-40ff-a965-683fe87bdd6c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:29:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b0d21c-20b5-40ff-a965-683fe87bdd6c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:29:05.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b0d21c-20b5-40ff-a965-683fe87bdd6c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:29:07.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b0d21c-20b5-40ff-a965-683fe87bdd6c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751459346980_127.0.0.1_4961
20:29:07.257 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b0d21c-20b5-40ff-a965-683fe87bdd6c_config-0] Notify connected event to listeners.
20:29:07.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b0d21c-20b5-40ff-a965-683fe87bdd6c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:29:07.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b0d21c-20b5-40ff-a965-683fe87bdd6c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000142014f0ad8
20:29:07.435 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:29:09.224 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:29:09.224 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:29:09.224 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:29:09.459 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:29:10.241 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:29:10.241 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:29:10.241 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:29:11.291 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:29:11.307 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:29:11.317 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:29:11.319 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:29:11.321 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
20:29:13.204 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:29:13.206 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:29:13.206 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:29:13.487 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:29:14.469 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:29:14.472 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:29:14.474 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:29:15.666 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:29:15.666 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:29:15.683 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:29:15.683 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:29:15.687 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
20:29:53.465 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:29:54.603 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0524e6d2-ed95-48c4-ba02-ebfb9917aeb7_config-0
20:29:54.709 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
20:29:54.759 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
20:29:54.765 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
20:29:54.777 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
20:29:54.798 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 7 values 
20:29:54.811 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
20:29:54.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0524e6d2-ed95-48c4-ba02-ebfb9917aeb7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:29:54.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0524e6d2-ed95-48c4-ba02-ebfb9917aeb7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f8dc3ce480
20:29:54.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0524e6d2-ed95-48c4-ba02-ebfb9917aeb7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001f8dc3ce6a0
20:29:54.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0524e6d2-ed95-48c4-ba02-ebfb9917aeb7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:29:54.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0524e6d2-ed95-48c4-ba02-ebfb9917aeb7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:29:54.831 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0524e6d2-ed95-48c4-ba02-ebfb9917aeb7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:29:56.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0524e6d2-ed95-48c4-ba02-ebfb9917aeb7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751459395850_127.0.0.1_5126
20:29:56.107 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0524e6d2-ed95-48c4-ba02-ebfb9917aeb7_config-0] Notify connected event to listeners.
20:29:56.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0524e6d2-ed95-48c4-ba02-ebfb9917aeb7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:29:56.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0524e6d2-ed95-48c4-ba02-ebfb9917aeb7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f8dc508228
20:29:56.278 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:30:01.947 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:30:01.947 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:30:01.947 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:30:02.250 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:30:03.179 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:30:03.185 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:30:03.185 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:30:04.359 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:30:04.364 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:30:04.374 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:30:04.374 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:30:04.374 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
20:34:21.520 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:34:22.606 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ab9d5d5a-f1cc-40ef-915f-11c36cd78a2c_config-0
20:34:22.705 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
20:34:22.762 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
20:34:22.772 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
20:34:22.785 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
20:34:22.799 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
20:34:22.811 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
20:34:22.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab9d5d5a-f1cc-40ef-915f-11c36cd78a2c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:34:22.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab9d5d5a-f1cc-40ef-915f-11c36cd78a2c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000017c3e39ef80
20:34:22.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab9d5d5a-f1cc-40ef-915f-11c36cd78a2c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000017c3e39f1a0
20:34:22.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab9d5d5a-f1cc-40ef-915f-11c36cd78a2c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:34:22.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab9d5d5a-f1cc-40ef-915f-11c36cd78a2c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:34:22.833 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab9d5d5a-f1cc-40ef-915f-11c36cd78a2c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:34:24.087 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab9d5d5a-f1cc-40ef-915f-11c36cd78a2c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751459663834_127.0.0.1_5730
20:34:24.089 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab9d5d5a-f1cc-40ef-915f-11c36cd78a2c_config-0] Notify connected event to listeners.
20:34:24.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab9d5d5a-f1cc-40ef-915f-11c36cd78a2c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:34:24.091 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab9d5d5a-f1cc-40ef-915f-11c36cd78a2c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017c3e518ad8
20:34:24.248 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:34:29.371 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:34:29.372 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:34:29.372 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:34:29.703 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:34:30.714 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:34:30.717 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:34:30.717 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:34:41.067 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:34:45.275 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4c7b40a6-d8f2-41a2-a418-8a63702cdaac
20:34:45.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c7b40a6-d8f2-41a2-a418-8a63702cdaac] RpcClient init label, labels = {module=naming, source=sdk}
20:34:45.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c7b40a6-d8f2-41a2-a418-8a63702cdaac] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:34:45.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c7b40a6-d8f2-41a2-a418-8a63702cdaac] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:34:45.281 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c7b40a6-d8f2-41a2-a418-8a63702cdaac] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:34:45.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c7b40a6-d8f2-41a2-a418-8a63702cdaac] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:34:45.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c7b40a6-d8f2-41a2-a418-8a63702cdaac] Success to connect to server [localhost:8848] on start up, connectionId = 1751459685297_127.0.0.1_5806
20:34:45.413 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c7b40a6-d8f2-41a2-a418-8a63702cdaac] Notify connected event to listeners.
20:34:45.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c7b40a6-d8f2-41a2-a418-8a63702cdaac] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:34:45.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c7b40a6-d8f2-41a2-a418-8a63702cdaac] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017c3e518ad8
20:34:45.506 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:34:45.560 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:34:45.739 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.099 seconds (JVM running for 26.562)
20:34:45.762 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:34:45.763 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:34:45.763 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:34:45.991 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c7b40a6-d8f2-41a2-a418-8a63702cdaac] Receive server push request, request = NotifySubscriberRequest, requestId = 145
20:34:46.014 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c7b40a6-d8f2-41a2-a418-8a63702cdaac] Ack server push request, request = NotifySubscriberRequest, requestId = 145
20:34:46.308 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:40:15.098 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:40:15.099 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:40:15.435 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:40:15.435 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@f7ed11f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:40:15.436 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751459685297_127.0.0.1_5806
20:40:15.439 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@40edb968[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 77]
20:40:15.439 [nacos-grpc-client-executor-77] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751459685297_127.0.0.1_5806]Ignore complete event,isRunning:false,isAbandon=false
20:40:15.605 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:40:15.608 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:40:15.619 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:40:15.619 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:40:22.062 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:40:23.174 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a2925b53-76e7-43d8-9616-61d8e6142fc0_config-0
20:40:23.258 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
20:40:23.310 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
20:40:23.321 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
20:40:23.343 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 5 values 
20:40:23.356 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
20:40:23.368 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
20:40:23.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2925b53-76e7-43d8-9616-61d8e6142fc0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:40:23.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2925b53-76e7-43d8-9616-61d8e6142fc0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002a5223c0b08
20:40:23.373 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2925b53-76e7-43d8-9616-61d8e6142fc0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002a5223c0d28
20:40:23.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2925b53-76e7-43d8-9616-61d8e6142fc0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:40:23.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2925b53-76e7-43d8-9616-61d8e6142fc0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:40:23.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2925b53-76e7-43d8-9616-61d8e6142fc0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:40:24.658 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2925b53-76e7-43d8-9616-61d8e6142fc0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751460024397_127.0.0.1_6635
20:40:24.660 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2925b53-76e7-43d8-9616-61d8e6142fc0_config-0] Notify connected event to listeners.
20:40:24.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2925b53-76e7-43d8-9616-61d8e6142fc0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:40:24.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2925b53-76e7-43d8-9616-61d8e6142fc0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002a5224fa958
20:40:24.835 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:40:30.456 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:40:30.457 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:40:30.457 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:40:30.720 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:40:31.612 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:40:31.614 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:40:31.614 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:40:42.122 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:40:46.326 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 59d1b29e-0ad8-49d3-a03e-ba56dc3cc8c1
20:40:46.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59d1b29e-0ad8-49d3-a03e-ba56dc3cc8c1] RpcClient init label, labels = {module=naming, source=sdk}
20:40:46.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59d1b29e-0ad8-49d3-a03e-ba56dc3cc8c1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:40:46.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59d1b29e-0ad8-49d3-a03e-ba56dc3cc8c1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:40:46.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59d1b29e-0ad8-49d3-a03e-ba56dc3cc8c1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:40:46.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59d1b29e-0ad8-49d3-a03e-ba56dc3cc8c1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:40:46.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59d1b29e-0ad8-49d3-a03e-ba56dc3cc8c1] Success to connect to server [localhost:8848] on start up, connectionId = 1751460046347_127.0.0.1_6702
20:40:46.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59d1b29e-0ad8-49d3-a03e-ba56dc3cc8c1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:40:46.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59d1b29e-0ad8-49d3-a03e-ba56dc3cc8c1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002a5224fa958
20:40:46.466 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59d1b29e-0ad8-49d3-a03e-ba56dc3cc8c1] Notify connected event to listeners.
20:40:46.564 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:40:46.628 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:40:46.811 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.626 seconds (JVM running for 27.081)
20:40:46.833 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:40:46.834 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:40:46.835 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:40:47.120 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59d1b29e-0ad8-49d3-a03e-ba56dc3cc8c1] Receive server push request, request = NotifySubscriberRequest, requestId = 150
20:40:47.134 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:40:47.160 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59d1b29e-0ad8-49d3-a03e-ba56dc3cc8c1] Ack server push request, request = NotifySubscriberRequest, requestId = 150
20:47:22.417 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:47:22.425 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:47:22.742 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:47:22.742 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@74336e33[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:47:22.742 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751460046347_127.0.0.1_6702
20:47:22.746 [nacos-grpc-client-executor-89] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751460046347_127.0.0.1_6702]Ignore complete event,isRunning:false,isAbandon=false
20:47:22.751 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@148b5158[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 90]
20:47:22.911 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:47:22.916 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:47:22.934 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:47:22.934 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:47:29.506 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:47:30.552 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 443c3bae-ce7c-438d-b015-92596ca61cf8_config-0
20:47:30.663 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
20:47:30.718 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
20:47:30.730 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
20:47:30.743 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
20:47:30.759 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
20:47:30.775 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
20:47:30.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [443c3bae-ce7c-438d-b015-92596ca61cf8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:47:30.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [443c3bae-ce7c-438d-b015-92596ca61cf8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f4e339dd70
20:47:30.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [443c3bae-ce7c-438d-b015-92596ca61cf8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001f4e339df90
20:47:30.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [443c3bae-ce7c-438d-b015-92596ca61cf8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:47:30.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [443c3bae-ce7c-438d-b015-92596ca61cf8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:47:30.792 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [443c3bae-ce7c-438d-b015-92596ca61cf8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:47:32.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [443c3bae-ce7c-438d-b015-92596ca61cf8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751460451781_127.0.0.1_7361
20:47:32.045 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [443c3bae-ce7c-438d-b015-92596ca61cf8_config-0] Notify connected event to listeners.
20:47:32.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [443c3bae-ce7c-438d-b015-92596ca61cf8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:47:32.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [443c3bae-ce7c-438d-b015-92596ca61cf8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f4e3517b88
20:47:32.263 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:47:37.503 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:47:37.504 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:47:37.504 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:47:37.723 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:47:38.619 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:47:38.622 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:47:38.622 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:47:54.296 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:48:00.412 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6734119c-9763-4729-bd48-866e5814c739
20:48:00.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6734119c-9763-4729-bd48-866e5814c739] RpcClient init label, labels = {module=naming, source=sdk}
20:48:00.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6734119c-9763-4729-bd48-866e5814c739] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:48:00.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6734119c-9763-4729-bd48-866e5814c739] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:48:00.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6734119c-9763-4729-bd48-866e5814c739] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:48:00.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6734119c-9763-4729-bd48-866e5814c739] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:48:00.571 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6734119c-9763-4729-bd48-866e5814c739] Success to connect to server [localhost:8848] on start up, connectionId = 1751460480443_127.0.0.1_7436
20:48:00.572 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6734119c-9763-4729-bd48-866e5814c739] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:48:00.573 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6734119c-9763-4729-bd48-866e5814c739] Notify connected event to listeners.
20:48:00.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6734119c-9763-4729-bd48-866e5814c739] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f4e3517b88
20:48:00.682 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:48:00.763 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:48:01.057 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 32.367 seconds (JVM running for 33.84)
20:48:01.090 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:48:01.091 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:48:01.092 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:48:01.252 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6734119c-9763-4729-bd48-866e5814c739] Receive server push request, request = NotifySubscriberRequest, requestId = 155
20:48:01.280 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6734119c-9763-4729-bd48-866e5814c739] Ack server push request, request = NotifySubscriberRequest, requestId = 155
20:48:01.665 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:50:04.524 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:50:04.527 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:50:04.862 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:50:04.863 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2fb96150[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:50:04.864 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751460480443_127.0.0.1_7436
20:50:04.868 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751460480443_127.0.0.1_7436]Ignore complete event,isRunning:false,isAbandon=false
20:50:04.872 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 35]
20:50:05.031 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:50:05.031 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:50:05.044 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:50:05.048 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:50:11.975 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:50:13.103 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2009aac1-45fd-4309-baa4-c46315497389_config-0
20:50:13.213 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 59 ms to scan 1 urls, producing 3 keys and 6 values 
20:50:13.265 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
20:50:13.277 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
20:50:13.292 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 5 values 
20:50:13.302 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
20:50:13.317 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
20:50:13.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:50:13.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002281f3b6480
20:50:13.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002281f3b66a0
20:50:13.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:50:13.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:50:13.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:50:14.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751460614307_127.0.0.1_7709
20:50:14.573 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Notify connected event to listeners.
20:50:14.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:50:14.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002281f4f0228
20:50:14.781 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:50:21.706 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:50:21.707 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:50:21.707 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:50:22.121 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:50:23.423 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:50:23.426 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:50:23.426 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:50:38.786 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:50:42.795 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 237526f5-2fbb-4aa3-b8ed-accfb3afc84f
20:50:42.795 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] RpcClient init label, labels = {module=naming, source=sdk}
20:50:42.795 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:50:42.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:50:42.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:50:42.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:50:42.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Success to connect to server [localhost:8848] on start up, connectionId = 1751460642807_127.0.0.1_7777
20:50:42.936 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Notify connected event to listeners.
20:50:42.936 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:50:42.936 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002281f4f0228
20:50:43.036 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:50:43.099 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:50:43.289 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 32.208 seconds (JVM running for 34.0)
20:50:43.306 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:50:43.306 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:50:43.308 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:50:43.541 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Receive server push request, request = NotifySubscriberRequest, requestId = 160
20:50:43.578 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Ack server push request, request = NotifySubscriberRequest, requestId = 160
20:57:15.467 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:58:07.091 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
20:58:07.091 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
21:12:22.412 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:12:22.412 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:12:22.544 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:22.545 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:22.753 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:22.753 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:23.063 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:23.094 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:23.477 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:23.501 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:23.984 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:24.035 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:24.602 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:24.642 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:25.325 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:25.394 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:25.462 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
21:12:25.786 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
21:12:26.123 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
21:12:26.125 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@52063fd1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
21:12:26.126 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751460642807_127.0.0.1_7777
21:12:26.126 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [237526f5-2fbb-4aa3-b8ed-accfb3afc84f] Client is shutdown, stop reconnect to server
21:12:26.126 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4de8db26[Running, pool size = 19, active threads = 0, queued tasks = 0, completed tasks = 284]
21:12:26.209 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2009aac1-45fd-4309-baa4-c46315497389_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:26.263 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
21:12:26.266 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
21:12:26.271 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
21:12:26.271 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
21:12:26.271 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
21:12:26.271 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
