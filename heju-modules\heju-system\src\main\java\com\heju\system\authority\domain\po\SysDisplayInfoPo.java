package com.heju.system.authority.domain.po;

import com.heju.common.core.web.tenant.base.TBaseEntity;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 显隐列 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_display_info", excludeProperty = { STATUS, UPDATE_BY, SORT, CREATE_BY, DEL_FLAG, CREATE_TIME, UPDATE_TIME, REMARK, NAME })
public class SysDisplayInfoPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**  */
    @Excel(name = "")
    protected String displayInfo;

    /**  */
    @Excel(name = "")
    protected String apiCode;


    protected Long userId;

}
