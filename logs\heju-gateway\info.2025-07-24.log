09:12:15.798 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:12:16.656 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3e63aecb-d93a-479c-abec-7ce23d49b35d_config-0
09:12:16.758 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 52 ms to scan 1 urls, producing 3 keys and 6 values 
09:12:16.803 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 4 keys and 9 values 
09:12:16.822 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 3 keys and 10 values 
09:12:16.837 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 5 values 
09:12:16.854 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 7 values 
09:12:16.873 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
09:12:16.879 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e63aecb-d93a-479c-abec-7ce23d49b35d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:12:16.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e63aecb-d93a-479c-abec-7ce23d49b35d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000262593ccfb8
09:12:16.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e63aecb-d93a-479c-abec-7ce23d49b35d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000262593cd1d8
09:12:16.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e63aecb-d93a-479c-abec-7ce23d49b35d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:12:16.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e63aecb-d93a-479c-abec-7ce23d49b35d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:12:16.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e63aecb-d93a-479c-abec-7ce23d49b35d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:18.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e63aecb-d93a-479c-abec-7ce23d49b35d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753319538547_127.0.0.1_12569
09:12:18.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e63aecb-d93a-479c-abec-7ce23d49b35d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:18.850 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e63aecb-d93a-479c-abec-7ce23d49b35d_config-0] Notify connected event to listeners.
09:12:18.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e63aecb-d93a-479c-abec-7ce23d49b35d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000026259508228
09:12:19.273 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:12:27.128 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:12:29.883 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:12:30.815 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 07aaed18-533f-40a8-96ea-66aa980b8ad0_config-0
09:12:30.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07aaed18-533f-40a8-96ea-66aa980b8ad0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:12:30.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07aaed18-533f-40a8-96ea-66aa980b8ad0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000262593ccfb8
09:12:30.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07aaed18-533f-40a8-96ea-66aa980b8ad0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000262593cd1d8
09:12:30.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07aaed18-533f-40a8-96ea-66aa980b8ad0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:12:30.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07aaed18-533f-40a8-96ea-66aa980b8ad0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:12:30.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07aaed18-533f-40a8-96ea-66aa980b8ad0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:30.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07aaed18-533f-40a8-96ea-66aa980b8ad0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753319550833_127.0.0.1_12684
09:12:30.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07aaed18-533f-40a8-96ea-66aa980b8ad0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:30.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07aaed18-533f-40a8-96ea-66aa980b8ad0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000026259508228
09:12:30.963 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07aaed18-533f-40a8-96ea-66aa980b8ad0_config-0] Notify connected event to listeners.
09:12:31.157 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 48d7e641-8c16-4771-bd58-d96211cce0f2
09:12:31.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] RpcClient init label, labels = {module=naming, source=sdk}
09:12:31.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:12:31.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:12:31.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:12:31.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:31.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Success to connect to server [localhost:8848] on start up, connectionId = 1753319551176_127.0.0.1_12686
09:12:31.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:31.306 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Notify connected event to listeners.
09:12:31.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000026259508228
09:12:31.962 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:12:31.962 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:12:32.071 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:12:32.081 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:12:32.117 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:12:32.136 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:12:32.597 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.34:8081 register finished
09:12:32.698 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 17.66 seconds (JVM running for 19.827)
09:12:32.710 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:12:32.712 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:12:32.713 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:12:33.174 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:12:33.175 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:13:02.140 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:13:02.141 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:13:02.151 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:13:02.152 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 9
11:49:17.955 [nacos-grpc-client-executor-3103] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 20
11:49:17.976 [nacos-grpc-client-executor-3103] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 20
11:49:45.309 [nacos-grpc-client-executor-3113] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 24
11:49:45.323 [nacos-grpc-client-executor-3113] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 24
11:51:27.481 [nacos-grpc-client-executor-3145] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 29
11:51:27.503 [nacos-grpc-client-executor-3145] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 29
11:51:53.057 [nacos-grpc-client-executor-3155] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 34
11:51:53.077 [nacos-grpc-client-executor-3155] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 34
13:28:55.933 [nacos-grpc-client-executor-5062] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 38
13:28:55.950 [nacos-grpc-client-executor-5062] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 38
13:29:39.234 [nacos-grpc-client-executor-5080] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 42
13:29:39.249 [nacos-grpc-client-executor-5080] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 42
13:31:40.251 [nacos-grpc-client-executor-5120] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 47
13:31:40.267 [nacos-grpc-client-executor-5120] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 47
13:31:58.819 [nacos-grpc-client-executor-5124] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 52
13:31:58.834 [nacos-grpc-client-executor-5124] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 52
13:37:01.866 [nacos-grpc-client-executor-5218] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 56
13:37:01.888 [nacos-grpc-client-executor-5218] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 56
13:37:18.893 [nacos-grpc-client-executor-5224] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 61
13:37:18.900 [nacos-grpc-client-executor-5224] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 61
13:40:17.535 [nacos-grpc-client-executor-5275] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 66
13:40:17.552 [nacos-grpc-client-executor-5275] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 66
13:40:32.374 [nacos-grpc-client-executor-5278] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Receive server push request, request = NotifySubscriberRequest, requestId = 71
13:40:32.386 [nacos-grpc-client-executor-5278] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [48d7e641-8c16-4771-bd58-d96211cce0f2] Ack server push request, request = NotifySubscriberRequest, requestId = 71
13:41:02.131 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:41:02.135 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:41:02.467 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:41:02.467 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@46e3c581[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:41:02.467 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753319551176_127.0.0.1_12686
13:41:02.467 [nacos-grpc-client-executor-5291] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753319551176_127.0.0.1_12686]Ignore complete event,isRunning:false,isAbandon=false
13:41:02.476 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@60f716d7[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5292]
13:41:30.732 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:41:31.266 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 04575ed7-e343-43fc-b306-19a690488618_config-0
13:41:31.336 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
13:41:31.365 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
13:41:31.373 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
13:41:31.382 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
13:41:31.393 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
13:41:31.403 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
13:41:31.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04575ed7-e343-43fc-b306-19a690488618_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:41:31.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04575ed7-e343-43fc-b306-19a690488618_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000240be3b8d60
13:41:31.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04575ed7-e343-43fc-b306-19a690488618_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000240be3b8f80
13:41:31.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04575ed7-e343-43fc-b306-19a690488618_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:41:31.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04575ed7-e343-43fc-b306-19a690488618_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:41:31.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04575ed7-e343-43fc-b306-19a690488618_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:41:32.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04575ed7-e343-43fc-b306-19a690488618_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753335692132_127.0.0.1_1401
13:41:32.462 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04575ed7-e343-43fc-b306-19a690488618_config-0] Notify connected event to listeners.
13:41:32.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04575ed7-e343-43fc-b306-19a690488618_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:41:32.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04575ed7-e343-43fc-b306-19a690488618_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000240be4f0668
13:41:32.646 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:41:39.906 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
13:41:43.213 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
13:41:44.515 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bde3448f-65ff-4870-93d1-34dc3f553dbe_config-0
13:41:44.516 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bde3448f-65ff-4870-93d1-34dc3f553dbe_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:41:44.516 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bde3448f-65ff-4870-93d1-34dc3f553dbe_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000240be3b8d60
13:41:44.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bde3448f-65ff-4870-93d1-34dc3f553dbe_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000240be3b8f80
13:41:44.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bde3448f-65ff-4870-93d1-34dc3f553dbe_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:41:44.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bde3448f-65ff-4870-93d1-34dc3f553dbe_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:41:44.519 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bde3448f-65ff-4870-93d1-34dc3f553dbe_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:41:44.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bde3448f-65ff-4870-93d1-34dc3f553dbe_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753335704537_127.0.0.1_1435
13:41:44.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bde3448f-65ff-4870-93d1-34dc3f553dbe_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:41:44.662 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bde3448f-65ff-4870-93d1-34dc3f553dbe_config-0] Notify connected event to listeners.
13:41:44.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bde3448f-65ff-4870-93d1-34dc3f553dbe_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000240be4f0668
13:41:44.919 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9a703457-5103-499d-ac34-bf6eee1c80c8
13:41:44.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] RpcClient init label, labels = {module=naming, source=sdk}
13:41:44.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:41:44.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:41:44.926 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:41:44.927 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:41:45.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Success to connect to server [localhost:8848] on start up, connectionId = 1753335704948_127.0.0.1_1436
13:41:45.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:41:45.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000240be4f0668
13:41:45.080 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Notify connected event to listeners.
13:41:46.223 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Receive server push request, request = NotifySubscriberRequest, requestId = 1
13:41:46.225 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Ack server push request, request = NotifySubscriberRequest, requestId = 1
13:41:46.297 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.34:8081 register finished
13:41:46.378 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 16.29 seconds (JVM running for 19.232)
13:41:46.393 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
13:41:46.396 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
13:41:46.398 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
13:41:46.797 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Receive server push request, request = NotifySubscriberRequest, requestId = 2
13:41:46.798 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Ack server push request, request = NotifySubscriberRequest, requestId = 2
13:42:15.986 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Receive server push request, request = NotifySubscriberRequest, requestId = 8
13:42:15.986 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Ack server push request, request = NotifySubscriberRequest, requestId = 8
13:42:15.994 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Receive server push request, request = NotifySubscriberRequest, requestId = 7
13:42:15.996 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Ack server push request, request = NotifySubscriberRequest, requestId = 7
13:42:16.004 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Receive server push request, request = NotifySubscriberRequest, requestId = 10
13:42:16.004 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Ack server push request, request = NotifySubscriberRequest, requestId = 10
13:42:16.010 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Receive server push request, request = NotifySubscriberRequest, requestId = 9
13:42:16.011 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Ack server push request, request = NotifySubscriberRequest, requestId = 9
14:16:12.947 [nacos-grpc-client-executor-702] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Receive server push request, request = NotifySubscriberRequest, requestId = 11
14:16:12.960 [nacos-grpc-client-executor-702] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Ack server push request, request = NotifySubscriberRequest, requestId = 11
14:16:54.240 [nacos-grpc-client-executor-719] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Receive server push request, request = NotifySubscriberRequest, requestId = 12
14:16:54.260 [nacos-grpc-client-executor-719] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Ack server push request, request = NotifySubscriberRequest, requestId = 12
14:17:52.962 [nacos-grpc-client-executor-738] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Receive server push request, request = NotifySubscriberRequest, requestId = 14
14:17:52.977 [nacos-grpc-client-executor-738] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Ack server push request, request = NotifySubscriberRequest, requestId = 14
14:18:11.259 [nacos-grpc-client-executor-742] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Receive server push request, request = NotifySubscriberRequest, requestId = 15
14:18:11.278 [nacos-grpc-client-executor-742] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Ack server push request, request = NotifySubscriberRequest, requestId = 15
16:02:02.100 [nacos-grpc-client-executor-2738] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Receive server push request, request = NotifySubscriberRequest, requestId = 17
16:02:02.128 [nacos-grpc-client-executor-2738] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Ack server push request, request = NotifySubscriberRequest, requestId = 17
16:02:04.909 [nacos-grpc-client-executor-2739] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Receive server push request, request = NotifySubscriberRequest, requestId = 18
16:02:04.933 [nacos-grpc-client-executor-2739] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a703457-5103-499d-ac34-bf6eee1c80c8] Ack server push request, request = NotifySubscriberRequest, requestId = 18
20:42:17.647 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:42:17.652 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:42:17.970 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:42:17.970 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@34e540f0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:42:17.970 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753335704948_127.0.0.1_1436
20:42:17.972 [nacos-grpc-client-executor-8185] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753335704948_127.0.0.1_1436]Ignore complete event,isRunning:false,isAbandon=false
20:42:17.978 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4e4aa1c4[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 8186]
