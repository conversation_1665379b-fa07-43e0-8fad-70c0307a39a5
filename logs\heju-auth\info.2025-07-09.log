09:49:28.498 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:49:29.153 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8bf8d0da-2755-49cd-9db9-3c61e424059e_config-0
09:49:29.224 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 31 ms to scan 1 urls, producing 3 keys and 6 values 
09:49:29.248 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 4 keys and 9 values 
09:49:29.256 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 3 keys and 10 values 
09:49:29.267 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:49:29.276 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 7 values 
09:49:29.287 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:49:29.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf8d0da-2755-49cd-9db9-3c61e424059e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:49:29.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf8d0da-2755-49cd-9db9-3c61e424059e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001c2493ca7e8
09:49:29.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf8d0da-2755-49cd-9db9-3c61e424059e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001c2493caa08
09:49:29.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf8d0da-2755-49cd-9db9-3c61e424059e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:49:29.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf8d0da-2755-49cd-9db9-3c61e424059e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:49:29.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf8d0da-2755-49cd-9db9-3c61e424059e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:49:30.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf8d0da-2755-49cd-9db9-3c61e424059e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752025770009_127.0.0.1_7999
09:49:30.210 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf8d0da-2755-49cd-9db9-3c61e424059e_config-0] Notify connected event to listeners.
09:49:30.210 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf8d0da-2755-49cd-9db9-3c61e424059e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:49:30.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bf8d0da-2755-49cd-9db9-3c61e424059e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001c249504720
09:49:30.326 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:49:32.186 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:49:32.186 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:49:32.186 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:49:32.312 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:49:33.891 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:49:35.196 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bdb55599-f630-4293-afd2-109e41262a57
09:49:35.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdb55599-f630-4293-afd2-109e41262a57] RpcClient init label, labels = {module=naming, source=sdk}
09:49:35.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdb55599-f630-4293-afd2-109e41262a57] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:49:35.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdb55599-f630-4293-afd2-109e41262a57] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:49:35.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdb55599-f630-4293-afd2-109e41262a57] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:49:35.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdb55599-f630-4293-afd2-109e41262a57] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:49:35.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdb55599-f630-4293-afd2-109e41262a57] Success to connect to server [localhost:8848] on start up, connectionId = 1752025775213_127.0.0.1_8020
09:49:35.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdb55599-f630-4293-afd2-109e41262a57] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:49:35.332 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdb55599-f630-4293-afd2-109e41262a57] Notify connected event to listeners.
09:49:35.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdb55599-f630-4293-afd2-109e41262a57] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001c249504720
09:49:35.382 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:49:35.415 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ***********:9200 register finished
09:49:35.573 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 7.777 seconds (JVM running for 8.934)
09:49:35.585 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:49:35.585 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:49:35.588 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:49:35.958 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdb55599-f630-4293-afd2-109e41262a57] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:49:35.977 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdb55599-f630-4293-afd2-109e41262a57] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:49:35.987 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:56:53.725 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:56:53.731 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:56:54.060 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:56:54.061 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3848bd23[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:56:54.061 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752025775213_127.0.0.1_8020
09:56:54.062 [nacos-grpc-client-executor-101] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752025775213_127.0.0.1_8020]Ignore complete event,isRunning:false,isAbandon=false
09:56:54.064 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@ced1fbe[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 102]
09:57:47.147 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:57:48.791 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1d18ad66-60ac-4f19-9d38-304a7275cb6d_config-0
09:57:48.926 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 67 ms to scan 1 urls, producing 3 keys and 6 values 
09:57:48.978 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 4 keys and 9 values 
09:57:48.996 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
09:57:49.016 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
09:57:49.036 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
09:57:49.065 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 2 keys and 8 values 
09:57:49.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d18ad66-60ac-4f19-9d38-304a7275cb6d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:57:49.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d18ad66-60ac-4f19-9d38-304a7275cb6d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002b6e539a328
09:57:49.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d18ad66-60ac-4f19-9d38-304a7275cb6d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002b6e539a548
09:57:49.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d18ad66-60ac-4f19-9d38-304a7275cb6d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:57:49.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d18ad66-60ac-4f19-9d38-304a7275cb6d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:57:49.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d18ad66-60ac-4f19-9d38-304a7275cb6d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:57:51.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d18ad66-60ac-4f19-9d38-304a7275cb6d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752026270901_127.0.0.1_9007
09:57:51.202 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d18ad66-60ac-4f19-9d38-304a7275cb6d_config-0] Notify connected event to listeners.
09:57:51.203 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d18ad66-60ac-4f19-9d38-304a7275cb6d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:57:51.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d18ad66-60ac-4f19-9d38-304a7275cb6d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002b6e5514480
09:57:51.398 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:57:55.521 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:57:55.522 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:57:55.523 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:57:55.817 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:57:58.388 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:58:00.561 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6ef65113-976b-41a1-baab-8c3542d90231
09:58:00.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] RpcClient init label, labels = {module=naming, source=sdk}
09:58:00.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:58:00.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:58:00.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:58:00.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:58:00.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] Success to connect to server [localhost:8848] on start up, connectionId = 1752026280586_127.0.0.1_9037
09:58:00.717 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:58:00.718 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] Notify connected event to listeners.
09:58:00.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002b6e5514480
09:58:00.944 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:58:01.009 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ***********:9200 register finished
09:58:01.322 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] Receive server push request, request = NotifySubscriberRequest, requestId = 23
09:58:01.351 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] Ack server push request, request = NotifySubscriberRequest, requestId = 23
09:58:01.869 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 16.204 seconds (JVM running for 18.866)
09:58:01.897 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:58:01.898 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:58:01.918 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:58:02.362 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:00:27.472 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] Receive server push request, request = NotifySubscriberRequest, requestId = 29
10:00:27.474 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] Ack server push request, request = NotifySubscriberRequest, requestId = 29
10:00:30.525 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] Receive server push request, request = NotifySubscriberRequest, requestId = 30
10:00:30.532 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ef65113-976b-41a1-baab-8c3542d90231] Ack server push request, request = NotifySubscriberRequest, requestId = 30
15:14:48.007 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:14:48.013 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:14:48.361 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:14:48.361 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3e13f6e4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:14:48.361 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752026280586_127.0.0.1_9037
15:14:48.365 [nacos-grpc-client-executor-3940] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752026280586_127.0.0.1_9037]Ignore complete event,isRunning:false,isAbandon=false
15:14:48.378 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@20d682ab[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3941]
15:32:24.635 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:32:26.042 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f63772f1-35bc-487e-a350-4b636f36a478_config-0
15:32:26.134 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
15:32:26.164 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
15:32:26.174 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
15:32:26.187 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
15:32:26.201 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
15:32:26.218 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
15:32:26.222 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63772f1-35bc-487e-a350-4b636f36a478_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:32:26.223 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63772f1-35bc-487e-a350-4b636f36a478_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000019a913ca328
15:32:26.223 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63772f1-35bc-487e-a350-4b636f36a478_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000019a913ca548
15:32:26.224 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63772f1-35bc-487e-a350-4b636f36a478_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:32:26.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63772f1-35bc-487e-a350-4b636f36a478_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:32:26.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63772f1-35bc-487e-a350-4b636f36a478_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:32:27.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63772f1-35bc-487e-a350-4b636f36a478_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752046347318_127.0.0.1_8627
15:32:27.565 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63772f1-35bc-487e-a350-4b636f36a478_config-0] Notify connected event to listeners.
15:32:27.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63772f1-35bc-487e-a350-4b636f36a478_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:32:27.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63772f1-35bc-487e-a350-4b636f36a478_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000019a91504200
15:32:27.712 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:32:30.430 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
15:32:30.431 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:32:30.431 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:32:30.643 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:32:32.415 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:32:34.233 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ae0370fd-2198-418f-a831-dfea0589dcb3
15:32:34.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae0370fd-2198-418f-a831-dfea0589dcb3] RpcClient init label, labels = {module=naming, source=sdk}
15:32:34.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae0370fd-2198-418f-a831-dfea0589dcb3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:32:34.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae0370fd-2198-418f-a831-dfea0589dcb3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:32:34.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae0370fd-2198-418f-a831-dfea0589dcb3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:32:34.237 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae0370fd-2198-418f-a831-dfea0589dcb3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:32:34.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae0370fd-2198-418f-a831-dfea0589dcb3] Success to connect to server [localhost:8848] on start up, connectionId = 1752046354247_127.0.0.1_8683
15:32:34.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae0370fd-2198-418f-a831-dfea0589dcb3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:32:34.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae0370fd-2198-418f-a831-dfea0589dcb3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000019a91504200
15:32:34.370 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae0370fd-2198-418f-a831-dfea0589dcb3] Notify connected event to listeners.
15:32:34.413 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
15:32:34.435 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ***********:9200 register finished
15:32:34.601 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 10.588 seconds (JVM running for 11.54)
15:32:34.613 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
15:32:34.614 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
15:32:34.628 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
15:32:34.768 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:32:34.924 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae0370fd-2198-418f-a831-dfea0589dcb3] Receive server push request, request = NotifySubscriberRequest, requestId = 59
15:32:34.941 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae0370fd-2198-418f-a831-dfea0589dcb3] Ack server push request, request = NotifySubscriberRequest, requestId = 59
16:28:58.143 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:28:58.151 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:28:58.543 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:28:58.543 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7183fafc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:28:58.543 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752046354247_127.0.0.1_8683
16:28:58.562 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@10a796b[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 689]
17:36:30.986 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:36:32.484 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 07af527e-c37f-4a16-a436-14fbe6643f63_config-0
17:36:32.650 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 93 ms to scan 1 urls, producing 3 keys and 6 values 
17:36:32.712 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
17:36:32.732 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
17:36:32.754 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 5 values 
17:36:32.775 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
17:36:32.808 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 2 keys and 8 values 
17:36:32.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07af527e-c37f-4a16-a436-14fbe6643f63_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:36:32.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07af527e-c37f-4a16-a436-14fbe6643f63_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000018a6739a328
17:36:32.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07af527e-c37f-4a16-a436-14fbe6643f63_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000018a6739a548
17:36:32.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07af527e-c37f-4a16-a436-14fbe6643f63_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:36:32.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07af527e-c37f-4a16-a436-14fbe6643f63_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:36:32.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07af527e-c37f-4a16-a436-14fbe6643f63_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:36:34.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07af527e-c37f-4a16-a436-14fbe6643f63_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752053794476_127.0.0.1_11145
17:36:34.793 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07af527e-c37f-4a16-a436-14fbe6643f63_config-0] Notify connected event to listeners.
17:36:34.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07af527e-c37f-4a16-a436-14fbe6643f63_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:36:34.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07af527e-c37f-4a16-a436-14fbe6643f63_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000018a67514200
17:36:34.981 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:36:40.234 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
17:36:40.234 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:36:40.234 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:36:40.584 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:36:43.759 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:36:47.143 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8f8ad3aa-b61f-40ef-b508-24d1b3bb560a
17:36:47.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8ad3aa-b61f-40ef-b508-24d1b3bb560a] RpcClient init label, labels = {module=naming, source=sdk}
17:36:47.151 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8ad3aa-b61f-40ef-b508-24d1b3bb560a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:36:47.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8ad3aa-b61f-40ef-b508-24d1b3bb560a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:36:47.156 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8ad3aa-b61f-40ef-b508-24d1b3bb560a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:36:47.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8ad3aa-b61f-40ef-b508-24d1b3bb560a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:36:47.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8ad3aa-b61f-40ef-b508-24d1b3bb560a] Success to connect to server [localhost:8848] on start up, connectionId = 1752053807188_127.0.0.1_11228
17:36:47.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8ad3aa-b61f-40ef-b508-24d1b3bb560a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:36:47.331 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8ad3aa-b61f-40ef-b508-24d1b3bb560a] Notify connected event to listeners.
17:36:47.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8ad3aa-b61f-40ef-b508-24d1b3bb560a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000018a67514200
17:36:47.454 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
17:36:47.531 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ***********:9200 register finished
17:36:48.018 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8ad3aa-b61f-40ef-b508-24d1b3bb560a] Receive server push request, request = NotifySubscriberRequest, requestId = 76
17:36:48.068 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8ad3aa-b61f-40ef-b508-24d1b3bb560a] Ack server push request, request = NotifySubscriberRequest, requestId = 76
17:36:48.230 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 18.747 seconds (JVM running for 20.904)
17:36:48.274 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
17:36:48.278 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
17:36:48.285 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
17:36:48.773 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:28:57.624 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:28:57.628 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:28:57.969 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:28:57.969 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@8186851[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:28:57.970 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752053807188_127.0.0.1_11228
19:28:57.972 [nacos-grpc-client-executor-1359] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752053807188_127.0.0.1_11228]Ignore complete event,isRunning:false,isAbandon=false
19:28:57.981 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1a7e183a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1360]
