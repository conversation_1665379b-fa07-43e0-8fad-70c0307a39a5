09:02:31.879 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:02:33.175 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6eadb826-fece-46af-ba08-562e6d1965fd_config-0
09:02:33.271 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 47 ms to scan 1 urls, producing 3 keys and 6 values 
09:02:33.319 [main] INFO  o.r.Reflections - [scan,232] - <PERSON>flections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
09:02:33.352 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 29 ms to scan 1 urls, producing 3 keys and 10 values 
09:02:33.372 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 14 ms to scan 1 urls, producing 1 keys and 5 values 
09:02:33.388 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 7 values 
09:02:33.396 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
09:02:33.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eadb826-fece-46af-ba08-562e6d1965fd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:02:33.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eadb826-fece-46af-ba08-562e6d1965fd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001bd4a39dd00
09:02:33.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eadb826-fece-46af-ba08-562e6d1965fd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001bd4a39df20
09:02:33.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eadb826-fece-46af-ba08-562e6d1965fd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:02:33.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eadb826-fece-46af-ba08-562e6d1965fd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:02:33.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eadb826-fece-46af-ba08-562e6d1965fd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:02:35.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eadb826-fece-46af-ba08-562e6d1965fd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754269354854_127.0.0.1_8917
09:02:35.161 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eadb826-fece-46af-ba08-562e6d1965fd_config-0] Notify connected event to listeners.
09:02:35.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eadb826-fece-46af-ba08-562e6d1965fd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:02:35.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eadb826-fece-46af-ba08-562e6d1965fd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001bd4a515f18
09:02:35.483 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:02:44.414 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:02:44.417 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:02:44.417 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:02:44.845 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:02:46.291 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:02:46.294 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:02:46.294 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:02:50.332 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:02:53.573 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0193a198-b315-4dc8-b29b-822cf494108c
09:02:53.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] RpcClient init label, labels = {module=naming, source=sdk}
09:02:53.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:02:53.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:02:53.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:02:53.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:02:53.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Success to connect to server [localhost:8848] on start up, connectionId = 1754269373583_127.0.0.1_9282
09:02:53.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Notify connected event to listeners.
09:02:53.700 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:02:53.700 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001bd4a515f18
09:02:53.748 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:02:53.781 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:02:53.955 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 23.348 seconds (JVM running for 40.655)
09:02:53.967 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:02:53.971 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:02:53.971 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:02:54.244 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:02:54.261 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:13:48.992 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:13:54.673 [nacos-grpc-client-executor-144] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:13:54.673 [nacos-grpc-client-executor-144] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Ack server push request, request = NotifySubscriberRequest, requestId = 14
10:09:06.104 [nacos-grpc-client-executor-862] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Receive server push request, request = NotifySubscriberRequest, requestId = 17
10:09:06.119 [nacos-grpc-client-executor-862] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Ack server push request, request = NotifySubscriberRequest, requestId = 17
10:25:04.730 [nacos-grpc-client-executor-1069] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Receive server push request, request = NotifySubscriberRequest, requestId = 20
10:25:04.744 [nacos-grpc-client-executor-1069] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Ack server push request, request = NotifySubscriberRequest, requestId = 20
10:43:24.631 [nacos-grpc-client-executor-1294] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Receive server push request, request = NotifySubscriberRequest, requestId = 24
10:43:24.646 [nacos-grpc-client-executor-1294] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Ack server push request, request = NotifySubscriberRequest, requestId = 24
10:43:42.427 [nacos-grpc-client-executor-1297] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Receive server push request, request = NotifySubscriberRequest, requestId = 28
10:43:42.439 [nacos-grpc-client-executor-1297] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Ack server push request, request = NotifySubscriberRequest, requestId = 28
11:43:53.012 [nacos-grpc-client-executor-2053] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Receive server push request, request = NotifySubscriberRequest, requestId = 31
11:43:53.021 [nacos-grpc-client-executor-2053] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Ack server push request, request = NotifySubscriberRequest, requestId = 31
11:44:14.413 [nacos-grpc-client-executor-2059] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Receive server push request, request = NotifySubscriberRequest, requestId = 35
11:44:14.424 [nacos-grpc-client-executor-2059] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Ack server push request, request = NotifySubscriberRequest, requestId = 35
13:35:14.320 [nacos-grpc-client-executor-3390] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Receive server push request, request = NotifySubscriberRequest, requestId = 41
13:35:14.339 [nacos-grpc-client-executor-3390] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Ack server push request, request = NotifySubscriberRequest, requestId = 41
13:35:47.531 [nacos-grpc-client-executor-3397] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Receive server push request, request = NotifySubscriberRequest, requestId = 46
13:35:47.544 [nacos-grpc-client-executor-3397] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0193a198-b315-4dc8-b29b-822cf494108c] Ack server push request, request = NotifySubscriberRequest, requestId = 46
18:50:00.027 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:50:00.031 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:50:00.367 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:50:00.367 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@78f4d5b8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:50:00.369 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754269373583_127.0.0.1_9282
18:50:00.371 [nacos-grpc-client-executor-7333] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754269373583_127.0.0.1_9282]Ignore complete event,isRunning:false,isAbandon=false
18:50:00.373 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4abccd0a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 7334]
18:50:00.527 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:50:00.529 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:50:00.529 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:50:00.529 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
