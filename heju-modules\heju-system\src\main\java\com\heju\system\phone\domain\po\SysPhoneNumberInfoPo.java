package com.heju.system.phone.domain.po;

import com.heju.common.core.web.tenant.base.TBaseEntity;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.NAME;

/**
 * 手机号 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_phone_number_info", excludeProperty = { NAME })
public class SysPhoneNumberInfoPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 手机品牌 */
    @Excel(name = "手机品牌")
    protected String brandName;

    /** 手机型号 */
    @Excel(name = "手机型号")
    protected String phoneModel;

    /** 手机号码SIM1 */
    @Excel(name = "手机号码SIM1")
    protected String simOne;

    /** 手机号码SIM2 */
    @Excel(name = "手机号码SIM2")
    protected String simTwo;

    /** 保管员 */
    @Excel(name = "保管员")
    protected Long custody;

    /** 存放位置 */
    @Excel(name = "存放位置")
    protected String position;

}
