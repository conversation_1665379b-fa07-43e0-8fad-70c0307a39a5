package com.heju.system.api.authority.feign;

import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.constant.basic.ServiceConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.system.api.authority.domain.dto.SysRoleDto;
import com.heju.system.api.authority.domain.dto.SysRoleGroupDto;
import com.heju.system.api.authority.feign.factory.RemoteRoleFallbackFactory;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 菜单服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteRoleService", value = ServiceConstants.SYSTEM_SERVICE, fallbackFactory = RemoteRoleFallbackFactory.class)
public interface RemoteRoleService {

    /**
     *  添加角色 | 内部调用
     * @param role 角色dto
     * @param source 请求来源
     * @return 成功/失败
     */
    @PostMapping("/role/addInner")
    AjaxResult addInner(@RequestBody SysRoleDto role, @RequestHeader(SecurityConstants.FROM_SOURCE) String source,@RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName,@RequestHeader(SecurityConstants.ENTERPRISE_ID) Long enterpriseId);

    /**
     *  根据roleKey查询roleId | 内部调用
     * @param roleKey 角色权限字符串
     * @param source 请求来源
     * @return 成功/失败
     */
    @GetMapping("/role/getIdsByRoleKey")
    R<SysUserDto> getRoleIdsByRoleKey(@RequestParam(value = "roleKey") String roleKey, @RequestHeader(SecurityConstants.FROM_SOURCE) String source, @RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName, @RequestHeader(SecurityConstants.ENTERPRISE_ID) Long enterpriseId);

    /**
     * 添加角色组 | 内部调用
     * @param roleGroup 角色组dto
     * @param source 请求来源
     * @param sourceName 数据源
     * @return 成功/失败
     */
    @PostMapping("/roleGroup/addInner")
    AjaxResult addInner(@RequestBody SysRoleGroupDto roleGroup,@RequestHeader(SecurityConstants.FROM_SOURCE) String source,@RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName);


    /**
     * 查询角色详情
     * @param id
     * @param source
     * @param sourceName
     * @return
     */
    @GetMapping("/role/getInfoInner/{id}")
    AjaxResult getInfoInner(@PathVariable("id") Long id, @RequestHeader(SecurityConstants.FROM_SOURCE) String source,@RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName);
}