09:21:12.621 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:21:16.789 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 713cd241-cc10-4814-b56b-460c62a4f656_config-0
09:21:17.151 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 206 ms to scan 1 urls, producing 3 keys and 6 values 
09:21:17.307 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 79 ms to scan 1 urls, producing 4 keys and 9 values 
09:21:17.365 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 48 ms to scan 1 urls, producing 3 keys and 10 values 
09:21:17.430 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 50 ms to scan 1 urls, producing 1 keys and 5 values 
09:21:17.504 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 57 ms to scan 1 urls, producing 1 keys and 7 values 
09:21:17.560 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 2 keys and 8 values 
09:21:17.571 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:21:17.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000170823b2a48
09:21:17.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000170823b2c68
09:21:17.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:21:17.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:21:17.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:22.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:22.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:22.306 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:21:22.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:22.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000170824c4430
09:21:22.472 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:22.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:23.044 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:23.487 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:24.041 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:24.372 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:21:24.691 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:25.437 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:26.279 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:27.294 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:28.703 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:30.096 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:31.594 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:33.359 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:34.666 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:21:34.668 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:21:34.669 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:21:35.063 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:35.224 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:21:36.772 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:38.617 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:40.810 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:41.813 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:21:42.869 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:45.086 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:47.351 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:47.354 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff
09:21:47.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] RpcClient init label, labels = {module=naming, source=sdk}
09:21:47.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:47.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:47.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:47.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:47.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:47.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:47.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:47.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:21:47.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000170824c4430
09:21:47.545 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:47.757 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:21:47.781 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:48.101 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:48.529 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:48.773 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:21:48.774 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@40e7aea9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:21:48.775 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9fe6340f-8683-4ca7-b5bb-d5c80ce0cfff] Client is shutdown, stop reconnect to server
09:21:48.775 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@63ff861e[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:21:48.789 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e5f9524a-9f0f-4529-9b11-039268ecc38b
09:21:48.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f9524a-9f0f-4529-9b11-039268ecc38b] RpcClient init label, labels = {module=naming, source=sdk}
09:21:48.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f9524a-9f0f-4529-9b11-039268ecc38b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:48.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f9524a-9f0f-4529-9b11-039268ecc38b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:48.792 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f9524a-9f0f-4529-9b11-039268ecc38b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:48.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f9524a-9f0f-4529-9b11-039268ecc38b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:48.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f9524a-9f0f-4529-9b11-039268ecc38b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:48.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f9524a-9f0f-4529-9b11-039268ecc38b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:48.866 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f9524a-9f0f-4529-9b11-039268ecc38b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:48.866 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f9524a-9f0f-4529-9b11-039268ecc38b] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:21:48.866 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f9524a-9f0f-4529-9b11-039268ecc38b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000170824c4430
09:21:49.059 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f9524a-9f0f-4529-9b11-039268ecc38b] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:49.312 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f9524a-9f0f-4529-9b11-039268ecc38b] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:49.404 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
09:21:49.404 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:21:49.428 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
09:21:49.440 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
09:21:49.479 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [713cd241-cc10-4814-b56b-460c62a4f656_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:49.634 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f9524a-9f0f-4529-9b11-039268ecc38b] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:50.110 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f9524a-9f0f-4529-9b11-039268ecc38b] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:22:42.375 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:22:44.284 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0
09:22:44.434 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 79 ms to scan 1 urls, producing 3 keys and 6 values 
09:22:44.547 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 55 ms to scan 1 urls, producing 4 keys and 9 values 
09:22:44.563 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
09:22:44.595 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 1 keys and 5 values 
09:22:44.636 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 1 keys and 7 values 
09:22:44.683 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 2 keys and 8 values 
09:22:44.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:22:44.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000015d813b2328
09:22:44.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000015d813b2548
09:22:44.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:22:44.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:22:44.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:22:48.218 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751419367768_127.0.0.1_3527
09:22:48.219 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] Notify connected event to listeners.
09:22:48.223 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:22:48.223 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000015d814ec6f8
09:22:48.617 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:22:55.478 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:22:55.481 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:22:55.481 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:22:55.796 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:23:03.089 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:23:06.684 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6eca9ffc-420d-43e6-8e4a-80ed4245b114
09:23:06.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] RpcClient init label, labels = {module=naming, source=sdk}
09:23:06.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:23:06.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:23:06.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:23:06.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:23:06.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751419386716_127.0.0.1_3554
09:23:06.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:06.850 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Notify connected event to listeners.
09:23:06.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000015d814ec6f8
09:23:06.975 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:23:07.059 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth 192.168.2.43:9200 register finished
09:23:07.469 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:23:07.517 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:23:07.700 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 27.236 seconds (JVM running for 30.329)
09:23:07.745 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:23:07.747 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:23:07.757 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:42:55.347 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Server healthy check fail, currentConnection = 1751419386716_127.0.0.1_3554
09:42:55.347 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:42:59.040 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] Server healthy check fail, currentConnection = 1751419367768_127.0.0.1_3527
09:42:59.040 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:42:59.720 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Success to connect a server [127.0.0.1:8848], connectionId = 1751420579524_127.0.0.1_5610
09:42:59.720 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1751419386716_127.0.0.1_3554
09:42:59.720 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751419386716_127.0.0.1_3554
09:42:59.721 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Notify disconnected event to listeners
09:42:59.722 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Notify connected event to listeners.
09:42:59.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1751420579385_127.0.0.1_5607
09:42:59.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1751419367768_127.0.0.1_3527
09:42:59.736 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751419367768_127.0.0.1_3527
09:42:59.736 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] Notify disconnected event to listeners
09:42:59.738 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cfabcdd3-da71-4d20-87e0-c1d373ac2864_config-0] Notify connected event to listeners.
09:43:02.595 [nacos-grpc-client-executor-257] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Receive server push request, request = NotifySubscriberRequest, requestId = 23
09:43:02.595 [nacos-grpc-client-executor-257] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Ack server push request, request = NotifySubscriberRequest, requestId = 23
09:53:58.850 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Server healthy check fail, currentConnection = 1751420579524_127.0.0.1_5610
09:53:58.850 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:54:02.231 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Success to connect a server [127.0.0.1:8848], connectionId = 1751421242014_127.0.0.1_6331
09:54:02.231 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1751420579524_127.0.0.1_5610
09:54:02.231 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751420579524_127.0.0.1_5610
09:54:02.232 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Notify disconnected event to listeners
09:54:02.233 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Notify connected event to listeners.
09:54:02.274 [nacos-grpc-client-executor-390] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751420579524_127.0.0.1_5610]Ignore complete event,isRunning:true,isAbandon=true
09:54:05.495 [nacos-grpc-client-executor-393] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Receive server push request, request = NotifySubscriberRequest, requestId = 33
09:54:05.496 [nacos-grpc-client-executor-393] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6eca9ffc-420d-43e6-8e4a-80ed4245b114] Ack server push request, request = NotifySubscriberRequest, requestId = 33
13:40:37.961 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:40:37.963 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:40:38.312 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:40:38.312 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1015111f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:40:38.312 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751421242014_127.0.0.1_6331
13:40:38.316 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@338b0263[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 3103]
13:40:38.316 [nacos-grpc-client-executor-3103] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751421242014_127.0.0.1_6331]Ignore complete event,isRunning:false,isAbandon=false
14:14:59.061 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:14:59.877 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0ee4d5a0-8203-483b-bcd9-3928e1e252a4_config-0
14:14:59.961 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
14:14:59.995 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
14:15:00.008 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
14:15:00.025 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
14:15:00.037 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
14:15:00.053 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
14:15:00.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ee4d5a0-8203-483b-bcd9-3928e1e252a4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:15:00.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ee4d5a0-8203-483b-bcd9-3928e1e252a4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001974f39b188
14:15:00.059 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ee4d5a0-8203-483b-bcd9-3928e1e252a4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001974f39b3a8
14:15:00.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ee4d5a0-8203-483b-bcd9-3928e1e252a4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:15:00.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ee4d5a0-8203-483b-bcd9-3928e1e252a4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:15:00.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ee4d5a0-8203-483b-bcd9-3928e1e252a4_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:15:01.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ee4d5a0-8203-483b-bcd9-3928e1e252a4_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751436901114_127.0.0.1_9446
14:15:01.359 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ee4d5a0-8203-483b-bcd9-3928e1e252a4_config-0] Notify connected event to listeners.
14:15:01.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ee4d5a0-8203-483b-bcd9-3928e1e252a4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:15:01.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ee4d5a0-8203-483b-bcd9-3928e1e252a4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001974f514d90
14:15:01.490 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:15:04.115 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
14:15:04.116 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:15:04.116 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:15:04.306 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:15:06.344 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:15:07.874 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c0ac73ee-b850-4946-be24-50533cadb0e7
14:15:07.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ac73ee-b850-4946-be24-50533cadb0e7] RpcClient init label, labels = {module=naming, source=sdk}
14:15:07.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ac73ee-b850-4946-be24-50533cadb0e7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:15:07.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ac73ee-b850-4946-be24-50533cadb0e7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:15:07.879 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ac73ee-b850-4946-be24-50533cadb0e7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:15:07.880 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ac73ee-b850-4946-be24-50533cadb0e7] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:15:08.019 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ac73ee-b850-4946-be24-50533cadb0e7] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751436907894_127.0.0.1_9477
14:15:08.020 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ac73ee-b850-4946-be24-50533cadb0e7] Notify connected event to listeners.
14:15:08.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ac73ee-b850-4946-be24-50533cadb0e7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:15:08.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ac73ee-b850-4946-be24-50533cadb0e7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001974f514d90
14:15:08.094 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
14:15:08.136 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth 192.168.2.43:9200 register finished
14:15:08.305 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 10.07 seconds (JVM running for 11.348)
14:15:08.322 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
14:15:08.323 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
14:15:08.351 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
14:15:08.431 [RMI TCP Connection(3)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:15:08.647 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ac73ee-b850-4946-be24-50533cadb0e7] Receive server push request, request = NotifySubscriberRequest, requestId = 74
14:15:08.668 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ac73ee-b850-4946-be24-50533cadb0e7] Ack server push request, request = NotifySubscriberRequest, requestId = 74
14:20:06.279 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:20:06.290 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:20:06.627 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:20:06.628 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@79c24103[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:20:06.628 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751436907894_127.0.0.1_9477
14:20:06.632 [nacos-grpc-client-executor-71] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751436907894_127.0.0.1_9477]Ignore complete event,isRunning:false,isAbandon=false
14:20:06.634 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2fa6f172[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 72]
17:34:52.138 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:34:53.431 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0
17:34:53.567 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 75 ms to scan 1 urls, producing 3 keys and 6 values 
17:34:53.631 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 4 keys and 9 values 
17:34:53.652 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
17:34:53.677 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
17:34:53.696 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
17:34:53.713 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
17:34:53.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:34:53.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001f5273b7d80
17:34:53.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001f5273b8000
17:34:53.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:34:53.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:34:53.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:34:55.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751448895456_127.0.0.1_12961
17:34:55.841 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Notify connected event to listeners.
17:34:55.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:34:55.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001f5274f0228
17:34:56.284 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:35:03.244 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
17:35:03.244 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:35:03.246 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:35:03.595 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:35:06.750 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:35:10.056 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 778ee417-956a-4870-9ba9-986fcb9e3141
17:35:10.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] RpcClient init label, labels = {module=naming, source=sdk}
17:35:10.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:35:10.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:35:10.064 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:35:10.064 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:35:10.208 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751448910081_127.0.0.1_12986
17:35:10.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:10.209 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Notify connected event to listeners.
17:35:10.210 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001f5274f0228
17:35:10.302 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
17:35:10.365 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth 192.168.2.43:9200 register finished
17:35:10.690 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 19.95 seconds (JVM running for 32.348)
17:35:10.716 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
17:35:10.718 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
17:35:10.723 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
17:35:10.842 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Receive server push request, request = NotifySubscriberRequest, requestId = 91
17:35:10.880 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Ack server push request, request = NotifySubscriberRequest, requestId = 91
21:12:22.448 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:12:22.449 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:12:22.598 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:22.599 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:22.823 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:22.824 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:23.152 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:23.154 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:23.562 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:23.578 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:24.082 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:24.103 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:24.702 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:24.734 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:25.409 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6204ba2-21c6-4852-96f9-7e60132b3f3c_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:25.444 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:25.475 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
21:12:25.786 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
21:12:26.121 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
21:12:26.121 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6f782324[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
21:12:26.121 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751448910081_127.0.0.1_12986
21:12:26.122 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Client is shutdown, stop reconnect to server
21:12:26.122 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778ee417-956a-4870-9ba9-986fcb9e3141] Notify disconnected event to listeners
21:12:26.122 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@18b85a07[Running, pool size = 18, active threads = 0, queued tasks = 0, completed tasks = 2625]
