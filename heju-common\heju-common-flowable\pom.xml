<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.heju</groupId>
        <artifactId>heju-common</artifactId>
        <version>2.5.0</version>
    </parent>


    <modelVersion>4.0.0</modelVersion>

    <artifactId>heju-common-flowable</artifactId>

    <description>
        flowable工作流
    </description>

    <dependencies>

        <dependency>
            <groupId>com.heju</groupId>
            <artifactId>heju-common-security</artifactId>

        </dependency>

        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-engine</artifactId>
            <version>6.8.0</version>
        </dependency>
        <!-- 选择性引入 spring 整合包，但不要用 starter -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring</artifactId>
            <version>6.8.0</version> <!-- 请根据你使用的 Flowable 版本调整 -->
        </dependency>

        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.3.1</version>
        </dependency>

    </dependencies>
</project>
