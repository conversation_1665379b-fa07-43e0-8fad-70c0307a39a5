package com.heju.system.file.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.file.domain.dto.SysFileRoleMergeDto;
import com.heju.system.file.domain.po.SysFileRoleMergePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:36+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysFileRoleMergeConverterImpl implements SysFileRoleMergeConverter {

    @Override
    public SysFileRoleMergeDto mapperDto(SysFileRoleMergePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFileRoleMergeDto sysFileRoleMergeDto = new SysFileRoleMergeDto();

        sysFileRoleMergeDto.setId( arg0.getId() );
        sysFileRoleMergeDto.setSourceName( arg0.getSourceName() );
        sysFileRoleMergeDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFileRoleMergeDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFileRoleMergeDto.setName( arg0.getName() );
        sysFileRoleMergeDto.setStatus( arg0.getStatus() );
        sysFileRoleMergeDto.setSort( arg0.getSort() );
        sysFileRoleMergeDto.setRemark( arg0.getRemark() );
        sysFileRoleMergeDto.setCreateBy( arg0.getCreateBy() );
        sysFileRoleMergeDto.setCreateTime( arg0.getCreateTime() );
        sysFileRoleMergeDto.setUpdateBy( arg0.getUpdateBy() );
        sysFileRoleMergeDto.setUpdateTime( arg0.getUpdateTime() );
        sysFileRoleMergeDto.setDelFlag( arg0.getDelFlag() );
        sysFileRoleMergeDto.setCreateName( arg0.getCreateName() );
        sysFileRoleMergeDto.setUpdateName( arg0.getUpdateName() );
        sysFileRoleMergeDto.setFileId( arg0.getFileId() );
        sysFileRoleMergeDto.setRoleId( arg0.getRoleId() );
        sysFileRoleMergeDto.setOperateType( arg0.getOperateType() );

        return sysFileRoleMergeDto;
    }

    @Override
    public List<SysFileRoleMergeDto> mapperDto(Collection<SysFileRoleMergePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFileRoleMergeDto> list = new ArrayList<SysFileRoleMergeDto>( arg0.size() );
        for ( SysFileRoleMergePo sysFileRoleMergePo : arg0 ) {
            list.add( mapperDto( sysFileRoleMergePo ) );
        }

        return list;
    }

    @Override
    public Page<SysFileRoleMergeDto> mapperPageDto(Collection<SysFileRoleMergePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFileRoleMergeDto> page = new Page<SysFileRoleMergeDto>();
        for ( SysFileRoleMergePo sysFileRoleMergePo : arg0 ) {
            page.add( mapperDto( sysFileRoleMergePo ) );
        }

        return page;
    }

    @Override
    public SysFileRoleMergePo mapperPo(SysFileRoleMergeDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFileRoleMergePo sysFileRoleMergePo = new SysFileRoleMergePo();

        sysFileRoleMergePo.setId( arg0.getId() );
        sysFileRoleMergePo.setSourceName( arg0.getSourceName() );
        sysFileRoleMergePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFileRoleMergePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFileRoleMergePo.setName( arg0.getName() );
        sysFileRoleMergePo.setStatus( arg0.getStatus() );
        sysFileRoleMergePo.setSort( arg0.getSort() );
        sysFileRoleMergePo.setRemark( arg0.getRemark() );
        sysFileRoleMergePo.setCreateBy( arg0.getCreateBy() );
        sysFileRoleMergePo.setCreateTime( arg0.getCreateTime() );
        sysFileRoleMergePo.setUpdateBy( arg0.getUpdateBy() );
        sysFileRoleMergePo.setUpdateTime( arg0.getUpdateTime() );
        sysFileRoleMergePo.setDelFlag( arg0.getDelFlag() );
        sysFileRoleMergePo.setCreateName( arg0.getCreateName() );
        sysFileRoleMergePo.setUpdateName( arg0.getUpdateName() );
        sysFileRoleMergePo.setFileId( arg0.getFileId() );
        sysFileRoleMergePo.setRoleId( arg0.getRoleId() );
        sysFileRoleMergePo.setOperateType( arg0.getOperateType() );

        return sysFileRoleMergePo;
    }

    @Override
    public List<SysFileRoleMergePo> mapperPo(Collection<SysFileRoleMergeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFileRoleMergePo> list = new ArrayList<SysFileRoleMergePo>( arg0.size() );
        for ( SysFileRoleMergeDto sysFileRoleMergeDto : arg0 ) {
            list.add( mapperPo( sysFileRoleMergeDto ) );
        }

        return list;
    }

    @Override
    public Page<SysFileRoleMergePo> mapperPagePo(Collection<SysFileRoleMergeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFileRoleMergePo> page = new Page<SysFileRoleMergePo>();
        for ( SysFileRoleMergeDto sysFileRoleMergeDto : arg0 ) {
            page.add( mapperPo( sysFileRoleMergeDto ) );
        }

        return page;
    }
}
