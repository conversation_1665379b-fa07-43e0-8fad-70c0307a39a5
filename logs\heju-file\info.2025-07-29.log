09:13:43.210 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:13:43.903 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3de4b311-07c2-49e8-9224-2a71363d6060_config-0
09:13:43.985 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 40 ms to scan 1 urls, producing 3 keys and 6 values 
09:13:44.018 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:13:44.028 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:13:44.038 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:13:44.049 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:13:44.063 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:13:44.067 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3de4b311-07c2-49e8-9224-2a71363d6060_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:13:44.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3de4b311-07c2-49e8-9224-2a71363d6060_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000184813b3e00
09:13:44.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3de4b311-07c2-49e8-9224-2a71363d6060_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000184813b4020
09:13:44.069 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3de4b311-07c2-49e8-9224-2a71363d6060_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:13:44.070 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3de4b311-07c2-49e8-9224-2a71363d6060_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:13:44.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3de4b311-07c2-49e8-9224-2a71363d6060_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:13:45.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3de4b311-07c2-49e8-9224-2a71363d6060_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753751624934_127.0.0.1_10287
09:13:45.221 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3de4b311-07c2-49e8-9224-2a71363d6060_config-0] Notify connected event to listeners.
09:13:45.222 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3de4b311-07c2-49e8-9224-2a71363d6060_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:13:45.223 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3de4b311-07c2-49e8-9224-2a71363d6060_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000184814f0228
09:13:45.398 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:13:48.372 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:13:48.373 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:13:48.373 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:13:48.644 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:13:52.276 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:14:01.431 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2bdebfe2-32b0-474d-894b-3fe5ede65def
09:14:01.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bdebfe2-32b0-474d-894b-3fe5ede65def] RpcClient init label, labels = {module=naming, source=sdk}
09:14:01.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bdebfe2-32b0-474d-894b-3fe5ede65def] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:14:01.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bdebfe2-32b0-474d-894b-3fe5ede65def] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:14:01.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bdebfe2-32b0-474d-894b-3fe5ede65def] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:14:01.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bdebfe2-32b0-474d-894b-3fe5ede65def] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:01.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bdebfe2-32b0-474d-894b-3fe5ede65def] Success to connect to server [localhost:8848] on start up, connectionId = 1753751641465_127.0.0.1_10548
09:14:01.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bdebfe2-32b0-474d-894b-3fe5ede65def] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:01.600 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bdebfe2-32b0-474d-894b-3fe5ede65def] Notify connected event to listeners.
09:14:01.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bdebfe2-32b0-474d-894b-3fe5ede65def] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000184814f0228
09:14:01.712 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:14:01.797 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:14:02.214 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bdebfe2-32b0-474d-894b-3fe5ede65def] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:14:02.255 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bdebfe2-32b0-474d-894b-3fe5ede65def] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:14:02.305 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 19.826 seconds (JVM running for 25.813)
09:14:02.340 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:14:02.342 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:14:02.388 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
11:41:03.912 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:41:03.916 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:41:04.213 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:41:04.214 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5fce42f7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:41:04.214 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753751641465_127.0.0.1_10548
11:41:04.220 [nacos-grpc-client-executor-1783] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753751641465_127.0.0.1_10548]Ignore complete event,isRunning:false,isAbandon=false
11:41:04.226 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2789fbad[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1784]
11:44:55.807 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:44:56.323 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 71bbdcc0-9811-4f5e-b393-79eae62c05c3_config-0
11:44:56.380 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
11:44:56.402 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
11:44:56.411 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:44:56.422 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
11:44:56.428 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
11:44:56.439 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
11:44:56.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71bbdcc0-9811-4f5e-b393-79eae62c05c3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:44:56.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71bbdcc0-9811-4f5e-b393-79eae62c05c3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000281353b6440
11:44:56.445 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71bbdcc0-9811-4f5e-b393-79eae62c05c3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000281353b6660
11:44:56.445 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71bbdcc0-9811-4f5e-b393-79eae62c05c3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:44:56.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71bbdcc0-9811-4f5e-b393-79eae62c05c3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:44:56.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71bbdcc0-9811-4f5e-b393-79eae62c05c3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:44:57.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71bbdcc0-9811-4f5e-b393-79eae62c05c3_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753760697169_127.0.0.1_7230
11:44:57.388 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71bbdcc0-9811-4f5e-b393-79eae62c05c3_config-0] Notify connected event to listeners.
11:44:57.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71bbdcc0-9811-4f5e-b393-79eae62c05c3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:44:57.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71bbdcc0-9811-4f5e-b393-79eae62c05c3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000281354f0228
11:44:57.481 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:44:59.772 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
11:44:59.772 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:44:59.772 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:44:59.921 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:45:01.665 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:45:03.974 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dcc28a2c-c7f4-4212-bdfe-8ac8f09fe0f3
11:45:03.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dcc28a2c-c7f4-4212-bdfe-8ac8f09fe0f3] RpcClient init label, labels = {module=naming, source=sdk}
11:45:03.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dcc28a2c-c7f4-4212-bdfe-8ac8f09fe0f3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:45:03.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dcc28a2c-c7f4-4212-bdfe-8ac8f09fe0f3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:45:03.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dcc28a2c-c7f4-4212-bdfe-8ac8f09fe0f3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:45:03.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dcc28a2c-c7f4-4212-bdfe-8ac8f09fe0f3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:45:04.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dcc28a2c-c7f4-4212-bdfe-8ac8f09fe0f3] Success to connect to server [localhost:8848] on start up, connectionId = 1753760703983_127.0.0.1_7262
11:45:04.093 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dcc28a2c-c7f4-4212-bdfe-8ac8f09fe0f3] Notify connected event to listeners.
11:45:04.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dcc28a2c-c7f4-4212-bdfe-8ac8f09fe0f3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:45:04.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dcc28a2c-c7f4-4212-bdfe-8ac8f09fe0f3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000281354f0228
11:45:04.143 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
11:45:04.167 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
11:45:04.326 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 8.998 seconds (JVM running for 9.879)
11:45:04.341 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
11:45:04.342 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
11:45:04.345 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
11:45:04.542 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:45:04.641 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dcc28a2c-c7f4-4212-bdfe-8ac8f09fe0f3] Receive server push request, request = NotifySubscriberRequest, requestId = 37
11:45:04.658 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dcc28a2c-c7f4-4212-bdfe-8ac8f09fe0f3] Ack server push request, request = NotifySubscriberRequest, requestId = 37
20:27:08.161 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:27:08.172 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:27:08.509 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:27:08.509 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@679bf528[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:27:08.514 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753760703983_127.0.0.1_7262
20:27:08.518 [nacos-grpc-client-executor-6268] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753760703983_127.0.0.1_7262]Ignore complete event,isRunning:false,isAbandon=false
20:27:08.524 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@15753ad4[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6269]
