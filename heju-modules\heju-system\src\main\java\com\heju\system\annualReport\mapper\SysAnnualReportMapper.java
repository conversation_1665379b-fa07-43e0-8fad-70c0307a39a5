package com.heju.system.annualReport.mapper;

import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.system.annualReport.domain.dto.SysAnnualReportDto;
import com.heju.system.annualReport.domain.po.SysAnnualReportPo;
import com.heju.system.annualReport.domain.query.SysAnnualReportQuery;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 工商年报管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysAnnualReportMapper extends BaseMapper<SysAnnualReportQuery, SysAnnualReportDto, SysAnnualReportPo> {

    @Select("<script>" +
            "select DISTINCT entity_id from sys_annual_report where del_flag=0 " +
            "<if test='query.entityId != null'> and entity_id = #{query.entityId} </if>" +
            "<if test='query.annualReportType != null'> and annual_report_type = #{query.annualReportType} </if>" +
            "<if test='query.reportYear != null'> and report_year = #{query.reportYear} </if>" +
            " limit #{query.page}, #{query.pageSize}" +
            "</script>")
    @Results({
            @Result(property = "entityId", column = "entity_id"),
            @Result(property = "relatedItems",
                    column = "entity_id",
                    javaType = List.class,
                    many = @Many(select = "com.heju.system.annualReport.mapper.SysAnnualReportMapper.selectWithEntityId"))
    })
    List<Map<String, Object>> selectGroupByEntityId(@Param("query") SysAnnualReportQuery query);

    @Select("select * from sys_annual_report where entity_id = #{entityId} and del_flag=0 order by report_year")
    List<Map<String, Object>> selectWithEntityId(@Param("entityId") Long entityId);


    @Select("<script>" +
            "select * from sys_annual_report where del_flag = '0'" +
            "and (case when #{entityId} is not null then entity_id = #{entityId} else 1=1 end)" +
            "and (case when #{reportYear} is not null then report_year = #{reportYear} else 1=1 end)" +
            "</script>")
    SysAnnualReportPo selectByEntityAndYear(SysAnnualReportDto annualReport);
}