package com.heju.system.file.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.annotation.Excel;
import com.heju.common.core.web.tenant.base.TBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 文件借阅记录 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_file_borrow_record", excludeProperty = { STATUS, UPDATE_BY, SORT, DEL_FLAG, UPDATE_TIME, REMARK, NAME })
public class SysFileBorrowRecordPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 文件id */
    @Excel(name = "文件id")
    protected Long fileId;

    /** 借阅用户id */
    @Excel(name = "借阅用户id")
    protected Long borrowUserId;

    /** 开始时间 */
    @Excel(name = "开始时间")
    protected Date startTime;

    /** 结束时间 */
    @Excel(name = "结束时间")
    protected Date endTime;

    /** 是否可查看（1-是；0-否） */
    @Excel(name = "是否可查看", readConverterExp = "1=-是；0-否")
    protected Integer isView;

    /** 是否可下载（1-是；0-否） */
    @Excel(name = "是否可下载", readConverterExp = "1=-是；0-否")
    protected Integer isDownload;

}
