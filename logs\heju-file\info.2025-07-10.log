09:16:00.457 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:16:01.747 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a4f23105-1852-4ae5-9a95-8934479d9108_config-0
09:16:01.885 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 60 ms to scan 1 urls, producing 3 keys and 6 values 
09:16:01.933 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:16:01.949 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 3 keys and 10 values 
09:16:01.965 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:16:01.981 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 7 values 
09:16:01.998 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:16:02.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:16:02.009 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002461f3af470
09:16:02.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002461f3af690
09:16:02.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:16:02.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:16:02.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:03.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:03.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:03.229 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:16:03.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:16:03.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002461f4b9650
09:16:03.354 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:03.571 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:03.900 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:04.316 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:04.829 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:04.970 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:16:05.458 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:06.182 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:07.005 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:07.928 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:08.954 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:09.535 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:16:09.536 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:16:09.536 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:16:09.811 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:16:10.207 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:11.568 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:12.651 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:16:13.052 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:14.555 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:15.928 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d396faec-5ad4-4dba-a0cb-90dadbfa67c5
09:16:15.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] RpcClient init label, labels = {module=naming, source=sdk}
09:16:15.937 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:16:15.937 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:16:15.937 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:16:15.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:16.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:16.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:16.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:16:16.022 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:16:16.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002461f4b9650
09:16:16.077 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:16.140 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:16.353 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:16:16.357 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:16.706 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:17.120 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:17.364 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:16:17.364 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4c2812aa[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:16:17.364 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d396faec-5ad4-4dba-a0cb-90dadbfa67c5] Client is shutdown, stop reconnect to server
09:16:17.364 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@346e76ad[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:16:17.373 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a5fa89d9-b091-4813-9563-92da48a74d5c
09:16:17.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5fa89d9-b091-4813-9563-92da48a74d5c] RpcClient init label, labels = {module=naming, source=sdk}
09:16:17.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5fa89d9-b091-4813-9563-92da48a74d5c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:16:17.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5fa89d9-b091-4813-9563-92da48a74d5c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:16:17.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5fa89d9-b091-4813-9563-92da48a74d5c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:16:17.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5fa89d9-b091-4813-9563-92da48a74d5c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:17.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5fa89d9-b091-4813-9563-92da48a74d5c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:17.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5fa89d9-b091-4813-9563-92da48a74d5c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:17.437 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5fa89d9-b091-4813-9563-92da48a74d5c] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:16:17.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5fa89d9-b091-4813-9563-92da48a74d5c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:16:17.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5fa89d9-b091-4813-9563-92da48a74d5c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002461f4b9650
09:16:17.567 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5fa89d9-b091-4813-9563-92da48a74d5c] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:17.697 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4f23105-1852-4ae5-9a95-8934479d9108_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:17.773 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5fa89d9-b091-4813-9563-92da48a74d5c] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:17.787 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9300"]
09:16:17.790 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:16:17.798 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9300"]
09:16:17.802 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9300"]
09:18:30.627 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:18:31.316 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of acd6a3ff-8646-47f6-9c72-916c82732779_config-0
09:18:31.393 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
09:18:31.427 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:18:31.438 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:18:31.450 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:18:31.460 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:18:31.473 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:18:31.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [acd6a3ff-8646-47f6-9c72-916c82732779_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:31.478 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [acd6a3ff-8646-47f6-9c72-916c82732779_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000184363b68d8
09:18:31.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [acd6a3ff-8646-47f6-9c72-916c82732779_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000184363b6af8
09:18:31.480 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [acd6a3ff-8646-47f6-9c72-916c82732779_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:31.480 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [acd6a3ff-8646-47f6-9c72-916c82732779_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:31.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [acd6a3ff-8646-47f6-9c72-916c82732779_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:32.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [acd6a3ff-8646-47f6-9c72-916c82732779_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752110312401_127.0.0.1_4188
09:18:32.637 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [acd6a3ff-8646-47f6-9c72-916c82732779_config-0] Notify connected event to listeners.
09:18:32.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [acd6a3ff-8646-47f6-9c72-916c82732779_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:32.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [acd6a3ff-8646-47f6-9c72-916c82732779_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000184364f0440
09:18:32.774 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:18:35.877 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:18:35.878 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:18:35.879 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:18:36.090 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:18:38.054 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:18:40.965 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3ce95fcf-3c02-408c-a851-9b1b8322e663
09:18:40.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ce95fcf-3c02-408c-a851-9b1b8322e663] RpcClient init label, labels = {module=naming, source=sdk}
09:18:40.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ce95fcf-3c02-408c-a851-9b1b8322e663] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:40.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ce95fcf-3c02-408c-a851-9b1b8322e663] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:40.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ce95fcf-3c02-408c-a851-9b1b8322e663] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:40.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ce95fcf-3c02-408c-a851-9b1b8322e663] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:41.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ce95fcf-3c02-408c-a851-9b1b8322e663] Success to connect to server [localhost:8848] on start up, connectionId = 1752110320979_127.0.0.1_4259
09:18:41.096 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ce95fcf-3c02-408c-a851-9b1b8322e663] Notify connected event to listeners.
09:18:41.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ce95fcf-3c02-408c-a851-9b1b8322e663] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:41.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ce95fcf-3c02-408c-a851-9b1b8322e663] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000184364f0440
09:18:41.149 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:18:41.186 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ***********:9300 register finished
09:18:41.397 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 11.482 seconds (JVM running for 12.686)
09:18:41.415 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:18:41.416 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:18:41.420 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:18:41.588 [RMI TCP Connection(7)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:18:41.709 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ce95fcf-3c02-408c-a851-9b1b8322e663] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:18:41.744 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ce95fcf-3c02-408c-a851-9b1b8322e663] Ack server push request, request = NotifySubscriberRequest, requestId = 3
13:38:02.362 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:38:02.372 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:38:02.719 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:38:02.719 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@10840b89[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:38:02.719 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752110320979_127.0.0.1_4259
13:38:02.719 [nacos-grpc-client-executor-3122] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752110320979_127.0.0.1_4259]Ignore complete event,isRunning:false,isAbandon=false
13:38:02.719 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4b5eca08[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3123]
13:45:28.199 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:45:29.427 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c1539f85-4aaa-4e08-bbbf-da342f0e05ad_config-0
13:45:29.573 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 74 ms to scan 1 urls, producing 3 keys and 6 values 
13:45:29.616 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
13:45:29.630 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
13:45:29.645 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
13:45:29.658 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
13:45:29.680 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
13:45:29.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1539f85-4aaa-4e08-bbbf-da342f0e05ad_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:45:29.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1539f85-4aaa-4e08-bbbf-da342f0e05ad_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000240053af470
13:45:29.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1539f85-4aaa-4e08-bbbf-da342f0e05ad_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000240053af690
13:45:29.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1539f85-4aaa-4e08-bbbf-da342f0e05ad_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:45:29.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1539f85-4aaa-4e08-bbbf-da342f0e05ad_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:45:29.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1539f85-4aaa-4e08-bbbf-da342f0e05ad_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:45:31.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1539f85-4aaa-4e08-bbbf-da342f0e05ad_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752126330954_127.0.0.1_10472
13:45:31.228 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1539f85-4aaa-4e08-bbbf-da342f0e05ad_config-0] Notify connected event to listeners.
13:45:31.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1539f85-4aaa-4e08-bbbf-da342f0e05ad_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:45:31.231 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1539f85-4aaa-4e08-bbbf-da342f0e05ad_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000240054e8fb0
13:45:31.387 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:45:35.064 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
13:45:35.065 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:45:35.066 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:45:35.392 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:45:38.238 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:45:42.904 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a8c0e9f5-0700-435b-865d-53cae3d236ca
13:45:42.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8c0e9f5-0700-435b-865d-53cae3d236ca] RpcClient init label, labels = {module=naming, source=sdk}
13:45:42.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8c0e9f5-0700-435b-865d-53cae3d236ca] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:45:42.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8c0e9f5-0700-435b-865d-53cae3d236ca] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:45:42.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8c0e9f5-0700-435b-865d-53cae3d236ca] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:45:42.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8c0e9f5-0700-435b-865d-53cae3d236ca] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:45:43.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8c0e9f5-0700-435b-865d-53cae3d236ca] Success to connect to server [localhost:8848] on start up, connectionId = 1752126342941_127.0.0.1_10521
13:45:43.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8c0e9f5-0700-435b-865d-53cae3d236ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:45:43.062 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8c0e9f5-0700-435b-865d-53cae3d236ca] Notify connected event to listeners.
13:45:43.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8c0e9f5-0700-435b-865d-53cae3d236ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000240054e8fb0
13:45:43.125 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
13:45:43.171 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ***********:9300 register finished
13:45:43.300 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 16.535 seconds (JVM running for 19.47)
13:45:43.314 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
13:45:43.314 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
13:45:43.330 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
13:45:43.639 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8c0e9f5-0700-435b-865d-53cae3d236ca] Receive server push request, request = NotifySubscriberRequest, requestId = 32
13:45:43.653 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8c0e9f5-0700-435b-865d-53cae3d236ca] Ack server push request, request = NotifySubscriberRequest, requestId = 32
14:20:37.934 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:20:37.936 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:20:38.262 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:20:38.263 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@81f151f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:20:38.263 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752126342941_127.0.0.1_10521
14:20:38.266 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6904b451[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 428]
14:31:33.838 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:31:34.452 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 66fafadc-e186-46f0-af5f-aa1eeb437ae7_config-0
14:31:34.513 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 
14:31:34.536 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
14:31:34.544 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
14:31:34.553 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
14:31:34.561 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
14:31:34.574 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
14:31:34.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66fafadc-e186-46f0-af5f-aa1eeb437ae7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:31:34.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66fafadc-e186-46f0-af5f-aa1eeb437ae7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001e1013c6b40
14:31:34.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66fafadc-e186-46f0-af5f-aa1eeb437ae7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001e1013c6d60
14:31:34.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66fafadc-e186-46f0-af5f-aa1eeb437ae7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:31:34.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66fafadc-e186-46f0-af5f-aa1eeb437ae7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:31:34.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66fafadc-e186-46f0-af5f-aa1eeb437ae7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:35.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66fafadc-e186-46f0-af5f-aa1eeb437ae7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752129095296_127.0.0.1_1595
14:31:35.517 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66fafadc-e186-46f0-af5f-aa1eeb437ae7_config-0] Notify connected event to listeners.
14:31:35.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66fafadc-e186-46f0-af5f-aa1eeb437ae7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:35.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66fafadc-e186-46f0-af5f-aa1eeb437ae7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001e101500ad8
14:31:35.617 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:31:38.269 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
14:31:38.270 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:31:38.270 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:31:38.436 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:31:40.281 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:31:44.557 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6a8d7edf-12d3-433f-a7af-44b094f6bd0a
14:31:44.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8d7edf-12d3-433f-a7af-44b094f6bd0a] RpcClient init label, labels = {module=naming, source=sdk}
14:31:44.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8d7edf-12d3-433f-a7af-44b094f6bd0a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:31:44.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8d7edf-12d3-433f-a7af-44b094f6bd0a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:31:44.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8d7edf-12d3-433f-a7af-44b094f6bd0a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:31:44.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8d7edf-12d3-433f-a7af-44b094f6bd0a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:44.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8d7edf-12d3-433f-a7af-44b094f6bd0a] Success to connect to server [localhost:8848] on start up, connectionId = 1752129104580_127.0.0.1_1658
14:31:44.709 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8d7edf-12d3-433f-a7af-44b094f6bd0a] Notify connected event to listeners.
14:31:44.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8d7edf-12d3-433f-a7af-44b094f6bd0a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:44.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8d7edf-12d3-433f-a7af-44b094f6bd0a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001e101500ad8
14:31:44.801 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
14:31:44.846 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ***********:9300 register finished
14:31:45.071 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 11.918 seconds (JVM running for 12.971)
14:31:45.090 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
14:31:45.091 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
14:31:45.117 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
14:31:45.276 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:31:45.325 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8d7edf-12d3-433f-a7af-44b094f6bd0a] Receive server push request, request = NotifySubscriberRequest, requestId = 39
14:31:45.353 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a8d7edf-12d3-433f-a7af-44b094f6bd0a] Ack server push request, request = NotifySubscriberRequest, requestId = 39
14:58:53.310 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:58:53.316 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:58:53.649 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:58:53.649 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@17995a31[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:58:53.649 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752129104580_127.0.0.1_1658
14:58:53.652 [nacos-grpc-client-executor-339] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752129104580_127.0.0.1_1658]Ignore complete event,isRunning:false,isAbandon=false
14:58:53.654 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@267c78b1[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 340]
18:20:59.711 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:21:00.326 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fe6d997d-31a0-4e32-89c8-f51a8b7d8d59_config-0
18:21:00.401 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
18:21:00.426 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
18:21:00.435 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
18:21:00.446 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
18:21:00.454 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
18:21:00.466 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
18:21:00.469 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe6d997d-31a0-4e32-89c8-f51a8b7d8d59_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:21:00.469 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe6d997d-31a0-4e32-89c8-f51a8b7d8d59_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001f6013b0200
18:21:00.470 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe6d997d-31a0-4e32-89c8-f51a8b7d8d59_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001f6013b0420
18:21:00.470 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe6d997d-31a0-4e32-89c8-f51a8b7d8d59_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:21:00.471 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe6d997d-31a0-4e32-89c8-f51a8b7d8d59_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:21:00.480 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe6d997d-31a0-4e32-89c8-f51a8b7d8d59_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:21:01.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe6d997d-31a0-4e32-89c8-f51a8b7d8d59_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752142861269_127.0.0.1_8975
18:21:01.528 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe6d997d-31a0-4e32-89c8-f51a8b7d8d59_config-0] Notify connected event to listeners.
18:21:01.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe6d997d-31a0-4e32-89c8-f51a8b7d8d59_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:21:01.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe6d997d-31a0-4e32-89c8-f51a8b7d8d59_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001f6014ea6e0
18:21:01.658 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:21:03.851 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
18:21:03.851 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:21:03.851 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:21:04.004 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:21:05.381 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:21:07.772 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7df74f7a-8c1c-4c75-8c1d-374a9d0de3cf
18:21:07.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7df74f7a-8c1c-4c75-8c1d-374a9d0de3cf] RpcClient init label, labels = {module=naming, source=sdk}
18:21:07.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7df74f7a-8c1c-4c75-8c1d-374a9d0de3cf] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:21:07.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7df74f7a-8c1c-4c75-8c1d-374a9d0de3cf] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:21:07.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7df74f7a-8c1c-4c75-8c1d-374a9d0de3cf] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:21:07.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7df74f7a-8c1c-4c75-8c1d-374a9d0de3cf] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:21:07.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7df74f7a-8c1c-4c75-8c1d-374a9d0de3cf] Success to connect to server [localhost:8848] on start up, connectionId = 1752142867789_127.0.0.1_8990
18:21:07.912 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7df74f7a-8c1c-4c75-8c1d-374a9d0de3cf] Notify connected event to listeners.
18:21:07.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7df74f7a-8c1c-4c75-8c1d-374a9d0de3cf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:21:07.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7df74f7a-8c1c-4c75-8c1d-374a9d0de3cf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001f6014ea6e0
18:21:07.971 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
18:21:08.009 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ***********:9300 register finished
18:21:08.156 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 9.043 seconds (JVM running for 10.877)
18:21:08.170 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
18:21:08.171 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
18:21:08.174 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
18:21:08.475 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7df74f7a-8c1c-4c75-8c1d-374a9d0de3cf] Receive server push request, request = NotifySubscriberRequest, requestId = 77
18:21:08.495 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7df74f7a-8c1c-4c75-8c1d-374a9d0de3cf] Ack server push request, request = NotifySubscriberRequest, requestId = 77
20:41:31.046 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:41:31.051 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:41:31.383 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:41:31.385 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6e0d6773[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:41:31.385 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752142867789_127.0.0.1_8990
20:41:31.388 [nacos-grpc-client-executor-1694] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752142867789_127.0.0.1_8990]Ignore complete event,isRunning:false,isAbandon=false
20:41:31.396 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@40c4d45f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1695]
