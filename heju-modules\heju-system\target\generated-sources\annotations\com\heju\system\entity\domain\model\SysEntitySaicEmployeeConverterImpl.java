package com.heju.system.entity.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.entity.domain.dto.SysEntitySaicEmployeeDto;
import com.heju.system.entity.domain.po.SysEntitySaicEmployeePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysEntitySaicEmployeeConverterImpl implements SysEntitySaicEmployeeConverter {

    @Override
    public SysEntitySaicEmployeeDto mapperDto(SysEntitySaicEmployeePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntitySaicEmployeeDto sysEntitySaicEmployeeDto = new SysEntitySaicEmployeeDto();

        sysEntitySaicEmployeeDto.setId( arg0.getId() );
        sysEntitySaicEmployeeDto.setSourceName( arg0.getSourceName() );
        sysEntitySaicEmployeeDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntitySaicEmployeeDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntitySaicEmployeeDto.setStatus( arg0.getStatus() );
        sysEntitySaicEmployeeDto.setSort( arg0.getSort() );
        sysEntitySaicEmployeeDto.setRemark( arg0.getRemark() );
        sysEntitySaicEmployeeDto.setCreateBy( arg0.getCreateBy() );
        sysEntitySaicEmployeeDto.setCreateTime( arg0.getCreateTime() );
        sysEntitySaicEmployeeDto.setUpdateBy( arg0.getUpdateBy() );
        sysEntitySaicEmployeeDto.setUpdateTime( arg0.getUpdateTime() );
        sysEntitySaicEmployeeDto.setDelFlag( arg0.getDelFlag() );
        sysEntitySaicEmployeeDto.setCreateName( arg0.getCreateName() );
        sysEntitySaicEmployeeDto.setUpdateName( arg0.getUpdateName() );
        sysEntitySaicEmployeeDto.setTitle( arg0.getTitle() );
        sysEntitySaicEmployeeDto.setName( arg0.getName() );
        sysEntitySaicEmployeeDto.setEntityId( arg0.getEntityId() );

        return sysEntitySaicEmployeeDto;
    }

    @Override
    public List<SysEntitySaicEmployeeDto> mapperDto(Collection<SysEntitySaicEmployeePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntitySaicEmployeeDto> list = new ArrayList<SysEntitySaicEmployeeDto>( arg0.size() );
        for ( SysEntitySaicEmployeePo sysEntitySaicEmployeePo : arg0 ) {
            list.add( mapperDto( sysEntitySaicEmployeePo ) );
        }

        return list;
    }

    @Override
    public Page<SysEntitySaicEmployeeDto> mapperPageDto(Collection<SysEntitySaicEmployeePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntitySaicEmployeeDto> page = new Page<SysEntitySaicEmployeeDto>();
        for ( SysEntitySaicEmployeePo sysEntitySaicEmployeePo : arg0 ) {
            page.add( mapperDto( sysEntitySaicEmployeePo ) );
        }

        return page;
    }

    @Override
    public SysEntitySaicEmployeePo mapperPo(SysEntitySaicEmployeeDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntitySaicEmployeePo sysEntitySaicEmployeePo = new SysEntitySaicEmployeePo();

        sysEntitySaicEmployeePo.setId( arg0.getId() );
        sysEntitySaicEmployeePo.setSourceName( arg0.getSourceName() );
        sysEntitySaicEmployeePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntitySaicEmployeePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntitySaicEmployeePo.setStatus( arg0.getStatus() );
        sysEntitySaicEmployeePo.setSort( arg0.getSort() );
        sysEntitySaicEmployeePo.setRemark( arg0.getRemark() );
        sysEntitySaicEmployeePo.setCreateBy( arg0.getCreateBy() );
        sysEntitySaicEmployeePo.setCreateTime( arg0.getCreateTime() );
        sysEntitySaicEmployeePo.setUpdateBy( arg0.getUpdateBy() );
        sysEntitySaicEmployeePo.setUpdateTime( arg0.getUpdateTime() );
        sysEntitySaicEmployeePo.setDelFlag( arg0.getDelFlag() );
        sysEntitySaicEmployeePo.setCreateName( arg0.getCreateName() );
        sysEntitySaicEmployeePo.setUpdateName( arg0.getUpdateName() );
        sysEntitySaicEmployeePo.setTitle( arg0.getTitle() );
        sysEntitySaicEmployeePo.setName( arg0.getName() );
        sysEntitySaicEmployeePo.setEntityId( arg0.getEntityId() );

        return sysEntitySaicEmployeePo;
    }

    @Override
    public List<SysEntitySaicEmployeePo> mapperPo(Collection<SysEntitySaicEmployeeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntitySaicEmployeePo> list = new ArrayList<SysEntitySaicEmployeePo>( arg0.size() );
        for ( SysEntitySaicEmployeeDto sysEntitySaicEmployeeDto : arg0 ) {
            list.add( mapperPo( sysEntitySaicEmployeeDto ) );
        }

        return list;
    }

    @Override
    public Page<SysEntitySaicEmployeePo> mapperPagePo(Collection<SysEntitySaicEmployeeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntitySaicEmployeePo> page = new Page<SysEntitySaicEmployeePo>();
        for ( SysEntitySaicEmployeeDto sysEntitySaicEmployeeDto : arg0 ) {
            page.add( mapperPo( sysEntitySaicEmployeeDto ) );
        }

        return page;
    }
}
