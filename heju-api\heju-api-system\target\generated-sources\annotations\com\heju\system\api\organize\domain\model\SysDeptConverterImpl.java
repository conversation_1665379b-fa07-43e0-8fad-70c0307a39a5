package com.heju.system.api.organize.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.api.organize.domain.dto.SysDeptDto;
import com.heju.system.api.organize.domain.po.SysDeptPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:51+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysDeptConverterImpl implements SysDeptConverter {

    @Override
    public Page<SysDeptDto> mapperPageDto(Collection<SysDeptPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysDeptDto> page = new Page<SysDeptDto>();
        for ( SysDeptPo sysDeptPo : arg0 ) {
            page.add( mapperDto( sysDeptPo ) );
        }

        return page;
    }

    @Override
    public Page<SysDeptPo> mapperPagePo(Collection<SysDeptDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysDeptPo> page = new Page<SysDeptPo>();
        for ( SysDeptDto sysDeptDto : arg0 ) {
            page.add( mapperPo( sysDeptDto ) );
        }

        return page;
    }

    @Override
    public SysDeptDto mapperDto(SysDeptPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysDeptDto sysDeptDto = new SysDeptDto();

        sysDeptDto.setId( arg0.getId() );
        sysDeptDto.setSourceName( arg0.getSourceName() );
        sysDeptDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysDeptDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysDeptDto.setStatus( arg0.getStatus() );
        sysDeptDto.setSort( arg0.getSort() );
        sysDeptDto.setRemark( arg0.getRemark() );
        sysDeptDto.setCreateBy( arg0.getCreateBy() );
        sysDeptDto.setCreateTime( arg0.getCreateTime() );
        sysDeptDto.setUpdateBy( arg0.getUpdateBy() );
        sysDeptDto.setUpdateTime( arg0.getUpdateTime() );
        sysDeptDto.setDelFlag( arg0.getDelFlag() );
        sysDeptDto.setCreateName( arg0.getCreateName() );
        sysDeptDto.setUpdateName( arg0.getUpdateName() );
        sysDeptDto.setParentId( arg0.getParentId() );
        sysDeptDto.setParentName( arg0.getParentName() );
        sysDeptDto.setAncestors( arg0.getAncestors() );
        sysDeptDto.setLevel( arg0.getLevel() );
        sysDeptDto.setDefaultNode( arg0.getDefaultNode() );
        sysDeptDto.setOldAncestors( arg0.getOldAncestors() );
        sysDeptDto.setOldLevel( arg0.getOldLevel() );
        sysDeptDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysDeptDto.setCode( arg0.getCode() );
        sysDeptDto.setName( arg0.getName() );
        sysDeptDto.setLeader( arg0.getLeader() );
        sysDeptDto.setPhone( arg0.getPhone() );
        sysDeptDto.setEmail( arg0.getEmail() );
        sysDeptDto.setCompanyId( arg0.getCompanyId() );
        sysDeptDto.setCompanyName( arg0.getCompanyName() );

        return sysDeptDto;
    }

    @Override
    public List<SysDeptDto> mapperDto(Collection<SysDeptPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysDeptDto> list = new ArrayList<SysDeptDto>( arg0.size() );
        for ( SysDeptPo sysDeptPo : arg0 ) {
            list.add( mapperDto( sysDeptPo ) );
        }

        return list;
    }

    @Override
    public SysDeptPo mapperPo(SysDeptDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysDeptPo sysDeptPo = new SysDeptPo();

        sysDeptPo.setId( arg0.getId() );
        sysDeptPo.setSourceName( arg0.getSourceName() );
        sysDeptPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysDeptPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysDeptPo.setStatus( arg0.getStatus() );
        sysDeptPo.setSort( arg0.getSort() );
        sysDeptPo.setRemark( arg0.getRemark() );
        sysDeptPo.setCreateBy( arg0.getCreateBy() );
        sysDeptPo.setCreateTime( arg0.getCreateTime() );
        sysDeptPo.setUpdateBy( arg0.getUpdateBy() );
        sysDeptPo.setUpdateTime( arg0.getUpdateTime() );
        sysDeptPo.setDelFlag( arg0.getDelFlag() );
        sysDeptPo.setCreateName( arg0.getCreateName() );
        sysDeptPo.setUpdateName( arg0.getUpdateName() );
        sysDeptPo.setParentId( arg0.getParentId() );
        sysDeptPo.setParentName( arg0.getParentName() );
        sysDeptPo.setAncestors( arg0.getAncestors() );
        sysDeptPo.setLevel( arg0.getLevel() );
        sysDeptPo.setDefaultNode( arg0.getDefaultNode() );
        sysDeptPo.setOldAncestors( arg0.getOldAncestors() );
        sysDeptPo.setOldLevel( arg0.getOldLevel() );
        sysDeptPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysDeptPo.setCode( arg0.getCode() );
        sysDeptPo.setName( arg0.getName() );
        sysDeptPo.setLeader( arg0.getLeader() );
        sysDeptPo.setPhone( arg0.getPhone() );
        sysDeptPo.setEmail( arg0.getEmail() );
        sysDeptPo.setCompanyId( arg0.getCompanyId() );
        sysDeptPo.setCompanyName( arg0.getCompanyName() );

        return sysDeptPo;
    }

    @Override
    public List<SysDeptPo> mapperPo(Collection<SysDeptDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysDeptPo> list = new ArrayList<SysDeptPo>( arg0.size() );
        for ( SysDeptDto sysDeptDto : arg0 ) {
            list.add( mapperPo( sysDeptDto ) );
        }

        return list;
    }
}
