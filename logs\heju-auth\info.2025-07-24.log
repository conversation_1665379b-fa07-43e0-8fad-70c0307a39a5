09:12:13.072 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:12:13.852 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b75b0e4f-560e-4b6e-a14f-bfe8e58286fa_config-0
09:12:13.949 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 41 ms to scan 1 urls, producing 3 keys and 6 values 
09:12:13.991 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 4 keys and 9 values 
09:12:14.001 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:12:14.013 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:12:14.024 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:12:14.034 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:12:14.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b75b0e4f-560e-4b6e-a14f-bfe8e58286fa_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:12:14.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b75b0e4f-560e-4b6e-a14f-bfe8e58286fa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000018fd03b4958
09:12:14.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b75b0e4f-560e-4b6e-a14f-bfe8e58286fa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000018fd03b4b78
09:12:14.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b75b0e4f-560e-4b6e-a14f-bfe8e58286fa_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:12:14.040 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b75b0e4f-560e-4b6e-a14f-bfe8e58286fa_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:12:14.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b75b0e4f-560e-4b6e-a14f-bfe8e58286fa_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:15.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b75b0e4f-560e-4b6e-a14f-bfe8e58286fa_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753319535063_127.0.0.1_12535
09:12:15.314 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b75b0e4f-560e-4b6e-a14f-bfe8e58286fa_config-0] Notify connected event to listeners.
09:12:15.314 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b75b0e4f-560e-4b6e-a14f-bfe8e58286fa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:15.314 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b75b0e4f-560e-4b6e-a14f-bfe8e58286fa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000018fd04ecb18
09:12:15.459 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:12:18.772 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:12:18.773 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:12:18.774 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:12:19.172 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:12:22.132 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:12:23.913 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8d45171f-5873-4a93-bfa0-28dcb5433e42
09:12:23.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] RpcClient init label, labels = {module=naming, source=sdk}
09:12:23.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:12:23.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:12:23.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:12:23.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:24.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Success to connect to server [localhost:8848] on start up, connectionId = 1753319543931_127.0.0.1_12627
09:12:24.093 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Notify connected event to listeners.
09:12:24.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:24.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000018fd04ecb18
09:12:24.210 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:12:24.249 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:12:24.447 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 12.12 seconds (JVM running for 14.83)
09:12:24.459 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:12:24.460 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:12:24.464 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:12:24.673 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:12:24.690 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:16:05.081 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:16:14.092 [nacos-grpc-client-executor-63] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:16:14.093 [nacos-grpc-client-executor-63] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:16:16.705 [nacos-grpc-client-executor-66] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:16:16.707 [nacos-grpc-client-executor-66] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 12
11:49:17.955 [nacos-grpc-client-executor-1954] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 18
11:49:17.976 [nacos-grpc-client-executor-1954] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 18
11:49:45.305 [nacos-grpc-client-executor-1961] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 22
11:49:45.323 [nacos-grpc-client-executor-1961] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 22
11:51:27.479 [nacos-grpc-client-executor-1982] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 27
11:51:27.501 [nacos-grpc-client-executor-1982] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 27
11:51:53.056 [nacos-grpc-client-executor-1988] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 31
11:51:53.077 [nacos-grpc-client-executor-1988] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 31
13:28:55.935 [nacos-grpc-client-executor-3153] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 36
13:28:55.950 [nacos-grpc-client-executor-3153] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 36
13:29:39.235 [nacos-grpc-client-executor-3163] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 40
13:29:39.249 [nacos-grpc-client-executor-3163] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 40
13:31:40.251 [nacos-grpc-client-executor-3188] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 45
13:31:40.267 [nacos-grpc-client-executor-3188] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 45
13:31:58.819 [nacos-grpc-client-executor-3192] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 49
13:31:58.835 [nacos-grpc-client-executor-3192] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 49
13:37:01.865 [nacos-grpc-client-executor-3252] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 54
13:37:01.888 [nacos-grpc-client-executor-3252] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 54
13:37:18.886 [nacos-grpc-client-executor-3256] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 58
13:37:18.900 [nacos-grpc-client-executor-3256] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 58
13:40:17.535 [nacos-grpc-client-executor-3292] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 64
13:40:17.552 [nacos-grpc-client-executor-3292] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 64
13:40:32.374 [nacos-grpc-client-executor-3296] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Receive server push request, request = NotifySubscriberRequest, requestId = 69
13:40:32.388 [nacos-grpc-client-executor-3296] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d45171f-5873-4a93-bfa0-28dcb5433e42] Ack server push request, request = NotifySubscriberRequest, requestId = 69
13:41:02.192 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:41:02.196 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:41:02.531 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:41:02.532 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@b085db4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:41:02.532 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753319543931_127.0.0.1_12627
13:41:02.533 [nacos-grpc-client-executor-3304] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753319543931_127.0.0.1_12627]Ignore complete event,isRunning:false,isAbandon=false
13:41:02.538 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2140837f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3305]
13:41:59.484 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:42:00.905 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 074de6bd-1b5c-4f88-a75c-5266a147c1c5_config-0
13:42:01.005 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 58 ms to scan 1 urls, producing 3 keys and 6 values 
13:42:01.062 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 4 keys and 9 values 
13:42:01.077 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
13:42:01.093 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
13:42:01.110 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
13:42:01.124 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
13:42:01.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [074de6bd-1b5c-4f88-a75c-5266a147c1c5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:42:01.129 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [074de6bd-1b5c-4f88-a75c-5266a147c1c5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002cac23b3188
13:42:01.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [074de6bd-1b5c-4f88-a75c-5266a147c1c5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002cac23b33a8
13:42:01.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [074de6bd-1b5c-4f88-a75c-5266a147c1c5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:42:01.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [074de6bd-1b5c-4f88-a75c-5266a147c1c5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:42:01.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [074de6bd-1b5c-4f88-a75c-5266a147c1c5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:42:02.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [074de6bd-1b5c-4f88-a75c-5266a147c1c5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753335722412_127.0.0.1_1494
13:42:02.682 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [074de6bd-1b5c-4f88-a75c-5266a147c1c5_config-0] Notify connected event to listeners.
13:42:02.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [074de6bd-1b5c-4f88-a75c-5266a147c1c5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:42:02.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [074de6bd-1b5c-4f88-a75c-5266a147c1c5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002cac24ecd90
13:42:02.838 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:42:05.127 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
13:42:05.127 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:42:05.127 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:42:05.260 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:42:06.794 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:42:07.915 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 91eeb5ec-cc26-41cd-98bd-b9fca1099ab8
13:42:07.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] RpcClient init label, labels = {module=naming, source=sdk}
13:42:07.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:42:07.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:42:07.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:42:07.920 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:42:08.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] Success to connect to server [localhost:8848] on start up, connectionId = 1753335727930_127.0.0.1_1501
13:42:08.058 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] Notify connected event to listeners.
13:42:08.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:42:08.059 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002cac24ecd90
13:42:08.112 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
13:42:08.139 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
13:42:08.278 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 10.408 seconds (JVM running for 13.21)
13:42:08.293 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
13:42:08.294 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
13:42:08.296 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
13:42:08.670 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] Receive server push request, request = NotifySubscriberRequest, requestId = 5
13:42:08.689 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] Ack server push request, request = NotifySubscriberRequest, requestId = 5
16:31:16.433 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:31:20.166 [nacos-grpc-client-executor-2042] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] Receive server push request, request = NotifySubscriberRequest, requestId = 20
16:31:20.166 [nacos-grpc-client-executor-2042] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] Ack server push request, request = NotifySubscriberRequest, requestId = 20
16:32:56.338 [nacos-grpc-client-executor-2063] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] Receive server push request, request = NotifySubscriberRequest, requestId = 21
16:32:56.338 [nacos-grpc-client-executor-2063] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91eeb5ec-cc26-41cd-98bd-b9fca1099ab8] Ack server push request, request = NotifySubscriberRequest, requestId = 21
20:42:17.724 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:42:17.730 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:42:18.065 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:42:18.066 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6bb37ac[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:42:18.066 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753335727930_127.0.0.1_1501
20:42:18.068 [nacos-grpc-client-executor-5054] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753335727930_127.0.0.1_1501]Ignore complete event,isRunning:false,isAbandon=false
20:42:18.073 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@27a723a8[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5055]
