package com.heju.system.api.organize.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.api.organize.domain.po.SysPostPo;
import com.heju.system.api.organize.domain.query.SysPostQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 岗位 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysPostConverter extends BaseConverter<SysPostQuery, SysPostDto, SysPostPo> {
}
