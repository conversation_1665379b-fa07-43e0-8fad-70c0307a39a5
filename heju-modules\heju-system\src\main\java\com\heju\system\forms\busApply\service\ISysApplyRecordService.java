package com.heju.system.forms.busApply.service;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.system.forms.busApply.domain.query.SysApplyRecordQuery;
import com.heju.system.forms.busApply.domain.dto.SysApplyRecordDto;
import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.forms.busApply.domain.query.UniversalApplyQuery;

/**
 * 行政申请管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysApplyRecordService extends IBaseService<SysApplyRecordQuery, SysApplyRecordDto> {

    AjaxResult getSealApplyField(UniversalApplyQuery query);

    AjaxResult getSealApplyList(UniversalApplyQuery query);

    AjaxResult getAddField(UniversalApplyQuery query);

    AjaxResult getAddList(UniversalApplyQuery query);

    AjaxResult editApplyStatus(SysApplyRecordDto applyRecord);

    AjaxResult universalList(UniversalApplyQuery query);

    AjaxResult manageList(UniversalApplyQuery query);

    AjaxResult manageFieldList(UniversalApplyQuery query);
}
