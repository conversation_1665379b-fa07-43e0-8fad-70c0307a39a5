package com.heju.system.api.organize.domain.merge;

/**
 * 组织管理 关联命名常量
 *
 * <AUTHOR>
 */
public interface MergeGroup {

    /** 部门岗位关联 -》 部门 */
    String DEPT_SysPost_GROUP = "DEPT_SysPost_GROUP";

    /** 组织-角色关联（角色绑定） -》 部门 */
    String DEPT_OrganizeRoleMerge_GROUP = "DEPT_OrganizeRoleMerge_GROUP";

    /** 组织-角色关联（角色绑定） -》 岗位 */
    String POST_OrganizeRoleMerge_GROUP = "POST_OrganizeRoleMerge_GROUP";

    /** 组织-角色关联（角色绑定） -》 用户 */
    String USER_OrganizeRoleMerge_GROUP = "USER_OrganizeRoleMerge_GROUP";

    /** 角色-部门关联（权限范围） -》 部门 */
    String DEPT_SysRoleDeptMerge_GROUP = "DEPT_SysRoleDeptMerge_GROUP";

    /** 角色-岗位关联（权限范围） -》 岗位 */
    String POST_SysRolePostMerge_GROUP = "SYS_POST_SysRolePostMerge_GROUP";

    /** 用户岗位关联 -》 用户 */
    String USER_SysUserPostMerge_GROUP = "USER_SysUserPostMerge_GROUP";

    /** 用户岗位关联 -》 岗位 */
    String POST_SysUserPostMerge_GROUP = "POST_SysUserPostMerge_GROUP";

    /** 公司第三方关联 -》 公司 */
    String Company_SysCompanyThirdMerge_GROUP = "USER_SysUserPostMerge_GROUP";

    /** 公司第三方关联 -》 第三方 */
    String Third_SysCompanyThirdMerge_GROUP = "POST_SysUserPostMerge_GROUP";

    /** 第三方模块关联认证信息 -》 认证信息 */
    String Third_SysThirdAuth_GROUP = "Third_SysThirdAuth_GROUP";
}
