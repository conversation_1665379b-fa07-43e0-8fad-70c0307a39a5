package com.heju.system.phone.manager;

import com.heju.system.phone.domain.dto.SysPhoneNumberInfoDto;
import com.heju.system.phone.domain.query.SysPhoneNumberInfoQuery;
import com.heju.common.web.entity.manager.IBaseManager;

import java.util.List;

/**
 * 手机号管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysPhoneNumberInfoManager extends IBaseManager<SysPhoneNumberInfoQuery, SysPhoneNumberInfoDto> {

    /**
     * 查询数据对象列表
     *
     * @param query 数据查询对象
     * @return 数据对象集合
     */
    List<SysPhoneNumberInfoDto> selectList(SysPhoneNumberInfoQuery query);
}
