package com.heju.system.api.authority.feign;

import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.constant.basic.ServiceConstants;
import com.heju.common.core.web.result.R;
import com.heju.system.api.authority.feign.factory.RemoteLoginFallbackFactory;
import com.heju.system.api.model.LoginUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 登录服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteLoginService", value = ServiceConstants.SYSTEM_SERVICE, fallbackFactory = RemoteLoginFallbackFactory.class)
public interface RemoteLoginService {

    /**
     * 查询登录登录信息
     *
     * @param enterpriseName 企业账号
     * @param userName       员工账号
     * @param password       密码
     * @param source         请求来源
     * @return 结果
     */
    @GetMapping("/login/inner/loginInfo/{enterpriseName}/{userName}/{password}")
    R<LoginUser> getLoginInfoInner(@PathVariable("enterpriseName") String enterpriseName, @PathVariable("userName") String userName, @PathVariable("password") String password, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 查询登录登录信息
     *
     * @param enterpriseName 企业账号
     * @param telephone      手机号码
     * @param source         请求来源
     * @return 结果
     */
    @GetMapping("/login/inner/loginInfoByTelephone/{enterpriseName}/{telephone}/{userId}")
    R<LoginUser> loginInfoByTelephone(@PathVariable("enterpriseName") String enterpriseName, @PathVariable("telephone") String telephone, @PathVariable("userId") Long userId,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询登录登录信息
     *
     * @param enterpriseName 企业账号
     * @param unionId        微信公众号unionId
     * @param source         请求来源
     * @return 结果
     */
    @GetMapping("/login/inner/loginInfoByUnionId/{enterpriseName}/{unionId}")
    R<LoginUser> loginInfoByUnionId(@PathVariable("enterpriseName") String enterpriseName, @PathVariable("unionId") String unionId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
