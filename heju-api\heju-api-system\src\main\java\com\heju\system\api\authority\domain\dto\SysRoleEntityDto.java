package com.heju.system.api.authority.domain.dto;

import com.heju.system.api.authority.domain.po.SysRolePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysRoleEntityDto extends SysRolePo {
    @Serial
    private static final long serialVersionUID = 1L;
    private Long id;
    private Long roleId;
    private List<Long> entityFieldId;

}
