package com.heju.system.api.dict.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.api.dict.domain.dto.SysConfigDto;
import com.heju.system.api.dict.domain.po.SysConfigPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:51+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysConfigConverterImpl implements SysConfigConverter {

    @Override
    public SysConfigDto mapperDto(SysConfigPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysConfigDto sysConfigDto = new SysConfigDto();

        sysConfigDto.setId( arg0.getId() );
        sysConfigDto.setSourceName( arg0.getSourceName() );
        sysConfigDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysConfigDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysConfigDto.setName( arg0.getName() );
        sysConfigDto.setStatus( arg0.getStatus() );
        sysConfigDto.setSort( arg0.getSort() );
        sysConfigDto.setRemark( arg0.getRemark() );
        sysConfigDto.setCreateBy( arg0.getCreateBy() );
        sysConfigDto.setCreateTime( arg0.getCreateTime() );
        sysConfigDto.setUpdateBy( arg0.getUpdateBy() );
        sysConfigDto.setUpdateTime( arg0.getUpdateTime() );
        sysConfigDto.setDelFlag( arg0.getDelFlag() );
        sysConfigDto.setCreateName( arg0.getCreateName() );
        sysConfigDto.setUpdateName( arg0.getUpdateName() );
        sysConfigDto.setCode( arg0.getCode() );
        sysConfigDto.setValue( arg0.getValue() );
        sysConfigDto.setType( arg0.getType() );

        return sysConfigDto;
    }

    @Override
    public List<SysConfigDto> mapperDto(Collection<SysConfigPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysConfigDto> list = new ArrayList<SysConfigDto>( arg0.size() );
        for ( SysConfigPo sysConfigPo : arg0 ) {
            list.add( mapperDto( sysConfigPo ) );
        }

        return list;
    }

    @Override
    public Page<SysConfigDto> mapperPageDto(Collection<SysConfigPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysConfigDto> page = new Page<SysConfigDto>();
        for ( SysConfigPo sysConfigPo : arg0 ) {
            page.add( mapperDto( sysConfigPo ) );
        }

        return page;
    }

    @Override
    public SysConfigPo mapperPo(SysConfigDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysConfigPo sysConfigPo = new SysConfigPo();

        sysConfigPo.setId( arg0.getId() );
        sysConfigPo.setSourceName( arg0.getSourceName() );
        sysConfigPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysConfigPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysConfigPo.setName( arg0.getName() );
        sysConfigPo.setStatus( arg0.getStatus() );
        sysConfigPo.setSort( arg0.getSort() );
        sysConfigPo.setRemark( arg0.getRemark() );
        sysConfigPo.setCreateBy( arg0.getCreateBy() );
        sysConfigPo.setCreateTime( arg0.getCreateTime() );
        sysConfigPo.setUpdateBy( arg0.getUpdateBy() );
        sysConfigPo.setUpdateTime( arg0.getUpdateTime() );
        sysConfigPo.setDelFlag( arg0.getDelFlag() );
        sysConfigPo.setCreateName( arg0.getCreateName() );
        sysConfigPo.setUpdateName( arg0.getUpdateName() );
        sysConfigPo.setCode( arg0.getCode() );
        sysConfigPo.setValue( arg0.getValue() );
        sysConfigPo.setType( arg0.getType() );

        return sysConfigPo;
    }

    @Override
    public List<SysConfigPo> mapperPo(Collection<SysConfigDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysConfigPo> list = new ArrayList<SysConfigPo>( arg0.size() );
        for ( SysConfigDto sysConfigDto : arg0 ) {
            list.add( mapperPo( sysConfigDto ) );
        }

        return list;
    }

    @Override
    public Page<SysConfigPo> mapperPagePo(Collection<SysConfigDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysConfigPo> page = new Page<SysConfigPo>();
        for ( SysConfigDto sysConfigDto : arg0 ) {
            page.add( mapperPo( sysConfigDto ) );
        }

        return page;
    }
}
