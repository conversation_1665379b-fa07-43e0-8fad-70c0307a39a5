package com.heju.system.api.organize.domain.dto;

import com.heju.common.core.constant.system.AuthorityConstants;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.system.api.organize.domain.po.SysEnterprisePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 企业 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysEnterpriseDto extends SysEnterprisePo {

    @Serial
    private static final long serialVersionUID = 1L;

    public boolean isAdmin() {
        return isAdmin(getIsLessor());
    }

    public static boolean isAdmin(String isLessor) {
        return StrUtil.equals(AuthorityConstants.TenantType.ADMIN.getCode(), isLessor);
    }

}
