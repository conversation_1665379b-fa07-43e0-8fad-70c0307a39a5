package com.heju.tenant.api.source.domain.model;

import com.github.pagehelper.Page;
import com.heju.tenant.api.source.domain.dto.TeSourceDto;
import com.heju.tenant.api.source.domain.po.TeSourcePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:53+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class TeSourceConverterImpl implements TeSourceConverter {

    @Override
    public TeSourceDto mapperDto(TeSourcePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        TeSourceDto teSourceDto = new TeSourceDto();

        teSourceDto.setId( arg0.getId() );
        teSourceDto.setSourceName( arg0.getSourceName() );
        teSourceDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            teSourceDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        teSourceDto.setName( arg0.getName() );
        teSourceDto.setStatus( arg0.getStatus() );
        teSourceDto.setSort( arg0.getSort() );
        teSourceDto.setRemark( arg0.getRemark() );
        teSourceDto.setCreateBy( arg0.getCreateBy() );
        teSourceDto.setCreateTime( arg0.getCreateTime() );
        teSourceDto.setUpdateBy( arg0.getUpdateBy() );
        teSourceDto.setUpdateTime( arg0.getUpdateTime() );
        teSourceDto.setDelFlag( arg0.getDelFlag() );
        teSourceDto.setCreateName( arg0.getCreateName() );
        teSourceDto.setUpdateName( arg0.getUpdateName() );
        teSourceDto.setSlave( arg0.getSlave() );
        teSourceDto.setDriverClassName( arg0.getDriverClassName() );
        teSourceDto.setUrlPrepend( arg0.getUrlPrepend() );
        teSourceDto.setUrlAppend( arg0.getUrlAppend() );
        teSourceDto.setUserName( arg0.getUserName() );
        teSourceDto.setPassword( arg0.getPassword() );
        teSourceDto.setIsDefault( arg0.getIsDefault() );

        return teSourceDto;
    }

    @Override
    public List<TeSourceDto> mapperDto(Collection<TeSourcePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<TeSourceDto> list = new ArrayList<TeSourceDto>( arg0.size() );
        for ( TeSourcePo teSourcePo : arg0 ) {
            list.add( mapperDto( teSourcePo ) );
        }

        return list;
    }

    @Override
    public Page<TeSourceDto> mapperPageDto(Collection<TeSourcePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<TeSourceDto> page = new Page<TeSourceDto>();
        for ( TeSourcePo teSourcePo : arg0 ) {
            page.add( mapperDto( teSourcePo ) );
        }

        return page;
    }

    @Override
    public TeSourcePo mapperPo(TeSourceDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        TeSourcePo teSourcePo = new TeSourcePo();

        teSourcePo.setId( arg0.getId() );
        teSourcePo.setSourceName( arg0.getSourceName() );
        teSourcePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            teSourcePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        teSourcePo.setName( arg0.getName() );
        teSourcePo.setStatus( arg0.getStatus() );
        teSourcePo.setSort( arg0.getSort() );
        teSourcePo.setRemark( arg0.getRemark() );
        teSourcePo.setCreateBy( arg0.getCreateBy() );
        teSourcePo.setCreateTime( arg0.getCreateTime() );
        teSourcePo.setUpdateBy( arg0.getUpdateBy() );
        teSourcePo.setUpdateTime( arg0.getUpdateTime() );
        teSourcePo.setDelFlag( arg0.getDelFlag() );
        teSourcePo.setCreateName( arg0.getCreateName() );
        teSourcePo.setUpdateName( arg0.getUpdateName() );
        teSourcePo.setSlave( arg0.getSlave() );
        teSourcePo.setDriverClassName( arg0.getDriverClassName() );
        teSourcePo.setUrlPrepend( arg0.getUrlPrepend() );
        teSourcePo.setUrlAppend( arg0.getUrlAppend() );
        teSourcePo.setUserName( arg0.getUserName() );
        teSourcePo.setPassword( arg0.getPassword() );
        teSourcePo.setIsDefault( arg0.getIsDefault() );

        return teSourcePo;
    }

    @Override
    public List<TeSourcePo> mapperPo(Collection<TeSourceDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<TeSourcePo> list = new ArrayList<TeSourcePo>( arg0.size() );
        for ( TeSourceDto teSourceDto : arg0 ) {
            list.add( mapperPo( teSourceDto ) );
        }

        return list;
    }

    @Override
    public Page<TeSourcePo> mapperPagePo(Collection<TeSourceDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<TeSourcePo> page = new Page<TeSourcePo>();
        for ( TeSourceDto teSourceDto : arg0 ) {
            page.add( mapperPo( teSourceDto ) );
        }

        return page;
    }
}
