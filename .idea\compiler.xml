<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
      </profile>
      <profile name="Annotation profile for heju" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../../Java-repo/repository/org/mapstruct/mapstruct-processor/1.5.3.Final/mapstruct-processor-1.5.3.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../Java-repo/repository/org/mapstruct/mapstruct/1.5.3.Final/mapstruct-1.5.3.Final.jar" />
          <entry name="$PROJECT_DIR$/../../../Java-repo/repository/org/projectlombok/lombok/1.18.24/lombok-1.18.24.jar" />
          <entry name="$PROJECT_DIR$/../../../Java-repo/repository/org/projectlombok/lombok-mapstruct-binding/0.2.0/lombok-mapstruct-binding-0.2.0.jar" />
        </processorPath>
        <module name="heju-modules-system" />
        <module name="heju-modules-gen" />
        <module name="heju-common-swagger" />
        <module name="heju-modules-tenant" />
        <module name="heju-api-job" />
        <module name="heju-common-web" />
        <module name="heju-common-cache" />
        <module name="heju-api-file" />
        <module name="heju-visual-monitor" />
        <module name="heju-auth" />
        <module name="heju-modules-file" />
        <module name="heju-common-datasource" />
        <module name="heju-common-seata" />
        <module name="heju-common-flowable" />
        <module name="heju-common-security" />
        <module name="heju-modules-job" />
        <module name="heju-api-tenant" />
        <module name="heju-common-log" />
        <module name="heju-modules-flowable" />
        <module name="heju-common-datascope" />
        <module name="heju-api-system" />
        <module name="heju-gateway" />
        <module name="heju-common-redis" />
        <module name="heju-common-core" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="heju-kkfileview" target="17" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="heju-kkfileview" options="" />
    </option>
  </component>
</project>