package com.heju.system.authority.manager.impl;

import com.heju.system.authority.domain.po.SysDisplayInfoPo;
import com.heju.system.authority.domain.dto.SysDisplayInfoDto;
import com.heju.system.authority.domain.query.SysDisplayInfoQuery;
import com.heju.system.authority.domain.model.SysDisplayInfoConverter;
import com.heju.system.authority.mapper.SysDisplayInfoMapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.authority.manager.ISysDisplayInfoManager;
import org.springframework.stereotype.Component;

/**
 * 显隐列管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysDisplayInfoManager extends BaseManagerImpl<SysDisplayInfoQuery, SysDisplayInfoDto, SysDisplayInfoPo, SysDisplayInfoMapper, SysDisplayInfoConverter> implements ISysDisplayInfoManager {
}
