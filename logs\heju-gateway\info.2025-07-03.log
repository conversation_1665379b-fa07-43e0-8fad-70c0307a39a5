09:41:15.680 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:41:16.710 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2c1f9944-4b40-4474-af25-85dd7124bc23_config-0
09:41:16.802 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 42 ms to scan 1 urls, producing 3 keys and 6 values 
09:41:16.837 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:41:16.837 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 3 keys and 10 values 
09:41:16.862 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:41:16.881 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:41:16.891 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:41:16.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c1f9944-4b40-4474-af25-85dd7124bc23_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:41:16.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c1f9944-4b40-4474-af25-85dd7124bc23_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001a2283cc958
09:41:16.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c1f9944-4b40-4474-af25-85dd7124bc23_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001a2283ccb78
09:41:16.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c1f9944-4b40-4474-af25-85dd7124bc23_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:41:16.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c1f9944-4b40-4474-af25-85dd7124bc23_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:41:16.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c1f9944-4b40-4474-af25-85dd7124bc23_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:41:18.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c1f9944-4b40-4474-af25-85dd7124bc23_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751506878286_127.0.0.1_1082
09:41:18.554 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c1f9944-4b40-4474-af25-85dd7124bc23_config-0] Notify connected event to listeners.
09:41:18.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c1f9944-4b40-4474-af25-85dd7124bc23_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:41:18.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c1f9944-4b40-4474-af25-85dd7124bc23_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001a2285069a8
09:41:18.761 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:41:23.653 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:41:25.234 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:41:26.185 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0f67dcfe-7e54-4ee6-af17-4645b697418b_config-0
09:41:26.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f67dcfe-7e54-4ee6-af17-4645b697418b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:41:26.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f67dcfe-7e54-4ee6-af17-4645b697418b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001a2283cc958
09:41:26.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f67dcfe-7e54-4ee6-af17-4645b697418b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001a2283ccb78
09:41:26.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f67dcfe-7e54-4ee6-af17-4645b697418b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:41:26.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f67dcfe-7e54-4ee6-af17-4645b697418b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:41:26.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f67dcfe-7e54-4ee6-af17-4645b697418b_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:41:26.321 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f67dcfe-7e54-4ee6-af17-4645b697418b_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751506886201_127.0.0.1_1153
09:41:26.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f67dcfe-7e54-4ee6-af17-4645b697418b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:41:26.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f67dcfe-7e54-4ee6-af17-4645b697418b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001a2285069a8
09:41:26.322 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f67dcfe-7e54-4ee6-af17-4645b697418b_config-0] Notify connected event to listeners.
09:41:26.490 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of aaf9c183-f480-4b7a-8b5b-c0aa53e126e7
09:41:26.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] RpcClient init label, labels = {module=naming, source=sdk}
09:41:26.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:41:26.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:41:26.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:41:26.496 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:41:26.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751506886511_127.0.0.1_1166
09:41:26.633 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Notify connected event to listeners.
09:41:26.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:41:26.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001a2285069a8
09:41:27.239 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:41:27.240 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:41:27.323 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:41:27.324 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:41:27.334 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:41:27.337 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:41:27.349 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:41:27.349 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:41:27.366 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:41:27.370 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:41:27.382 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:41:27.383 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:41:27.391 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
09:41:27.463 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 12.569 seconds (JVM running for 14.092)
09:41:27.482 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:41:27.483 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:41:27.484 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:41:27.979 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:41:27.986 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:55:01.103 [nacos-grpc-client-executor-338] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Receive server push request, request = NotifySubscriberRequest, requestId = 15
09:55:01.128 [nacos-grpc-client-executor-338] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaf9c183-f480-4b7a-8b5b-c0aa53e126e7] Ack server push request, request = NotifySubscriberRequest, requestId = 15
09:55:05.771 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:55:05.775 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:55:06.110 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:55:06.110 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@35c263ae[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:55:06.110 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751506886511_127.0.0.1_1166
09:55:06.113 [nacos-grpc-client-executor-341] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751506886511_127.0.0.1_1166]Ignore complete event,isRunning:false,isAbandon=false
09:55:06.116 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@33eed5b9[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 342]
09:55:11.330 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:55:12.132 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of df8f8b09-d6b1-4e96-b4c3-415c1c9f6746_config-0
09:55:12.218 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
09:55:12.248 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:55:12.261 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:55:12.274 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:55:12.289 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
09:55:12.301 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:55:12.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df8f8b09-d6b1-4e96-b4c3-415c1c9f6746_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:55:12.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df8f8b09-d6b1-4e96-b4c3-415c1c9f6746_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000157973cc2b8
09:55:12.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df8f8b09-d6b1-4e96-b4c3-415c1c9f6746_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000157973cc4d8
09:55:12.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df8f8b09-d6b1-4e96-b4c3-415c1c9f6746_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:55:12.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df8f8b09-d6b1-4e96-b4c3-415c1c9f6746_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:55:12.319 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df8f8b09-d6b1-4e96-b4c3-415c1c9f6746_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:55:13.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df8f8b09-d6b1-4e96-b4c3-415c1c9f6746_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751507713307_127.0.0.1_5544
09:55:13.569 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df8f8b09-d6b1-4e96-b4c3-415c1c9f6746_config-0] Notify connected event to listeners.
09:55:13.571 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df8f8b09-d6b1-4e96-b4c3-415c1c9f6746_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:55:13.572 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df8f8b09-d6b1-4e96-b4c3-415c1c9f6746_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000015797506370
09:55:13.792 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:55:18.629 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:55:20.118 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:55:20.867 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 83181f38-6d0e-402d-a3cf-cb799e817613_config-0
09:55:20.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83181f38-6d0e-402d-a3cf-cb799e817613_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:55:20.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83181f38-6d0e-402d-a3cf-cb799e817613_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000157973cc2b8
09:55:20.869 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83181f38-6d0e-402d-a3cf-cb799e817613_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000157973cc4d8
09:55:20.869 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83181f38-6d0e-402d-a3cf-cb799e817613_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:55:20.869 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83181f38-6d0e-402d-a3cf-cb799e817613_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:55:20.870 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83181f38-6d0e-402d-a3cf-cb799e817613_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:55:20.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83181f38-6d0e-402d-a3cf-cb799e817613_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751507720884_127.0.0.1_5569
09:55:20.999 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83181f38-6d0e-402d-a3cf-cb799e817613_config-0] Notify connected event to listeners.
09:55:20.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83181f38-6d0e-402d-a3cf-cb799e817613_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:55:20.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83181f38-6d0e-402d-a3cf-cb799e817613_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000015797506370
09:55:21.161 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 78f6a70a-1799-41e3-9905-07fa5b627aad
09:55:21.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] RpcClient init label, labels = {module=naming, source=sdk}
09:55:21.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:55:21.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:55:21.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:55:21.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:55:21.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751507721180_127.0.0.1_5570
09:55:21.309 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Notify connected event to listeners.
09:55:21.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:55:21.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000015797506370
09:55:21.872 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Receive server push request, request = NotifySubscriberRequest, requestId = 17
09:55:21.873 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Ack server push request, request = NotifySubscriberRequest, requestId = 17
09:55:22.044 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Receive server push request, request = NotifySubscriberRequest, requestId = 21
09:55:22.045 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Ack server push request, request = NotifySubscriberRequest, requestId = 21
09:55:22.055 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Receive server push request, request = NotifySubscriberRequest, requestId = 18
09:55:22.055 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Ack server push request, request = NotifySubscriberRequest, requestId = 18
09:55:22.063 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Receive server push request, request = NotifySubscriberRequest, requestId = 19
09:55:22.063 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Ack server push request, request = NotifySubscriberRequest, requestId = 19
09:55:22.074 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Receive server push request, request = NotifySubscriberRequest, requestId = 20
09:55:22.077 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Ack server push request, request = NotifySubscriberRequest, requestId = 20
09:55:22.235 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
09:55:22.275 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 11.794 seconds (JVM running for 13.17)
09:55:22.284 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:55:22.286 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:55:22.287 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:55:22.807 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Receive server push request, request = NotifySubscriberRequest, requestId = 22
09:55:22.830 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Ack server push request, request = NotifySubscriberRequest, requestId = 22
09:55:32.018 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Receive server push request, request = NotifySubscriberRequest, requestId = 23
09:55:32.045 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78f6a70a-1799-41e3-9905-07fa5b627aad] Ack server push request, request = NotifySubscriberRequest, requestId = 23
09:56:25.238 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:56:25.243 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:56:25.580 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:56:25.581 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5133243a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:56:25.581 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751507721180_127.0.0.1_5570
09:56:25.586 [nacos-grpc-client-executor-73] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751507721180_127.0.0.1_5570]Ignore complete event,isRunning:false,isAbandon=false
09:56:25.590 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7ea867e7[Running, pool size = 12, active threads = 0, queued tasks = 0, completed tasks = 74]
09:56:31.958 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:56:32.680 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ea2c3241-d403-439b-b083-88fee38f51ad_config-0
09:56:32.775 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
09:56:32.810 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:56:32.821 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:56:32.834 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:56:32.851 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
09:56:32.864 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:56:32.870 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea2c3241-d403-439b-b083-88fee38f51ad_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:56:32.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea2c3241-d403-439b-b083-88fee38f51ad_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001b9043cb8c8
09:56:32.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea2c3241-d403-439b-b083-88fee38f51ad_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001b9043cbae8
09:56:32.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea2c3241-d403-439b-b083-88fee38f51ad_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:56:32.874 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea2c3241-d403-439b-b083-88fee38f51ad_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:56:32.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea2c3241-d403-439b-b083-88fee38f51ad_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:56:34.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea2c3241-d403-439b-b083-88fee38f51ad_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751507793949_127.0.0.1_6000
09:56:34.202 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea2c3241-d403-439b-b083-88fee38f51ad_config-0] Notify connected event to listeners.
09:56:34.202 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea2c3241-d403-439b-b083-88fee38f51ad_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:56:34.204 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ea2c3241-d403-439b-b083-88fee38f51ad_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001b904505920
09:56:34.386 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:56:39.135 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:56:40.531 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:56:41.159 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1f9ac86d-3961-4e3d-94ca-8db4bb28cd34_config-0
09:56:41.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f9ac86d-3961-4e3d-94ca-8db4bb28cd34_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:56:41.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f9ac86d-3961-4e3d-94ca-8db4bb28cd34_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001b9043cb8c8
09:56:41.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f9ac86d-3961-4e3d-94ca-8db4bb28cd34_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001b9043cbae8
09:56:41.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f9ac86d-3961-4e3d-94ca-8db4bb28cd34_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:56:41.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f9ac86d-3961-4e3d-94ca-8db4bb28cd34_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:56:41.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f9ac86d-3961-4e3d-94ca-8db4bb28cd34_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:56:41.288 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f9ac86d-3961-4e3d-94ca-8db4bb28cd34_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751507801174_127.0.0.1_6047
09:56:41.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f9ac86d-3961-4e3d-94ca-8db4bb28cd34_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:56:41.289 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f9ac86d-3961-4e3d-94ca-8db4bb28cd34_config-0] Notify connected event to listeners.
09:56:41.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f9ac86d-3961-4e3d-94ca-8db4bb28cd34_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001b904505920
09:56:41.428 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9611b8e2-8d62-40af-800d-b86e618920da
09:56:41.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] RpcClient init label, labels = {module=naming, source=sdk}
09:56:41.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:56:41.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:56:41.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:56:41.449 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:56:41.583 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751507801461_127.0.0.1_6048
09:56:41.584 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Notify connected event to listeners.
09:56:41.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:56:41.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001b904505920
09:56:42.137 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Receive server push request, request = NotifySubscriberRequest, requestId = 26
09:56:42.139 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Ack server push request, request = NotifySubscriberRequest, requestId = 26
09:56:42.212 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
09:56:42.265 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 11.118 seconds (JVM running for 12.517)
09:56:42.275 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:56:42.277 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:56:42.281 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:56:42.329 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Receive server push request, request = NotifySubscriberRequest, requestId = 27
09:56:42.330 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Ack server push request, request = NotifySubscriberRequest, requestId = 27
09:56:42.342 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Receive server push request, request = NotifySubscriberRequest, requestId = 29
09:56:42.343 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Ack server push request, request = NotifySubscriberRequest, requestId = 29
09:56:42.354 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Receive server push request, request = NotifySubscriberRequest, requestId = 30
09:56:42.354 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Ack server push request, request = NotifySubscriberRequest, requestId = 30
09:56:42.367 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Receive server push request, request = NotifySubscriberRequest, requestId = 28
09:56:42.371 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Ack server push request, request = NotifySubscriberRequest, requestId = 28
09:56:42.764 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Receive server push request, request = NotifySubscriberRequest, requestId = 31
09:56:42.788 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9611b8e2-8d62-40af-800d-b86e618920da] Ack server push request, request = NotifySubscriberRequest, requestId = 31
10:01:37.688 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:01:37.693 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:01:38.045 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:01:38.045 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6fce0588[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:01:38.045 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751507801461_127.0.0.1_6048
10:01:38.049 [nacos-grpc-client-executor-143] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751507801461_127.0.0.1_6048]Ignore complete event,isRunning:false,isAbandon=false
10:01:38.051 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@591f9698[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 144]
10:01:44.556 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:01:45.364 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b2c5afb3-beaf-4bff-928d-552d04857b39_config-0
10:01:45.461 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
10:01:45.500 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
10:01:45.511 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
10:01:45.524 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
10:01:45.540 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
10:01:45.556 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
10:01:45.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2c5afb3-beaf-4bff-928d-552d04857b39_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:01:45.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2c5afb3-beaf-4bff-928d-552d04857b39_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000170b73cbda8
10:01:45.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2c5afb3-beaf-4bff-928d-552d04857b39_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000170b73cbfc8
10:01:45.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2c5afb3-beaf-4bff-928d-552d04857b39_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:01:45.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2c5afb3-beaf-4bff-928d-552d04857b39_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:01:45.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2c5afb3-beaf-4bff-928d-552d04857b39_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:01:47.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2c5afb3-beaf-4bff-928d-552d04857b39_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751508106823_127.0.0.1_6866
10:01:47.103 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2c5afb3-beaf-4bff-928d-552d04857b39_config-0] Notify connected event to listeners.
10:01:47.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2c5afb3-beaf-4bff-928d-552d04857b39_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:01:47.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2c5afb3-beaf-4bff-928d-552d04857b39_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000170b7505f18
10:01:47.298 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:01:51.818 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:01:53.342 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:01:54.022 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 86784ebf-8cda-49a7-8a6a-6300528569b9_config-0
10:01:54.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86784ebf-8cda-49a7-8a6a-6300528569b9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:01:54.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86784ebf-8cda-49a7-8a6a-6300528569b9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000170b73cbda8
10:01:54.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86784ebf-8cda-49a7-8a6a-6300528569b9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000170b73cbfc8
10:01:54.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86784ebf-8cda-49a7-8a6a-6300528569b9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:01:54.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86784ebf-8cda-49a7-8a6a-6300528569b9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:01:54.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86784ebf-8cda-49a7-8a6a-6300528569b9_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:01:54.154 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86784ebf-8cda-49a7-8a6a-6300528569b9_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751508114040_127.0.0.1_6918
10:01:54.155 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86784ebf-8cda-49a7-8a6a-6300528569b9_config-0] Notify connected event to listeners.
10:01:54.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86784ebf-8cda-49a7-8a6a-6300528569b9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:01:54.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86784ebf-8cda-49a7-8a6a-6300528569b9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000170b7505f18
10:01:54.306 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 49b70ff2-be9b-4c93-bef3-ba017e883cd5
10:01:54.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] RpcClient init label, labels = {module=naming, source=sdk}
10:01:54.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:01:54.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:01:54.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:01:54.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:01:54.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751508114325_127.0.0.1_6919
10:01:54.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:01:54.444 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Notify connected event to listeners.
10:01:54.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000170b7505f18
10:01:55.092 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 32
10:01:55.093 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 32
10:01:55.111 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
10:01:55.172 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 11.46 seconds (JVM running for 12.853)
10:01:55.179 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 33
10:01:55.179 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 33
10:01:55.183 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
10:01:55.183 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
10:01:55.185 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
10:01:55.195 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 35
10:01:55.195 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 35
10:01:55.200 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 36
10:01:55.200 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 36
10:01:55.207 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 34
10:01:55.218 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 34
10:01:55.612 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 37
10:01:55.644 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 37
10:03:26.427 [nacos-grpc-client-executor-76] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 38
10:03:26.429 [nacos-grpc-client-executor-76] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 38
10:03:58.645 [nacos-grpc-client-executor-93] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 40
10:03:58.685 [nacos-grpc-client-executor-93] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 40
10:12:59.180 [nacos-grpc-client-executor-266] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 43
10:12:59.206 [nacos-grpc-client-executor-266] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 43
10:13:31.727 [nacos-grpc-client-executor-274] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 47
10:13:31.749 [nacos-grpc-client-executor-274] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 47
10:15:15.109 [nacos-grpc-client-executor-306] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 56
10:15:15.133 [nacos-grpc-client-executor-306] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 56
10:15:55.421 [nacos-grpc-client-executor-317] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 59
10:15:55.446 [nacos-grpc-client-executor-317] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 59
10:18:41.307 [nacos-grpc-client-executor-369] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 62
10:18:41.329 [nacos-grpc-client-executor-369] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 62
10:19:06.489 [nacos-grpc-client-executor-380] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 64
10:19:06.518 [nacos-grpc-client-executor-380] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 64
10:23:41.429 [nacos-grpc-client-executor-464] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 66
10:23:41.463 [nacos-grpc-client-executor-464] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 66
10:23:44.271 [nacos-grpc-client-executor-465] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 67
10:23:44.299 [nacos-grpc-client-executor-465] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 67
10:33:00.422 [nacos-grpc-client-executor-652] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 70
10:33:00.445 [nacos-grpc-client-executor-652] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 70
10:33:37.587 [nacos-grpc-client-executor-660] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 74
10:33:37.612 [nacos-grpc-client-executor-660] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 74
10:40:02.476 [nacos-grpc-client-executor-794] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 77
10:40:02.503 [nacos-grpc-client-executor-794] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 77
10:40:33.292 [nacos-grpc-client-executor-802] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 80
10:40:33.327 [nacos-grpc-client-executor-802] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 80
10:42:59.138 [nacos-grpc-client-executor-857] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 84
10:42:59.199 [nacos-grpc-client-executor-857] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 84
10:43:19.249 [nacos-grpc-client-executor-861] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 87
10:43:19.268 [nacos-grpc-client-executor-861] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 87
11:23:38.607 [nacos-grpc-client-executor-1658] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 91
11:23:38.633 [nacos-grpc-client-executor-1658] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 91
11:24:07.561 [nacos-grpc-client-executor-1670] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 94
11:24:07.581 [nacos-grpc-client-executor-1670] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 94
11:35:28.536 [nacos-grpc-client-executor-1883] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 98
11:35:28.537 [nacos-grpc-client-executor-1883] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 98
11:37:41.204 [nacos-grpc-client-executor-1929] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 99
11:37:41.440 [nacos-grpc-client-executor-1929] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 99
11:45:55.841 [nacos-grpc-client-executor-2091] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 100
11:45:55.870 [nacos-grpc-client-executor-2091] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 100
11:46:44.352 [nacos-grpc-client-executor-2110] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 102
11:46:44.376 [nacos-grpc-client-executor-2110] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 102
12:00:23.751 [nacos-grpc-client-executor-2384] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 105
12:00:23.774 [nacos-grpc-client-executor-2384] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 105
12:01:00.137 [nacos-grpc-client-executor-2400] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 107
12:01:00.163 [nacos-grpc-client-executor-2400] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 107
12:06:35.627 [nacos-grpc-client-executor-2511] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 110
12:06:35.862 [nacos-grpc-client-executor-2511] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 110
12:09:23.145 [nacos-grpc-client-executor-2566] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 115
12:09:23.197 [nacos-grpc-client-executor-2566] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 115
12:09:49.975 [nacos-grpc-client-executor-2574] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 116
12:09:50.068 [nacos-grpc-client-executor-2574] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 116
12:10:04.403 [nacos-grpc-client-executor-2583] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 118
12:10:04.417 [nacos-grpc-client-executor-2583] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 118
12:10:29.784 [nacos-grpc-client-executor-2590] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 119
12:10:29.818 [nacos-grpc-client-executor-2590] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 119
12:11:33.101 [nacos-grpc-client-executor-2612] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 121
12:11:33.127 [nacos-grpc-client-executor-2612] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 121
12:12:03.708 [nacos-grpc-client-executor-2624] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 122
12:12:03.743 [nacos-grpc-client-executor-2624] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 122
12:15:51.444 [nacos-grpc-client-executor-2695] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 125
12:15:51.463 [nacos-grpc-client-executor-2695] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 125
12:16:23.187 [nacos-grpc-client-executor-2708] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 132
12:16:23.194 [nacos-grpc-client-executor-2708] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 132
12:25:09.753 [nacos-grpc-client-executor-2878] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 138
12:25:09.782 [nacos-grpc-client-executor-2878] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 138
12:25:41.657 [nacos-grpc-client-executor-2887] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Receive server push request, request = NotifySubscriberRequest, requestId = 142
12:25:41.682 [nacos-grpc-client-executor-2887] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49b70ff2-be9b-4c93-bef3-ba017e883cd5] Ack server push request, request = NotifySubscriberRequest, requestId = 142
12:32:13.872 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:32:13.882 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:32:14.222 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:32:14.223 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@737d3881[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:32:14.223 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751508114325_127.0.0.1_6919
12:32:14.234 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1bd18556[Running, pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3011]
12:32:14.257 [nacos-grpc-client-executor-3011] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751508114325_127.0.0.1_6919]Ignore complete event,isRunning:false,isAbandon=false
12:33:00.330 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:33:01.039 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9d5a0d16-9714-4925-901a-d75d17f74175_config-0
12:33:01.125 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
12:33:01.156 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
12:33:01.166 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
12:33:01.178 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
12:33:01.189 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
12:33:01.205 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
12:33:01.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d5a0d16-9714-4925-901a-d75d17f74175_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:33:01.210 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d5a0d16-9714-4925-901a-d75d17f74175_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000025b013ccfb8
12:33:01.210 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d5a0d16-9714-4925-901a-d75d17f74175_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000025b013cd1d8
12:33:01.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d5a0d16-9714-4925-901a-d75d17f74175_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:33:01.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d5a0d16-9714-4925-901a-d75d17f74175_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:33:01.223 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d5a0d16-9714-4925-901a-d75d17f74175_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:33:02.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d5a0d16-9714-4925-901a-d75d17f74175_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751517182266_127.0.0.1_11113
12:33:02.522 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d5a0d16-9714-4925-901a-d75d17f74175_config-0] Notify connected event to listeners.
12:33:02.522 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d5a0d16-9714-4925-901a-d75d17f74175_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:33:02.523 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d5a0d16-9714-4925-901a-d75d17f74175_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000025b01507b88
12:33:02.679 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:33:07.834 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
12:33:09.377 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
12:33:10.141 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7625ba3e-4bb2-4d94-bd3e-fa746f7f31cb_config-0
12:33:10.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7625ba3e-4bb2-4d94-bd3e-fa746f7f31cb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:33:10.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7625ba3e-4bb2-4d94-bd3e-fa746f7f31cb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000025b013ccfb8
12:33:10.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7625ba3e-4bb2-4d94-bd3e-fa746f7f31cb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000025b013cd1d8
12:33:10.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7625ba3e-4bb2-4d94-bd3e-fa746f7f31cb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:33:10.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7625ba3e-4bb2-4d94-bd3e-fa746f7f31cb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:33:10.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7625ba3e-4bb2-4d94-bd3e-fa746f7f31cb_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:33:10.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7625ba3e-4bb2-4d94-bd3e-fa746f7f31cb_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751517190158_127.0.0.1_11205
12:33:10.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7625ba3e-4bb2-4d94-bd3e-fa746f7f31cb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:33:10.273 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7625ba3e-4bb2-4d94-bd3e-fa746f7f31cb_config-0] Notify connected event to listeners.
12:33:10.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7625ba3e-4bb2-4d94-bd3e-fa746f7f31cb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000025b01507b88
12:33:10.424 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 23673fea-663c-422f-a843-83f619a8973f
12:33:10.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] RpcClient init label, labels = {module=naming, source=sdk}
12:33:10.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:33:10.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:33:10.430 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:33:10.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:33:10.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751517190443_127.0.0.1_11207
12:33:10.568 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Notify connected event to listeners.
12:33:10.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:33:10.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000025b01507b88
12:33:11.182 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Receive server push request, request = NotifySubscriberRequest, requestId = 149
12:33:11.183 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Ack server push request, request = NotifySubscriberRequest, requestId = 149
12:33:11.272 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Receive server push request, request = NotifySubscriberRequest, requestId = 153
12:33:11.272 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Ack server push request, request = NotifySubscriberRequest, requestId = 153
12:33:11.282 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Receive server push request, request = NotifySubscriberRequest, requestId = 151
12:33:11.285 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Ack server push request, request = NotifySubscriberRequest, requestId = 151
12:33:11.293 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Receive server push request, request = NotifySubscriberRequest, requestId = 150
12:33:11.294 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Ack server push request, request = NotifySubscriberRequest, requestId = 150
12:33:11.299 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
12:33:11.303 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Receive server push request, request = NotifySubscriberRequest, requestId = 154
12:33:11.303 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Ack server push request, request = NotifySubscriberRequest, requestId = 154
12:33:11.312 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Receive server push request, request = NotifySubscriberRequest, requestId = 152
12:33:11.312 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Ack server push request, request = NotifySubscriberRequest, requestId = 152
12:33:11.342 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 11.837 seconds (JVM running for 13.166)
12:33:11.365 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
12:33:11.366 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
12:33:11.367 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
12:33:11.815 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Receive server push request, request = NotifySubscriberRequest, requestId = 155
12:33:11.834 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Ack server push request, request = NotifySubscriberRequest, requestId = 155
12:33:14.974 [nacos-grpc-client-executor-65] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Receive server push request, request = NotifySubscriberRequest, requestId = 157
12:33:15.001 [nacos-grpc-client-executor-65] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Ack server push request, request = NotifySubscriberRequest, requestId = 157
12:33:22.603 [nacos-grpc-client-executor-70] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Receive server push request, request = NotifySubscriberRequest, requestId = 159
12:33:22.620 [nacos-grpc-client-executor-70] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Ack server push request, request = NotifySubscriberRequest, requestId = 159
12:33:30.554 [nacos-grpc-client-executor-74] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Receive server push request, request = NotifySubscriberRequest, requestId = 160
12:33:30.585 [nacos-grpc-client-executor-74] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Ack server push request, request = NotifySubscriberRequest, requestId = 160
12:33:36.007 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Receive server push request, request = NotifySubscriberRequest, requestId = 162
12:33:36.047 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Ack server push request, request = NotifySubscriberRequest, requestId = 162
12:34:10.897 [nacos-grpc-client-executor-86] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Receive server push request, request = NotifySubscriberRequest, requestId = 164
12:34:10.938 [nacos-grpc-client-executor-86] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Ack server push request, request = NotifySubscriberRequest, requestId = 164
12:40:32.876 [nacos-grpc-client-executor-200] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Receive server push request, request = NotifySubscriberRequest, requestId = 169
12:40:32.899 [nacos-grpc-client-executor-200] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Ack server push request, request = NotifySubscriberRequest, requestId = 169
12:40:46.428 [nacos-grpc-client-executor-205] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Receive server push request, request = NotifySubscriberRequest, requestId = 171
12:40:46.472 [nacos-grpc-client-executor-205] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [23673fea-663c-422f-a843-83f619a8973f] Ack server push request, request = NotifySubscriberRequest, requestId = 171
13:33:05.789 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:33:05.794 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:33:06.123 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:33:06.124 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@238f1191[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:33:06.124 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751517190443_127.0.0.1_11207
13:33:06.127 [nacos-grpc-client-executor-1170] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751517190443_127.0.0.1_11207]Ignore complete event,isRunning:false,isAbandon=false
13:33:06.130 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@599bdb64[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1171]
14:04:12.191 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:04:13.098 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0
14:04:13.186 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
14:04:13.223 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
14:04:13.236 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
14:04:13.250 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:04:13.266 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
14:04:13.277 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
14:04:13.283 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:04:13.283 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001db523bb8c8
14:04:13.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001db523bbae8
14:04:13.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:04:13.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:04:13.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:04:17.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:04:20.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:04:23.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:04:23.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001db524ca720
14:04:23.131 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:04:24.997 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:04:29.274 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:04:31.625 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:04:32.507 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:04:33.328 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:04:34.323 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5ac07453-9239-4def-8105-8dfedcce53ee_config-0
14:04:34.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ac07453-9239-4def-8105-8dfedcce53ee_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:04:34.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ac07453-9239-4def-8105-8dfedcce53ee_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001db523bb8c8
14:04:34.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ac07453-9239-4def-8105-8dfedcce53ee_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001db523bbae8
14:04:34.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ac07453-9239-4def-8105-8dfedcce53ee_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:04:34.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ac07453-9239-4def-8105-8dfedcce53ee_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:04:34.326 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ac07453-9239-4def-8105-8dfedcce53ee_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:04:35.829 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:04:37.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ac07453-9239-4def-8105-8dfedcce53ee_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:04:39.252 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:04:40.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ac07453-9239-4def-8105-8dfedcce53ee_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:04:42.783 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:04:43.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ac07453-9239-4def-8105-8dfedcce53ee_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:04:43.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ac07453-9239-4def-8105-8dfedcce53ee_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001db524ca720
14:04:43.364 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ac07453-9239-4def-8105-8dfedcce53ee_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:04:43.846 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ac51a169-6e3e-4173-878a-a6b165901ee3
14:04:43.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac51a169-6e3e-4173-878a-a6b165901ee3] RpcClient init label, labels = {module=naming, source=sdk}
14:04:43.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac51a169-6e3e-4173-878a-a6b165901ee3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:04:43.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac51a169-6e3e-4173-878a-a6b165901ee3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:04:43.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac51a169-6e3e-4173-878a-a6b165901ee3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:04:43.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac51a169-6e3e-4173-878a-a6b165901ee3] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:04:46.404 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:04:46.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac51a169-6e3e-4173-878a-a6b165901ee3] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:04:49.499 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ac07453-9239-4def-8105-8dfedcce53ee_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:04:49.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac51a169-6e3e-4173-878a-a6b165901ee3] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:04:50.133 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:04:52.706 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ac07453-9239-4def-8105-8dfedcce53ee_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:04:52.905 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac51a169-6e3e-4173-878a-a6b165901ee3] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:04:52.905 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac51a169-6e3e-4173-878a-a6b165901ee3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:04:52.905 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac51a169-6e3e-4173-878a-a6b165901ee3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001db524ca720
14:04:53.960 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0e06396-9a77-4b3d-9b70-7b742aef205f_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:04:54.842 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:04:54.842 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6a450cb5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:04:54.842 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@c4bb119[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3]
14:04:54.949 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac51a169-6e3e-4173-878a-a6b165901ee3] Client is shutdown, stop reconnect to server
14:04:56.023 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ac07453-9239-4def-8105-8dfedcce53ee_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:10:05.311 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:10:06.170 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0
14:10:06.287 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
14:10:06.332 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
14:10:06.348 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
14:10:06.361 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:10:06.382 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
14:10:06.400 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
14:10:06.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:10:06.408 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000025e813cbda8
14:10:06.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000025e813cbfc8
14:10:06.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:10:06.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:10:06.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:10:10.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:10:13.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:10:16.381 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:10:16.382 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:16.383 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000025e814daf60
14:10:18.172 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:10:22.512 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:10:23.699 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:10:25.553 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:10:25.732 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:10:26.424 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0
14:10:26.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:10:26.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000025e813cbda8
14:10:26.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000025e813cbfc8
14:10:26.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:10:26.427 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:10:26.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:10:29.051 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:10:29.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:10:32.445 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:10:32.460 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:10:35.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:35.464 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:10:35.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000025e814daf60
14:10:35.975 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d8c853a7-e25c-48d2-a41a-754f4ecba659
14:10:35.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c853a7-e25c-48d2-a41a-754f4ecba659] RpcClient init label, labels = {module=naming, source=sdk}
14:10:35.976 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:10:35.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c853a7-e25c-48d2-a41a-754f4ecba659] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:10:35.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c853a7-e25c-48d2-a41a-754f4ecba659] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:10:35.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c853a7-e25c-48d2-a41a-754f4ecba659] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:10:35.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c853a7-e25c-48d2-a41a-754f4ecba659] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:10:38.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c853a7-e25c-48d2-a41a-754f4ecba659] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:10:39.599 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:10:41.600 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:10:42.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c853a7-e25c-48d2-a41a-754f4ecba659] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:10:43.321 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:10:44.833 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:10:45.022 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c853a7-e25c-48d2-a41a-754f4ecba659] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:10:45.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c853a7-e25c-48d2-a41a-754f4ecba659] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:45.023 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c853a7-e25c-48d2-a41a-754f4ecba659] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000025e814daf60
14:10:46.978 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:10:46.979 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1abb719a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:10:46.980 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@706dee4[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3]
14:10:47.094 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8c853a7-e25c-48d2-a41a-754f4ecba659] Client is shutdown, stop reconnect to server
14:10:47.144 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0794bfab-2c4e-4263-b60c-db86c509a5e6_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:10:48.151 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ceb88ed-66ec-46ae-a202-308f84ebdfee_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:27:47.032 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:27:47.811 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0
14:27:47.895 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
14:27:47.927 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:27:47.939 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:27:47.952 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:27:47.969 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
14:27:47.980 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:27:47.986 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:27:47.987 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000024e093cc2b8
14:27:47.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000024e093cc4d8
14:27:47.989 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:27:47.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:27:48.003 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:27:51.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:27:54.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:27:57.680 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:27:57.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:57.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000024e094db3c0
14:27:59.444 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:28:03.819 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:04.014 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:28:05.426 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:28:06.113 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d1397d42-142d-432f-9377-1f0e25ac446a_config-0
14:28:06.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1397d42-142d-432f-9377-1f0e25ac446a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:28:06.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1397d42-142d-432f-9377-1f0e25ac446a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000024e093cc2b8
14:28:06.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1397d42-142d-432f-9377-1f0e25ac446a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000024e093cc4d8
14:28:06.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1397d42-142d-432f-9377-1f0e25ac446a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:28:06.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1397d42-142d-432f-9377-1f0e25ac446a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:28:06.115 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1397d42-142d-432f-9377-1f0e25ac446a_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:28:07.031 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:09.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1397d42-142d-432f-9377-1f0e25ac446a_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:28:10.347 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:12.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1397d42-142d-432f-9377-1f0e25ac446a_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:28:13.768 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:15.156 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1397d42-142d-432f-9377-1f0e25ac446a_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:28:15.156 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1397d42-142d-432f-9377-1f0e25ac446a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:28:15.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1397d42-142d-432f-9377-1f0e25ac446a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000024e094db3c0
14:28:15.617 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b3036e3b-cb37-4e30-9816-358025e44130
14:28:15.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3036e3b-cb37-4e30-9816-358025e44130] RpcClient init label, labels = {module=naming, source=sdk}
14:28:15.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3036e3b-cb37-4e30-9816-358025e44130] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:28:15.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3036e3b-cb37-4e30-9816-358025e44130] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:28:15.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3036e3b-cb37-4e30-9816-358025e44130] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:28:15.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3036e3b-cb37-4e30-9816-358025e44130] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:28:17.293 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:18.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3036e3b-cb37-4e30-9816-358025e44130] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:28:20.915 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:21.289 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1397d42-142d-432f-9377-1f0e25ac446a_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:21.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3036e3b-cb37-4e30-9816-358025e44130] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:28:24.508 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1397d42-142d-432f-9377-1f0e25ac446a_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:24.634 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:24.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3036e3b-cb37-4e30-9816-358025e44130] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:28:24.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3036e3b-cb37-4e30-9816-358025e44130] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000024e094db3c0
14:28:24.650 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3036e3b-cb37-4e30-9816-358025e44130] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:28:26.820 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:28:26.822 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@146b4c6c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:28:26.822 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5b166420[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3]
14:28:26.940 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b3036e3b-cb37-4e30-9816-358025e44130] Client is shutdown, stop reconnect to server
14:28:27.845 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1397d42-142d-432f-9377-1f0e25ac446a_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:28:28.454 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b11ca6e-e1e3-480d-9244-f8f972629e1f_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:33:44.804 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:33:45.639 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 34946951-b91d-4d01-9d02-12eafff3f455_config-0
14:33:45.740 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 54 ms to scan 1 urls, producing 3 keys and 6 values 
14:33:45.777 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:33:45.790 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
14:33:45.805 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:33:45.818 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:33:45.833 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
14:33:45.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34946951-b91d-4d01-9d02-12eafff3f455_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:33:45.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34946951-b91d-4d01-9d02-12eafff3f455_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001e0323b42b8
14:33:45.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34946951-b91d-4d01-9d02-12eafff3f455_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001e0323b44d8
14:33:45.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34946951-b91d-4d01-9d02-12eafff3f455_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:33:45.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34946951-b91d-4d01-9d02-12eafff3f455_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:33:45.857 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34946951-b91d-4d01-9d02-12eafff3f455_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:33:47.346 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34946951-b91d-4d01-9d02-12eafff3f455_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751524427079_127.0.0.1_6426
14:33:47.347 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34946951-b91d-4d01-9d02-12eafff3f455_config-0] Notify connected event to listeners.
14:33:47.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34946951-b91d-4d01-9d02-12eafff3f455_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:33:47.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34946951-b91d-4d01-9d02-12eafff3f455_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001e0324ee350
14:33:47.482 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:33:52.838 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:33:54.552 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:33:55.248 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0
14:33:55.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:33:55.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001e0323b42b8
14:33:55.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001e0323b44d8
14:33:55.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:33:55.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:33:55.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:33:58.269 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:34:01.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Try to connect to server on start up, server: {serverIp = '**********', server main port = 8848}
14:34:04.319 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:34:04.321 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:04.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001e0324ee350
14:34:04.766 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 262d70a0-4a27-4e5b-a734-cf255ffcb0d1
14:34:04.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] RpcClient init label, labels = {module=naming, source=sdk}
14:34:04.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:34:04.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:34:04.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:34:04.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:34:04.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Success to connect to server [localhost:8848] on start up, connectionId = 1751524444783_127.0.0.1_6574
14:34:04.913 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Notify connected event to listeners.
14:34:04.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:04.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001e0324ee350
14:34:05.482 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Receive server push request, request = NotifySubscriberRequest, requestId = 175
14:34:05.483 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Ack server push request, request = NotifySubscriberRequest, requestId = 175
14:34:05.484 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
14:34:05.543 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 21.587 seconds (JVM running for 22.955)
14:34:05.570 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Receive server push request, request = NotifySubscriberRequest, requestId = 177
14:34:05.571 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Ack server push request, request = NotifySubscriberRequest, requestId = 177
14:34:05.577 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
14:34:05.577 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
14:34:05.578 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
14:34:05.581 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Receive server push request, request = NotifySubscriberRequest, requestId = 176
14:34:05.582 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Ack server push request, request = NotifySubscriberRequest, requestId = 176
14:34:05.591 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Receive server push request, request = NotifySubscriberRequest, requestId = 178
14:34:05.592 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Ack server push request, request = NotifySubscriberRequest, requestId = 178
14:34:06.009 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Receive server push request, request = NotifySubscriberRequest, requestId = 179
14:34:06.010 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Ack server push request, request = NotifySubscriberRequest, requestId = 179
14:34:10.448 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:34:13.675 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:34:17.000 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:34:20.411 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:34:23.943 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:34:27.568 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:34:31.298 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:34:35.127 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:34:35.677 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Receive server push request, request = NotifySubscriberRequest, requestId = 181
14:34:35.677 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [262d70a0-4a27-4e5b-a734-cf255ffcb0d1] Ack server push request, request = NotifySubscriberRequest, requestId = 181
14:34:39.063 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:34:43.092 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:34:47.203 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:34:51.410 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:34:55.734 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:35:00.152 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:35:04.671 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:35:09.287 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:35:13.998 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:35:18.830 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:35:23.754 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:35:28.780 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:35:33.906 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:35:39.132 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:35:44.459 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:35:49.892 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:35:55.405 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:36:01.030 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 26 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:36:06.747 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 27 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:36:12.569 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 28 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:36:18.492 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 29 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:36:24.507 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 30 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:36:30.634 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 31 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:36:36.863 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 32 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:36:43.193 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 33 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:36:49.598 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 34 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:36:56.130 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 35 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:37:02.753 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ebbabed-6faf-40fa-9c15-5bc82b127b8e_config-0] Fail to connect server, after trying 36 times, last try server is {serverIp = '**********', server main port = 8848}, error = unknown
14:37:04.359 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:37:04.365 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:37:04.702 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:37:04.703 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@a4ce330[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:37:04.703 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751524444783_127.0.0.1_6574
14:37:04.705 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@64505b01[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 93]
14:37:12.713 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:37:13.473 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4dad0007-b13f-4b06-93c3-48a7f897e3a8_config-0
14:37:13.567 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
14:37:13.605 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
14:37:13.617 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
14:37:13.629 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:37:13.646 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
14:37:13.662 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
14:37:13.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4dad0007-b13f-4b06-93c3-48a7f897e3a8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:37:13.670 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4dad0007-b13f-4b06-93c3-48a7f897e3a8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000022de13cbb48
14:37:13.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4dad0007-b13f-4b06-93c3-48a7f897e3a8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000022de13cbd68
14:37:13.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4dad0007-b13f-4b06-93c3-48a7f897e3a8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:37:13.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4dad0007-b13f-4b06-93c3-48a7f897e3a8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:37:13.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4dad0007-b13f-4b06-93c3-48a7f897e3a8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:37:14.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4dad0007-b13f-4b06-93c3-48a7f897e3a8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751524634594_127.0.0.1_7813
14:37:14.854 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4dad0007-b13f-4b06-93c3-48a7f897e3a8_config-0] Notify connected event to listeners.
14:37:14.855 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4dad0007-b13f-4b06-93c3-48a7f897e3a8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:37:14.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4dad0007-b13f-4b06-93c3-48a7f897e3a8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000022de1505f18
14:37:15.008 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:37:19.312 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:37:20.813 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:37:21.470 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d07235a7-8e97-4dcd-910d-db26436a9fdf_config-0
14:37:21.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d07235a7-8e97-4dcd-910d-db26436a9fdf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:37:21.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d07235a7-8e97-4dcd-910d-db26436a9fdf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000022de13cbb48
14:37:21.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d07235a7-8e97-4dcd-910d-db26436a9fdf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000022de13cbd68
14:37:21.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d07235a7-8e97-4dcd-910d-db26436a9fdf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:37:21.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d07235a7-8e97-4dcd-910d-db26436a9fdf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:37:21.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d07235a7-8e97-4dcd-910d-db26436a9fdf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:37:21.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d07235a7-8e97-4dcd-910d-db26436a9fdf_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751524641488_127.0.0.1_7864
14:37:21.605 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d07235a7-8e97-4dcd-910d-db26436a9fdf_config-0] Notify connected event to listeners.
14:37:21.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d07235a7-8e97-4dcd-910d-db26436a9fdf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:37:21.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d07235a7-8e97-4dcd-910d-db26436a9fdf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000022de1505f18
14:37:21.771 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4803069c-ce1e-4b8e-899e-5d0b7ff658b8
14:37:21.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] RpcClient init label, labels = {module=naming, source=sdk}
14:37:21.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:37:21.774 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:37:21.774 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:37:21.774 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:37:21.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Success to connect to server [localhost:8848] on start up, connectionId = 1751524641786_127.0.0.1_7865
14:37:21.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:37:21.916 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Notify connected event to listeners.
14:37:21.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000022de1505f18
14:37:22.483 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 182
14:37:22.484 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 182
14:37:22.498 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
14:37:22.560 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 10.622 seconds (JVM running for 11.981)
14:37:22.570 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
14:37:22.573 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
14:37:22.575 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
14:37:22.576 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 185
14:37:22.577 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 185
14:37:22.589 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 183
14:37:22.594 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 183
14:37:22.603 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 186
14:37:22.603 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 186
14:37:22.613 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 184
14:37:22.617 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 184
14:37:23.012 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 187
14:37:23.042 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 187
14:48:52.940 [nacos-grpc-client-executor-267] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 191
14:48:52.941 [nacos-grpc-client-executor-267] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 191
15:17:11.492 [nacos-grpc-client-executor-861] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 193
15:17:11.519 [nacos-grpc-client-executor-861] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 193
15:17:34.167 [nacos-grpc-client-executor-873] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 195
15:17:34.201 [nacos-grpc-client-executor-873] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 195
15:20:34.292 [nacos-grpc-client-executor-937] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 198
15:20:34.315 [nacos-grpc-client-executor-937] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 198
15:20:36.708 [nacos-grpc-client-executor-938] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 199
15:20:36.731 [nacos-grpc-client-executor-938] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 199
15:21:30.459 [nacos-grpc-client-executor-956] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 212
15:21:30.488 [nacos-grpc-client-executor-956] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 212
15:21:51.449 [nacos-grpc-client-executor-960] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 213
15:21:51.483 [nacos-grpc-client-executor-960] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 213
15:23:17.603 [nacos-grpc-client-executor-986] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 217
15:23:17.635 [nacos-grpc-client-executor-986] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 217
15:23:37.363 [nacos-grpc-client-executor-996] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 218
15:23:37.389 [nacos-grpc-client-executor-996] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 218
15:47:55.586 [nacos-grpc-client-executor-1491] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 222
15:47:55.624 [nacos-grpc-client-executor-1491] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 222
15:48:20.605 [nacos-grpc-client-executor-1496] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 223
15:48:20.631 [nacos-grpc-client-executor-1496] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 223
15:48:27.645 [nacos-grpc-client-executor-1505] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 225
15:48:27.678 [nacos-grpc-client-executor-1505] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 225
15:48:59.102 [nacos-grpc-client-executor-1512] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 229
15:48:59.145 [nacos-grpc-client-executor-1512] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 229
15:57:24.436 [nacos-grpc-client-executor-1689] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 233
15:57:24.457 [nacos-grpc-client-executor-1689] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 233
15:57:29.587 [nacos-grpc-client-executor-1697] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 234
15:57:29.603 [nacos-grpc-client-executor-1697] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 234
15:58:28.041 [nacos-grpc-client-executor-1719] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 236
15:58:28.076 [nacos-grpc-client-executor-1719] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 236
15:58:46.350 [nacos-grpc-client-executor-1723] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 241
15:58:46.474 [nacos-grpc-client-executor-1723] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 241
16:04:38.555 [nacos-grpc-client-executor-1838] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 261
16:04:38.571 [nacos-grpc-client-executor-1838] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 261
16:04:53.045 [nacos-grpc-client-executor-1841] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 262
16:04:53.173 [nacos-grpc-client-executor-1841] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 262
16:07:53.249 [nacos-grpc-client-executor-1898] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 267
16:07:53.278 [nacos-grpc-client-executor-1898] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 267
16:08:08.321 [nacos-grpc-client-executor-1904] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Receive server push request, request = NotifySubscriberRequest, requestId = 269
16:08:08.369 [nacos-grpc-client-executor-1904] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Ack server push request, request = NotifySubscriberRequest, requestId = 269
16:14:21.841 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:14:21.845 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:14:22.172 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:14:22.172 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1478be79[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:14:22.172 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751524641786_127.0.0.1_7865
16:14:22.175 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@60326a54[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 2023]
16:14:22.177 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4803069c-ce1e-4b8e-899e-5d0b7ff658b8] Notify disconnected event to listeners
16:14:22.198 [nacos-grpc-client-executor-2023] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751524641786_127.0.0.1_7865]Ignore complete event,isRunning:false,isAbandon=false
16:50:40.992 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:50:41.543 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5222c60f-e288-49d8-a3b9-8000f704bfa7_config-0
16:50:41.607 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
16:50:41.634 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
16:50:41.642 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
16:50:41.650 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
16:50:41.658 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
16:50:41.669 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
16:50:41.672 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5222c60f-e288-49d8-a3b9-8000f704bfa7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:50:41.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5222c60f-e288-49d8-a3b9-8000f704bfa7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000022f633cb8c8
16:50:41.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5222c60f-e288-49d8-a3b9-8000f704bfa7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000022f633cbae8
16:50:41.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5222c60f-e288-49d8-a3b9-8000f704bfa7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:50:41.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5222c60f-e288-49d8-a3b9-8000f704bfa7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:50:41.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5222c60f-e288-49d8-a3b9-8000f704bfa7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:50:42.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5222c60f-e288-49d8-a3b9-8000f704bfa7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751532642503_127.0.0.1_3642
16:50:42.711 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5222c60f-e288-49d8-a3b9-8000f704bfa7_config-0] Notify connected event to listeners.
16:50:42.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5222c60f-e288-49d8-a3b9-8000f704bfa7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:50:42.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5222c60f-e288-49d8-a3b9-8000f704bfa7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000022f63505418
16:50:42.848 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:50:46.678 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
16:50:48.525 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
16:50:51.564 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3ed3bd5b-60ae-40e6-a234-e2b2128b5e11_config-0
16:50:51.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3bd5b-60ae-40e6-a234-e2b2128b5e11_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:50:51.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3bd5b-60ae-40e6-a234-e2b2128b5e11_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000022f633cb8c8
16:50:51.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3bd5b-60ae-40e6-a234-e2b2128b5e11_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000022f633cbae8
16:50:51.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3bd5b-60ae-40e6-a234-e2b2128b5e11_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:50:51.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3bd5b-60ae-40e6-a234-e2b2128b5e11_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:50:51.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3bd5b-60ae-40e6-a234-e2b2128b5e11_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:50:51.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3bd5b-60ae-40e6-a234-e2b2128b5e11_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751532651643_127.0.0.1_3671
16:50:51.806 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3bd5b-60ae-40e6-a234-e2b2128b5e11_config-0] Notify connected event to listeners.
16:50:51.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3bd5b-60ae-40e6-a234-e2b2128b5e11_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:50:51.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3ed3bd5b-60ae-40e6-a234-e2b2128b5e11_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000022f63505418
16:50:52.803 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1720a0a5-8612-43f0-be86-01315cdc47b5
16:50:52.807 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] RpcClient init label, labels = {module=naming, source=sdk}
16:50:52.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:50:52.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:50:52.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:50:52.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:50:53.156 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Success to connect to server [localhost:8848] on start up, connectionId = 1751532652947_127.0.0.1_3672
16:50:53.160 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Notify connected event to listeners.
16:50:53.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:50:53.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000022f63505418
16:50:54.055 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 278
16:50:54.059 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 278
16:50:58.273 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
16:50:58.456 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 18.024 seconds (JVM running for 18.97)
16:50:58.536 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
16:50:58.543 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
16:50:58.547 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
16:50:58.852 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 279
16:50:58.855 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 279
16:51:25.302 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 282
16:51:25.304 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 282
16:51:25.328 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 283
16:51:25.328 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 283
16:51:55.369 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 286
16:51:55.373 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 286
16:51:55.424 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 287
16:51:55.425 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 287
16:52:14.926 [nacos-grpc-client-executor-63] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 289
16:52:14.927 [nacos-grpc-client-executor-63] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 289
17:35:11.890 [nacos-grpc-client-executor-879] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 292
17:35:11.914 [nacos-grpc-client-executor-879] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 292
17:35:44.180 [nacos-grpc-client-executor-889] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 295
17:35:44.201 [nacos-grpc-client-executor-889] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 295
17:57:38.444 [nacos-grpc-client-executor-1296] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 297
17:57:38.469 [nacos-grpc-client-executor-1296] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 297
17:58:12.009 [nacos-grpc-client-executor-1308] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 300
17:58:12.037 [nacos-grpc-client-executor-1308] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 300
19:11:15.566 [nacos-grpc-client-executor-2691] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 302
19:11:15.594 [nacos-grpc-client-executor-2691] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 302
19:11:57.344 [nacos-grpc-client-executor-2703] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 304
19:11:57.368 [nacos-grpc-client-executor-2703] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 304
20:17:11.931 [nacos-grpc-client-executor-3947] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 307
20:17:11.962 [nacos-grpc-client-executor-3947] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 307
20:17:20.776 [nacos-grpc-client-executor-3948] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 308
20:17:20.791 [nacos-grpc-client-executor-3948] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 308
20:18:25.232 [nacos-grpc-client-executor-3971] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 310
20:18:25.250 [nacos-grpc-client-executor-3971] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 310
20:20:48.988 [nacos-grpc-client-executor-4017] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 311
20:20:49.006 [nacos-grpc-client-executor-4017] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 311
20:21:23.288 [nacos-grpc-client-executor-4028] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 313
20:21:23.311 [nacos-grpc-client-executor-4028] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 313
20:22:05.109 [nacos-grpc-client-executor-4043] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 316
20:22:05.136 [nacos-grpc-client-executor-4043] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 316
20:26:54.416 [nacos-grpc-client-executor-4127] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 321
20:26:54.431 [nacos-grpc-client-executor-4127] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 321
20:26:56.953 [nacos-grpc-client-executor-4128] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Receive server push request, request = NotifySubscriberRequest, requestId = 323
20:26:56.972 [nacos-grpc-client-executor-4128] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1720a0a5-8612-43f0-be86-01315cdc47b5] Ack server push request, request = NotifySubscriberRequest, requestId = 323
20:46:23.673 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:46:23.676 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:46:23.998 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:46:24.001 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@298982e9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:46:24.001 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751532652947_127.0.0.1_3672
20:46:24.008 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@39a04dc6[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4486]
20:46:24.015 [nacos-grpc-client-executor-4486] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751532652947_127.0.0.1_3672]Ignore complete event,isRunning:false,isAbandon=false
