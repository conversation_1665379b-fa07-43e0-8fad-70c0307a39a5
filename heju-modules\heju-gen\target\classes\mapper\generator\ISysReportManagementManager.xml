<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.heju.gen.mapper.GenTableMapper">

	<resultMap type="com.heju.gen.domain.dto.GenTableDto" id="GenTableResult">
	    <id     property="id"        				column="id"          			/>
		<result property="name"      				column="name"        			/>
		<result property="comment"   				column="comment"     			/>
		<result property="createTime"     			column="create_time"       		/>
		<result property="updateTime"     			column="update_time"       		/>
	</resultMap>

	<sql id="schema">
		select table_name as name, table_comment as comment, create_time, update_time
		from information_schema.tables
	</sql>

	<select id="selectDbTableList" parameterType="com.heju.gen.domain.dto.GenTableDto" resultMap="GenTableResult">
		<include refid="schema"/>
		where table_schema = (select database())
        AND table_name NOT LIKE 'qrtz_%' AND table_name NOT LIKE 'gen_%'
		AND table_name NOT IN (select name from gen_table)
		<if test="name != null and name != ''">
			AND lower(table_name) like lower(concat('%', #{name}, '%'))
		</if>
		<if test="comment != null and comment != ''">
			AND lower(table_comment) like lower(concat('%', #{comment}, '%'))
		</if>
		order by create_time desc
	</select>

	<select id="selectDbTableListByNames" resultMap="GenTableResult">
		<include refid="schema"/>
		where table_name NOT LIKE 'qrtz_%' and table_name NOT LIKE 'gen_%' and table_schema = (select database())
		and table_name in
	    <foreach collection="array" item="name" open="(" separator="," close=")">
 			#{name}
        </foreach>
	</select>

	<select id="selectDbTableByName" parameterType="String" resultMap="GenTableResult">
		<include refid="schema"/>
		where table_comment <![CDATA[ <> ]]> '' and table_schema = (select database())
		and table_name = #{name}
	</select>

</mapper>