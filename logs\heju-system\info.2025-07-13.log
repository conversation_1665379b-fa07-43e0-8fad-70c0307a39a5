10:04:15.920 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:04:16.777 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1ec6683d-565a-4389-99c1-d875e3c002a2_config-0
10:04:16.880 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 46 ms to scan 1 urls, producing 3 keys and 6 values 
10:04:16.938 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 4 keys and 9 values 
10:04:16.946 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 3 keys and 10 values 
10:04:16.958 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
10:04:16.966 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 7 values 
10:04:16.977 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
10:04:16.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ec6683d-565a-4389-99c1-d875e3c002a2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:04:16.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ec6683d-565a-4389-99c1-d875e3c002a2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000277843b8fc8
10:04:16.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ec6683d-565a-4389-99c1-d875e3c002a2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000277843b91e8
10:04:16.987 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ec6683d-565a-4389-99c1-d875e3c002a2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:04:16.987 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ec6683d-565a-4389-99c1-d875e3c002a2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:04:16.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ec6683d-565a-4389-99c1-d875e3c002a2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:17.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ec6683d-565a-4389-99c1-d875e3c002a2_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752372257751_127.0.0.1_8191
10:04:17.983 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ec6683d-565a-4389-99c1-d875e3c002a2_config-0] Notify connected event to listeners.
10:04:17.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ec6683d-565a-4389-99c1-d875e3c002a2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:17.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ec6683d-565a-4389-99c1-d875e3c002a2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000277844f3250
10:04:18.222 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:04:22.541 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:04:22.541 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:04:22.541 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:04:22.749 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:04:23.525 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:04:23.526 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:04:23.526 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:04:33.360 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:04:39.697 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9c8e6a3a-12a5-4e3c-b828-5dec8e113201
10:04:39.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] RpcClient init label, labels = {module=naming, source=sdk}
10:04:39.701 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:04:39.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:04:39.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:04:39.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:39.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Success to connect to server [localhost:8848] on start up, connectionId = 1752372279719_127.0.0.1_8280
10:04:39.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:39.843 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Notify connected event to listeners.
10:04:39.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000277844f3250
10:04:39.931 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:04:39.990 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:04:40.318 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.01 seconds (JVM running for 26.516)
10:04:40.349 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:04:40.351 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:04:40.352 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:04:40.479 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Receive server push request, request = NotifySubscriberRequest, requestId = 6
10:04:40.497 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Ack server push request, request = NotifySubscriberRequest, requestId = 6
10:13:27.981 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:13:31.597 [nacos-grpc-client-executor-121] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Receive server push request, request = NotifySubscriberRequest, requestId = 12
10:13:31.599 [nacos-grpc-client-executor-121] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Ack server push request, request = NotifySubscriberRequest, requestId = 12
10:13:32.402 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:13:32.405 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:13:32.592 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:13:32.593 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
10:20:12.805 [nacos-grpc-client-executor-203] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Receive server push request, request = NotifySubscriberRequest, requestId = 15
10:20:12.826 [nacos-grpc-client-executor-203] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Ack server push request, request = NotifySubscriberRequest, requestId = 15
10:20:29.181 [nacos-grpc-client-executor-206] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Receive server push request, request = NotifySubscriberRequest, requestId = 18
10:20:29.201 [nacos-grpc-client-executor-206] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c8e6a3a-12a5-4e3c-b828-5dec8e113201] Ack server push request, request = NotifySubscriberRequest, requestId = 18
10:47:03.203 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:47:03.209 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:47:03.556 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:47:03.557 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@452dcfb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:47:03.557 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752372279719_127.0.0.1_8280
10:47:03.560 [nacos-grpc-client-executor-526] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752372279719_127.0.0.1_8280]Ignore complete event,isRunning:false,isAbandon=false
10:47:03.565 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@43c63b66[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 527]
10:47:03.747 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:47:03.750 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:47:03.758 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:47:03.759 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:47:03.760 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:47:03.761 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:47:03.762 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:47:03.762 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:47:09.571 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:47:10.402 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d330a131-7d73-4e36-a292-1da7e1331491_config-0
10:47:10.493 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
10:47:10.538 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
10:47:10.561 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
10:47:10.580 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
10:47:10.602 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 7 values 
10:47:10.627 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 2 keys and 8 values 
10:47:10.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330a131-7d73-4e36-a292-1da7e1331491_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:47:10.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330a131-7d73-4e36-a292-1da7e1331491_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000020e9539e8d8
10:47:10.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330a131-7d73-4e36-a292-1da7e1331491_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000020e9539eaf8
10:47:10.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330a131-7d73-4e36-a292-1da7e1331491_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:47:10.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330a131-7d73-4e36-a292-1da7e1331491_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:47:10.647 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330a131-7d73-4e36-a292-1da7e1331491_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:47:11.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330a131-7d73-4e36-a292-1da7e1331491_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752374831546_127.0.0.1_13053
10:47:11.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330a131-7d73-4e36-a292-1da7e1331491_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:47:11.751 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330a131-7d73-4e36-a292-1da7e1331491_config-0] Notify connected event to listeners.
10:47:11.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d330a131-7d73-4e36-a292-1da7e1331491_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020e95518ad8
10:47:11.912 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:47:15.897 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:47:15.897 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:47:15.898 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:47:16.087 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:47:17.020 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:47:17.023 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:47:17.023 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:47:25.263 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:47:28.452 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9cfdc25c-df6b-45d1-8cf2-104cf5b6b77e
10:47:28.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cfdc25c-df6b-45d1-8cf2-104cf5b6b77e] RpcClient init label, labels = {module=naming, source=sdk}
10:47:28.471 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cfdc25c-df6b-45d1-8cf2-104cf5b6b77e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:47:28.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cfdc25c-df6b-45d1-8cf2-104cf5b6b77e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:47:28.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cfdc25c-df6b-45d1-8cf2-104cf5b6b77e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:47:28.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cfdc25c-df6b-45d1-8cf2-104cf5b6b77e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:47:28.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cfdc25c-df6b-45d1-8cf2-104cf5b6b77e] Success to connect to server [localhost:8848] on start up, connectionId = 1752374848497_127.0.0.1_13086
10:47:28.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cfdc25c-df6b-45d1-8cf2-104cf5b6b77e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:47:28.622 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cfdc25c-df6b-45d1-8cf2-104cf5b6b77e] Notify connected event to listeners.
10:47:28.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cfdc25c-df6b-45d1-8cf2-104cf5b6b77e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020e95518ad8
10:47:28.694 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:47:28.728 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:47:28.854 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.884 seconds (JVM running for 20.852)
10:47:28.881 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:47:28.881 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:47:28.882 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:47:29.263 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cfdc25c-df6b-45d1-8cf2-104cf5b6b77e] Receive server push request, request = NotifySubscriberRequest, requestId = 25
10:47:29.283 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cfdc25c-df6b-45d1-8cf2-104cf5b6b77e] Ack server push request, request = NotifySubscriberRequest, requestId = 25
10:47:29.364 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:51:59.804 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:51:59.805 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:48:39.005 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:48:39.005 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:48:39.354 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:48:39.354 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2a2aef54[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:48:39.354 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752374848497_127.0.0.1_13086
13:48:39.356 [nacos-grpc-client-executor-2182] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752374848497_127.0.0.1_13086]Ignore complete event,isRunning:false,isAbandon=false
13:48:39.358 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@12152a12[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 2183]
13:48:39.516 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:48:39.519 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:48:39.524 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:48:39.524 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:48:39.525 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:48:39.525 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:48:45.328 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:48:45.884 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 435f3fc2-6d72-4961-a031-e797ffb32d5a_config-0
13:48:45.934 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
13:48:45.963 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
13:48:45.969 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
13:48:45.980 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
13:48:45.987 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
13:48:45.995 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
13:48:45.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [435f3fc2-6d72-4961-a031-e797ffb32d5a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:48:45.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [435f3fc2-6d72-4961-a031-e797ffb32d5a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000200173cdb10
13:48:45.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [435f3fc2-6d72-4961-a031-e797ffb32d5a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000200173cdd30
13:48:45.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [435f3fc2-6d72-4961-a031-e797ffb32d5a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:48:45.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [435f3fc2-6d72-4961-a031-e797ffb32d5a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:48:46.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [435f3fc2-6d72-4961-a031-e797ffb32d5a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:48:46.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [435f3fc2-6d72-4961-a031-e797ffb32d5a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752385726498_127.0.0.1_1358
13:48:46.686 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [435f3fc2-6d72-4961-a031-e797ffb32d5a_config-0] Notify connected event to listeners.
13:48:46.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [435f3fc2-6d72-4961-a031-e797ffb32d5a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:48:46.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [435f3fc2-6d72-4961-a031-e797ffb32d5a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020017507b88
13:48:46.769 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:48:49.246 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:48:49.247 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:48:49.247 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:48:49.364 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:48:49.860 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:48:49.862 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:48:49.862 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:48:54.991 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:48:57.214 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0fe3bbdb-d599-4c05-827c-eb60e75c3be9
13:48:57.214 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fe3bbdb-d599-4c05-827c-eb60e75c3be9] RpcClient init label, labels = {module=naming, source=sdk}
13:48:57.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fe3bbdb-d599-4c05-827c-eb60e75c3be9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:48:57.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fe3bbdb-d599-4c05-827c-eb60e75c3be9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:48:57.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fe3bbdb-d599-4c05-827c-eb60e75c3be9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:48:57.218 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fe3bbdb-d599-4c05-827c-eb60e75c3be9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:48:57.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fe3bbdb-d599-4c05-827c-eb60e75c3be9] Success to connect to server [localhost:8848] on start up, connectionId = 1752385737233_127.0.0.1_1386
13:48:57.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fe3bbdb-d599-4c05-827c-eb60e75c3be9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:48:57.368 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fe3bbdb-d599-4c05-827c-eb60e75c3be9] Notify connected event to listeners.
13:48:57.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fe3bbdb-d599-4c05-827c-eb60e75c3be9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020017507b88
13:48:57.413 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:48:57.441 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:48:57.531 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.676 seconds (JVM running for 13.608)
13:48:57.542 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:48:57.542 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:48:57.542 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:48:57.946 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fe3bbdb-d599-4c05-827c-eb60e75c3be9] Receive server push request, request = NotifySubscriberRequest, requestId = 29
13:48:57.966 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0fe3bbdb-d599-4c05-827c-eb60e75c3be9] Ack server push request, request = NotifySubscriberRequest, requestId = 29
13:48:58.009 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:49:56.045 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:49:56.046 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:20:39.637 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:20:39.639 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:20:39.969 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:20:39.969 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5c36082f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:20:39.969 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752385737233_127.0.0.1_1386
14:20:39.970 [nacos-grpc-client-executor-418] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752385737233_127.0.0.1_1386]Ignore complete event,isRunning:false,isAbandon=false
14:20:39.971 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@69f04d7b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 419]
14:20:40.104 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:20:40.106 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:20:40.111 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:20:40.111 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:20:40.112 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:20:40.112 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:20:44.874 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:20:45.498 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 326fa8e8-3b58-47e3-947d-4470302e64ca_config-0
14:20:45.543 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
14:20:45.574 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
14:20:45.585 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:20:45.592 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
14:20:45.600 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
14:20:45.606 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
14:20:45.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326fa8e8-3b58-47e3-947d-4470302e64ca_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:20:45.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326fa8e8-3b58-47e3-947d-4470302e64ca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000026acd39eaf8
14:20:45.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326fa8e8-3b58-47e3-947d-4470302e64ca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000026acd39ed18
14:20:45.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326fa8e8-3b58-47e3-947d-4470302e64ca_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:20:45.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326fa8e8-3b58-47e3-947d-4470302e64ca_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:20:45.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326fa8e8-3b58-47e3-947d-4470302e64ca_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:20:46.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326fa8e8-3b58-47e3-947d-4470302e64ca_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752387646301_127.0.0.1_4974
14:20:46.501 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326fa8e8-3b58-47e3-947d-4470302e64ca_config-0] Notify connected event to listeners.
14:20:46.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326fa8e8-3b58-47e3-947d-4470302e64ca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:20:46.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326fa8e8-3b58-47e3-947d-4470302e64ca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026acd518668
14:20:46.591 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:20:49.351 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:20:49.351 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:20:49.351 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:20:49.493 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:20:50.067 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:20:50.068 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:20:50.068 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:20:55.400 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:20:57.770 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a33f8131-e794-488c-af7a-aa00dd81863a
14:20:57.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a33f8131-e794-488c-af7a-aa00dd81863a] RpcClient init label, labels = {module=naming, source=sdk}
14:20:57.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a33f8131-e794-488c-af7a-aa00dd81863a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:20:57.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a33f8131-e794-488c-af7a-aa00dd81863a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:20:57.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a33f8131-e794-488c-af7a-aa00dd81863a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:20:57.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a33f8131-e794-488c-af7a-aa00dd81863a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:20:57.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a33f8131-e794-488c-af7a-aa00dd81863a] Success to connect to server [localhost:8848] on start up, connectionId = 1752387657781_127.0.0.1_4988
14:20:57.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a33f8131-e794-488c-af7a-aa00dd81863a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:20:57.901 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a33f8131-e794-488c-af7a-aa00dd81863a] Notify connected event to listeners.
14:20:57.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a33f8131-e794-488c-af7a-aa00dd81863a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026acd518668
14:20:57.938 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:20:57.960 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:20:58.045 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.695 seconds (JVM running for 14.815)
14:20:58.055 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:20:58.056 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:20:58.056 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:20:58.346 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:20:58.496 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a33f8131-e794-488c-af7a-aa00dd81863a] Receive server push request, request = NotifySubscriberRequest, requestId = 34
14:20:58.511 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a33f8131-e794-488c-af7a-aa00dd81863a] Ack server push request, request = NotifySubscriberRequest, requestId = 34
14:22:06.917 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:22:06.921 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:22:07.255 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:22:07.255 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3c064f7b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:22:07.255 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752387657781_127.0.0.1_4988
14:22:07.257 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752387657781_127.0.0.1_4988]Ignore complete event,isRunning:false,isAbandon=false
14:22:07.257 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1c1b5532[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 25]
14:22:07.386 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:22:07.388 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:22:07.393 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:22:07.393 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:22:11.738 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:22:12.324 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0a3ca0b7-868f-4a62-aab6-477104789995_config-0
14:22:12.371 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 6 values 
14:22:12.397 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
14:22:12.403 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
14:22:12.409 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
14:22:12.415 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
14:22:12.423 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
14:22:12.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3ca0b7-868f-4a62-aab6-477104789995_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:22:12.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3ca0b7-868f-4a62-aab6-477104789995_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002502139f1c0
14:22:12.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3ca0b7-868f-4a62-aab6-477104789995_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002502139f3e0
14:22:12.427 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3ca0b7-868f-4a62-aab6-477104789995_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:22:12.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3ca0b7-868f-4a62-aab6-477104789995_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:22:12.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3ca0b7-868f-4a62-aab6-477104789995_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:22:13.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3ca0b7-868f-4a62-aab6-477104789995_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752387732936_127.0.0.1_5140
14:22:13.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3ca0b7-868f-4a62-aab6-477104789995_config-0] Notify connected event to listeners.
14:22:13.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3ca0b7-868f-4a62-aab6-477104789995_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:22:13.115 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3ca0b7-868f-4a62-aab6-477104789995_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025021518ad8
14:22:13.196 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:22:15.560 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:22:15.560 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:22:15.560 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:22:15.674 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:22:16.160 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:22:16.162 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:22:16.162 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:22:21.246 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:22:23.537 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ada793ce-4209-449c-80d5-eb4e5daba519
14:22:23.538 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ada793ce-4209-449c-80d5-eb4e5daba519] RpcClient init label, labels = {module=naming, source=sdk}
14:22:23.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ada793ce-4209-449c-80d5-eb4e5daba519] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:22:23.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ada793ce-4209-449c-80d5-eb4e5daba519] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:22:23.540 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ada793ce-4209-449c-80d5-eb4e5daba519] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:22:23.540 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ada793ce-4209-449c-80d5-eb4e5daba519] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:22:23.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ada793ce-4209-449c-80d5-eb4e5daba519] Success to connect to server [localhost:8848] on start up, connectionId = 1752387743548_127.0.0.1_5152
14:22:23.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ada793ce-4209-449c-80d5-eb4e5daba519] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:22:23.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ada793ce-4209-449c-80d5-eb4e5daba519] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025021518ad8
14:22:23.664 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ada793ce-4209-449c-80d5-eb4e5daba519] Notify connected event to listeners.
14:22:23.707 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:22:23.742 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:22:23.844 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.616 seconds (JVM running for 13.455)
14:22:23.859 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:22:23.859 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:22:23.860 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:22:24.193 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ada793ce-4209-449c-80d5-eb4e5daba519] Receive server push request, request = NotifySubscriberRequest, requestId = 39
14:22:24.207 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ada793ce-4209-449c-80d5-eb4e5daba519] Ack server push request, request = NotifySubscriberRequest, requestId = 39
14:22:24.286 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:22:42.195 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:22:42.195 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:23:44.119 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:23:44.131 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:23:44.465 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:23:44.465 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@19c53b98[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:23:44.466 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752387743548_127.0.0.1_5152
14:23:44.468 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752387743548_127.0.0.1_5152]Ignore complete event,isRunning:false,isAbandon=false
14:23:44.470 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6be3f641[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 28]
14:23:44.610 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:23:44.612 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:23:44.616 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:23:44.616 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:23:44.617 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:23:44.617 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:23:49.234 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:23:49.841 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of abbad7cc-352d-4a51-b9f4-a8861aa58abe_config-0
14:23:49.898 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
14:23:49.934 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
14:23:49.940 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
14:23:49.946 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
14:23:49.955 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:23:49.966 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:23:49.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbad7cc-352d-4a51-b9f4-a8861aa58abe_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:23:49.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbad7cc-352d-4a51-b9f4-a8861aa58abe_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002171439ef80
14:23:49.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbad7cc-352d-4a51-b9f4-a8861aa58abe_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002171439f1a0
14:23:49.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbad7cc-352d-4a51-b9f4-a8861aa58abe_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:23:49.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbad7cc-352d-4a51-b9f4-a8861aa58abe_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:23:49.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbad7cc-352d-4a51-b9f4-a8861aa58abe_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:23:50.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbad7cc-352d-4a51-b9f4-a8861aa58abe_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752387830562_127.0.0.1_5421
14:23:50.778 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbad7cc-352d-4a51-b9f4-a8861aa58abe_config-0] Notify connected event to listeners.
14:23:50.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbad7cc-352d-4a51-b9f4-a8861aa58abe_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:23:50.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [abbad7cc-352d-4a51-b9f4-a8861aa58abe_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021714518fb0
14:23:50.881 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:23:54.017 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:23:54.017 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:23:54.017 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:23:54.185 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:23:54.777 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:23:54.779 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:23:54.779 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:24:06.216 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:24:10.723 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b8b38f2a-cf08-47c4-bccf-351c868d68e9
14:24:10.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8b38f2a-cf08-47c4-bccf-351c868d68e9] RpcClient init label, labels = {module=naming, source=sdk}
14:24:10.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8b38f2a-cf08-47c4-bccf-351c868d68e9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:24:10.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8b38f2a-cf08-47c4-bccf-351c868d68e9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:24:10.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8b38f2a-cf08-47c4-bccf-351c868d68e9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:24:10.729 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8b38f2a-cf08-47c4-bccf-351c868d68e9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:24:10.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8b38f2a-cf08-47c4-bccf-351c868d68e9] Success to connect to server [localhost:8848] on start up, connectionId = 1752387850731_127.0.0.1_5480
14:24:10.851 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8b38f2a-cf08-47c4-bccf-351c868d68e9] Notify connected event to listeners.
14:24:10.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8b38f2a-cf08-47c4-bccf-351c868d68e9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:24:10.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8b38f2a-cf08-47c4-bccf-351c868d68e9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021714518fb0
14:24:10.915 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:24:10.953 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:24:11.190 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 22.7 seconds (JVM running for 23.578)
14:24:11.211 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:24:11.213 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:24:11.213 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:24:11.395 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8b38f2a-cf08-47c4-bccf-351c868d68e9] Receive server push request, request = NotifySubscriberRequest, requestId = 43
14:24:11.422 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8b38f2a-cf08-47c4-bccf-351c868d68e9] Ack server push request, request = NotifySubscriberRequest, requestId = 43
14:24:11.763 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:24:17.608 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:24:17.608 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:20:01.501 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:20:01.511 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:20:01.840 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:20:01.840 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@72a6c63b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:20:01.840 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752387850731_127.0.0.1_5480
19:20:01.840 [nacos-grpc-client-executor-3761] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752387850731_127.0.0.1_5480]Ignore complete event,isRunning:false,isAbandon=false
19:20:01.852 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2e52999[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3762]
19:20:02.030 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:20:02.087 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:20:02.096 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:20:02.096 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:20:02.099 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:20:02.101 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:21:54.739 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:21:57.260 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0
19:21:57.548 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 116 ms to scan 1 urls, producing 3 keys and 6 values 
19:21:57.670 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 4 keys and 9 values 
19:21:57.695 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 10 values 
19:21:57.725 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 5 values 
19:21:57.757 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 1 keys and 7 values 
19:21:57.780 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
19:21:57.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:21:57.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000017e2c3b6d38
19:21:57.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000017e2c3b6f58
19:21:57.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:21:57.792 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:21:57.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:00.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:00.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:00.687 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
19:22:00.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:22:00.694 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000017e2c4c1000
19:22:00.958 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:01.189 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:01.522 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:02.015 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:02.576 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:03.316 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:03.484 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:22:04.069 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:04.919 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:05.841 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:08.046 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:09.428 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:10.872 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:12.485 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:14.395 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:16.101 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:17.917 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:20.005 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:20.721 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:22:20.722 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:22:20.723 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:22:21.513 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:22:21.977 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:24.111 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:22:24.114 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:22:24.115 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:22:24.142 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Success to connect a server [localhost:8848], connectionId = 1752405743998_127.0.0.1_2835
19:22:24.145 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21fc5867-c4cd-4def-bc58-1249ce3f8e44_config-0] Notify connected event to listeners.
19:22:49.600 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:22:59.506 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7e584a51-88e0-4759-be5b-b29135d5c8b8
19:22:59.507 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e584a51-88e0-4759-be5b-b29135d5c8b8] RpcClient init label, labels = {module=naming, source=sdk}
19:22:59.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e584a51-88e0-4759-be5b-b29135d5c8b8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:22:59.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e584a51-88e0-4759-be5b-b29135d5c8b8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:22:59.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e584a51-88e0-4759-be5b-b29135d5c8b8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:22:59.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e584a51-88e0-4759-be5b-b29135d5c8b8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:59.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e584a51-88e0-4759-be5b-b29135d5c8b8] Success to connect to server [localhost:8848] on start up, connectionId = 1752405779527_127.0.0.1_3070
19:22:59.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e584a51-88e0-4759-be5b-b29135d5c8b8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:22:59.641 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e584a51-88e0-4759-be5b-b29135d5c8b8] Notify connected event to listeners.
19:22:59.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e584a51-88e0-4759-be5b-b29135d5c8b8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000017e2c4c1000
19:22:59.742 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:22:59.822 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:23:00.259 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e584a51-88e0-4759-be5b-b29135d5c8b8] Receive server push request, request = NotifySubscriberRequest, requestId = 7
19:23:00.280 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e584a51-88e0-4759-be5b-b29135d5c8b8] Ack server push request, request = NotifySubscriberRequest, requestId = 7
19:23:00.303 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 67.64 seconds (JVM running for 71.16)
19:23:00.337 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:23:00.337 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:23:00.341 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:24:17.792 [http-nio-9600-exec-3] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:24:20.608 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e584a51-88e0-4759-be5b-b29135d5c8b8] Receive server push request, request = NotifySubscriberRequest, requestId = 11
19:24:20.608 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e584a51-88e0-4759-be5b-b29135d5c8b8] Ack server push request, request = NotifySubscriberRequest, requestId = 11
19:24:22.575 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
19:24:22.575 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:24:22.575 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
19:24:22.582 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
19:24:22.593 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:24:22.593 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
19:24:22.593 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:24:22.593 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
19:24:22.602 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
19:24:22.602 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:24:22.666 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
19:24:22.666 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-6} inited
19:24:22.666 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:24:22.677 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:24:22.679 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-7} inited
19:24:22.679 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:24:22.681 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
19:24:22.682 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
19:24:22.683 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:24:22.683 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-7} closing ...
19:24:22.685 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-7} closed
19:24:22.686 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:34:22.152 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:34:22.152 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:34:22.493 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:34:22.493 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3accf2dc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:34:22.493 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752405779527_127.0.0.1_3070
19:34:22.494 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7a252728[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 155]
19:34:22.648 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:34:22.648 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-6} closing ...
19:34:22.650 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-6} closed
19:34:22.650 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:34:22.652 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:34:22.652 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
