package com.heju.system.file.service;

import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.file.domain.dto.SysFileBorrowRecordDto;
import com.heju.system.file.domain.query.SysFileBorrowRecordQuery;

import java.util.List;

/**
 * 文件借阅记录管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysFileBorrowRecordService extends IBaseService<SysFileBorrowRecordQuery, SysFileBorrowRecordDto> {

    /**
     * 操作记录
     */
    List<SysFileBorrowRecordDto> overTimeList(SysFileBorrowRecordQuery fileBorrowRecord);

    /**
     * 批量操作(新增、修改)
     */
    int batchOperation(List<SysFileBorrowRecordDto> fileBorrowRecords);

}
