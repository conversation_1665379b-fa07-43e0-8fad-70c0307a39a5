package ${packageName}.manager;

#if($table.base)
#set($Entity="Base")
#elseif($table.tree)
#set($Entity="Tree")
#end
import ${packageName}.domain.dto.${ClassName}Dto;
import ${packageName}.domain.query.${ClassName}Query;
import com.heju.common.web.entity.manager.I${Entity}Manager;

/**
 * ${functionName}管理 数据封装层
 *
 * <AUTHOR>
 */
public interface I${ClassName}Manager extends I${Entity}Manager<${ClassName}Query, ${ClassName}Dto> {
}