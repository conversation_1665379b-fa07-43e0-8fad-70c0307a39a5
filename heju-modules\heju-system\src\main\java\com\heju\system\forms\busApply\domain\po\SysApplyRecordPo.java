package com.heju.system.forms.busApply.domain.po;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.heju.common.core.web.entity.base.BaseEntity;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.UPDATE_BY;
import static com.heju.common.core.constant.basic.EntityConstants.DEL_FLAG;
import static com.heju.common.core.constant.basic.EntityConstants.UPDATE_TIME;
import static com.heju.common.core.constant.basic.EntityConstants.NAME;

/**
 * 行政申请 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_apply_record", excludeProperty = { UPDATE_BY, DEL_FLAG, UPDATE_TIME, NAME })
public class SysApplyRecordPo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 行政业务id */
    @Excel(name = "行政业务id")
    protected Long businessId;

    /** 表名api_name */
    @Excel(name = "表名api_name")
    protected String apiName;

    /** 申请时间 */
    @Excel(name = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected Date applyTime;

    /** 申请原因 */
    @Excel(name = "申请原因")
    protected String applyReason;

    /** 驳回原因 */
    @Excel(name = "驳回原因")
    protected String rejectReason;

    /** 状态 0-通过，1-驳回，2-审核中，3-终止授权 */
    @Excel(name = "状态 0-通过，1-驳回，2-审核中，3-终止授权")
    protected String applyStatus;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;

}
