14:15:44.687 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:15:45.669 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fea595f9-a894-42fa-903b-714820ca7421_config-0
14:15:45.802 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 53 ms to scan 1 urls, producing 3 keys and 6 values 
14:15:45.841 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:15:45.853 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:15:45.868 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:15:45.889 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 1 keys and 7 values 
14:15:45.903 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:15:45.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fea595f9-a894-42fa-903b-714820ca7421_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:15:45.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fea595f9-a894-42fa-903b-714820ca7421_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000012b013cf8e0
14:15:45.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fea595f9-a894-42fa-903b-714820ca7421_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000012b013cfb00
14:15:45.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fea595f9-a894-42fa-903b-714820ca7421_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:15:45.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fea595f9-a894-42fa-903b-714820ca7421_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:15:45.922 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fea595f9-a894-42fa-903b-714820ca7421_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:15:47.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fea595f9-a894-42fa-903b-714820ca7421_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750832146894_127.0.0.1_7442
14:15:47.143 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fea595f9-a894-42fa-903b-714820ca7421_config-0] Notify connected event to listeners.
14:15:47.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fea595f9-a894-42fa-903b-714820ca7421_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:15:47.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fea595f9-a894-42fa-903b-714820ca7421_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000012b01509450
14:15:47.318 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:15:51.543 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:15:51.544 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:15:51.545 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:15:51.802 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:15:52.775 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:15:52.776 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:15:52.777 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:15:56.917 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:16:00.749 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2f7a5c0f-af73-48cc-afcc-5f6d93cbc8ca
14:16:00.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7a5c0f-af73-48cc-afcc-5f6d93cbc8ca] RpcClient init label, labels = {module=naming, source=sdk}
14:16:00.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7a5c0f-af73-48cc-afcc-5f6d93cbc8ca] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:16:00.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7a5c0f-af73-48cc-afcc-5f6d93cbc8ca] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:16:00.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7a5c0f-af73-48cc-afcc-5f6d93cbc8ca] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:16:00.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7a5c0f-af73-48cc-afcc-5f6d93cbc8ca] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:16:00.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7a5c0f-af73-48cc-afcc-5f6d93cbc8ca] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750832160766_127.0.0.1_7483
14:16:00.890 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7a5c0f-af73-48cc-afcc-5f6d93cbc8ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:16:00.890 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7a5c0f-af73-48cc-afcc-5f6d93cbc8ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000012b01509450
14:16:00.890 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7a5c0f-af73-48cc-afcc-5f6d93cbc8ca] Notify connected event to listeners.
14:16:00.956 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:16:01.001 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
14:16:01.175 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 17.416 seconds (JVM running for 18.839)
14:16:01.193 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
14:16:01.197 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
14:16:01.197 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
14:16:01.395 [RMI TCP Connection(3)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:16:01.447 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7a5c0f-af73-48cc-afcc-5f6d93cbc8ca] Receive server push request, request = NotifySubscriberRequest, requestId = 20
14:16:01.469 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7a5c0f-af73-48cc-afcc-5f6d93cbc8ca] Ack server push request, request = NotifySubscriberRequest, requestId = 20
14:22:12.307 [nacos-grpc-client-executor-84] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7a5c0f-af73-48cc-afcc-5f6d93cbc8ca] Receive server push request, request = NotifySubscriberRequest, requestId = 31
14:22:12.308 [nacos-grpc-client-executor-84] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2f7a5c0f-af73-48cc-afcc-5f6d93cbc8ca] Ack server push request, request = NotifySubscriberRequest, requestId = 31
14:27:01.783 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:27:01.788 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:27:02.128 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:27:02.129 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@66e66f1c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:27:02.129 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750832160766_127.0.0.1_7483
14:27:02.132 [nacos-grpc-client-executor-144] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750832160766_127.0.0.1_7483]Ignore complete event,isRunning:false,isAbandon=false
14:27:02.134 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6d887805[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 145]
14:27:02.273 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:27:02.278 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:27:02.294 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:27:02.294 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:27:16.395 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:27:17.439 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ffd8e849-7233-4fdb-8904-54510d6e4720_config-0
14:27:17.518 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
14:27:17.563 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 4 keys and 9 values 
14:27:17.575 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:27:17.587 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:27:17.601 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
14:27:17.612 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:27:17.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffd8e849-7233-4fdb-8904-54510d6e4720_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:27:17.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffd8e849-7233-4fdb-8904-54510d6e4720_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000017b3139fd80
14:27:17.619 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffd8e849-7233-4fdb-8904-54510d6e4720_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000017b313a0000
14:27:17.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffd8e849-7233-4fdb-8904-54510d6e4720_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:27:17.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffd8e849-7233-4fdb-8904-54510d6e4720_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:27:17.631 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffd8e849-7233-4fdb-8904-54510d6e4720_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:27:18.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffd8e849-7233-4fdb-8904-54510d6e4720_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750832838690_127.0.0.1_9963
14:27:18.953 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffd8e849-7233-4fdb-8904-54510d6e4720_config-0] Notify connected event to listeners.
14:27:18.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffd8e849-7233-4fdb-8904-54510d6e4720_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:18.954 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffd8e849-7233-4fdb-8904-54510d6e4720_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000017b3151a6e0
14:27:19.131 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:27:23.537 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:27:23.538 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:27:23.539 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:27:23.872 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:27:24.903 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:27:24.905 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:27:24.906 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:27:29.137 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:27:33.203 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of eead2e59-79d6-45ac-b938-ce5fc23842b7
14:27:33.203 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] RpcClient init label, labels = {module=naming, source=sdk}
14:27:33.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:27:33.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:27:33.208 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:27:33.208 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:27:33.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750832853223_127.0.0.1_10021
14:27:33.340 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] Notify connected event to listeners.
14:27:33.340 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:33.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000017b3151a6e0
14:27:33.409 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:27:33.460 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
14:27:33.640 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 18.186 seconds (JVM running for 19.635)
14:27:33.659 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
14:27:33.662 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
14:27:33.664 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
14:27:33.932 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] Receive server push request, request = NotifySubscriberRequest, requestId = 41
14:27:33.958 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] Ack server push request, request = NotifySubscriberRequest, requestId = 41
14:27:34.081 [RMI TCP Connection(7)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:28:35.789 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] Receive server push request, request = NotifySubscriberRequest, requestId = 49
14:28:35.791 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] Ack server push request, request = NotifySubscriberRequest, requestId = 49
14:30:01.516 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] Receive server push request, request = NotifySubscriberRequest, requestId = 52
14:30:01.536 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eead2e59-79d6-45ac-b938-ce5fc23842b7] Ack server push request, request = NotifySubscriberRequest, requestId = 52
14:30:08.347 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:30:08.352 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:30:08.688 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:30:08.688 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@36b76df1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:30:08.688 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750832853223_127.0.0.1_10021
14:30:08.691 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750832853223_127.0.0.1_10021]Ignore complete event,isRunning:false,isAbandon=false
14:30:08.694 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@367f492b[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 48]
14:30:08.840 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:30:08.844 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:30:08.852 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:30:08.853 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:34:14.237 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:34:15.280 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 82abe41b-7346-4f1a-a05e-d8cf81cfd25a_config-0
14:34:15.374 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 51 ms to scan 1 urls, producing 3 keys and 6 values 
14:34:15.410 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
14:34:15.424 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
14:34:15.441 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
14:34:15.461 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
14:34:15.474 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:34:15.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [82abe41b-7346-4f1a-a05e-d8cf81cfd25a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:34:15.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [82abe41b-7346-4f1a-a05e-d8cf81cfd25a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002801939fd80
14:34:15.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [82abe41b-7346-4f1a-a05e-d8cf81cfd25a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000280193a0000
14:34:15.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [82abe41b-7346-4f1a-a05e-d8cf81cfd25a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:34:15.486 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [82abe41b-7346-4f1a-a05e-d8cf81cfd25a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:34:15.498 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [82abe41b-7346-4f1a-a05e-d8cf81cfd25a_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:34:16.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [82abe41b-7346-4f1a-a05e-d8cf81cfd25a_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750833256642_127.0.0.1_10920
14:34:16.883 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [82abe41b-7346-4f1a-a05e-d8cf81cfd25a_config-0] Notify connected event to listeners.
14:34:16.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [82abe41b-7346-4f1a-a05e-d8cf81cfd25a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:16.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [82abe41b-7346-4f1a-a05e-d8cf81cfd25a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002801951a0a0
14:34:17.040 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:34:22.283 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:34:22.284 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:34:22.284 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:34:22.542 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:34:23.472 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:34:23.474 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:34:23.475 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:34:27.908 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:34:32.285 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c56f3a3a-dc8a-4bc6-b9e1-cb8cf7228ce6
14:34:32.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56f3a3a-dc8a-4bc6-b9e1-cb8cf7228ce6] RpcClient init label, labels = {module=naming, source=sdk}
14:34:32.288 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56f3a3a-dc8a-4bc6-b9e1-cb8cf7228ce6] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:34:32.288 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56f3a3a-dc8a-4bc6-b9e1-cb8cf7228ce6] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:34:32.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56f3a3a-dc8a-4bc6-b9e1-cb8cf7228ce6] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:34:32.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56f3a3a-dc8a-4bc6-b9e1-cb8cf7228ce6] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:34:32.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56f3a3a-dc8a-4bc6-b9e1-cb8cf7228ce6] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750833272302_127.0.0.1_10996
14:34:32.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56f3a3a-dc8a-4bc6-b9e1-cb8cf7228ce6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:32.419 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56f3a3a-dc8a-4bc6-b9e1-cb8cf7228ce6] Notify connected event to listeners.
14:34:32.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56f3a3a-dc8a-4bc6-b9e1-cb8cf7228ce6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002801951a0a0
14:34:32.493 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:34:32.539 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
14:34:32.719 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 19.37 seconds (JVM running for 20.83)
14:34:32.735 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
14:34:32.738 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
14:34:32.739 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
14:34:32.825 [RMI TCP Connection(6)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:34:33.012 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56f3a3a-dc8a-4bc6-b9e1-cb8cf7228ce6] Receive server push request, request = NotifySubscriberRequest, requestId = 58
14:34:33.044 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56f3a3a-dc8a-4bc6-b9e1-cb8cf7228ce6] Ack server push request, request = NotifySubscriberRequest, requestId = 58
14:35:33.764 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56f3a3a-dc8a-4bc6-b9e1-cb8cf7228ce6] Receive server push request, request = NotifySubscriberRequest, requestId = 66
14:35:33.765 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56f3a3a-dc8a-4bc6-b9e1-cb8cf7228ce6] Ack server push request, request = NotifySubscriberRequest, requestId = 66
14:36:52.380 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:36:52.384 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:36:52.706 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:36:52.706 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@779a8d98[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:36:52.707 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750833272302_127.0.0.1_10996
14:36:52.709 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750833272302_127.0.0.1_10996]Ignore complete event,isRunning:false,isAbandon=false
14:36:52.714 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@35efbaf[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 42]
14:36:52.859 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:36:52.865 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:36:52.881 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:36:52.882 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:40:00.214 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:40:01.265 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 59989c2f-7bbe-4938-9ea6-3e3fc03a6049_config-0
14:40:01.363 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
14:40:01.413 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 4 keys and 9 values 
14:40:01.429 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
14:40:01.445 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
14:40:01.460 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
14:40:01.472 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:40:01.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59989c2f-7bbe-4938-9ea6-3e3fc03a6049_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:40:01.478 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59989c2f-7bbe-4938-9ea6-3e3fc03a6049_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000025b1239f268
14:40:01.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59989c2f-7bbe-4938-9ea6-3e3fc03a6049_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000025b1239f488
14:40:01.480 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59989c2f-7bbe-4938-9ea6-3e3fc03a6049_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:40:01.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59989c2f-7bbe-4938-9ea6-3e3fc03a6049_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:40:01.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59989c2f-7bbe-4938-9ea6-3e3fc03a6049_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:40:02.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59989c2f-7bbe-4938-9ea6-3e3fc03a6049_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750833602725_127.0.0.1_11756
14:40:02.969 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59989c2f-7bbe-4938-9ea6-3e3fc03a6049_config-0] Notify connected event to listeners.
14:40:02.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59989c2f-7bbe-4938-9ea6-3e3fc03a6049_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:40:02.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59989c2f-7bbe-4938-9ea6-3e3fc03a6049_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025b125194a8
14:40:03.123 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:40:08.440 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:40:08.445 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:40:08.445 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:40:08.705 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:40:09.694 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:40:09.696 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:40:09.696 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:40:13.665 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:40:17.436 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of *************-497c-b0f7-c8c93e286908
14:40:17.436 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-497c-b0f7-c8c93e286908] RpcClient init label, labels = {module=naming, source=sdk}
14:40:17.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-497c-b0f7-c8c93e286908] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:40:17.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-497c-b0f7-c8c93e286908] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:40:17.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-497c-b0f7-c8c93e286908] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:40:17.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-497c-b0f7-c8c93e286908] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:40:17.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-497c-b0f7-c8c93e286908] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750833617455_127.0.0.1_11842
14:40:17.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-497c-b0f7-c8c93e286908] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:40:17.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-497c-b0f7-c8c93e286908] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025b125194a8
14:40:17.574 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-497c-b0f7-c8c93e286908] Notify connected event to listeners.
14:40:17.651 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:40:17.697 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
14:40:17.859 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 18.586 seconds (JVM running for 20.049)
14:40:17.877 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
14:40:17.880 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
14:40:17.883 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
14:40:18.053 [RMI TCP Connection(6)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:40:18.127 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-497c-b0f7-c8c93e286908] Receive server push request, request = NotifySubscriberRequest, requestId = 72
14:40:18.151 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-497c-b0f7-c8c93e286908] Ack server push request, request = NotifySubscriberRequest, requestId = 72
14:43:36.413 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-497c-b0f7-c8c93e286908] Receive server push request, request = NotifySubscriberRequest, requestId = 80
14:43:36.414 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-497c-b0f7-c8c93e286908] Ack server push request, request = NotifySubscriberRequest, requestId = 80
14:53:01.310 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:53:01.318 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:53:01.646 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:53:01.646 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2be53feb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:53:01.646 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750833617455_127.0.0.1_11842
14:53:01.648 [nacos-grpc-client-executor-165] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750833617455_127.0.0.1_11842]Ignore complete event,isRunning:false,isAbandon=false
14:53:01.651 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5eeb481c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 166]
14:53:02.407 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:53:02.410 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:53:02.419 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:53:02.419 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:54:20.699 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:54:21.757 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a3c31e7d-8601-4e82-afa6-cfb6e4e6ec85_config-0
14:54:21.839 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
14:54:21.870 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:54:21.883 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:54:21.896 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:54:21.915 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
14:54:21.927 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:54:21.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3c31e7d-8601-4e82-afa6-cfb6e4e6ec85_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:54:21.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3c31e7d-8601-4e82-afa6-cfb6e4e6ec85_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001cf1d39fd80
14:54:21.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3c31e7d-8601-4e82-afa6-cfb6e4e6ec85_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001cf1d3a0000
14:54:21.936 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3c31e7d-8601-4e82-afa6-cfb6e4e6ec85_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:54:21.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3c31e7d-8601-4e82-afa6-cfb6e4e6ec85_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:54:21.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3c31e7d-8601-4e82-afa6-cfb6e4e6ec85_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:54:23.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3c31e7d-8601-4e82-afa6-cfb6e4e6ec85_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750834462947_127.0.0.1_14366
14:54:23.180 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3c31e7d-8601-4e82-afa6-cfb6e4e6ec85_config-0] Notify connected event to listeners.
14:54:23.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3c31e7d-8601-4e82-afa6-cfb6e4e6ec85_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:54:23.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3c31e7d-8601-4e82-afa6-cfb6e4e6ec85_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001cf1d51a0a0
14:54:23.391 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:54:27.498 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:54:27.499 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:54:27.499 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:54:27.734 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:54:28.590 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:54:28.593 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:54:28.593 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:54:32.354 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:54:36.011 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ec5df1ec-9197-492d-b6ce-68a1c7a0c8e1
14:54:36.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5df1ec-9197-492d-b6ce-68a1c7a0c8e1] RpcClient init label, labels = {module=naming, source=sdk}
14:54:36.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5df1ec-9197-492d-b6ce-68a1c7a0c8e1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:54:36.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5df1ec-9197-492d-b6ce-68a1c7a0c8e1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:54:36.015 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5df1ec-9197-492d-b6ce-68a1c7a0c8e1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:54:36.015 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5df1ec-9197-492d-b6ce-68a1c7a0c8e1] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:54:36.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5df1ec-9197-492d-b6ce-68a1c7a0c8e1] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750834476029_127.0.0.1_14391
14:54:36.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5df1ec-9197-492d-b6ce-68a1c7a0c8e1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:54:36.148 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5df1ec-9197-492d-b6ce-68a1c7a0c8e1] Notify connected event to listeners.
14:54:36.149 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5df1ec-9197-492d-b6ce-68a1c7a0c8e1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001cf1d51a0a0
14:54:36.261 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:54:36.312 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
14:54:36.506 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 16.746 seconds (JVM running for 18.243)
14:54:36.521 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
14:54:36.526 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
14:54:36.527 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
14:54:36.656 [RMI TCP Connection(1)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:54:36.750 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5df1ec-9197-492d-b6ce-68a1c7a0c8e1] Receive server push request, request = NotifySubscriberRequest, requestId = 89
14:54:36.780 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec5df1ec-9197-492d-b6ce-68a1c7a0c8e1] Ack server push request, request = NotifySubscriberRequest, requestId = 89
14:56:16.348 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:56:16.353 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:56:16.675 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:56:16.676 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2d96f43[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:56:16.676 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750834476029_127.0.0.1_14391
14:56:16.679 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750834476029_127.0.0.1_14391]Ignore complete event,isRunning:false,isAbandon=false
14:56:16.682 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@66d30f7d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 31]
14:56:16.836 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:56:16.841 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:56:16.850 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:56:16.851 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:57:56.665 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:57:57.600 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a2a45332-2bf3-4ac6-878a-67e83618c138_config-0
14:57:57.681 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
14:57:57.715 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:57:57.725 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
14:57:57.747 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 5 values 
14:57:57.760 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:57:57.772 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:57:57.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2a45332-2bf3-4ac6-878a-67e83618c138_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:57:57.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2a45332-2bf3-4ac6-878a-67e83618c138_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000125aa39f268
14:57:57.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2a45332-2bf3-4ac6-878a-67e83618c138_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000125aa39f488
14:57:57.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2a45332-2bf3-4ac6-878a-67e83618c138_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:57:57.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2a45332-2bf3-4ac6-878a-67e83618c138_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:57:57.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2a45332-2bf3-4ac6-878a-67e83618c138_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:57:59.067 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2a45332-2bf3-4ac6-878a-67e83618c138_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750834678835_127.0.0.1_14810
14:57:59.068 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2a45332-2bf3-4ac6-878a-67e83618c138_config-0] Notify connected event to listeners.
14:57:59.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2a45332-2bf3-4ac6-878a-67e83618c138_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:57:59.069 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2a45332-2bf3-4ac6-878a-67e83618c138_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000125aa5194a8
14:57:59.240 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:58:03.327 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:58:03.328 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:58:03.328 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:58:03.550 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:58:04.432 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:58:04.435 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:58:04.436 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:58:09.012 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:58:13.336 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9a94ff1b-b098-4dd7-acce-74aabe6c057d
14:58:13.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a94ff1b-b098-4dd7-acce-74aabe6c057d] RpcClient init label, labels = {module=naming, source=sdk}
14:58:13.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a94ff1b-b098-4dd7-acce-74aabe6c057d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:58:13.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a94ff1b-b098-4dd7-acce-74aabe6c057d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:58:13.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a94ff1b-b098-4dd7-acce-74aabe6c057d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:58:13.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a94ff1b-b098-4dd7-acce-74aabe6c057d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:58:13.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a94ff1b-b098-4dd7-acce-74aabe6c057d] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750834693353_127.0.0.1_14851
14:58:13.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a94ff1b-b098-4dd7-acce-74aabe6c057d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:58:13.482 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a94ff1b-b098-4dd7-acce-74aabe6c057d] Notify connected event to listeners.
14:58:13.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a94ff1b-b098-4dd7-acce-74aabe6c057d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000125aa5194a8
14:58:13.567 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:58:13.618 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
14:58:13.796 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 17.992 seconds (JVM running for 19.34)
14:58:13.815 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
14:58:13.818 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
14:58:13.818 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
14:58:13.932 [RMI TCP Connection(5)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:58:14.054 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a94ff1b-b098-4dd7-acce-74aabe6c057d] Receive server push request, request = NotifySubscriberRequest, requestId = 104
14:58:14.076 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a94ff1b-b098-4dd7-acce-74aabe6c057d] Ack server push request, request = NotifySubscriberRequest, requestId = 104
15:51:01.649 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:51:01.652 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:51:01.987 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:51:01.988 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@26c2e900[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:51:01.988 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750834693353_127.0.0.1_14851
15:51:01.991 [nacos-grpc-client-executor-644] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750834693353_127.0.0.1_14851]Ignore complete event,isRunning:false,isAbandon=false
15:51:01.994 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6e8a694d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 645]
15:51:02.152 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:51:02.158 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:51:02.174 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:51:02.176 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:51:10.046 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:51:11.012 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bc5541df-452d-4826-bf00-5f6c7accae9d_config-0
15:51:11.092 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
15:51:11.132 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
15:51:11.144 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
15:51:11.156 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
15:51:11.167 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
15:51:11.180 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
15:51:11.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc5541df-452d-4826-bf00-5f6c7accae9d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:51:11.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc5541df-452d-4826-bf00-5f6c7accae9d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002083739f8e0
15:51:11.188 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc5541df-452d-4826-bf00-5f6c7accae9d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002083739fb00
15:51:11.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc5541df-452d-4826-bf00-5f6c7accae9d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:51:11.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc5541df-452d-4826-bf00-5f6c7accae9d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:51:11.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc5541df-452d-4826-bf00-5f6c7accae9d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:51:12.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc5541df-452d-4826-bf00-5f6c7accae9d_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750837872209_127.0.0.1_5986
15:51:12.439 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc5541df-452d-4826-bf00-5f6c7accae9d_config-0] Notify connected event to listeners.
15:51:12.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc5541df-452d-4826-bf00-5f6c7accae9d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:51:12.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc5541df-452d-4826-bf00-5f6c7accae9d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000020837519a90
15:51:12.637 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:51:16.635 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
15:51:16.636 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:51:16.636 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:51:16.863 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:51:17.784 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:51:17.786 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:51:17.787 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:51:21.462 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:51:25.443 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a03f84d6-b60a-4f40-bca4-ea85daa69c4a
15:51:25.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a03f84d6-b60a-4f40-bca4-ea85daa69c4a] RpcClient init label, labels = {module=naming, source=sdk}
15:51:25.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a03f84d6-b60a-4f40-bca4-ea85daa69c4a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:51:25.447 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a03f84d6-b60a-4f40-bca4-ea85daa69c4a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:51:25.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a03f84d6-b60a-4f40-bca4-ea85daa69c4a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:51:25.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a03f84d6-b60a-4f40-bca4-ea85daa69c4a] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:51:25.583 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a03f84d6-b60a-4f40-bca4-ea85daa69c4a] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750837885461_127.0.0.1_6021
15:51:25.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a03f84d6-b60a-4f40-bca4-ea85daa69c4a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:51:25.584 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a03f84d6-b60a-4f40-bca4-ea85daa69c4a] Notify connected event to listeners.
15:51:25.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a03f84d6-b60a-4f40-bca4-ea85daa69c4a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000020837519a90
15:51:25.657 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
15:51:25.708 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
15:51:25.876 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 16.693 seconds (JVM running for 17.996)
15:51:25.894 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
15:51:25.900 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
15:51:25.901 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
15:51:26.244 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a03f84d6-b60a-4f40-bca4-ea85daa69c4a] Receive server push request, request = NotifySubscriberRequest, requestId = 112
15:51:26.282 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a03f84d6-b60a-4f40-bca4-ea85daa69c4a] Ack server push request, request = NotifySubscriberRequest, requestId = 112
15:51:26.307 [RMI TCP Connection(5)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:29:41.323 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:29:41.328 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:29:41.665 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:29:41.666 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4ea27001[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:29:41.666 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750837885461_127.0.0.1_6021
16:29:41.669 [nacos-grpc-client-executor-467] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750837885461_127.0.0.1_6021]Ignore complete event,isRunning:false,isAbandon=false
16:29:41.675 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@27a2749[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 468]
16:29:42.026 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:29:42.037 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:29:42.075 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:29:42.079 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
