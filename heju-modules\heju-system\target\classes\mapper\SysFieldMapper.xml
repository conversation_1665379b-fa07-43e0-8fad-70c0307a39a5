<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.heju.system.forms.field.mapper.SysFieldMapper">
    <update id="updateRelatedTable">
        CREATE TRIGGER ${triggerName}
            AFTER UPDATE ON ${quoteSheetApiName}
            FOR EACH ROW
        BEGIN
            UPDATE ${sheetApiName}
            SET ${apiName} = NEW.${quoteApiName}
            WHERE ${relationApiName} = NEW.${relationQuoteApiName};
        END;
    </update>

</mapper>

