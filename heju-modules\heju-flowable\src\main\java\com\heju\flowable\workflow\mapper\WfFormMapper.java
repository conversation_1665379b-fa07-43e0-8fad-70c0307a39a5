package com.heju.flowable.workflow.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.flowable.core.mapper.BaseMapperPlus;
import com.heju.flowable.workflow.domain.WfForm;
import com.heju.flowable.workflow.domain.vo.WfFormVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程表单Mapper接口
 *
 * <AUTHOR>
 * @createTime 2022/3/7 22:07
 */
@Isolate
public interface WfFormMapper extends BaseMapperPlus<WfFormMapper, WfForm, WfFormVo> {

    List<WfFormVo> selectFormVoList(@Param(Constants.WRAPPER) Wrapper<WfForm> queryWrapper);
}
