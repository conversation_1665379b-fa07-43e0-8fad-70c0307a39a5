09:05:57.544 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:05:59.512 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5ca02640-891d-4071-af3c-0118eec7b79d_config-0
09:05:59.653 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 71 ms to scan 1 urls, producing 3 keys and 6 values 
09:05:59.715 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 26 ms to scan 1 urls, producing 4 keys and 9 values 
09:05:59.747 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 28 ms to scan 1 urls, producing 3 keys and 10 values 
09:05:59.776 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 19 ms to scan 1 urls, producing 1 keys and 5 values 
09:05:59.802 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 1 keys and 7 values 
09:05:59.824 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
09:05:59.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ca02640-891d-4071-af3c-0118eec7b79d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:05:59.830 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ca02640-891d-4071-af3c-0118eec7b79d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001e40139a328
09:05:59.831 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ca02640-891d-4071-af3c-0118eec7b79d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001e40139a548
09:05:59.833 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ca02640-891d-4071-af3c-0118eec7b79d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:05:59.835 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ca02640-891d-4071-af3c-0118eec7b79d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:05:59.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ca02640-891d-4071-af3c-0118eec7b79d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:02.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ca02640-891d-4071-af3c-0118eec7b79d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753664762575_127.0.0.1_11247
09:06:02.876 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ca02640-891d-4071-af3c-0118eec7b79d_config-0] Notify connected event to listeners.
09:06:02.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ca02640-891d-4071-af3c-0118eec7b79d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:02.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ca02640-891d-4071-af3c-0118eec7b79d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001e401514688
09:06:03.078 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:06:07.633 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:06:07.634 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:06:07.634 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:06:07.903 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:06:10.513 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:06:14.306 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d827c45a-e9c3-4bab-becf-4a68bd7fbb92
09:06:14.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] RpcClient init label, labels = {module=naming, source=sdk}
09:06:14.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:06:14.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:06:14.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:06:14.315 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:14.449 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] Success to connect to server [localhost:8848] on start up, connectionId = 1753664774327_127.0.0.1_11319
09:06:14.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:14.450 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] Notify connected event to listeners.
09:06:14.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001e401514688
09:06:14.522 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:06:14.572 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:06:14.827 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 18.876 seconds (JVM running for 21.648)
09:06:14.841 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:06:14.842 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:06:14.858 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:06:15.005 [RMI TCP Connection(11)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:06:15.017 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:06:15.037 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:11:42.153 [nacos-grpc-client-executor-78] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:11:42.153 [nacos-grpc-client-executor-78] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:11:45.859 [nacos-grpc-client-executor-81] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:11:45.862 [nacos-grpc-client-executor-81] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d827c45a-e9c3-4bab-becf-4a68bd7fbb92] Ack server push request, request = NotifySubscriberRequest, requestId = 12
11:05:01.368 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:05:01.379 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:05:01.713 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:05:01.713 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@29b52b6b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:05:01.713 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753664774327_127.0.0.1_11319
11:05:01.716 [nacos-grpc-client-executor-1474] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753664774327_127.0.0.1_11319]Ignore complete event,isRunning:false,isAbandon=false
11:05:01.728 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5c930b98[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1475]
11:05:15.249 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:05:15.876 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d19fe08b-a339-475e-86c0-d36a61ea848a_config-0
11:05:15.951 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
11:05:15.977 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
11:05:15.977 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
11:05:15.992 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
11:05:16.008 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
11:05:16.008 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
11:05:16.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19fe08b-a339-475e-86c0-d36a61ea848a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:05:16.024 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19fe08b-a339-475e-86c0-d36a61ea848a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000019d453b78e0
11:05:16.024 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19fe08b-a339-475e-86c0-d36a61ea848a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000019d453b7b00
11:05:16.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19fe08b-a339-475e-86c0-d36a61ea848a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:05:16.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19fe08b-a339-475e-86c0-d36a61ea848a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:05:16.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19fe08b-a339-475e-86c0-d36a61ea848a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:05:16.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19fe08b-a339-475e-86c0-d36a61ea848a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753671916786_127.0.0.1_6821
11:05:16.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19fe08b-a339-475e-86c0-d36a61ea848a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:05:16.994 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19fe08b-a339-475e-86c0-d36a61ea848a_config-0] Notify connected event to listeners.
11:05:16.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19fe08b-a339-475e-86c0-d36a61ea848a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000019d454efb88
11:05:17.184 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:05:19.092 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
11:05:19.092 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:05:19.092 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:05:19.222 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:05:20.584 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:05:21.673 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b43407ef-e868-4f32-abbb-1829ecbad35a
11:05:21.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43407ef-e868-4f32-abbb-1829ecbad35a] RpcClient init label, labels = {module=naming, source=sdk}
11:05:21.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43407ef-e868-4f32-abbb-1829ecbad35a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:05:21.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43407ef-e868-4f32-abbb-1829ecbad35a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:05:21.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43407ef-e868-4f32-abbb-1829ecbad35a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:05:21.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43407ef-e868-4f32-abbb-1829ecbad35a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:05:21.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43407ef-e868-4f32-abbb-1829ecbad35a] Success to connect to server [localhost:8848] on start up, connectionId = 1753671921688_127.0.0.1_6843
11:05:21.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43407ef-e868-4f32-abbb-1829ecbad35a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:05:21.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43407ef-e868-4f32-abbb-1829ecbad35a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000019d454efb88
11:05:21.805 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43407ef-e868-4f32-abbb-1829ecbad35a] Notify connected event to listeners.
11:05:21.873 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
11:05:21.910 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
11:05:22.060 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 7.4 seconds (JVM running for 18.266)
11:05:22.071 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
11:05:22.076 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
11:05:22.076 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
11:05:22.397 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43407ef-e868-4f32-abbb-1829ecbad35a] Receive server push request, request = NotifySubscriberRequest, requestId = 16
11:05:22.416 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b43407ef-e868-4f32-abbb-1829ecbad35a] Ack server push request, request = NotifySubscriberRequest, requestId = 16
19:28:45.074 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:28:45.087 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:28:45.425 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:28:45.428 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@16149104[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:28:45.428 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753671921688_127.0.0.1_6843
19:28:45.430 [nacos-grpc-client-executor-6042] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753671921688_127.0.0.1_6843]Ignore complete event,isRunning:false,isAbandon=false
19:28:45.436 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@41a3a462[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6043]
