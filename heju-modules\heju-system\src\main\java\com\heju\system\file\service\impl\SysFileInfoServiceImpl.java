package com.heju.system.file.service.impl;


import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.authority.domain.dto.SysRoleDto;
import com.heju.system.file.domain.dto.*;
import com.heju.system.file.domain.dto.SysFileClassifyDto;
import com.heju.system.file.domain.dto.SysFileInfoDto;
import com.heju.system.file.domain.dto.SysFileRoleMergeDto;
import com.heju.system.file.domain.po.SysFileRecordPo;
import com.heju.system.file.domain.query.SysFileClassifyQuery;
import com.heju.system.file.domain.query.SysFileInfoQuery;
import com.heju.system.file.domain.query.SysFileRoleMergeQuery;
import com.heju.system.file.manager.ISysFileInfoManager;
import com.heju.system.file.manager.ISysFileRoleMergeManager;
import com.heju.system.file.mapper.SysFileInfoMapper;
import com.heju.system.file.service.*;
import com.heju.system.organize.service.ISysUserService;
import com.heju.system.utils.FileSizeFormatter;
import com.heju.system.utils.MinioUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件信息管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SysFileInfoServiceImpl extends BaseServiceImpl<SysFileInfoQuery, SysFileInfoDto, ISysFileInfoManager> implements ISysFileInfoService {

    @Resource
    private  ISysFileInfoManager sysFileInfoManager;

    @Resource
    private ISysFileRoleMergeManager fileRoleMergeManager;
    @Resource
    private ISysFileRecordService sysFileRecordService;
    @Resource
    private MinioUtil minioUtil;

    @Autowired
    private ISysFileClassifyService sysFileClassifyService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysFileInfoMapper sysFileInfoMapper;


    @Override
    public List<SysFileInfoDto> selectListScope(SysFileInfoQuery fileInfo) {
        //塞角色id
        List<SysRoleDto> roles = SecurityUtils.getUser().getRoles();
        Long[] roleIdsArray = roles.stream().map(SysRoleDto::getId).toArray(Long[]::new);
        fileInfo.setRoleIds(roleIdsArray);
        //塞创建人id
        fileInfo.setCreateBy(SecurityUtils.getUserId());
        //查数据
        List<SysFileInfoDto> sysFileInfoDtos = new ArrayList<>();
        if (SecurityUtils.getUser().isAdmin()) {
            sysFileInfoDtos = sysFileInfoManager.selectAdminList(fileInfo);
        } else {
            sysFileInfoDtos = sysFileInfoManager.selectByQuery(fileInfo);
        }
        //数据处理
        for (SysFileInfoDto sysFileInfoDto : sysFileInfoDtos) {
            String bytes = FileSizeFormatter.formatBytes(sysFileInfoDto.getSize());
            sysFileInfoDto.setFileSize(bytes);
            //如果是创建人  返回   4
           if(sysFileInfoDto.getCreateBy().equals(SecurityUtils.getUserId())
                   || SecurityUtils.getUser().isAdmin()){
               sysFileInfoDto.setOperateType(NumberUtil.Four);//能看，能下载，能删除
           }else {
               //不是创建人  操作类型不为 null
               if(sysFileInfoDto.getOperateTypes()!=null) {
                   String operateTypes = sysFileInfoDto.getOperateTypes();
                   if(operateTypes.contains("3") || (operateTypes.contains("1") && operateTypes.contains("2"))){
                       sysFileInfoDto.setOperateType(NumberUtil.Three);//能看，能下载
                   }else if(operateTypes.contains("1")){
                       sysFileInfoDto.setOperateType(NumberUtil.One);
                   }else if(operateTypes.contains("2")){
                       sysFileInfoDto.setOperateType(NumberUtil.Two);
                   }
               }else{
                   if(sysFileInfoDto.getIsDownloads()!=null && sysFileInfoDto.getIsDownloads().contains("1") && sysFileInfoDto.getIsViews()!=null && sysFileInfoDto.getIsViews().contains("1")){
                       sysFileInfoDto.setOperateType(NumberUtil.Three);//能看，能下载
                   }else if(sysFileInfoDto.getIsDownloads()!=null && sysFileInfoDto.getIsDownloads().contains("1")){
                       sysFileInfoDto.setOperateType(NumberUtil.Two);//能下载
                   }else if(sysFileInfoDto.getIsViews()!=null && sysFileInfoDto.getIsViews().contains("1"))
                       sysFileInfoDto.setOperateType(NumberUtil.One);//能看
               }
           }
        }
        return sysFileInfoDtos;
    }

    @Override
    public Integer insertFile(MultipartFile file, SysFileInfoDto fileInfo) {
        //上传文件
        try {
            String name = file.getOriginalFilename();
            Long size = file.getSize();
            fileInfo.setName(name);
            fileInfo.setSize(size);
            //上传文件
            String fileUrl = minioUtil.upload(file);
            fileInfo.setUrl(fileUrl);
            int row = baseManager.insert(fileInfo);
            if(row>0){
                String[] viewRole=new String[]{};
                if(fileInfo.getViewRoleIds()!=null) {
                    viewRole = fileInfo.getViewRoleIds().split(",");
                }
                String[] downloadRole=new String[]{};
                if(fileInfo.getDownloadRoleIds()!=null) {
                    downloadRole = fileInfo.getDownloadRoleIds().split(",");
                }
                //保存角色-文件关联表
                baseManager.insertFileRole(viewRole,downloadRole,fileInfo.getId());

                SysFileRecordDto recordDto = new SysFileRecordDto();
                recordDto.setFileId(fileInfo.getId());
                recordDto.setOperateType(NumberUtil.One);
                sysFileRecordService.insert(recordDto);
                return row;
            }else{
                throw new RuntimeException();
            }
        }catch (Exception e){
            return null;
        }
    }

    /**
     * 修改数据对象
     *
     * @param fileInfo 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int update(SysFileInfoDto fileInfo) {
        SysFileInfoDto originDto = selectById(fileInfo.getId());
        startHandle(OperateConstants.ServiceType.EDIT, originDto, fileInfo);
        int row = baseManager.update(fileInfo);
        fileRoleMergeManager.deleteByFileId(fileInfo.getId());
        String[] viewRole=new String[]{};
        if(fileInfo.getViewRoleIds()!=null) {
            viewRole = fileInfo.getViewRoleIds().split(",");
        }
        String[] downloadRole=new String[]{};
        if(fileInfo.getDownloadRoleIds()!=null) {
            downloadRole = fileInfo.getDownloadRoleIds().split(",");
        }
        baseManager.insertFileRole(viewRole,downloadRole,fileInfo.getId());

        SysFileRecordDto recordDto = new SysFileRecordDto();
        recordDto.setFileId(fileInfo.getId());
        recordDto.setOperateType(NumberUtil.Two);
        sysFileRecordService.insert(recordDto);

        endHandle(OperateConstants.ServiceType.EDIT, row, originDto, fileInfo);
        return row;
    }

    /**
     * 根据Id查询单条数据对象
     *
     * @param id Id
     * @return 数据对象
     */
    @Override
    public SysFileInfoDto selectById(Serializable id) {
        SysFileInfoDto dto = sysFileInfoManager.selectInfoById(id);

        String bytes = FileSizeFormatter.formatBytes(dto.getSize());
        dto.setFileSize(bytes);

        SysFileRoleMergeQuery query=new SysFileRoleMergeQuery();
        query.setFileId(dto.getId());
        List<SysFileRoleMergeDto> fileRoleMergeDtos = fileRoleMergeManager.selectList(query);
        //TODO 最高权限为3，拆分成1，2
        if(dto.getRoleViewIds() == null) {
            dto.setRoleViewIds(new ArrayList<>());
        }
        if(dto.getRoleDownloadIds() == null) {
            dto.setRoleDownloadIds(new ArrayList<>());
        }
        for (SysFileRoleMergeDto fileRoleMergeDto : fileRoleMergeDtos) {
            Long roleId = fileRoleMergeDto.getRoleId();
            if (fileRoleMergeDto.getOperateType().equals(NumberUtil.One)){
                dto.getRoleViewIds().add(roleId);
            }
            if (fileRoleMergeDto.getOperateType().equals(NumberUtil.Two)){
                dto.getRoleDownloadIds().add(roleId);
            }
            if (fileRoleMergeDto.getOperateType().equals(NumberUtil.Three)){
                dto.getRoleViewIds().add(roleId);
                dto.getRoleDownloadIds().add(roleId);
            }
        }
        return subCorrelates(dto);
    }

    /**
     * 回收站列表
     * @param query
     * @return
     */
    @Override
    public List<SysFileInfoDto> selectrecyclList(SysFileInfoQuery query) {
        if (SecurityUtils.getUser().isNotAdmin()) {
            query.setCreateBy(SecurityUtils.getUserId());
        }
//        List<SysFileInfoDto> sysFileInfoDtos = baseManager.selectList(query);
        List<SysFileInfoDto> sysFileInfoDtos = baseManager.selectRecycList(query);
//        List<SysFileClassifyDto> sysFileClassifyDtos = sysFileClassifyService.selectList(new SysFileClassifyQuery());
//        List<SysFilePositionDto> sysFilePositionDtos = sysFilePositionService.selectList(new SysFilePositionQuery());
//        List<SysUserDto> sysUserDtos = userService.selectList(new SysUserQuery());
//        Map<Long, String> sysFileClassifyMap = sysFileClassifyDtos.stream().collect(Collectors.toMap(SysFileClassifyDto::getId, SysFileClassifyDto::getName, (v1, v2) -> v1));
//        Map<Long, String> sysFilePositionMap = sysFilePositionDtos.stream().collect(Collectors.toMap(SysFilePositionDto::getId, SysFilePositionDto::getName, (v1, v2) -> v1));
//        Map<Long, String> sysUserMap = sysUserDtos.stream().collect(Collectors.toMap(SysUserDto::getId, SysUserDto::getUserName, (v1, v2) -> v1));
//
        for (SysFileInfoDto sysFileInfoDto : sysFileInfoDtos) {
            String bytes = FileSizeFormatter.formatBytes(sysFileInfoDto.getSize());
            sysFileInfoDto.setFileSize(bytes);
//            if(sysFileInfoDto.getClassifyId()!=null){
//                sysFileInfoDto.setClassifyName(sysFileClassifyMap.get(sysFileInfoDto.getClassifyId()));
//            }
//            if(sysFileInfoDto.getPositionId()!=null){
//                sysFileInfoDto.setPositionName(sysFilePositionMap.get(sysFileInfoDto.getPositionId()));
//            }
//            if(sysFileInfoDto.getCreateBy()!=null){
//                sysFileInfoDto.setCreateName(sysUserMap.get(sysFileInfoDto.getCreateBy()));
//            }
        }
        return sysFileInfoDtos;
    }

    /**
     * 回收站批量恢复
     * @param fileIds
     * @return
     */
    @Override
    public AjaxResult batchReplyList(List<Long> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return AjaxResult.error("请选择要恢复的文件");
        }
        int row = sysFileInfoMapper.replyByIds(fileIds);

        //操作记录
        List<SysFileRecordDto> records = fileIds.stream()
                .map(id -> {
                    SysFileRecordDto dto = new SysFileRecordDto();
                    dto.setFileId(id);
                    dto.setOperateType(NumberUtil.Six); // 回收站恢复
                    return dto;
                })
                .collect(Collectors.toList());

        sysFileRecordService.insertBatch(records);
        return row > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 暂存管理列表
     * @param fileInfo
     * @return
     */
    @Override
    public List<SysFileInfoDto> selectTempStorageList(SysFileInfoQuery fileInfo) {
        List<SysFileInfoDto> SysFileInfoDtos = baseManager.tempStorageList(fileInfo);
        List<SysFileClassifyDto> sysFileClassifyDtos = sysFileClassifyService.selectList(new SysFileClassifyQuery());
        Map<Long, String> sysFileClassifyMap = sysFileClassifyDtos.stream().collect(Collectors.toMap(SysFileClassifyDto::getId, SysFileClassifyDto::getName, (v1, v2) -> v1));
        for (SysFileInfoDto sysFileInfoDto : SysFileInfoDtos) {
            String bytes = FileSizeFormatter.formatBytes(sysFileInfoDto.getSize());
            sysFileInfoDto.setFileSize(bytes);
            if(sysFileInfoDto.getClassifyId()!=null) {
                sysFileInfoDto.setClassifyName(sysFileClassifyMap.get(sysFileInfoDto.getClassifyId()));
            }
        }
        return SysFileInfoDtos;
    }

    /**
     * 回收站批量删除
     */
    @Override
    public AjaxResult batchDeleteByIds(List<Long> idList) {

        List<SysFileInfoDto> files = sysFileInfoMapper.selectDeleteByIdList(idList);
        if (files == null || files.isEmpty()) {
            return AjaxResult.error("未找到可删除的文件");
        }

//        for (SysFileInfoDto file : files) {
//            //插入删除时间及操作人
//            file.setDeleteTime(LocalDateTime.now());
//            file.setDeleteBy(SecurityUtils.getUser().getCreateBy());
//            sysFileInfoManager.update(file);
//        }
        int row = sysFileInfoMapper.deleteByIds(idList);
        // 3. 删除 minio 文件
        for (SysFileInfoDto file: files) {
            if (file.getUrl() != null) {
                try {
                    // 截取 minio 文件路径
                    String bucketName = "defalut-bucket";
                    String fileName = file.getUrl().substring(file.getUrl().lastIndexOf(bucketName)+ bucketName.length() + 1);
                    minioUtil.delete(fileName);
                } catch (Exception e) {
                    // 记录日志
                    log.error("删除文件失败，fileId: {}, url: {}", file.getId(), file.getUrl(), e);
                }
            }
        }
        return row > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 暂存批量删除
     * @param idList
     * @return
     */
    @Override
    public AjaxResult removeStorageByIds(List<Long> idList) {
        if (idList == null || idList.isEmpty()) {
            return AjaxResult.error("请选择要删除的数据");
        }
        //设置删除人、删除时间
        List<SysFileInfoDto> originList = selectListByIds(idList);
        for (SysFileInfoDto dto : originList) {
            dto.setDeleteTime(LocalDateTime.now());
            dto.setDeleteBy(SecurityUtils.getUser().getCreateBy());
        }
        baseManager.updateBatch(originList);
        //删除文件
        int row = sysFileInfoMapper.deleteStorageByIds(idList);
        //操作记录
        List<SysFileRecordDto> records = idList.stream()
                .map(id -> {
                    SysFileRecordDto dto = new SysFileRecordDto();
                    dto.setFileId(id);
                    dto.setOperateType(NumberUtil.Five); // 删除操作类型
                    return dto;
                })
                .collect(Collectors.toList());

        // 3. 批量插入操作记录（提升性能）
        sysFileRecordService.insertBatch(records);
        return row > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 文件管理批量删除
     */
    /**
     * 根据Id删除数据对象（批量）
     *
     * @param idList Id集合
     * @return 结果
     */
    @Override
    @DSTransactional
    public int deleteByIds(List<Long> idList) {
        List<SysFileInfoDto> originList = selectListByIds(idList);
        startBatchHandle(OperateConstants.ServiceType.BATCH_DELETE, originList, null);
        //设置删除人、删除时间
        LocalDateTime now = LocalDateTime.now();
        for (SysFileInfoDto dto : originList) {
            dto.setDeleteTime(now);
            dto.setDeleteBy(SecurityUtils.getUser().getCreateBy());
        }
        baseManager.updateBatch(originList);
        //删除文件
        int rows = baseManager.deleteByIds(idList);
        //插入操作记录
        List<SysFileRecordDto> recordList = new ArrayList<>();
        if (originList !=null && !originList.isEmpty()) {
            for (SysFileInfoDto fileInfo : originList) {
                SysFileRecordDto recordDto = new SysFileRecordDto();
                recordDto.setFileId(fileInfo.getId());
                recordDto.setOperateType(NumberUtil.Five);
                recordList.add(recordDto);
            }
            sysFileRecordService.insertBatch(recordList);
        }
        endBatchHandle(OperateConstants.ServiceType.BATCH_DELETE, rows, originList, null);
        return rows;
    }

    /**
     * 查询操作 新增操作记录
     * @param id  文件id
     */
    @Override
    public AjaxResult insertView(Long id) {
        SysFileRecordDto recordDto = new SysFileRecordDto();
        recordDto.setFileId(id);
        recordDto.setOperateType(NumberUtil.Three);
        return sysFileRecordService.insert(recordDto) > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 定时任务 删除过期 30 天文件
     * @return 删除文件数量
     */
    @Override
    public int deleteExpiredFiles() {
        // 1. 先查询超出 30 天的文件信息 找出文件 id
        List<SysFileInfoDto> files = sysFileInfoMapper.selectExpiredFiles();

        if (files.isEmpty()) return 0;
        List<Long> idList = files.stream().map(SysFileInfoDto::getId).toList();
        // 2. 调用 sysFileInfoMapper 封装方法 删除数据库文件
        sysFileInfoMapper.deleteByIds(idList);

        // 3. 删除 minio 文件
        for (SysFileInfoDto file: files) {
            if (file.getUrl() != null) {
                try {
                    // 截取 minio 文件路径
                    String bucketName = "defalut-bucket";
                    String fileName = file.getUrl().substring(file.getUrl().lastIndexOf(bucketName)+ bucketName.length() + 1);
                    // System.out.println(fileName);
                    minioUtil.delete(fileName);
                } catch (Exception e) {
                    // 记录日志
                    log.error("删除文件失败，fileId: {}, url: {}", file.getId(), file.getUrl(), e);
                }
            }
        }
        return idList.size();
    }

    /**
     * 单文件下载
     * @param id 文件id
     * @param response http请求response
     */
    @Override
    public void download(Long id, HttpServletResponse response) {
        try {
            // 1. 查询文件信息
            SysFileInfoDto file = selectById(id);
            if (file == null) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\": 500, \"msg\": \"文件不存在\"}");
                return;
            }
            // 2. 设置响应头
            // 截取 minio 文件路径
            String bucketName = "defalut-bucket";
            // minio 所需路径
            String key = file.getUrl().substring(file.getUrl().lastIndexOf(bucketName)+ bucketName.length() + 1);
            String fileName = key.substring(key.lastIndexOf("/") + 1);
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            // 3. 从 minio 下载文件流
            try (InputStream downloadStream = minioUtil.download(key)) {
                byte[] buffer = new byte[4096];
                int len;
                while ((len = downloadStream.read(buffer)) > 0) {
                    response.getOutputStream().write(buffer, 0, len);
                }
                response.getOutputStream().flush();
            }
        } catch (Exception e) {
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\": 500, \"msg\": \"下载失败:" + e.getMessage() + "\"}");
            } catch (Exception ignored) {}
        }
    }

    /**
     * 多文件批量下载
     * @param idList 文件id集合
     * @param response http请求response
     */
    @Override
    public void batchDownload(List<Long> idList, HttpServletResponse response) {
        try {
            // 1. 查询文件信息
            List<SysFileInfoDto> files = baseManager.selectListByIds(idList);
            if (files == null || files.isEmpty()) {
                response.setContentType("application/json");
                response.getWriter().write("{\"code\": 500, \"msg\": \"文件不存在\"}");
                return;
            }

            // 2. 设置响应头
            // zipFileName = 时间戳 + 当前用户名称
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String zipFileName = simpleDateFormat.format(new Date(System.currentTimeMillis())) + "_" + SecurityUtils.getUser().getUserName();
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + zipFileName + ".zip\"");

            // 3. 打包 zip
            try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
                for (SysFileInfoDto file : files) {
                    // 截取 minio 文件路径
                    String bucketName = "defalut-bucket";
                    // minio 所需路径
                    String key = file.getUrl().substring(file.getUrl().lastIndexOf(bucketName)+ bucketName.length() + 1);
                    String entryName = key.substring(key.lastIndexOf("/") + 1);
                    // 从 minio 下载文件流
                    InputStream downloadStream = minioUtil.download(key);
                    zos.putNextEntry(new ZipEntry(entryName));
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = downloadStream.read(buffer)) > 0) {
                        zos.write(buffer, 0, len);
                    }
                    zos.closeEntry();
                    downloadStream.close();
                }
                zos.finish();
            }
        } catch (Exception e) {
            try {
                response.setContentType("application/json");
                response.getWriter().write("{\"code\": 500, \"msg\": \"下载失败\"}");
            } catch (Exception ignored) {}
        }
    }


}
