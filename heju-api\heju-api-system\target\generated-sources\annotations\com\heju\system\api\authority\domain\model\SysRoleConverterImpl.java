package com.heju.system.api.authority.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.api.authority.domain.dto.SysRoleDto;
import com.heju.system.api.authority.domain.po.SysRolePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:51+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysRoleConverterImpl implements SysRoleConverter {

    @Override
    public SysRoleDto mapperDto(SysRolePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysRoleDto sysRoleDto = new SysRoleDto();

        sysRoleDto.setId( arg0.getId() );
        sysRoleDto.setSourceName( arg0.getSourceName() );
        sysRoleDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysRoleDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysRoleDto.setStatus( arg0.getStatus() );
        sysRoleDto.setSort( arg0.getSort() );
        sysRoleDto.setRemark( arg0.getRemark() );
        sysRoleDto.setCreateBy( arg0.getCreateBy() );
        sysRoleDto.setCreateTime( arg0.getCreateTime() );
        sysRoleDto.setUpdateBy( arg0.getUpdateBy() );
        sysRoleDto.setUpdateTime( arg0.getUpdateTime() );
        sysRoleDto.setDelFlag( arg0.getDelFlag() );
        sysRoleDto.setCreateName( arg0.getCreateName() );
        sysRoleDto.setUpdateName( arg0.getUpdateName() );
        sysRoleDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysRoleDto.setCode( arg0.getCode() );
        sysRoleDto.setName( arg0.getName() );
        sysRoleDto.setRoleKey( arg0.getRoleKey() );
        sysRoleDto.setDataScope( arg0.getDataScope() );
        sysRoleDto.setRoleGroupId( arg0.getRoleGroupId() );
        sysRoleDto.setIsRoleGroup( arg0.getIsRoleGroup() );

        return sysRoleDto;
    }

    @Override
    public List<SysRoleDto> mapperDto(Collection<SysRolePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysRoleDto> list = new ArrayList<SysRoleDto>( arg0.size() );
        for ( SysRolePo sysRolePo : arg0 ) {
            list.add( mapperDto( sysRolePo ) );
        }

        return list;
    }

    @Override
    public Page<SysRoleDto> mapperPageDto(Collection<SysRolePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysRoleDto> page = new Page<SysRoleDto>();
        for ( SysRolePo sysRolePo : arg0 ) {
            page.add( mapperDto( sysRolePo ) );
        }

        return page;
    }

    @Override
    public SysRolePo mapperPo(SysRoleDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysRolePo sysRolePo = new SysRolePo();

        sysRolePo.setId( arg0.getId() );
        sysRolePo.setSourceName( arg0.getSourceName() );
        sysRolePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysRolePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysRolePo.setStatus( arg0.getStatus() );
        sysRolePo.setSort( arg0.getSort() );
        sysRolePo.setRemark( arg0.getRemark() );
        sysRolePo.setCreateBy( arg0.getCreateBy() );
        sysRolePo.setCreateTime( arg0.getCreateTime() );
        sysRolePo.setUpdateBy( arg0.getUpdateBy() );
        sysRolePo.setUpdateTime( arg0.getUpdateTime() );
        sysRolePo.setDelFlag( arg0.getDelFlag() );
        sysRolePo.setCreateName( arg0.getCreateName() );
        sysRolePo.setUpdateName( arg0.getUpdateName() );
        sysRolePo.setEnterpriseId( arg0.getEnterpriseId() );
        sysRolePo.setCode( arg0.getCode() );
        sysRolePo.setName( arg0.getName() );
        sysRolePo.setRoleKey( arg0.getRoleKey() );
        sysRolePo.setDataScope( arg0.getDataScope() );
        sysRolePo.setRoleGroupId( arg0.getRoleGroupId() );
        sysRolePo.setIsRoleGroup( arg0.getIsRoleGroup() );

        return sysRolePo;
    }

    @Override
    public List<SysRolePo> mapperPo(Collection<SysRoleDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysRolePo> list = new ArrayList<SysRolePo>( arg0.size() );
        for ( SysRoleDto sysRoleDto : arg0 ) {
            list.add( mapperPo( sysRoleDto ) );
        }

        return list;
    }

    @Override
    public Page<SysRolePo> mapperPagePo(Collection<SysRoleDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysRolePo> page = new Page<SysRolePo>();
        for ( SysRoleDto sysRoleDto : arg0 ) {
            page.add( mapperPo( sysRoleDto ) );
        }

        return page;
    }
}
