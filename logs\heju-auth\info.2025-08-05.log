09:15:21.930 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:15:23.130 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 774fe8e1-5ad0-40d2-9983-cd0ec87c5fe7_config-0
09:15:23.251 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 68 ms to scan 1 urls, producing 3 keys and 6 values 
09:15:23.322 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 29 ms to scan 1 urls, producing 4 keys and 9 values 
09:15:23.340 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 3 keys and 10 values 
09:15:23.364 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 1 keys and 5 values 
09:15:23.382 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 7 values 
09:15:23.407 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
09:15:23.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [774fe8e1-5ad0-40d2-9983-cd0ec87c5fe7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:15:23.415 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [774fe8e1-5ad0-40d2-9983-cd0ec87c5fe7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000226813b4fb8
09:15:23.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [774fe8e1-5ad0-40d2-9983-cd0ec87c5fe7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000226813b51d8
09:15:23.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [774fe8e1-5ad0-40d2-9983-cd0ec87c5fe7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:15:23.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [774fe8e1-5ad0-40d2-9983-cd0ec87c5fe7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:15:23.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [774fe8e1-5ad0-40d2-9983-cd0ec87c5fe7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:15:26.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [774fe8e1-5ad0-40d2-9983-cd0ec87c5fe7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754356525743_127.0.0.1_8765
09:15:26.075 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [774fe8e1-5ad0-40d2-9983-cd0ec87c5fe7_config-0] Notify connected event to listeners.
09:15:26.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [774fe8e1-5ad0-40d2-9983-cd0ec87c5fe7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:15:26.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [774fe8e1-5ad0-40d2-9983-cd0ec87c5fe7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000226814ed418
09:15:26.255 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:15:31.027 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:15:31.030 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:15:31.031 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:15:31.445 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:15:34.805 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:15:38.954 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e5fde57a-5559-4116-abdf-f259b8ade287
09:15:38.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] RpcClient init label, labels = {module=naming, source=sdk}
09:15:38.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:15:38.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:15:38.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:15:38.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:15:39.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Success to connect to server [localhost:8848] on start up, connectionId = 1754356538976_127.0.0.1_8951
09:15:39.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Notify connected event to listeners.
09:15:39.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:15:39.115 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000226814ed418
09:15:39.231 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:15:39.291 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:15:39.672 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 19.16 seconds (JVM running for 23.626)
09:15:39.696 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:15:39.698 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:15:39.706 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:15:40.149 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:15:40.175 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:25:01.666 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:25:10.677 [nacos-grpc-client-executor-125] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:25:10.677 [nacos-grpc-client-executor-125] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:25:12.849 [nacos-grpc-client-executor-128] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:25:12.850 [nacos-grpc-client-executor-128] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 12
14:32:56.326 [nacos-grpc-client-executor-3988] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 18
14:32:56.341 [nacos-grpc-client-executor-3988] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 18
14:33:42.776 [nacos-grpc-client-executor-3997] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 22
14:33:42.790 [nacos-grpc-client-executor-3997] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 22
14:38:16.399 [nacos-grpc-client-executor-4053] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 27
14:38:16.411 [nacos-grpc-client-executor-4053] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 27
14:38:41.485 [nacos-grpc-client-executor-4058] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 31
14:38:41.497 [nacos-grpc-client-executor-4058] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 31
14:44:16.839 [nacos-grpc-client-executor-4131] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 36
14:44:16.850 [nacos-grpc-client-executor-4131] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 36
14:44:36.535 [nacos-grpc-client-executor-4135] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 40
14:44:36.558 [nacos-grpc-client-executor-4135] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 40
15:04:59.980 [nacos-grpc-client-executor-4390] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 45
15:04:59.996 [nacos-grpc-client-executor-4390] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 45
15:05:18.793 [nacos-grpc-client-executor-4394] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 49
15:05:18.808 [nacos-grpc-client-executor-4394] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 49
16:16:59.190 [nacos-grpc-client-executor-5288] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 55
16:16:59.220 [nacos-grpc-client-executor-5288] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 55
16:17:34.710 [nacos-grpc-client-executor-5295] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 60
16:17:34.727 [nacos-grpc-client-executor-5295] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 60
17:29:13.319 [nacos-grpc-client-executor-6184] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 64
17:29:13.336 [nacos-grpc-client-executor-6184] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 64
17:29:39.262 [nacos-grpc-client-executor-6189] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 68
17:29:39.276 [nacos-grpc-client-executor-6189] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 68
17:34:13.034 [nacos-grpc-client-executor-6248] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 73
17:34:13.051 [nacos-grpc-client-executor-6248] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 73
17:34:34.195 [nacos-grpc-client-executor-6252] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 78
17:34:34.208 [nacos-grpc-client-executor-6252] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 78
17:36:17.343 [nacos-grpc-client-executor-6273] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 82
17:36:17.347 [nacos-grpc-client-executor-6273] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 82
17:36:44.782 [nacos-grpc-client-executor-6279] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 86
17:36:44.799 [nacos-grpc-client-executor-6279] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 86
17:44:20.959 [nacos-grpc-client-executor-6373] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 91
17:44:20.992 [nacos-grpc-client-executor-6373] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 91
17:44:51.420 [nacos-grpc-client-executor-6380] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 95
17:44:51.433 [nacos-grpc-client-executor-6380] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 95
17:45:53.309 [nacos-grpc-client-executor-6393] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 100
17:45:53.326 [nacos-grpc-client-executor-6393] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 100
17:46:11.704 [nacos-grpc-client-executor-6398] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 105
17:46:11.721 [nacos-grpc-client-executor-6398] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 105
17:48:47.126 [nacos-grpc-client-executor-6430] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 109
17:48:47.145 [nacos-grpc-client-executor-6430] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 109
17:49:08.862 [nacos-grpc-client-executor-6435] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Receive server push request, request = NotifySubscriberRequest, requestId = 113
17:49:08.880 [nacos-grpc-client-executor-6435] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5fde57a-5559-4116-abdf-f259b8ade287] Ack server push request, request = NotifySubscriberRequest, requestId = 113
20:24:48.807 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:24:48.814 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:24:49.147 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:24:49.147 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@60eb9a06[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:24:49.147 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754356538976_127.0.0.1_8951
20:24:49.149 [nacos-grpc-client-executor-8302] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754356538976_127.0.0.1_8951]Ignore complete event,isRunning:false,isAbandon=false
20:24:49.159 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@60d89062[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 8303]
