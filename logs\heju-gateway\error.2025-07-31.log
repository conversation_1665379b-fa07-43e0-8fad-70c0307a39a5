11:08:59.319 [reactor-http-nio-3] ERROR c.h.g.f.<PERSON><PERSON><PERSON><PERSON> - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/enterprise/getInfo
11:09:00.243 [reactor-http-nio-4] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/user/getInfo
11:33:52.717 [reactor-http-nio-2] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:Connection prematurely closed BEFORE response
11:33:52.717 [reactor-http-nio-4] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:Connection prematurely closed BEFORE response
11:36:18.804 [boundedElastic-68] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
11:36:19.114 [boundedElastic-68] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
11:36:19.116 [boundedElastic-68] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
11:39:11.793 [reactor-http-nio-17] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:Connection prematurely closed BEFORE response
14:57:57.579 [reactor-http-nio-10] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/enterprises/list
15:07:35.049 [boundedElastic-5] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/option/value/getValueList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
15:07:35.440 [boundedElastic-5] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/option/value/getValueList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
16:54:33.912 [boundedElastic-82] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
16:54:33.945 [boundedElastic-82] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
16:54:34.220 [boundedElastic-82] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/enterprises/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
17:25:10.258 [reactor-http-nio-16] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:Connection prematurely closed BEFORE response
17:41:21.588 [reactor-http-nio-9] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:Connection refused: no further information: /192.168.1.43:9600
17:41:21.589 [reactor-http-nio-8] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:Connection refused: no further information: /192.168.1.43:9600
17:41:21.851 [reactor-http-nio-10] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/enterprises/list,异常信息:Connection refused: no further information: /192.168.1.43:9600
20:32:17.011 [boundedElastic-244] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/kkfileview/onlinePreview,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for kkfileview"
