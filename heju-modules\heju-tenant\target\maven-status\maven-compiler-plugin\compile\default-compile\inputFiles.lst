D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\domain\model\TeTenantRegister.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\source\service\impl\TeSourceServiceImpl.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\service\ITeInviteRegisterService.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\controller\TeTenantApprovalController.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\HeJuTenantApplication.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\manager\ITeTenantManager.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\service\impl\TeTenantApprovalServiceImpl.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\service\impl\TeTenantServiceImpl.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\config\DatabaseConfig.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\manager\impl\TeStrategyManagerImpl.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\manager\impl\TeTenantApprovalManager.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\utils\UpdateCreator.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\source\manager\ITeSourceManager.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\utils\TableCreator.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\service\ITeTenantApprovalService.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\source\controller\TeSourceController.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\controller\TeTenantController.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\mapper\TeInviteRegisterMapper.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\mapper\TeStrategyMapper.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\manager\impl\TeTenantManagerImpl.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\service\impl\TeInviteRegisterServiceImpl.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\manager\ITeStrategyManager.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\mapper\TeTenantApprovalMapper.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\utils\DefaultMenu.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\controller\SysInviteRegister.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\utils\MySQLDatabaseUtil.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\manager\impl\TeInviteRegisterManagerImpl.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\manager\ITeInviteRegisterManager.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\service\impl\TeStrategyServiceImpl.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\service\ITeStrategyService.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\utils\TriggerCreator.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\controller\TeStrategyController.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\service\ITeTenantService.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\source\manager\impl\TeSourceManagerImpl.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\source\service\ITeSourceService.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\mapper\TeTenantMapper.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\source\mapper\TeSourceMapper.java
D:\devapps\Projects\heju\HeJu-Multisaas\heju-modules\heju-tenant\src\main\java\com\heju\tenant\tenant\manager\ITeTenantApprovalManager.java
