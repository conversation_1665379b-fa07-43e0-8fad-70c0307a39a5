09:50:08.510 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:50:09.439 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e11907ea-2993-49bf-91d9-3edbcf00103f_config-0
09:50:09.533 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 48 ms to scan 1 urls, producing 3 keys and 6 values 
09:50:09.579 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:50:09.590 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:50:09.602 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:50:09.613 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:50:09.624 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:50:09.627 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11907ea-2993-49bf-91d9-3edbcf00103f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:50:09.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11907ea-2993-49bf-91d9-3edbcf00103f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001e1813b6af8
09:50:09.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11907ea-2993-49bf-91d9-3edbcf00103f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001e1813b6d18
09:50:09.630 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11907ea-2993-49bf-91d9-3edbcf00103f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:50:09.630 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11907ea-2993-49bf-91d9-3edbcf00103f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:50:09.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11907ea-2993-49bf-91d9-3edbcf00103f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:50:10.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11907ea-2993-49bf-91d9-3edbcf00103f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752025810433_127.0.0.1_8160
09:50:10.683 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11907ea-2993-49bf-91d9-3edbcf00103f_config-0] Notify connected event to listeners.
09:50:10.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11907ea-2993-49bf-91d9-3edbcf00103f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:50:10.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11907ea-2993-49bf-91d9-3edbcf00103f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001e1814f0668
09:50:10.859 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:50:15.387 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:50:15.387 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:50:15.387 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:50:15.577 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:50:16.620 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:50:16.622 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:50:16.623 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:50:26.049 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:50:29.155 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 21cfef1e-b07f-4d1f-818d-9bb35afc1b41
09:50:29.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21cfef1e-b07f-4d1f-818d-9bb35afc1b41] RpcClient init label, labels = {module=naming, source=sdk}
09:50:29.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21cfef1e-b07f-4d1f-818d-9bb35afc1b41] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:50:29.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21cfef1e-b07f-4d1f-818d-9bb35afc1b41] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:50:29.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21cfef1e-b07f-4d1f-818d-9bb35afc1b41] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:50:29.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21cfef1e-b07f-4d1f-818d-9bb35afc1b41] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:50:29.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21cfef1e-b07f-4d1f-818d-9bb35afc1b41] Success to connect to server [localhost:8848] on start up, connectionId = 1752025829173_127.0.0.1_8174
09:50:29.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21cfef1e-b07f-4d1f-818d-9bb35afc1b41] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:50:29.301 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21cfef1e-b07f-4d1f-818d-9bb35afc1b41] Notify connected event to listeners.
09:50:29.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21cfef1e-b07f-4d1f-818d-9bb35afc1b41] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001e1814f0668
09:50:29.373 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:50:29.420 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
09:50:29.570 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 22.145 seconds (JVM running for 23.875)
09:50:29.597 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:50:29.597 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:50:29.598 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:50:29.910 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21cfef1e-b07f-4d1f-818d-9bb35afc1b41] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:50:29.931 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21cfef1e-b07f-4d1f-818d-9bb35afc1b41] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:56:53.756 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:56:53.762 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:56:54.091 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:56:54.091 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7fef14d5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:56:54.092 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752025829173_127.0.0.1_8174
09:56:54.094 [nacos-grpc-client-executor-86] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752025829173_127.0.0.1_8174]Ignore complete event,isRunning:false,isAbandon=false
09:56:54.095 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@403fa96c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 87]
09:56:54.129 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:56:54.133 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:56:54.144 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:56:54.144 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:57:35.036 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:57:36.795 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 53d80a73-33e0-4cbc-9fc5-2030edb31025_config-0
09:57:36.950 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 72 ms to scan 1 urls, producing 3 keys and 6 values 
09:57:37.094 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 4 keys and 9 values 
09:57:37.120 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
09:57:37.140 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
09:57:37.162 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
09:57:37.188 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
09:57:37.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53d80a73-33e0-4cbc-9fc5-2030edb31025_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:57:37.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53d80a73-33e0-4cbc-9fc5-2030edb31025_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000020e4339e480
09:57:37.198 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53d80a73-33e0-4cbc-9fc5-2030edb31025_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000020e4339e6a0
09:57:37.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53d80a73-33e0-4cbc-9fc5-2030edb31025_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:57:37.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53d80a73-33e0-4cbc-9fc5-2030edb31025_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:57:37.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53d80a73-33e0-4cbc-9fc5-2030edb31025_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:57:39.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53d80a73-33e0-4cbc-9fc5-2030edb31025_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752026258997_127.0.0.1_8944
09:57:39.351 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53d80a73-33e0-4cbc-9fc5-2030edb31025_config-0] Notify connected event to listeners.
09:57:39.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53d80a73-33e0-4cbc-9fc5-2030edb31025_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:57:39.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53d80a73-33e0-4cbc-9fc5-2030edb31025_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020e43518440
09:57:39.585 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:57:50.886 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:57:50.887 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:57:50.888 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:57:51.254 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:57:53.045 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:57:53.047 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:57:53.047 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:58:08.606 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:58:12.782 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 88abe6f9-ce93-4418-8a88-410a653e8e7b
09:58:12.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88abe6f9-ce93-4418-8a88-410a653e8e7b] RpcClient init label, labels = {module=naming, source=sdk}
09:58:12.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88abe6f9-ce93-4418-8a88-410a653e8e7b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:58:12.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88abe6f9-ce93-4418-8a88-410a653e8e7b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:58:12.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88abe6f9-ce93-4418-8a88-410a653e8e7b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:58:12.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88abe6f9-ce93-4418-8a88-410a653e8e7b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:58:12.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88abe6f9-ce93-4418-8a88-410a653e8e7b] Success to connect to server [localhost:8848] on start up, connectionId = 1752026292791_127.0.0.1_9152
09:58:12.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88abe6f9-ce93-4418-8a88-410a653e8e7b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:58:12.917 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88abe6f9-ce93-4418-8a88-410a653e8e7b] Notify connected event to listeners.
09:58:12.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88abe6f9-ce93-4418-8a88-410a653e8e7b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020e43518440
09:58:12.966 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:58:13.006 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
09:58:13.179 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 39.551 seconds (JVM running for 41.918)
09:58:13.210 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:58:13.212 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:58:13.212 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:58:13.324 [RMI TCP Connection(7)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:58:13.483 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88abe6f9-ce93-4418-8a88-410a653e8e7b] Receive server push request, request = NotifySubscriberRequest, requestId = 28
09:58:13.500 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88abe6f9-ce93-4418-8a88-410a653e8e7b] Ack server push request, request = NotifySubscriberRequest, requestId = 28
10:00:32.929 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88abe6f9-ce93-4418-8a88-410a653e8e7b] Receive server push request, request = NotifySubscriberRequest, requestId = 31
10:00:32.930 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88abe6f9-ce93-4418-8a88-410a653e8e7b] Ack server push request, request = NotifySubscriberRequest, requestId = 31
10:00:34.587 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:00:34.587 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:00:34.910 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:00:34.911 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
15:14:48.051 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:14:48.054 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:14:48.401 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:14:48.401 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@186fd1dd[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:14:48.401 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752026292791_127.0.0.1_9152
15:14:48.409 [nacos-grpc-client-executor-3784] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752026292791_127.0.0.1_9152]Ignore complete event,isRunning:false,isAbandon=false
15:14:48.419 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@d64a0ae[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3785]
15:14:48.677 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:14:48.689 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:14:48.695 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:14:48.695 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:14:48.700 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:14:48.701 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:14:48.704 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:14:48.704 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:32:33.757 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:32:34.713 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 95dc900a-362b-4972-86ca-35cae19d433c_config-0
15:32:34.862 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
15:32:34.922 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
15:32:34.937 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
15:32:34.952 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
15:32:34.967 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
15:32:34.995 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 2 keys and 8 values 
15:32:35.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95dc900a-362b-4972-86ca-35cae19d433c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:32:35.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95dc900a-362b-4972-86ca-35cae19d433c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000015e2c39eaf8
15:32:35.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95dc900a-362b-4972-86ca-35cae19d433c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000015e2c39ed18
15:32:35.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95dc900a-362b-4972-86ca-35cae19d433c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:32:35.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95dc900a-362b-4972-86ca-35cae19d433c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:32:35.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95dc900a-362b-4972-86ca-35cae19d433c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:32:36.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95dc900a-362b-4972-86ca-35cae19d433c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752046355952_127.0.0.1_8698
15:32:36.182 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95dc900a-362b-4972-86ca-35cae19d433c_config-0] Notify connected event to listeners.
15:32:36.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95dc900a-362b-4972-86ca-35cae19d433c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:32:36.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95dc900a-362b-4972-86ca-35cae19d433c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000015e2c518ad8
15:32:36.324 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:32:40.277 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:32:40.278 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:32:40.278 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:32:40.557 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:32:41.439 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:32:41.440 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:32:41.440 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:32:49.727 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:32:53.017 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e11f90cd-7733-4aca-9074-8c2dea294e59
15:32:53.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11f90cd-7733-4aca-9074-8c2dea294e59] RpcClient init label, labels = {module=naming, source=sdk}
15:32:53.019 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11f90cd-7733-4aca-9074-8c2dea294e59] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:32:53.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11f90cd-7733-4aca-9074-8c2dea294e59] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:32:53.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11f90cd-7733-4aca-9074-8c2dea294e59] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:32:53.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11f90cd-7733-4aca-9074-8c2dea294e59] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:32:53.154 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11f90cd-7733-4aca-9074-8c2dea294e59] Success to connect to server [localhost:8848] on start up, connectionId = 1752046373032_127.0.0.1_8757
15:32:53.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11f90cd-7733-4aca-9074-8c2dea294e59] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:32:53.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11f90cd-7733-4aca-9074-8c2dea294e59] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000015e2c518ad8
15:32:53.157 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11f90cd-7733-4aca-9074-8c2dea294e59] Notify connected event to listeners.
15:32:53.248 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:32:53.292 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:32:53.467 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.837 seconds (JVM running for 22.087)
15:32:53.497 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:32:53.499 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:32:53.501 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:32:53.792 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11f90cd-7733-4aca-9074-8c2dea294e59] Receive server push request, request = NotifySubscriberRequest, requestId = 62
15:32:53.818 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11f90cd-7733-4aca-9074-8c2dea294e59] Ack server push request, request = NotifySubscriberRequest, requestId = 62
15:32:53.931 [RMI TCP Connection(6)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:33:12.616 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:33:12.616 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:28:58.743 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:28:58.763 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:28:59.314 [nacos-grpc-client-executor-683] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11f90cd-7733-4aca-9074-8c2dea294e59] Receive server push request, request = NotifySubscriberRequest, requestId = 74
16:28:59.355 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:28:59.356 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@14a2cfda[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:28:59.356 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752046373032_127.0.0.1_8757
16:28:59.364 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@12444b87[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 683]
16:28:59.367 [nacos-grpc-client-executor-683] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e11f90cd-7733-4aca-9074-8c2dea294e59] Ack server push request, request = NotifySubscriberRequest, requestId = 74
16:28:59.376 [nacos-grpc-client-executor-683] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752046373032_127.0.0.1_8757]Ignore complete event,isRunning:false,isAbandon=false
16:28:59.673 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:28:59.686 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:28:59.732 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:28:59.732 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:28:59.740 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:28:59.742 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:38:04.308 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:38:05.076 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0f534aea-e86f-46bd-906d-a1c0d49f36b5_config-0
17:38:05.187 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
17:38:05.234 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
17:38:05.249 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
17:38:05.249 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
17:38:05.274 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 1 keys and 7 values 
17:38:05.290 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
17:38:05.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f534aea-e86f-46bd-906d-a1c0d49f36b5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:38:05.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f534aea-e86f-46bd-906d-a1c0d49f36b5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b9a83beaf8
17:38:05.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f534aea-e86f-46bd-906d-a1c0d49f36b5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001b9a83bed18
17:38:05.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f534aea-e86f-46bd-906d-a1c0d49f36b5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:38:05.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f534aea-e86f-46bd-906d-a1c0d49f36b5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:38:05.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f534aea-e86f-46bd-906d-a1c0d49f36b5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:38:06.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f534aea-e86f-46bd-906d-a1c0d49f36b5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752053886206_127.0.0.1_11473
17:38:06.453 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f534aea-e86f-46bd-906d-a1c0d49f36b5_config-0] Notify connected event to listeners.
17:38:06.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f534aea-e86f-46bd-906d-a1c0d49f36b5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:38:06.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f534aea-e86f-46bd-906d-a1c0d49f36b5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b9a84f8668
17:38:06.612 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:38:14.685 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:38:14.685 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:38:14.685 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:38:15.037 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:38:16.399 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:38:16.399 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:38:16.399 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:38:28.631 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:38:33.176 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fc4299a3-b99d-4993-bd6c-77c14e6658ad
17:38:33.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4299a3-b99d-4993-bd6c-77c14e6658ad] RpcClient init label, labels = {module=naming, source=sdk}
17:38:33.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4299a3-b99d-4993-bd6c-77c14e6658ad] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:38:33.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4299a3-b99d-4993-bd6c-77c14e6658ad] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:38:33.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4299a3-b99d-4993-bd6c-77c14e6658ad] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:38:33.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4299a3-b99d-4993-bd6c-77c14e6658ad] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:38:33.318 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4299a3-b99d-4993-bd6c-77c14e6658ad] Success to connect to server [localhost:8848] on start up, connectionId = 1752053913192_127.0.0.1_11603
17:38:33.319 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4299a3-b99d-4993-bd6c-77c14e6658ad] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:38:33.319 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4299a3-b99d-4993-bd6c-77c14e6658ad] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b9a84f8668
17:38:33.319 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4299a3-b99d-4993-bd6c-77c14e6658ad] Notify connected event to listeners.
17:38:33.382 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:38:33.433 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
17:38:33.667 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 30.063 seconds (JVM running for 32.239)
17:38:33.709 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:38:33.709 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:38:33.709 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:38:33.909 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:38:33.956 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4299a3-b99d-4993-bd6c-77c14e6658ad] Receive server push request, request = NotifySubscriberRequest, requestId = 82
17:38:33.972 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4299a3-b99d-4993-bd6c-77c14e6658ad] Ack server push request, request = NotifySubscriberRequest, requestId = 82
17:49:54.943 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
17:49:54.943 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:49:54.942 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:49:54.959 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
17:49:54.959 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
17:49:54.961 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-6} inited
17:49:55.010 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
17:49:55.024 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
17:49:55.025 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:49:55.025 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:49:55.027 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:49:55.027 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:49:55.027 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-6} closing ...
17:49:55.029 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-6} closed
17:49:55.030 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:49:55.030 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
17:49:55.032 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
17:49:55.032 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:49:55.057 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-7} inited
17:49:55.058 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
17:49:55.059 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
17:49:55.059 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:28:57.621 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:28:57.625 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:28:57.968 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:28:57.968 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@254af297[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:28:57.969 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752053913192_127.0.0.1_11603
19:28:57.972 [nacos-grpc-client-executor-1335] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752053913192_127.0.0.1_11603]Ignore complete event,isRunning:false,isAbandon=false
19:28:57.979 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@79224ef9[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1336]
19:28:58.153 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:28:58.155 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-7} closing ...
19:28:58.160 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-7} closed
19:28:58.161 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:28:58.165 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:28:58.165 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
