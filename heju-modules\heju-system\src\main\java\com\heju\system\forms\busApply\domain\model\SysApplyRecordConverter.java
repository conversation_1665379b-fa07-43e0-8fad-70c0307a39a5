package com.heju.system.forms.busApply.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.forms.busApply.domain.dto.SysApplyRecordDto;
import com.heju.system.forms.busApply.domain.po.SysApplyRecordPo;
import com.heju.system.forms.busApply.domain.query.SysApplyRecordQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 行政申请 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysApplyRecordConverter extends BaseConverter<SysApplyRecordQuery, SysApplyRecordDto, SysApplyRecordPo> {
}
