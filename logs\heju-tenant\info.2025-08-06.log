09:27:15.813 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:27:17.430 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 013e70ed-c738-4901-b18c-ebfa914e5969_config-0
09:27:17.540 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 55 ms to scan 1 urls, producing 3 keys and 6 values 
09:27:17.594 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 4 keys and 9 values 
09:27:17.630 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 34 ms to scan 1 urls, producing 3 keys and 10 values 
09:27:17.653 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 5 values 
09:27:17.675 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 7 values 
09:27:17.694 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
09:27:17.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:27:17.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001da1c39dd00
09:27:17.705 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001da1c39df20
09:27:17.706 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:27:17.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:27:17.727 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:27:19.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754443639366_127.0.0.1_2997
09:27:19.600 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Notify connected event to listeners.
09:27:19.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:27:19.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001da1c515f18
09:27:19.983 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:27:25.697 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:27:25.697 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:27:25.697 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:27:25.993 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:27:27.468 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:27:27.472 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:27:27.472 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:27:31.669 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:27:36.992 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 807f577d-ff46-4580-a4eb-fe02dd031770
09:27:36.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] RpcClient init label, labels = {module=naming, source=sdk}
09:27:36.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:27:36.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:27:36.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:27:36.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:27:37.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Success to connect to server [localhost:8848] on start up, connectionId = 1754443657007_127.0.0.1_3231
09:27:37.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:27:37.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001da1c515f18
09:27:37.121 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Notify connected event to listeners.
09:27:37.160 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:27:37.206 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:27:37.339 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 25.005 seconds (JVM running for 33.117)
09:27:37.348 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:27:37.348 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:27:37.348 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:27:37.709 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:27:37.741 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:30:30.119 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:30:39.223 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Receive server push request, request = NotifySubscriberRequest, requestId = 15
09:30:39.225 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Ack server push request, request = NotifySubscriberRequest, requestId = 15
10:25:03.237 [nacos-grpc-client-executor-738] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Receive server push request, request = NotifySubscriberRequest, requestId = 19
10:25:03.290 [nacos-grpc-client-executor-738] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Ack server push request, request = NotifySubscriberRequest, requestId = 19
10:25:30.635 [nacos-grpc-client-executor-743] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Receive server push request, request = NotifySubscriberRequest, requestId = 22
10:25:30.656 [nacos-grpc-client-executor-743] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Ack server push request, request = NotifySubscriberRequest, requestId = 22
11:20:56.511 [nacos-grpc-client-executor-1453] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Receive server push request, request = NotifySubscriberRequest, requestId = 26
11:20:56.532 [nacos-grpc-client-executor-1453] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Ack server push request, request = NotifySubscriberRequest, requestId = 26
11:21:27.419 [nacos-grpc-client-executor-1459] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Receive server push request, request = NotifySubscriberRequest, requestId = 29
11:21:27.436 [nacos-grpc-client-executor-1459] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Ack server push request, request = NotifySubscriberRequest, requestId = 29
13:17:10.278 [lettuce-nioEventLoop-4-1] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
13:17:10.384 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /192.168.1.200:6379
13:17:19.478 [lettuce-eventExecutorLoop-1-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:17:27.776 [lettuce-eventExecutorLoop-1-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:17:27.813 [lettuce-nioEventLoop-4-15] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to 192.168.1.200/<unresolved>:6379
14:25:35.375 [nacos-grpc-client-executor-3803] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Receive server push request, request = NotifySubscriberRequest, requestId = 33
14:25:35.396 [nacos-grpc-client-executor-3803] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Ack server push request, request = NotifySubscriberRequest, requestId = 33
14:25:38.205 [nacos-grpc-client-executor-3804] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Receive server push request, request = NotifySubscriberRequest, requestId = 36
14:25:38.215 [nacos-grpc-client-executor-3804] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Ack server push request, request = NotifySubscriberRequest, requestId = 36
14:27:09.944 [nacos-grpc-client-executor-3823] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Receive server push request, request = NotifySubscriberRequest, requestId = 41
14:27:09.961 [nacos-grpc-client-executor-3823] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Ack server push request, request = NotifySubscriberRequest, requestId = 41
14:27:37.874 [nacos-grpc-client-executor-3829] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Receive server push request, request = NotifySubscriberRequest, requestId = 44
14:27:37.903 [nacos-grpc-client-executor-3829] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Ack server push request, request = NotifySubscriberRequest, requestId = 44
14:55:05.041 [nacos-grpc-client-executor-4173] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Receive server push request, request = NotifySubscriberRequest, requestId = 49
14:55:05.051 [nacos-grpc-client-executor-4173] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Ack server push request, request = NotifySubscriberRequest, requestId = 49
14:55:24.583 [nacos-grpc-client-executor-4177] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Receive server push request, request = NotifySubscriberRequest, requestId = 53
14:55:24.596 [nacos-grpc-client-executor-4177] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Ack server push request, request = NotifySubscriberRequest, requestId = 53
16:14:05.024 [nacos-grpc-client-executor-5177] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Receive server push request, request = NotifySubscriberRequest, requestId = 58
16:14:05.043 [nacos-grpc-client-executor-5177] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Ack server push request, request = NotifySubscriberRequest, requestId = 58
16:14:07.958 [nacos-grpc-client-executor-5178] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Receive server push request, request = NotifySubscriberRequest, requestId = 62
16:14:07.975 [nacos-grpc-client-executor-5178] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Ack server push request, request = NotifySubscriberRequest, requestId = 62
20:49:40.232 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:49:40.232 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:49:40.364 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.382 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.573 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.602 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.898 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:40.917 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:41.317 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:41.321 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:41.833 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:41.847 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:42.450 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:42.461 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:42.817 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:49:43.132 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:49:43.170 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [013e70ed-c738-4901-b18c-ebfa914e5969_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:43.182 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:49:43.470 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:49:43.472 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@562ab3b0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:49:43.472 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754443657007_127.0.0.1_3231
20:49:43.472 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@10c09d28[Running, pool size = 18, active threads = 0, queued tasks = 0, completed tasks = 8695]
20:49:43.474 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [807f577d-ff46-4580-a4eb-fe02dd031770] Client is shutdown, stop reconnect to server
20:49:43.640 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:49:43.647 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:49:43.651 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:49:43.651 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
