package com.heju.system.approval.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;

import com.heju.system.approval.domain.dto.SysApprovalCustomerinfoDto;
import com.heju.system.approval.domain.po.SysApprovalCustomerinfoPo;
import com.heju.system.approval.domain.query.SysApprovalCustomerinfoQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 客户信息审核 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysApprovalCustomerinfoConverter extends BaseConverter<SysApprovalCustomerinfoQuery, SysApprovalCustomerinfoDto, SysApprovalCustomerinfoPo> {
}
