package com.heju.system.api.authority.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.annotation.Excel;
import com.heju.common.core.web.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 角色组 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_role_group", excludeProperty = { UPDATE_BY, SORT, CREATE_BY, REMARK, NAME })
public class SysRoleGroupPo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Excel(name = "id")
    @TableId(type = IdType.AUTO)
    protected Long id;

    /** 角色组名 */
    @Excel(name = "角色组名")
    protected String roleGroupName;

    /** 所属管理用户 */
    @Excel(name = "所属管理用户")
    protected Long userId;

}