package com.heju.system.file.service;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.file.domain.dto.SysFileInfoDto;
import com.heju.system.file.domain.query.SysFileInfoQuery;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 文件信息管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysFileInfoService extends IBaseService<SysFileInfoQuery, SysFileInfoDto> {



    Integer insertFile(MultipartFile file, SysFileInfoDto fileInfo);

    /**
     * 查询文件暂存列表
     * @param query
     * @return
     */
    List<SysFileInfoDto> selectTempStorageList(SysFileInfoQuery query);

    /**
     * 查询回收站列表
     * @param query
     * @return
     */
    List<SysFileInfoDto> selectrecyclList(SysFileInfoQuery query);

    /**
     * 回收站批量恢复
     */
    AjaxResult batchReplyList(List<Long> fileIds);

    /**
     * 回收站批量删除
     */
    AjaxResult batchDeleteByIds(List<Long> idList);

    /**
     * 暂存批量删除
     */
    AjaxResult removeStorageByIds(List<Long> idList);

    /**
     * 文件管理根据Id删除数据对象（批量）
     *
     * @param idList Id集合
     * @return 结果
     */
    int deleteByIds(List<Long> idList);

    /**
     *查询操作 新增操作记录
     */
    AjaxResult insertView(Long id);

    /**
     * 查询 并 定时清理 回收站中已超过30天的文件
     * @return
     */
    int deleteExpiredFiles();

    /**
     * 单文件下载
     * @param id 文件id
     * @param response http请求response
     */
    void download(Long id, HttpServletResponse response);

    /**
     * 多文件批量下载
     * @param idList 文件id集合
     * @param response http请求response
     */
    void batchDownload(List<Long> idList, HttpServletResponse response);
}
