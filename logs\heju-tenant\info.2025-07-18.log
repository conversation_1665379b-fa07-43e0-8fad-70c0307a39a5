09:06:44.509 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:06:45.473 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0
09:06:45.574 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 52 ms to scan 1 urls, producing 3 keys and 6 values 
09:06:45.603 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:06:45.615 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:06:45.629 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:06:45.650 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 1 keys and 7 values 
09:06:45.664 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:06:45.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:06:45.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000013a523cf8e0
09:06:45.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000013a523cfb00
09:06:45.670 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:06:45.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:06:45.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:46.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:46.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:47.002 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:06:47.003 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:47.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000013a5250a068
09:06:47.258 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:47.509 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:47.858 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:48.307 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:48.851 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:49.093 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:06:49.502 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:06:50.375 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Success to connect a server [localhost:8848], connectionId = 1752800810219_127.0.0.1_6284
09:06:50.376 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [272a1832-9a4a-4184-96cc-1a288d5ad8f4_config-0] Notify connected event to listeners.
09:06:56.306 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:06:56.308 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:06:56.308 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:06:56.617 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:06:57.839 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:06:57.841 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:06:57.842 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:07:04.487 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:07:12.675 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9a3dc893-f03d-40d3-ba61-2e92bdbf875a
09:07:12.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] RpcClient init label, labels = {module=naming, source=sdk}
09:07:12.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:07:12.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:07:12.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:07:12.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:07:13.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Success to connect to server [localhost:8848] on start up, connectionId = 1752800832918_127.0.0.1_6623
09:07:13.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:07:13.420 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Notify connected event to listeners.
09:07:13.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000013a5250a068
09:07:13.586 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:07:13.690 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:07:14.245 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:07:14.392 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:07:14.610 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 31.041 seconds (JVM running for 32.565)
09:07:14.639 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:07:14.642 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:07:14.644 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:07:15.249 [RMI TCP Connection(13)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:09:21.750 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:09:21.752 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 14
09:14:31.958 [nacos-grpc-client-executor-97] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 15
09:14:31.976 [nacos-grpc-client-executor-97] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 15
09:15:24.909 [nacos-grpc-client-executor-109] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 18
09:15:24.925 [nacos-grpc-client-executor-109] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 18
10:04:03.254 [nacos-grpc-client-executor-693] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 22
10:04:03.267 [nacos-grpc-client-executor-693] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 22
10:04:32.835 [nacos-grpc-client-executor-700] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 25
10:04:32.848 [nacos-grpc-client-executor-700] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 25
14:40:27.492 [nacos-grpc-client-executor-4013] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 31
14:40:27.518 [nacos-grpc-client-executor-4013] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 31
14:40:52.430 [nacos-grpc-client-executor-4018] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 36
14:40:52.441 [nacos-grpc-client-executor-4018] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 36
17:45:10.394 [nacos-grpc-client-executor-6227] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 40
17:45:10.410 [nacos-grpc-client-executor-6227] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 40
17:45:52.376 [nacos-grpc-client-executor-6236] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 44
17:45:52.390 [nacos-grpc-client-executor-6236] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 44
18:03:04.794 [nacos-grpc-client-executor-6442] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 49
18:03:04.812 [nacos-grpc-client-executor-6442] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 49
18:03:21.444 [nacos-grpc-client-executor-6446] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 53
18:03:21.459 [nacos-grpc-client-executor-6446] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 53
18:05:31.062 [nacos-grpc-client-executor-6472] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 58
18:05:31.067 [nacos-grpc-client-executor-6472] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 58
18:05:47.868 [nacos-grpc-client-executor-6475] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 62
18:05:47.883 [nacos-grpc-client-executor-6475] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 62
18:13:42.274 [nacos-grpc-client-executor-6570] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 68
18:13:42.287 [nacos-grpc-client-executor-6570] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 68
18:13:58.561 [nacos-grpc-client-executor-6574] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 72
18:13:58.577 [nacos-grpc-client-executor-6574] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 72
18:18:32.559 [nacos-grpc-client-executor-6629] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 79
18:18:32.577 [nacos-grpc-client-executor-6629] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 79
18:18:35.965 [nacos-grpc-client-executor-6630] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 86
18:18:35.974 [nacos-grpc-client-executor-6630] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 86
18:21:37.910 [nacos-grpc-client-executor-6666] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 90
18:21:37.926 [nacos-grpc-client-executor-6666] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 90
18:21:54.154 [nacos-grpc-client-executor-6669] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Receive server push request, request = NotifySubscriberRequest, requestId = 94
18:21:54.164 [nacos-grpc-client-executor-6669] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a3dc893-f03d-40d3-ba61-2e92bdbf875a] Ack server push request, request = NotifySubscriberRequest, requestId = 94
18:47:19.438 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:47:19.442 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:47:19.771 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:47:19.771 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@562a3711[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:47:19.772 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752800832918_127.0.0.1_6623
18:47:19.773 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@40c6d78[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 6976]
18:47:19.774 [nacos-grpc-client-executor-6976] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752800832918_127.0.0.1_6623]Ignore complete event,isRunning:false,isAbandon=false
18:47:19.932 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:47:19.937 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:47:19.942 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:47:19.942 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
