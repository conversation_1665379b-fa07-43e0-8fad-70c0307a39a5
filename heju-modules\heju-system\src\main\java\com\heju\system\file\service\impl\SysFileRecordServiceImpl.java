package com.heju.system.file.service.impl;

import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.domain.query.SysUserQuery;
import com.heju.system.file.domain.dto.SysFileRecordDto;
import com.heju.system.file.domain.query.SysFileRecordQuery;
import com.heju.system.file.manager.ISysFileRecordManager;
import com.heju.system.file.service.ISysFileRecordService;
import com.heju.system.organize.manager.ISysUserManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件操作记录管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysFileRecordServiceImpl extends BaseServiceImpl<SysFileRecordQuery, SysFileRecordDto, ISysFileRecordManager> implements ISysFileRecordService {

    @Resource
    private ISysUserManager sysUserManager;

    /**
     * 查询文件操作记录对象列表 | 数据权限
     *
     * @param fileRecord 文件操作记录对象
     * @return 文件操作记录对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysFileRecordMapper"})
    public List<SysFileRecordDto> selectListScope(SysFileRecordQuery fileRecord) {
        List<SysFileRecordDto> sysFileRecordDtos = baseManager.selectList(fileRecord);

        if (sysFileRecordDtos == null || sysFileRecordDtos.isEmpty()){
            return Collections.emptyList();
        }
        // 收集所有 createBy 用户ID
        Set<Long> userIds = sysFileRecordDtos.stream()
                .map(SysFileRecordDto::getCreateBy)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        //查询用户信息
//        SysUserQuery query = new SysUserQuery();
//        List<SysUserDto> sysUserDtos = sysUserManager.selectList(query);

        List<SysUserDto> sysUserDtos = sysUserManager.selectListByIds(userIds);
        Map<Long, String> sysUserMap = sysUserDtos.stream().collect(Collectors.toMap(
                SysUserDto::getId,
                SysUserDto::getNickName,
                (existing, replacement) -> existing
        ));
        //填充字段
        for (SysFileRecordDto dto : sysFileRecordDtos) {
            if (dto.getCreateBy() != null) {
                dto.setOperateName(sysUserMap.get(dto.getCreateBy()));
            }
        }
        return sysFileRecordDtos;
    }

    /**
     * 文件操作记录列表
     * @param fileId
     * @return
     */
    @Override
    public List<SysFileRecordDto> selectByFileId(Serializable fileId) {
        List<SysFileRecordDto> sysFileRecordDtos = baseManager.selectByFileId(fileId);

        if (sysFileRecordDtos == null || sysFileRecordDtos.isEmpty()) {
            return Collections.emptyList();
        }

        // 收集所有 createBy 用户ID
        Set<Long> userIds = sysFileRecordDtos.stream()
                .map(SysFileRecordDto::getCreateBy)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 批量查询用户信息
        Map<Long, String> userMap = new HashMap<>();
        if (!userIds.isEmpty()) {
//            SysUserQuery userQuery = new SysUserQuery();
//            List<SysUserDto> sysUserDtos = sysUserManager.selectList(userQuery);

            List<SysUserDto> sysUserDtos = sysUserManager.selectListByIds(userIds);
            if (sysUserDtos != null && !sysUserDtos.isEmpty()) {
                userMap = sysUserDtos.stream()
                        .collect(Collectors.toMap(
                                SysUserDto::getId,
                                SysUserDto::getNickName,
                                (existing, replacement) -> existing // 防止 key 冲突
                        ));
            }
        }

        // 填充 operateName 字段
        for (SysFileRecordDto dto : sysFileRecordDtos) {
            Long createBy = dto.getCreateBy();
            if (createBy != null && userMap.containsKey(createBy)) {
                dto.setOperateName(userMap.get(createBy));
            }
        }

        // 应用其他合并逻辑
        return sysFileRecordDtos.stream()
                .map(this::subCorrelates)
                .collect(Collectors.toList());
    }

}
