09:49:21.929 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:49:22.531 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 04d8334d-75b2-4658-8b75-dd5866f4dcf3_config-0
09:49:22.587 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 3 keys and 6 values 
09:49:22.607 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 4 keys and 9 values 
09:49:22.616 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:49:22.624 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:49:22.635 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:49:22.642 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
09:49:22.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04d8334d-75b2-4658-8b75-dd5866f4dcf3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:49:22.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04d8334d-75b2-4658-8b75-dd5866f4dcf3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000181db3bc2b8
09:49:22.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04d8334d-75b2-4658-8b75-dd5866f4dcf3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000181db3bc4d8
09:49:22.647 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04d8334d-75b2-4658-8b75-dd5866f4dcf3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:49:22.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04d8334d-75b2-4658-8b75-dd5866f4dcf3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:49:22.654 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04d8334d-75b2-4658-8b75-dd5866f4dcf3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:49:23.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04d8334d-75b2-4658-8b75-dd5866f4dcf3_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752025763334_127.0.0.1_7982
09:49:23.553 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04d8334d-75b2-4658-8b75-dd5866f4dcf3_config-0] Notify connected event to listeners.
09:49:23.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04d8334d-75b2-4658-8b75-dd5866f4dcf3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:49:23.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04d8334d-75b2-4658-8b75-dd5866f4dcf3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000181db4f6370
09:49:23.735 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:49:27.138 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:49:28.367 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:49:29.011 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0c1ba45c-370d-4466-b7ab-f68eeb11e030_config-0
09:49:29.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1ba45c-370d-4466-b7ab-f68eeb11e030_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:49:29.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1ba45c-370d-4466-b7ab-f68eeb11e030_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000181db3bc2b8
09:49:29.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1ba45c-370d-4466-b7ab-f68eeb11e030_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000181db3bc4d8
09:49:29.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1ba45c-370d-4466-b7ab-f68eeb11e030_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:49:29.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1ba45c-370d-4466-b7ab-f68eeb11e030_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:49:29.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1ba45c-370d-4466-b7ab-f68eeb11e030_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:49:29.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1ba45c-370d-4466-b7ab-f68eeb11e030_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752025769025_127.0.0.1_7995
09:49:29.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1ba45c-370d-4466-b7ab-f68eeb11e030_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:49:29.141 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1ba45c-370d-4466-b7ab-f68eeb11e030_config-0] Notify connected event to listeners.
09:49:29.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c1ba45c-370d-4466-b7ab-f68eeb11e030_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000181db4f6370
09:49:29.250 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 589e1f1a-f8e7-4bfa-a009-b6ba4595ee26
09:49:29.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] RpcClient init label, labels = {module=naming, source=sdk}
09:49:29.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:49:29.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:49:29.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:49:29.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:49:29.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Success to connect to server [localhost:8848] on start up, connectionId = 1752025769264_127.0.0.1_7996
09:49:29.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:49:29.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000181db4f6370
09:49:29.388 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Notify connected event to listeners.
09:49:29.829 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
09:49:29.871 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 8.568 seconds (JVM running for 9.549)
09:49:29.882 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:49:29.884 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:49:29.885 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:49:29.953 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:49:29.954 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:49:30.041 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:49:30.042 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:50:00.184 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:50:00.185 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:50:00.195 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:50:00.195 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:50:29.887 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:50:29.888 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:50:30.104 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:50:30.104 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:55:08.543 [nacos-grpc-client-executor-149] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:55:08.559 [nacos-grpc-client-executor-149] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:55:18.724 [nacos-grpc-client-executor-151] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:55:18.746 [nacos-grpc-client-executor-151] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [589e1f1a-f8e7-4bfa-a009-b6ba4595ee26] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:56:53.666 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:56:53.672 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:56:53.999 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:56:53.999 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@52289f9f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:56:54.001 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752025769264_127.0.0.1_7996
09:56:54.002 [nacos-grpc-client-executor-183] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752025769264_127.0.0.1_7996]Ignore complete event,isRunning:false,isAbandon=false
09:56:54.005 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6496f0c2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 184]
09:57:23.089 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:57:23.827 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2126fb40-fbe7-46ac-89bb-0d2c70ad9cca_config-0
09:57:23.922 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
09:57:23.963 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
09:57:23.982 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
09:57:23.999 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
09:57:24.021 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
09:57:24.037 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:57:24.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2126fb40-fbe7-46ac-89bb-0d2c70ad9cca_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:57:24.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2126fb40-fbe7-46ac-89bb-0d2c70ad9cca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000023e893cb8c8
09:57:24.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2126fb40-fbe7-46ac-89bb-0d2c70ad9cca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000023e893cbae8
09:57:24.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2126fb40-fbe7-46ac-89bb-0d2c70ad9cca_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:57:24.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2126fb40-fbe7-46ac-89bb-0d2c70ad9cca_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:57:24.064 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2126fb40-fbe7-46ac-89bb-0d2c70ad9cca_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:57:25.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2126fb40-fbe7-46ac-89bb-0d2c70ad9cca_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752026245310_127.0.0.1_8908
09:57:25.579 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2126fb40-fbe7-46ac-89bb-0d2c70ad9cca_config-0] Notify connected event to listeners.
09:57:25.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2126fb40-fbe7-46ac-89bb-0d2c70ad9cca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:57:25.580 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2126fb40-fbe7-46ac-89bb-0d2c70ad9cca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000023e89505920
09:57:25.743 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:57:31.973 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:57:34.318 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:57:35.450 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6d3e2602-7337-4de8-b6b5-05f6c8e3519c_config-0
09:57:35.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d3e2602-7337-4de8-b6b5-05f6c8e3519c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:57:35.452 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d3e2602-7337-4de8-b6b5-05f6c8e3519c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000023e893cb8c8
09:57:35.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d3e2602-7337-4de8-b6b5-05f6c8e3519c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000023e893cbae8
09:57:35.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d3e2602-7337-4de8-b6b5-05f6c8e3519c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:57:35.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d3e2602-7337-4de8-b6b5-05f6c8e3519c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:57:35.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d3e2602-7337-4de8-b6b5-05f6c8e3519c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:57:35.583 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d3e2602-7337-4de8-b6b5-05f6c8e3519c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752026255467_127.0.0.1_8931
09:57:35.583 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d3e2602-7337-4de8-b6b5-05f6c8e3519c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:57:35.583 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d3e2602-7337-4de8-b6b5-05f6c8e3519c_config-0] Notify connected event to listeners.
09:57:35.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d3e2602-7337-4de8-b6b5-05f6c8e3519c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000023e89505920
09:57:35.794 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 21b56e0f-9e81-4fa4-a362-d684408ba282
09:57:35.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] RpcClient init label, labels = {module=naming, source=sdk}
09:57:35.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:57:35.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:57:35.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:57:35.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:57:35.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Success to connect to server [localhost:8848] on start up, connectionId = 1752026255815_127.0.0.1_8933
09:57:35.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:57:35.933 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Notify connected event to listeners.
09:57:35.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000023e89505920
09:57:36.570 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Receive server push request, request = NotifySubscriberRequest, requestId = 15
09:57:36.571 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Ack server push request, request = NotifySubscriberRequest, requestId = 15
09:57:36.664 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Receive server push request, request = NotifySubscriberRequest, requestId = 17
09:57:36.664 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Ack server push request, request = NotifySubscriberRequest, requestId = 17
09:57:36.684 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Receive server push request, request = NotifySubscriberRequest, requestId = 20
09:57:36.689 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Ack server push request, request = NotifySubscriberRequest, requestId = 20
09:57:36.700 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Receive server push request, request = NotifySubscriberRequest, requestId = 19
09:57:36.700 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Ack server push request, request = NotifySubscriberRequest, requestId = 19
09:57:36.717 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Receive server push request, request = NotifySubscriberRequest, requestId = 18
09:57:36.718 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Ack server push request, request = NotifySubscriberRequest, requestId = 18
09:57:36.731 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Receive server push request, request = NotifySubscriberRequest, requestId = 16
09:57:36.732 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Ack server push request, request = NotifySubscriberRequest, requestId = 16
09:57:36.980 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
09:57:37.049 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 14.607 seconds (JVM running for 15.62)
09:57:37.081 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:57:37.085 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:57:37.087 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:57:37.634 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Receive server push request, request = NotifySubscriberRequest, requestId = 21
09:57:37.678 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Ack server push request, request = NotifySubscriberRequest, requestId = 21
09:58:01.274 [nacos-grpc-client-executor-58] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Receive server push request, request = NotifySubscriberRequest, requestId = 22
09:58:01.330 [nacos-grpc-client-executor-58] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Ack server push request, request = NotifySubscriberRequest, requestId = 22
09:58:06.127 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Receive server push request, request = NotifySubscriberRequest, requestId = 24
09:58:06.144 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Ack server push request, request = NotifySubscriberRequest, requestId = 24
09:58:06.456 [nacos-grpc-client-executor-62] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Receive server push request, request = NotifySubscriberRequest, requestId = 25
09:58:06.474 [nacos-grpc-client-executor-62] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Ack server push request, request = NotifySubscriberRequest, requestId = 25
09:58:13.468 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Receive server push request, request = NotifySubscriberRequest, requestId = 27
09:58:13.480 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Ack server push request, request = NotifySubscriberRequest, requestId = 27
13:00:27.744 [nacos-grpc-client-executor-3823] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Receive server push request, request = NotifySubscriberRequest, requestId = 33
13:00:27.764 [nacos-grpc-client-executor-3823] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21b56e0f-9e81-4fa4-a362-d684408ba282] Ack server push request, request = NotifySubscriberRequest, requestId = 33
13:00:31.386 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:00:31.390 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:00:31.726 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:00:31.727 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7ce80f34[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:00:31.727 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752026255815_127.0.0.1_8933
13:00:31.729 [nacos-grpc-client-executor-3826] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752026255815_127.0.0.1_8933]Ignore complete event,isRunning:false,isAbandon=false
13:00:31.740 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7a07cddd[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3827]
13:00:37.480 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:00:38.103 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0
13:00:38.181 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
13:00:38.214 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
13:00:38.224 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
13:00:38.233 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
13:00:38.242 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
13:00:38.255 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
13:00:38.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:00:38.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001bd013cc958
13:00:38.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001bd013ccb78
13:00:38.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:00:38.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:00:38.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:00:39.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752037239178_127.0.0.1_1597
13:00:39.393 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0] Notify connected event to listeners.
13:00:39.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:00:39.395 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001bd015069a8
13:00:39.550 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:00:42.998 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
13:00:44.144 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
13:00:44.669 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4618ca91-64cc-40cd-b05b-fdc7824586cf_config-0
13:00:44.670 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4618ca91-64cc-40cd-b05b-fdc7824586cf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:00:44.670 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4618ca91-64cc-40cd-b05b-fdc7824586cf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001bd013cc958
13:00:44.670 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4618ca91-64cc-40cd-b05b-fdc7824586cf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001bd013ccb78
13:00:44.670 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4618ca91-64cc-40cd-b05b-fdc7824586cf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:00:44.670 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4618ca91-64cc-40cd-b05b-fdc7824586cf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:00:44.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4618ca91-64cc-40cd-b05b-fdc7824586cf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:00:44.792 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4618ca91-64cc-40cd-b05b-fdc7824586cf_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752037244681_127.0.0.1_1646
13:00:44.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4618ca91-64cc-40cd-b05b-fdc7824586cf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:00:44.793 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4618ca91-64cc-40cd-b05b-fdc7824586cf_config-0] Notify connected event to listeners.
13:00:44.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4618ca91-64cc-40cd-b05b-fdc7824586cf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001bd015069a8
13:00:44.960 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 941f87fc-f750-4bb2-98bd-a9dea68a5d9b
13:00:44.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] RpcClient init label, labels = {module=naming, source=sdk}
13:00:44.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:00:44.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:00:44.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:00:44.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:00:45.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Success to connect to server [localhost:8848] on start up, connectionId = 1752037244973_127.0.0.1_1649
13:00:45.089 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Notify connected event to listeners.
13:00:45.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:00:45.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001bd015069a8
13:00:45.664 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
13:00:45.710 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 34
13:00:45.720 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 8.944 seconds (JVM running for 10.06)
13:00:45.724 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 34
13:00:45.776 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
13:00:45.778 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
13:00:45.779 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
13:00:45.796 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 35
13:00:45.797 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 35
13:00:45.810 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 36
13:00:45.813 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 36
13:00:45.825 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 37
13:00:45.829 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 37
13:00:45.845 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 38
13:00:45.845 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 38
13:00:45.863 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 39
13:00:45.863 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 39
13:08:27.892 [nacos-grpc-client-executor-214] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 40
13:08:27.910 [nacos-grpc-client-executor-214] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 40
13:08:39.262 [nacos-grpc-client-executor-216] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 41
13:08:39.279 [nacos-grpc-client-executor-216] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 41
13:13:52.056 [nacos-grpc-client-executor-169] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0] Receive server push request, request = ConfigChangeNotifyRequest, requestId = 42
13:13:52.070 [nacos-grpc-client-executor-169] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0] Ack server push request, request = ConfigChangeNotifyRequest, requestId = 42
13:13:57.969 [nacos-grpc-client-executor-332] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 43
13:13:57.999 [nacos-grpc-client-executor-332] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 43
13:14:10.845 [nacos-grpc-client-executor-334] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 44
13:14:10.870 [nacos-grpc-client-executor-334] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 44
13:36:31.780 [nacos-grpc-client-executor-447] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0] Receive server push request, request = ConfigChangeNotifyRequest, requestId = 45
13:36:31.780 [nacos-grpc-client-executor-447] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8db62a2-ab78-4741-a4d4-1f9cd21136af_config-0] Ack server push request, request = ConfigChangeNotifyRequest, requestId = 45
13:36:52.630 [nacos-grpc-client-executor-830] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 46
13:36:52.650 [nacos-grpc-client-executor-830] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 46
13:37:05.465 [nacos-grpc-client-executor-833] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 47
13:37:05.488 [nacos-grpc-client-executor-833] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 47
13:54:44.268 [nacos-grpc-client-executor-1209] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 48
13:54:44.279 [nacos-grpc-client-executor-1209] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 48
13:54:54.217 [nacos-grpc-client-executor-1220] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 49
13:54:54.227 [nacos-grpc-client-executor-1220] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 49
14:35:02.898 [nacos-grpc-client-executor-2025] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 50
14:35:02.927 [nacos-grpc-client-executor-2025] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 50
14:35:13.886 [nacos-grpc-client-executor-2027] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 51
14:35:13.899 [nacos-grpc-client-executor-2027] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 51
14:36:44.141 [nacos-grpc-client-executor-2054] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 52
14:36:44.154 [nacos-grpc-client-executor-2054] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 52
14:36:55.377 [nacos-grpc-client-executor-2063] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 53
14:36:55.397 [nacos-grpc-client-executor-2063] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 53
14:39:09.626 [nacos-grpc-client-executor-2106] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 54
14:39:09.642 [nacos-grpc-client-executor-2106] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 54
14:39:16.487 [nacos-grpc-client-executor-2108] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Receive server push request, request = NotifySubscriberRequest, requestId = 55
14:39:16.498 [nacos-grpc-client-executor-2108] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [941f87fc-f750-4bb2-98bd-a9dea68a5d9b] Ack server push request, request = NotifySubscriberRequest, requestId = 55
15:14:47.738 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:14:47.748 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:14:48.088 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:14:48.088 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@db3b33a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:14:48.088 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752037244973_127.0.0.1_1649
15:14:48.088 [nacos-grpc-client-executor-2838] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752037244973_127.0.0.1_1649]Ignore complete event,isRunning:false,isAbandon=false
15:14:48.109 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4a4a0d67[Running, pool size = 13, active threads = 0, queued tasks = 0, completed tasks = 2839]
15:32:22.511 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:32:23.063 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6a3a3547-6a6f-452b-a736-891c3dc3f099_config-0
15:32:23.145 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
15:32:23.176 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
15:32:23.187 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
15:32:23.200 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
15:32:23.213 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
15:32:23.223 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
15:32:23.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a3a3547-6a6f-452b-a736-891c3dc3f099_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:32:23.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a3a3547-6a6f-452b-a736-891c3dc3f099_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001d9df3bc2b8
15:32:23.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a3a3547-6a6f-452b-a736-891c3dc3f099_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001d9df3bc4d8
15:32:23.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a3a3547-6a6f-452b-a736-891c3dc3f099_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:32:23.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a3a3547-6a6f-452b-a736-891c3dc3f099_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:32:23.238 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a3a3547-6a6f-452b-a736-891c3dc3f099_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:32:24.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a3a3547-6a6f-452b-a736-891c3dc3f099_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752046344041_127.0.0.1_8597
15:32:24.259 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a3a3547-6a6f-452b-a736-891c3dc3f099_config-0] Notify connected event to listeners.
15:32:24.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a3a3547-6a6f-452b-a736-891c3dc3f099_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:32:24.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a3a3547-6a6f-452b-a736-891c3dc3f099_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001d9df4f6350
15:32:24.367 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:32:29.514 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
15:32:30.871 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
15:32:31.506 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 90466a89-c2cb-4f1f-81dc-a4895a66560c_config-0
15:32:31.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90466a89-c2cb-4f1f-81dc-a4895a66560c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:32:31.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90466a89-c2cb-4f1f-81dc-a4895a66560c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001d9df3bc2b8
15:32:31.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90466a89-c2cb-4f1f-81dc-a4895a66560c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001d9df3bc4d8
15:32:31.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90466a89-c2cb-4f1f-81dc-a4895a66560c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:32:31.507 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90466a89-c2cb-4f1f-81dc-a4895a66560c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:32:31.507 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90466a89-c2cb-4f1f-81dc-a4895a66560c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:32:31.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90466a89-c2cb-4f1f-81dc-a4895a66560c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752046351516_127.0.0.1_8668
15:32:31.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90466a89-c2cb-4f1f-81dc-a4895a66560c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:32:31.628 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90466a89-c2cb-4f1f-81dc-a4895a66560c_config-0] Notify connected event to listeners.
15:32:31.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [90466a89-c2cb-4f1f-81dc-a4895a66560c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001d9df4f6350
15:32:31.782 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 67e1c7d7-39ad-4002-a1e7-fafb2c0aad40
15:32:31.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] RpcClient init label, labels = {module=naming, source=sdk}
15:32:31.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:32:31.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:32:31.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:32:31.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:32:31.922 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Success to connect to server [localhost:8848] on start up, connectionId = 1752046351798_127.0.0.1_8671
15:32:31.922 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Notify connected event to listeners.
15:32:31.922 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:32:31.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001d9df4f6350
15:32:32.502 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Receive server push request, request = NotifySubscriberRequest, requestId = 56
15:32:32.503 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Ack server push request, request = NotifySubscriberRequest, requestId = 56
15:32:32.536 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
15:32:32.582 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 10.734 seconds (JVM running for 11.848)
15:32:32.590 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
15:32:32.591 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
15:32:32.592 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
15:32:32.595 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Receive server push request, request = NotifySubscriberRequest, requestId = 57
15:32:32.596 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Ack server push request, request = NotifySubscriberRequest, requestId = 57
15:32:33.050 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Receive server push request, request = NotifySubscriberRequest, requestId = 58
15:32:33.050 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Ack server push request, request = NotifySubscriberRequest, requestId = 58
15:33:02.703 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Receive server push request, request = NotifySubscriberRequest, requestId = 64
15:33:02.704 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Ack server push request, request = NotifySubscriberRequest, requestId = 64
15:33:02.721 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Receive server push request, request = NotifySubscriberRequest, requestId = 66
15:33:02.721 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Ack server push request, request = NotifySubscriberRequest, requestId = 66
15:33:02.751 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Receive server push request, request = NotifySubscriberRequest, requestId = 65
15:33:02.752 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Ack server push request, request = NotifySubscriberRequest, requestId = 65
15:33:02.765 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Receive server push request, request = NotifySubscriberRequest, requestId = 63
15:33:02.767 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67e1c7d7-39ad-4002-a1e7-fafb2c0aad40] Ack server push request, request = NotifySubscriberRequest, requestId = 63
15:36:46.963 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a3a3547-6a6f-452b-a736-891c3dc3f099_config-0] Receive server push request, request = ConfigChangeNotifyRequest, requestId = 67
15:36:46.965 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a3a3547-6a6f-452b-a736-891c3dc3f099_config-0] Ack server push request, request = ConfigChangeNotifyRequest, requestId = 67
15:37:06.986 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:37:06.991 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:37:07.332 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:37:07.333 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@39195782[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:37:07.333 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752046351798_127.0.0.1_8671
15:37:07.336 [nacos-grpc-client-executor-134] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752046351798_127.0.0.1_8671]Ignore complete event,isRunning:false,isAbandon=false
15:37:07.338 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7d33e576[Running, pool size = 10, active threads = 0, queued tasks = 0, completed tasks = 135]
15:37:12.490 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:37:13.088 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 493b0124-b4af-4487-9935-8af52e7ab6fe_config-0
15:37:13.158 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
15:37:13.185 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
15:37:13.193 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
15:37:13.203 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
15:37:13.216 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
15:37:13.224 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
15:37:13.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [493b0124-b4af-4487-9935-8af52e7ab6fe_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:37:13.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [493b0124-b4af-4487-9935-8af52e7ab6fe_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000215013bb650
15:37:13.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [493b0124-b4af-4487-9935-8af52e7ab6fe_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000215013bb870
15:37:13.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [493b0124-b4af-4487-9935-8af52e7ab6fe_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:37:13.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [493b0124-b4af-4487-9935-8af52e7ab6fe_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:37:13.238 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [493b0124-b4af-4487-9935-8af52e7ab6fe_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:37:14.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [493b0124-b4af-4487-9935-8af52e7ab6fe_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752046634104_127.0.0.1_9240
15:37:14.306 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [493b0124-b4af-4487-9935-8af52e7ab6fe_config-0] Notify connected event to listeners.
15:37:14.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [493b0124-b4af-4487-9935-8af52e7ab6fe_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:37:14.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [493b0124-b4af-4487-9935-8af52e7ab6fe_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000215014f4d90
15:37:14.472 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:37:18.179 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
15:37:19.299 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
15:37:19.814 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b35940bd-6edb-4e76-9567-b2664dce0ac0_config-0
15:37:19.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b35940bd-6edb-4e76-9567-b2664dce0ac0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:37:19.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b35940bd-6edb-4e76-9567-b2664dce0ac0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000215013bb650
15:37:19.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b35940bd-6edb-4e76-9567-b2664dce0ac0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000215013bb870
15:37:19.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b35940bd-6edb-4e76-9567-b2664dce0ac0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:37:19.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b35940bd-6edb-4e76-9567-b2664dce0ac0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:37:19.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b35940bd-6edb-4e76-9567-b2664dce0ac0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:37:19.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b35940bd-6edb-4e76-9567-b2664dce0ac0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752046639827_127.0.0.1_9252
15:37:19.942 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b35940bd-6edb-4e76-9567-b2664dce0ac0_config-0] Notify connected event to listeners.
15:37:19.942 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b35940bd-6edb-4e76-9567-b2664dce0ac0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:37:19.943 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b35940bd-6edb-4e76-9567-b2664dce0ac0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000215014f4d90
15:37:20.098 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d440a757-aaa2-40f8-ac0c-98cf91dab397
15:37:20.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] RpcClient init label, labels = {module=naming, source=sdk}
15:37:20.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:37:20.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:37:20.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:37:20.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:37:20.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Success to connect to server [localhost:8848] on start up, connectionId = 1752046640115_127.0.0.1_9253
15:37:20.233 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Notify connected event to listeners.
15:37:20.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:37:20.233 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000215014f4d90
15:37:20.696 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
15:37:20.743 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 8.848 seconds (JVM running for 9.78)
15:37:20.751 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
15:37:20.752 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
15:37:20.752 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
15:37:20.841 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Receive server push request, request = NotifySubscriberRequest, requestId = 68
15:37:20.860 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Ack server push request, request = NotifySubscriberRequest, requestId = 68
15:37:20.921 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Receive server push request, request = NotifySubscriberRequest, requestId = 70
15:37:20.922 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Ack server push request, request = NotifySubscriberRequest, requestId = 70
15:37:20.937 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Receive server push request, request = NotifySubscriberRequest, requestId = 72
15:37:20.940 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Ack server push request, request = NotifySubscriberRequest, requestId = 72
15:37:20.952 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Receive server push request, request = NotifySubscriberRequest, requestId = 71
15:37:20.953 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Ack server push request, request = NotifySubscriberRequest, requestId = 71
15:37:20.962 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Receive server push request, request = NotifySubscriberRequest, requestId = 73
15:37:20.963 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Ack server push request, request = NotifySubscriberRequest, requestId = 73
15:37:20.977 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Receive server push request, request = NotifySubscriberRequest, requestId = 69
15:37:20.978 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d440a757-aaa2-40f8-ac0c-98cf91dab397] Ack server push request, request = NotifySubscriberRequest, requestId = 69
16:28:55.230 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:28:55.245 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:28:55.596 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:28:55.598 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@389ea7b8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:28:55.598 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752046640115_127.0.0.1_9253
16:28:55.602 [nacos-grpc-client-executor-1161] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752046640115_127.0.0.1_9253]Ignore complete event,isRunning:false,isAbandon=false
16:28:55.612 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2cc66fab[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 1162]
17:36:05.646 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:36:06.755 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 75cf4201-a74d-433c-846b-5a92ad8cf743_config-0
17:36:06.945 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 71 ms to scan 1 urls, producing 3 keys and 6 values 
17:36:07.008 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
17:36:07.027 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
17:36:07.048 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
17:36:07.074 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 7 values 
17:36:07.090 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
17:36:07.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf4201-a74d-433c-846b-5a92ad8cf743_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:36:07.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf4201-a74d-433c-846b-5a92ad8cf743_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000218473cb3d8
17:36:07.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf4201-a74d-433c-846b-5a92ad8cf743_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000218473cb5f8
17:36:07.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf4201-a74d-433c-846b-5a92ad8cf743_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:36:07.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf4201-a74d-433c-846b-5a92ad8cf743_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:36:07.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf4201-a74d-433c-846b-5a92ad8cf743_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:36:09.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf4201-a74d-433c-846b-5a92ad8cf743_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752053768923_127.0.0.1_10970
17:36:09.273 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf4201-a74d-433c-846b-5a92ad8cf743_config-0] Notify connected event to listeners.
17:36:09.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf4201-a74d-433c-846b-5a92ad8cf743_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:36:09.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75cf4201-a74d-433c-846b-5a92ad8cf743_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000021847504d90
17:36:09.665 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:36:19.415 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
17:36:21.535 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
17:36:22.453 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 25f52970-fa19-469d-bfa5-bd9dcdc06f43_config-0
17:36:22.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25f52970-fa19-469d-bfa5-bd9dcdc06f43_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:36:22.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25f52970-fa19-469d-bfa5-bd9dcdc06f43_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000218473cb3d8
17:36:22.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25f52970-fa19-469d-bfa5-bd9dcdc06f43_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000218473cb5f8
17:36:22.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25f52970-fa19-469d-bfa5-bd9dcdc06f43_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:36:22.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25f52970-fa19-469d-bfa5-bd9dcdc06f43_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:36:22.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25f52970-fa19-469d-bfa5-bd9dcdc06f43_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:36:22.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25f52970-fa19-469d-bfa5-bd9dcdc06f43_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752053782473_127.0.0.1_11031
17:36:22.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25f52970-fa19-469d-bfa5-bd9dcdc06f43_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:36:22.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25f52970-fa19-469d-bfa5-bd9dcdc06f43_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000021847504d90
17:36:22.591 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25f52970-fa19-469d-bfa5-bd9dcdc06f43_config-0] Notify connected event to listeners.
17:36:22.752 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e618b72c-448b-439d-9e50-2a81b356cb7f
17:36:22.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] RpcClient init label, labels = {module=naming, source=sdk}
17:36:22.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:36:22.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:36:22.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:36:22.757 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:36:22.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Success to connect to server [localhost:8848] on start up, connectionId = 1752053782773_127.0.0.1_11032
17:36:22.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:36:22.893 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Notify connected event to listeners.
17:36:22.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000021847504d90
17:36:23.503 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
17:36:23.540 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Receive server push request, request = NotifySubscriberRequest, requestId = 75
17:36:23.556 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Ack server push request, request = NotifySubscriberRequest, requestId = 75
17:36:23.557 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 18.924 seconds (JVM running for 20.462)
17:36:23.572 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
17:36:23.573 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
17:36:23.574 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
17:36:53.678 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Receive server push request, request = NotifySubscriberRequest, requestId = 77
17:36:53.685 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Ack server push request, request = NotifySubscriberRequest, requestId = 77
17:37:24.180 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Receive server push request, request = NotifySubscriberRequest, requestId = 79
17:37:24.182 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Ack server push request, request = NotifySubscriberRequest, requestId = 79
17:37:53.659 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Receive server push request, request = NotifySubscriberRequest, requestId = 80
17:37:53.659 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Ack server push request, request = NotifySubscriberRequest, requestId = 80
17:38:53.678 [nacos-grpc-client-executor-73] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Receive server push request, request = NotifySubscriberRequest, requestId = 83
17:38:53.680 [nacos-grpc-client-executor-73] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Ack server push request, request = NotifySubscriberRequest, requestId = 83
17:38:53.701 [nacos-grpc-client-executor-74] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Receive server push request, request = NotifySubscriberRequest, requestId = 84
17:38:53.706 [nacos-grpc-client-executor-74] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Ack server push request, request = NotifySubscriberRequest, requestId = 84
17:41:32.749 [nacos-grpc-client-executor-130] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Receive server push request, request = NotifySubscriberRequest, requestId = 85
17:41:32.764 [nacos-grpc-client-executor-130] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Ack server push request, request = NotifySubscriberRequest, requestId = 85
17:49:57.517 [nacos-grpc-client-executor-300] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Receive server push request, request = NotifySubscriberRequest, requestId = 86
17:49:57.534 [nacos-grpc-client-executor-300] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e618b72c-448b-439d-9e50-2a81b356cb7f] Ack server push request, request = NotifySubscriberRequest, requestId = 86
19:28:57.538 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:28:57.545 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:28:57.891 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:28:57.891 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@74e953df[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:28:57.892 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752053782773_127.0.0.1_11032
19:28:57.895 [nacos-grpc-client-executor-2275] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752053782773_127.0.0.1_11032]Ignore complete event,isRunning:false,isAbandon=false
19:28:57.911 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4401f82f[Running, pool size = 12, active threads = 0, queued tasks = 0, completed tasks = 2276]
