package com.heju.system.api.fileInfo.feign.factory;

import com.heju.common.core.web.result.R;
import com.heju.system.api.fileInfo.feign.RemoteFileInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


/**
 * 字典服务 降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteFileInfoFallbackFactory implements FallbackFactory<RemoteFileInfoService> {

    @Override
    public RemoteFileInfoService create(Throwable cause) {
        return new RemoteFileInfoService(){

            @Override
            public R<Integer> deleteExpiredFiles(Long enterpriseId, String sourceName, String source) {
                return R.fail("删除文件失败:" + cause.getMessage());
            }
        };
    }
}