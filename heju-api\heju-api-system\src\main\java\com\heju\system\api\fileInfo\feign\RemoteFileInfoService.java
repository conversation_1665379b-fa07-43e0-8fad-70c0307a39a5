package com.heju.system.api.fileInfo.feign;

import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.constant.basic.ServiceConstants;
import com.heju.common.core.web.result.R;
import com.heju.system.api.dict.feign.factory.RemoteDictFallbackFactory;
import com.heju.system.api.fileInfo.feign.factory.RemoteFileInfoFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 字典服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteFileInfoService", value = ServiceConstants.SYSTEM_SERVICE, fallbackFactory = RemoteFileInfoFallbackFactory.class)
public interface RemoteFileInfoService {

    @PostMapping("/file/info/deleteExpiredFiles")
    R<Integer> deleteExpiredFiles(@RequestHeader(SecurityConstants.ENTERPRISE_ID) Long enterpriseId,@RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}