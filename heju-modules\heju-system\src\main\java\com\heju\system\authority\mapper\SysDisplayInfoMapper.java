package com.heju.system.authority.mapper;

import com.heju.system.authority.domain.query.SysDisplayInfoQuery;
import com.heju.system.authority.domain.dto.SysDisplayInfoDto;
import com.heju.system.authority.domain.po.SysDisplayInfoPo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;

/**
 * 显隐列管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysDisplayInfoMapper extends BaseMapper<SysDisplayInfoQuery, SysDisplayInfoDto, SysDisplayInfoPo> {
}
