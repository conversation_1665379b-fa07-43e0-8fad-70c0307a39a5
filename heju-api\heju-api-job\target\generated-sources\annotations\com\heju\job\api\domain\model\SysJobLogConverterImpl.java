package com.heju.job.api.domain.model;

import com.github.pagehelper.Page;
import com.heju.job.api.domain.dto.SysJobLogDto;
import com.heju.job.api.domain.po.SysJobLogPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:46+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysJobLogConverterImpl implements SysJobLogConverter {

    @Override
    public SysJobLogDto mapperDto(SysJobLogPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysJobLogDto sysJobLogDto = new SysJobLogDto();

        sysJobLogDto.setId( arg0.getId() );
        sysJobLogDto.setSourceName( arg0.getSourceName() );
        sysJobLogDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysJobLogDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysJobLogDto.setName( arg0.getName() );
        sysJobLogDto.setSort( arg0.getSort() );
        sysJobLogDto.setRemark( arg0.getRemark() );
        sysJobLogDto.setCreateBy( arg0.getCreateBy() );
        sysJobLogDto.setCreateTime( arg0.getCreateTime() );
        sysJobLogDto.setUpdateBy( arg0.getUpdateBy() );
        sysJobLogDto.setUpdateTime( arg0.getUpdateTime() );
        sysJobLogDto.setDelFlag( arg0.getDelFlag() );
        sysJobLogDto.setCreateName( arg0.getCreateName() );
        sysJobLogDto.setUpdateName( arg0.getUpdateName() );
        sysJobLogDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysJobLogDto.setJobId( arg0.getJobId() );
        sysJobLogDto.setJobGroup( arg0.getJobGroup() );
        sysJobLogDto.setInvokeTarget( arg0.getInvokeTarget() );
        sysJobLogDto.setInvokeTenant( arg0.getInvokeTenant() );
        sysJobLogDto.setJobMessage( arg0.getJobMessage() );
        sysJobLogDto.setStatus( arg0.getStatus() );
        sysJobLogDto.setExceptionInfo( arg0.getExceptionInfo() );

        return sysJobLogDto;
    }

    @Override
    public List<SysJobLogDto> mapperDto(Collection<SysJobLogPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysJobLogDto> list = new ArrayList<SysJobLogDto>( arg0.size() );
        for ( SysJobLogPo sysJobLogPo : arg0 ) {
            list.add( mapperDto( sysJobLogPo ) );
        }

        return list;
    }

    @Override
    public Page<SysJobLogDto> mapperPageDto(Collection<SysJobLogPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysJobLogDto> page = new Page<SysJobLogDto>();
        for ( SysJobLogPo sysJobLogPo : arg0 ) {
            page.add( mapperDto( sysJobLogPo ) );
        }

        return page;
    }

    @Override
    public SysJobLogPo mapperPo(SysJobLogDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysJobLogPo sysJobLogPo = new SysJobLogPo();

        sysJobLogPo.setId( arg0.getId() );
        sysJobLogPo.setSourceName( arg0.getSourceName() );
        sysJobLogPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysJobLogPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysJobLogPo.setName( arg0.getName() );
        sysJobLogPo.setSort( arg0.getSort() );
        sysJobLogPo.setRemark( arg0.getRemark() );
        sysJobLogPo.setCreateBy( arg0.getCreateBy() );
        sysJobLogPo.setCreateTime( arg0.getCreateTime() );
        sysJobLogPo.setUpdateBy( arg0.getUpdateBy() );
        sysJobLogPo.setUpdateTime( arg0.getUpdateTime() );
        sysJobLogPo.setDelFlag( arg0.getDelFlag() );
        sysJobLogPo.setCreateName( arg0.getCreateName() );
        sysJobLogPo.setUpdateName( arg0.getUpdateName() );
        sysJobLogPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysJobLogPo.setJobId( arg0.getJobId() );
        sysJobLogPo.setJobGroup( arg0.getJobGroup() );
        sysJobLogPo.setInvokeTarget( arg0.getInvokeTarget() );
        sysJobLogPo.setInvokeTenant( arg0.getInvokeTenant() );
        sysJobLogPo.setJobMessage( arg0.getJobMessage() );
        sysJobLogPo.setStatus( arg0.getStatus() );
        sysJobLogPo.setExceptionInfo( arg0.getExceptionInfo() );

        return sysJobLogPo;
    }

    @Override
    public List<SysJobLogPo> mapperPo(Collection<SysJobLogDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysJobLogPo> list = new ArrayList<SysJobLogPo>( arg0.size() );
        for ( SysJobLogDto sysJobLogDto : arg0 ) {
            list.add( mapperPo( sysJobLogDto ) );
        }

        return list;
    }

    @Override
    public Page<SysJobLogPo> mapperPagePo(Collection<SysJobLogDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysJobLogPo> page = new Page<SysJobLogPo>();
        for ( SysJobLogDto sysJobLogDto : arg0 ) {
            page.add( mapperPo( sysJobLogDto ) );
        }

        return page;
    }
}
