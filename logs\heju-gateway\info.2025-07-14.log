09:36:49.134 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:36:49.962 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 64f6c5e4-9778-4303-bef4-df4dac5ccdbb_config-0
09:36:50.069 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 49 ms to scan 1 urls, producing 3 keys and 6 values 
09:36:50.105 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:36:50.116 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:36:50.127 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:36:50.143 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:36:50.153 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
09:36:50.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64f6c5e4-9778-4303-bef4-df4dac5ccdbb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:36:50.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64f6c5e4-9778-4303-bef4-df4dac5ccdbb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001d7a83b42b8
09:36:50.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64f6c5e4-9778-4303-bef4-df4dac5ccdbb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001d7a83b44d8
09:36:50.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64f6c5e4-9778-4303-bef4-df4dac5ccdbb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:36:50.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64f6c5e4-9778-4303-bef4-df4dac5ccdbb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:36:50.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64f6c5e4-9778-4303-bef4-df4dac5ccdbb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:51.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64f6c5e4-9778-4303-bef4-df4dac5ccdbb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752457011358_127.0.0.1_3176
09:36:51.691 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64f6c5e4-9778-4303-bef4-df4dac5ccdbb_config-0] Notify connected event to listeners.
09:36:51.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64f6c5e4-9778-4303-bef4-df4dac5ccdbb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:36:51.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64f6c5e4-9778-4303-bef4-df4dac5ccdbb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001d7a84f5d18
09:36:51.947 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:36:57.588 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:36:59.148 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:36:59.983 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c3daafa2-ec8e-4cec-b7eb-a21c1cef5c70_config-0
09:36:59.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3daafa2-ec8e-4cec-b7eb-a21c1cef5c70_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:36:59.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3daafa2-ec8e-4cec-b7eb-a21c1cef5c70_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001d7a83b42b8
09:36:59.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3daafa2-ec8e-4cec-b7eb-a21c1cef5c70_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001d7a83b44d8
09:36:59.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3daafa2-ec8e-4cec-b7eb-a21c1cef5c70_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:36:59.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3daafa2-ec8e-4cec-b7eb-a21c1cef5c70_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:36:59.986 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3daafa2-ec8e-4cec-b7eb-a21c1cef5c70_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:37:00.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3daafa2-ec8e-4cec-b7eb-a21c1cef5c70_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752457019997_127.0.0.1_3218
09:37:00.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3daafa2-ec8e-4cec-b7eb-a21c1cef5c70_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:37:00.117 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3daafa2-ec8e-4cec-b7eb-a21c1cef5c70_config-0] Notify connected event to listeners.
09:37:00.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3daafa2-ec8e-4cec-b7eb-a21c1cef5c70_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001d7a84f5d18
09:37:00.354 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1a13e5a5-2f69-4f80-877d-4bba62e072cf
09:37:00.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] RpcClient init label, labels = {module=naming, source=sdk}
09:37:00.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:37:00.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:37:00.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:37:00.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:37:00.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Success to connect to server [localhost:8848] on start up, connectionId = 1752457020376_127.0.0.1_3220
09:37:00.519 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Notify connected event to listeners.
09:37:00.519 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:37:00.519 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001d7a84f5d18
09:37:03.697 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:37:03.711 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:37:03.730 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:37:03.732 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:37:05.192 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.2:8081 register finished
09:37:05.296 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 17.074 seconds (JVM running for 20.144)
09:37:05.313 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:37:05.315 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:37:05.316 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:37:05.716 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:37:05.719 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:37:31.587 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:37:31.587 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:37:31.594 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:37:31.594 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:37:31.602 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:37:31.602 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a13e5a5-2f69-4f80-877d-4bba62e072cf] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:44:14.710 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:44:14.715 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:44:15.049 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:44:15.050 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@565372c6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:44:15.050 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752457020376_127.0.0.1_3220
09:44:15.053 [nacos-grpc-client-executor-193] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752457020376_127.0.0.1_3220]Ignore complete event,isRunning:false,isAbandon=false
09:44:15.118 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7680d2c1[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 194]
09:50:23.992 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:50:24.782 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5a34ccfd-0d92-4c8b-a5e9-452904693a2b_config-0
09:50:24.876 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
09:50:24.908 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:50:24.918 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:50:24.931 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:50:24.944 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:50:24.957 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:50:24.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a34ccfd-0d92-4c8b-a5e9-452904693a2b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:50:24.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a34ccfd-0d92-4c8b-a5e9-452904693a2b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000268813bb8c8
09:50:24.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a34ccfd-0d92-4c8b-a5e9-452904693a2b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000268813bbae8
09:50:24.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a34ccfd-0d92-4c8b-a5e9-452904693a2b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:50:24.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a34ccfd-0d92-4c8b-a5e9-452904693a2b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:50:24.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a34ccfd-0d92-4c8b-a5e9-452904693a2b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:50:26.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a34ccfd-0d92-4c8b-a5e9-452904693a2b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752457825972_127.0.0.1_5725
09:50:26.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a34ccfd-0d92-4c8b-a5e9-452904693a2b_config-0] Notify connected event to listeners.
09:50:26.195 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a34ccfd-0d92-4c8b-a5e9-452904693a2b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:50:26.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a34ccfd-0d92-4c8b-a5e9-452904693a2b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000268814f5418
09:50:26.384 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:50:32.691 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:50:35.463 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:50:36.643 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4118dd29-6077-4020-a704-0036d5741878_config-0
09:50:36.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4118dd29-6077-4020-a704-0036d5741878_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:50:36.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4118dd29-6077-4020-a704-0036d5741878_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000268813bb8c8
09:50:36.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4118dd29-6077-4020-a704-0036d5741878_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000268813bbae8
09:50:36.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4118dd29-6077-4020-a704-0036d5741878_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:50:36.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4118dd29-6077-4020-a704-0036d5741878_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:50:36.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4118dd29-6077-4020-a704-0036d5741878_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:50:36.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4118dd29-6077-4020-a704-0036d5741878_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752457836656_127.0.0.1_5768
09:50:36.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4118dd29-6077-4020-a704-0036d5741878_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:50:36.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4118dd29-6077-4020-a704-0036d5741878_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000268814f5418
09:50:36.774 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4118dd29-6077-4020-a704-0036d5741878_config-0] Notify connected event to listeners.
09:50:36.974 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 64a32868-a68d-4503-a946-9031d4c10d16
09:50:36.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] RpcClient init label, labels = {module=naming, source=sdk}
09:50:36.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:50:36.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:50:36.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:50:36.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:50:37.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Success to connect to server [localhost:8848] on start up, connectionId = 1752457836993_127.0.0.1_5771
09:50:37.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:50:37.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Notify connected event to listeners.
09:50:37.115 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000268814f5418
09:50:37.745 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:50:37.746 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 14
09:50:37.820 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 15
09:50:37.821 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 15
09:50:39.013 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.2:8081 register finished
09:50:39.071 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 15.93 seconds (JVM running for 17.327)
09:50:39.088 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:50:39.089 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:50:39.090 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:50:39.598 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 16
09:50:39.600 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 16
09:50:57.696 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 20
09:50:57.699 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 20
09:50:57.805 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 21
09:50:57.807 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 21
09:50:57.835 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 22
09:50:57.837 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 22
10:43:56.614 [nacos-grpc-client-executor-1023] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 26
10:43:56.640 [nacos-grpc-client-executor-1023] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 26
10:44:22.275 [nacos-grpc-client-executor-1032] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 29
10:44:22.289 [nacos-grpc-client-executor-1032] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 29
10:59:45.945 [nacos-grpc-client-executor-1296] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 31
10:59:45.969 [nacos-grpc-client-executor-1296] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 31
11:00:11.980 [nacos-grpc-client-executor-1304] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 33
11:00:11.999 [nacos-grpc-client-executor-1304] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 33
11:10:39.655 [nacos-grpc-client-executor-1492] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 36
11:10:39.687 [nacos-grpc-client-executor-1492] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 36
11:11:03.975 [nacos-grpc-client-executor-1500] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 39
11:11:03.992 [nacos-grpc-client-executor-1500] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 39
11:47:29.611 [nacos-grpc-client-executor-2138] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 40
11:47:29.611 [nacos-grpc-client-executor-2138] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 40
13:50:07.926 [nacos-grpc-client-executor-4368] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 42
13:50:07.952 [nacos-grpc-client-executor-4368] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 42
13:50:45.113 [nacos-grpc-client-executor-4382] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 45
13:50:45.128 [nacos-grpc-client-executor-4382] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 45
14:11:31.655 [nacos-grpc-client-executor-4763] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 47
14:11:31.658 [nacos-grpc-client-executor-4763] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 47
14:11:41.928 [nacos-grpc-client-executor-4765] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 49
14:11:41.944 [nacos-grpc-client-executor-4765] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 49
14:55:29.058 [nacos-grpc-client-executor-5565] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 89
14:55:29.074 [nacos-grpc-client-executor-5565] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 89
14:59:51.129 [nacos-grpc-client-executor-5647] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Receive server push request, request = NotifySubscriberRequest, requestId = 92
14:59:51.147 [nacos-grpc-client-executor-5647] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64a32868-a68d-4503-a946-9031d4c10d16] Ack server push request, request = NotifySubscriberRequest, requestId = 92
18:09:27.495 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:09:27.495 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:09:27.830 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:09:27.830 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@61d77b7c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:09:27.830 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752457836993_127.0.0.1_5771
18:09:27.830 [nacos-grpc-client-executor-9026] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752457836993_127.0.0.1_5771]Ignore complete event,isRunning:false,isAbandon=false
18:09:27.830 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5622fd26[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 9027]
