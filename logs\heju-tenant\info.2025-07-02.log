09:21:32.053 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:21:35.884 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0
09:21:36.130 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 137 ms to scan 1 urls, producing 3 keys and 6 values 
09:21:36.227 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 43 ms to scan 1 urls, producing 4 keys and 9 values 
09:21:36.292 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 56 ms to scan 1 urls, producing 3 keys and 10 values 
09:21:36.337 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 26 ms to scan 1 urls, producing 1 keys and 5 values 
09:21:36.375 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 38 ms to scan 1 urls, producing 1 keys and 7 values 
09:21:36.423 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 2 keys and 8 values 
09:21:36.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:21:36.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000022ccd39d7b8
09:21:36.438 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000022ccd39d9d8
09:21:36.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:21:36.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:21:36.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:39.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:39.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:39.463 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:21:39.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:39.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000022ccd4ab938
09:21:39.656 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:39.941 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:40.275 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:40.812 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:41.414 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:42.062 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:42.143 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:21:42.787 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:43.617 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:44.544 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:45.871 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:47.251 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:48.886 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:50.515 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:52.242 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:53.995 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:55.876 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:56.112 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:21:56.126 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:21:56.126 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:21:56.737 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:21:58.793 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:21:58.798 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:21:58.801 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:21:59.531 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:22:01.571 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:22:03.787 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:22:06.050 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:22:08.414 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:22:09.183 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:22:11.188 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1751419330994_127.0.0.1_3469
09:22:11.192 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f59f6f95-2e75-43dc-876d-fe47f4b939d3_config-0] Notify connected event to listeners.
09:22:18.519 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 33a67766-c1f3-4b83-b030-33c0885a4d15
09:22:18.519 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33a67766-c1f3-4b83-b030-33c0885a4d15] RpcClient init label, labels = {module=naming, source=sdk}
09:22:18.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33a67766-c1f3-4b83-b030-33c0885a4d15] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:22:18.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33a67766-c1f3-4b83-b030-33c0885a4d15] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:22:18.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33a67766-c1f3-4b83-b030-33c0885a4d15] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:22:18.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33a67766-c1f3-4b83-b030-33c0885a4d15] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:22:18.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33a67766-c1f3-4b83-b030-33c0885a4d15] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751419338541_127.0.0.1_3475
09:22:18.674 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33a67766-c1f3-4b83-b030-33c0885a4d15] Notify connected event to listeners.
09:22:18.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33a67766-c1f3-4b83-b030-33c0885a4d15] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:22:18.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33a67766-c1f3-4b83-b030-33c0885a4d15] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000022ccd4ab938
09:22:18.945 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
09:22:19.513 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 50.663 seconds (JVM running for 58.192)
09:22:19.561 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:22:19.563 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:22:19.565 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:22:19.685 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:22:19.705 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:22:20.053 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:22:20.053 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2c4df7b5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:22:20.053 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751419338541_127.0.0.1_3475
09:22:20.055 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4c940a67[Running, pool size = 11, active threads = 1, queued tasks = 0, completed tasks = 10]
09:22:20.073 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33a67766-c1f3-4b83-b030-33c0885a4d15] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:22:20.115 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33a67766-c1f3-4b83-b030-33c0885a4d15] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:22:20.125 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:22:20.130 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:22:20.151 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:22:20.151 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:22:28.221 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:22:30.288 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0
09:22:30.494 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 87 ms to scan 1 urls, producing 3 keys and 6 values 
09:22:30.603 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 4 keys and 9 values 
09:22:30.645 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 10 values 
09:22:30.693 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 1 keys and 5 values 
09:22:30.724 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 1 keys and 7 values 
09:22:30.739 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:22:30.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:22:30.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001e4dd3b7d80
09:22:30.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001e4dd3b8000
09:22:30.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:22:30.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:22:30.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:22:33.725 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751419353363_127.0.0.1_3500
09:22:33.725 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Notify connected event to listeners.
09:22:33.725 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:22:33.725 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001e4dd4f2598
09:22:34.108 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:22:44.123 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:22:44.123 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:22:44.123 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:22:44.688 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:22:47.105 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:22:47.112 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:22:47.112 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:22:57.122 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:23:08.126 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dd509702-44c6-4599-88e1-2b47d26b269f
09:23:08.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] RpcClient init label, labels = {module=naming, source=sdk}
09:23:08.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:23:08.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:23:08.134 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:23:08.134 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:23:08.281 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751419388146_127.0.0.1_3558
09:23:08.283 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:08.283 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001e4dd4f2598
09:23:08.283 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Notify connected event to listeners.
09:23:08.409 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:23:08.485 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
09:23:08.865 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:23:08.896 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:23:08.994 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 42.615 seconds (JVM running for 46.48)
09:23:09.024 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:23:09.031 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:23:09.033 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:42:55.370 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Server healthy check fail, currentConnection = 1751419353363_127.0.0.1_3500
09:42:55.370 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:42:55.680 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Server healthy check fail, currentConnection = 1751419388146_127.0.0.1_3558
09:42:55.680 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:43:00.038 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1751420579897_127.0.0.1_5611
09:43:00.038 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1751419353363_127.0.0.1_3500
09:43:00.038 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751419353363_127.0.0.1_3500
09:43:00.038 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Success to connect a server [127.0.0.1:8848], connectionId = 1751420579777_127.0.0.1_5612
09:43:00.038 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1751419388146_127.0.0.1_3558
09:43:00.038 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751419388146_127.0.0.1_3558
09:43:00.038 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Notify disconnected event to listeners
09:43:00.056 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Notify connected event to listeners.
09:43:00.062 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Notify disconnected event to listeners
09:43:00.063 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Notify connected event to listeners.
09:43:00.071 [nacos-grpc-client-executor-268] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751419353363_127.0.0.1_3500]Ignore complete event,isRunning:true,isAbandon=true
09:43:03.151 [nacos-grpc-client-executor-249] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Receive server push request, request = NotifySubscriberRequest, requestId = 25
09:43:03.151 [nacos-grpc-client-executor-249] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Ack server push request, request = NotifySubscriberRequest, requestId = 25
09:53:57.567 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Server healthy check fail, currentConnection = 1751420579897_127.0.0.1_5611
09:53:57.567 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:53:58.361 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Server healthy check fail, currentConnection = 1751420579777_127.0.0.1_5612
09:53:58.361 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:54:02.387 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1751421242168_127.0.0.1_6335
09:54:02.388 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1751420579897_127.0.0.1_5611
09:54:02.388 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751420579897_127.0.0.1_5611
09:54:02.390 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Notify disconnected event to listeners
09:54:02.390 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aaad3e91-aea0-49f7-9e83-d643d34ad5d3_config-0] Notify connected event to listeners.
09:54:02.388 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Success to connect a server [127.0.0.1:8848], connectionId = 1751421242148_127.0.0.1_6334
09:54:02.393 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1751420579777_127.0.0.1_5612
09:54:02.393 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751420579777_127.0.0.1_5612
09:54:02.393 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Notify disconnected event to listeners
09:54:02.394 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Notify connected event to listeners.
09:54:02.399 [nacos-grpc-client-executor-383] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751420579777_127.0.0.1_5612]Ignore complete event,isRunning:true,isAbandon=true
09:54:02.993 [nacos-grpc-client-executor-386] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Receive server push request, request = NotifySubscriberRequest, requestId = 26
09:54:02.994 [nacos-grpc-client-executor-386] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd509702-44c6-4599-88e1-2b47d26b269f] Ack server push request, request = NotifySubscriberRequest, requestId = 26
13:40:38.066 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:40:38.075 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:40:38.425 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:40:38.425 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6f92956[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:40:38.425 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751421242148_127.0.0.1_6334
13:40:38.427 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 3100]
13:40:38.513 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:40:38.527 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:40:38.565 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:40:38.565 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:35:48.655 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:35:49.830 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 01e7b4bf-be56-41de-9751-78cc1b13d390_config-0
17:35:49.923 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
17:35:49.956 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
17:35:49.990 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
17:35:50.006 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
17:35:50.037 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 7 values 
17:35:50.046 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
17:35:50.052 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:35:50.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001fe483b6da8
17:35:50.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001fe483b6fc8
17:35:50.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:35:50.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:35:50.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:35:51.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751448951388_127.0.0.1_13082
17:35:51.679 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Notify connected event to listeners.
17:35:51.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:51.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001fe484f0668
17:35:51.943 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:36:00.190 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
17:36:00.194 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:36:00.194 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:36:00.618 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:36:02.275 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:36:02.279 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:36:02.281 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:36:08.215 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:36:11.834 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-ea40-49c8-9265-e317cc7a7bde
17:36:11.834 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] RpcClient init label, labels = {module=naming, source=sdk}
17:36:11.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:36:11.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:36:11.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:36:11.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:36:11.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751448971848_127.0.0.1_13127
17:36:11.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:36:11.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001fe484f0668
17:36:11.968 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Notify connected event to listeners.
17:36:12.049 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
17:36:12.125 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
17:36:12.309 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 24.87 seconds (JVM running for 26.955)
17:36:12.327 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
17:36:12.327 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
17:36:12.327 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
17:36:12.626 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 101
17:36:12.642 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 101
19:09:54.017 [http-nio-9700-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:10:00.305 [nacos-grpc-client-executor-1136] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 118
19:10:00.307 [nacos-grpc-client-executor-1136] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 118
19:13:54.468 [nacos-grpc-client-executor-1183] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 121
19:13:54.499 [nacos-grpc-client-executor-1183] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 121
19:14:29.580 [nacos-grpc-client-executor-1191] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 123
19:14:29.606 [nacos-grpc-client-executor-1191] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 123
19:22:32.039 [nacos-grpc-client-executor-1289] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 126
19:22:32.063 [nacos-grpc-client-executor-1289] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 126
19:23:02.723 [nacos-grpc-client-executor-1296] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 129
19:23:02.753 [nacos-grpc-client-executor-1296] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 129
19:24:42.661 [nacos-grpc-client-executor-1316] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 131
19:24:42.694 [nacos-grpc-client-executor-1316] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 131
19:25:14.411 [nacos-grpc-client-executor-1322] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 133
19:25:14.456 [nacos-grpc-client-executor-1322] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 133
19:39:06.451 [nacos-grpc-client-executor-1489] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 136
19:39:06.475 [nacos-grpc-client-executor-1489] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 136
19:39:39.557 [nacos-grpc-client-executor-1496] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 139
19:39:39.607 [nacos-grpc-client-executor-1496] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 139
20:28:35.805 [nacos-grpc-client-executor-2081] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 142
20:28:35.825 [nacos-grpc-client-executor-2081] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 142
20:34:45.966 [nacos-grpc-client-executor-2156] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 144
20:34:45.992 [nacos-grpc-client-executor-2156] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 144
20:40:15.605 [nacos-grpc-client-executor-2222] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 147
20:40:15.625 [nacos-grpc-client-executor-2222] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 147
20:40:47.076 [nacos-grpc-client-executor-2229] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 149
20:40:47.097 [nacos-grpc-client-executor-2229] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 149
20:47:23.024 [nacos-grpc-client-executor-2309] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 152
20:47:23.054 [nacos-grpc-client-executor-2309] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 152
20:48:01.216 [nacos-grpc-client-executor-2317] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 154
20:48:01.257 [nacos-grpc-client-executor-2317] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 154
20:50:05.077 [nacos-grpc-client-executor-2342] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 157
20:50:05.102 [nacos-grpc-client-executor-2342] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 157
20:50:43.511 [nacos-grpc-client-executor-2349] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Receive server push request, request = NotifySubscriberRequest, requestId = 159
20:50:43.541 [nacos-grpc-client-executor-2349] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Ack server push request, request = NotifySubscriberRequest, requestId = 159
21:12:22.461 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:12:22.461 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:12:22.599 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:22.600 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:22.846 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:22.847 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:23.161 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:23.161 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:23.577 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:23.578 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:24.104 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:24.107 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:24.737 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:24.749 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:25.448 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:25.460 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:25.502 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
21:12:25.837 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
21:12:26.155 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
21:12:26.156 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@895e9d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
21:12:26.156 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751448971848_127.0.0.1_13127
21:12:26.156 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea40-49c8-9265-e317cc7a7bde] Client is shutdown, stop reconnect to server
21:12:26.156 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6e43d5ff[Running, pool size = 17, active threads = 0, queued tasks = 0, completed tasks = 2625]
21:12:26.271 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01e7b4bf-be56-41de-9751-78cc1b13d390_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:26.304 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
21:12:26.312 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
21:12:26.322 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
21:12:26.323 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
