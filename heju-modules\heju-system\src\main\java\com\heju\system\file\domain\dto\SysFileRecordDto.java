package com.heju.system.file.domain.dto;

import com.heju.system.file.domain.po.SysFileRecordPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 文件操作记录 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysFileRecordDto extends SysFileRecordPo {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 创建人名称
     */
    private String operateName;

}
