package com.heju.system.forms.sheet.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.forms.field.domain.po.SysFieldPo;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.domain.po.SysSheetPo;
import com.heju.system.forms.sheet.domain.query.SysSheetQuery;

import java.util.List;

/**
 * 表单管理管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysSheetManager extends IBaseManager<SysSheetQuery, SysSheetDto> {

    void createSheet(SysSheetDto dto);

    void updateSheet(SysSheetDto dto);

    void deleteSheet(SysSheetDto dto);

    /**
     * 校验参数编码是否唯一
     *
     * @param Id   参数Id
     * @param apiName API名称
     * @return 参数对象
     */
    SysSheetPo checkConfigCodeUnique(Long Id, String apiName);

    void insertField(SysSheetDto dto);

    List<SysFieldPo> selectFieldBySheetId(List<Long> idList);

    SysSheetDto selectOne(String apiName);

    List<Long> selectIdsBySheetName(String sheetName);
}