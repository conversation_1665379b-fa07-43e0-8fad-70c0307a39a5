package com.heju.system.api.organize.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.domain.po.SysUserPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:51+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysUserConverterImpl implements SysUserConverter {

    @Override
    public SysUserDto mapperDto(SysUserPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysUserDto sysUserDto = new SysUserDto();

        sysUserDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysUserDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysUserDto.setName( arg0.getName() );
        sysUserDto.setStatus( arg0.getStatus() );
        sysUserDto.setSort( arg0.getSort() );
        sysUserDto.setRemark( arg0.getRemark() );
        sysUserDto.setCreateBy( arg0.getCreateBy() );
        sysUserDto.setCreateTime( arg0.getCreateTime() );
        sysUserDto.setUpdateBy( arg0.getUpdateBy() );
        sysUserDto.setUpdateTime( arg0.getUpdateTime() );
        sysUserDto.setDelFlag( arg0.getDelFlag() );
        sysUserDto.setCreateName( arg0.getCreateName() );
        sysUserDto.setUpdateName( arg0.getUpdateName() );
        sysUserDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysUserDto.setCode( arg0.getCode() );
        sysUserDto.setUserName( arg0.getUserName() );
        sysUserDto.setNickName( arg0.getNickName() );
        sysUserDto.setUserType( arg0.getUserType() );
        sysUserDto.setPhone( arg0.getPhone() );
        sysUserDto.setEmail( arg0.getEmail() );
        sysUserDto.setSex( arg0.getSex() );
        sysUserDto.setAvatar( arg0.getAvatar() );
        sysUserDto.setProfile( arg0.getProfile() );
        sysUserDto.setPassword( arg0.getPassword() );
        sysUserDto.setLoginIp( arg0.getLoginIp() );
        sysUserDto.setLoginDate( arg0.getLoginDate() );
        sysUserDto.setOpenId( arg0.getOpenId() );
        sysUserDto.setReception( arg0.getReception() );
        sysUserDto.setId( arg0.getId() );
        sysUserDto.setSourceName( arg0.getSourceName() );

        return sysUserDto;
    }

    @Override
    public List<SysUserDto> mapperDto(Collection<SysUserPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysUserDto> list = new ArrayList<SysUserDto>( arg0.size() );
        for ( SysUserPo sysUserPo : arg0 ) {
            list.add( mapperDto( sysUserPo ) );
        }

        return list;
    }

    @Override
    public Page<SysUserDto> mapperPageDto(Collection<SysUserPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysUserDto> page = new Page<SysUserDto>();
        for ( SysUserPo sysUserPo : arg0 ) {
            page.add( mapperDto( sysUserPo ) );
        }

        return page;
    }

    @Override
    public SysUserPo mapperPo(SysUserDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysUserPo sysUserPo = new SysUserPo();

        sysUserPo.setId( arg0.getId() );
        sysUserPo.setSourceName( arg0.getSourceName() );
        sysUserPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysUserPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysUserPo.setName( arg0.getName() );
        sysUserPo.setStatus( arg0.getStatus() );
        sysUserPo.setSort( arg0.getSort() );
        sysUserPo.setRemark( arg0.getRemark() );
        sysUserPo.setCreateBy( arg0.getCreateBy() );
        sysUserPo.setCreateTime( arg0.getCreateTime() );
        sysUserPo.setUpdateBy( arg0.getUpdateBy() );
        sysUserPo.setUpdateTime( arg0.getUpdateTime() );
        sysUserPo.setDelFlag( arg0.getDelFlag() );
        sysUserPo.setCreateName( arg0.getCreateName() );
        sysUserPo.setUpdateName( arg0.getUpdateName() );
        sysUserPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysUserPo.setCode( arg0.getCode() );
        sysUserPo.setUserName( arg0.getUserName() );
        sysUserPo.setNickName( arg0.getNickName() );
        sysUserPo.setUserType( arg0.getUserType() );
        sysUserPo.setPhone( arg0.getPhone() );
        sysUserPo.setEmail( arg0.getEmail() );
        sysUserPo.setSex( arg0.getSex() );
        sysUserPo.setAvatar( arg0.getAvatar() );
        sysUserPo.setProfile( arg0.getProfile() );
        sysUserPo.setPassword( arg0.getPassword() );
        sysUserPo.setLoginIp( arg0.getLoginIp() );
        sysUserPo.setLoginDate( arg0.getLoginDate() );
        sysUserPo.setOpenId( arg0.getOpenId() );
        sysUserPo.setReception( arg0.getReception() );

        return sysUserPo;
    }

    @Override
    public List<SysUserPo> mapperPo(Collection<SysUserDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysUserPo> list = new ArrayList<SysUserPo>( arg0.size() );
        for ( SysUserDto sysUserDto : arg0 ) {
            list.add( mapperPo( sysUserDto ) );
        }

        return list;
    }

    @Override
    public Page<SysUserPo> mapperPagePo(Collection<SysUserDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysUserPo> page = new Page<SysUserPo>();
        for ( SysUserDto sysUserDto : arg0 ) {
            page.add( mapperPo( sysUserDto ) );
        }

        return page;
    }
}
