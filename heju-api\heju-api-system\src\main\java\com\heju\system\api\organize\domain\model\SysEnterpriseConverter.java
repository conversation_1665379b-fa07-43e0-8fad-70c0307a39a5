package com.heju.system.api.organize.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.api.organize.domain.dto.SysEnterpriseDto;
import com.heju.system.api.organize.domain.po.SysEnterprisePo;
import com.heju.system.api.organize.domain.query.SysEnterpriseQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 企业 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysEnterpriseConverter extends BaseConverter<SysEnterpriseQuery, SysEnterpriseDto, SysEnterprisePo> {
}
