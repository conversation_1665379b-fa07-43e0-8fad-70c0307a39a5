package com.heju.system.forms.cascade.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.forms.cascade.domain.dto.SysCascadeDto;
import com.heju.system.forms.cascade.domain.po.SysCascadePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:36+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysCascadeConverterImpl implements SysCascadeConverter {

    @Override
    public SysCascadeDto mapperDto(SysCascadePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysCascadeDto sysCascadeDto = new SysCascadeDto();

        sysCascadeDto.setId( arg0.getId() );
        sysCascadeDto.setSourceName( arg0.getSourceName() );
        sysCascadeDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysCascadeDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysCascadeDto.setName( arg0.getName() );
        sysCascadeDto.setStatus( arg0.getStatus() );
        sysCascadeDto.setSort( arg0.getSort() );
        sysCascadeDto.setCreateBy( arg0.getCreateBy() );
        sysCascadeDto.setCreateTime( arg0.getCreateTime() );
        sysCascadeDto.setUpdateBy( arg0.getUpdateBy() );
        sysCascadeDto.setUpdateTime( arg0.getUpdateTime() );
        sysCascadeDto.setDelFlag( arg0.getDelFlag() );
        sysCascadeDto.setCreateName( arg0.getCreateName() );
        sysCascadeDto.setUpdateName( arg0.getUpdateName() );
        sysCascadeDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysCascadeDto.setMainOptionId( arg0.getMainOptionId() );
        sysCascadeDto.setCascade1OptionId( arg0.getCascade1OptionId() );
        sysCascadeDto.setCascade2OptionId( arg0.getCascade2OptionId() );
        sysCascadeDto.setCascade3OptionId( arg0.getCascade3OptionId() );
        sysCascadeDto.setCascade4OptionId( arg0.getCascade4OptionId() );
        sysCascadeDto.setRemark( arg0.getRemark() );

        return sysCascadeDto;
    }

    @Override
    public List<SysCascadeDto> mapperDto(Collection<SysCascadePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysCascadeDto> list = new ArrayList<SysCascadeDto>( arg0.size() );
        for ( SysCascadePo sysCascadePo : arg0 ) {
            list.add( mapperDto( sysCascadePo ) );
        }

        return list;
    }

    @Override
    public Page<SysCascadeDto> mapperPageDto(Collection<SysCascadePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysCascadeDto> page = new Page<SysCascadeDto>();
        for ( SysCascadePo sysCascadePo : arg0 ) {
            page.add( mapperDto( sysCascadePo ) );
        }

        return page;
    }

    @Override
    public SysCascadePo mapperPo(SysCascadeDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysCascadePo sysCascadePo = new SysCascadePo();

        sysCascadePo.setId( arg0.getId() );
        sysCascadePo.setSourceName( arg0.getSourceName() );
        sysCascadePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysCascadePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysCascadePo.setName( arg0.getName() );
        sysCascadePo.setStatus( arg0.getStatus() );
        sysCascadePo.setSort( arg0.getSort() );
        sysCascadePo.setCreateBy( arg0.getCreateBy() );
        sysCascadePo.setCreateTime( arg0.getCreateTime() );
        sysCascadePo.setUpdateBy( arg0.getUpdateBy() );
        sysCascadePo.setUpdateTime( arg0.getUpdateTime() );
        sysCascadePo.setDelFlag( arg0.getDelFlag() );
        sysCascadePo.setCreateName( arg0.getCreateName() );
        sysCascadePo.setUpdateName( arg0.getUpdateName() );
        sysCascadePo.setEnterpriseId( arg0.getEnterpriseId() );
        sysCascadePo.setMainOptionId( arg0.getMainOptionId() );
        sysCascadePo.setCascade1OptionId( arg0.getCascade1OptionId() );
        sysCascadePo.setCascade2OptionId( arg0.getCascade2OptionId() );
        sysCascadePo.setCascade3OptionId( arg0.getCascade3OptionId() );
        sysCascadePo.setCascade4OptionId( arg0.getCascade4OptionId() );
        sysCascadePo.setRemark( arg0.getRemark() );

        return sysCascadePo;
    }

    @Override
    public List<SysCascadePo> mapperPo(Collection<SysCascadeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysCascadePo> list = new ArrayList<SysCascadePo>( arg0.size() );
        for ( SysCascadeDto sysCascadeDto : arg0 ) {
            list.add( mapperPo( sysCascadeDto ) );
        }

        return list;
    }

    @Override
    public Page<SysCascadePo> mapperPagePo(Collection<SysCascadeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysCascadePo> page = new Page<SysCascadePo>();
        for ( SysCascadeDto sysCascadeDto : arg0 ) {
            page.add( mapperPo( sysCascadeDto ) );
        }

        return page;
    }
}
