09:02:44.767 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:02:46.382 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a50fcd77-d2d1-4d34-a764-8567c81d1889_config-0
09:02:46.524 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 46 ms to scan 1 urls, producing 3 keys and 6 values 
09:02:46.612 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 4 keys and 9 values 
09:02:46.624 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:02:46.641 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:02:46.657 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 7 values 
09:02:46.671 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:02:46.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a50fcd77-d2d1-4d34-a764-8567c81d1889_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:02:46.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a50fcd77-d2d1-4d34-a764-8567c81d1889_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002363d39f1c0
09:02:46.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a50fcd77-d2d1-4d34-a764-8567c81d1889_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002363d39f3e0
09:02:46.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a50fcd77-d2d1-4d34-a764-8567c81d1889_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:02:46.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a50fcd77-d2d1-4d34-a764-8567c81d1889_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:02:46.700 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a50fcd77-d2d1-4d34-a764-8567c81d1889_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:02:48.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a50fcd77-d2d1-4d34-a764-8567c81d1889_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754269367948_127.0.0.1_9166
09:02:48.217 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a50fcd77-d2d1-4d34-a764-8567c81d1889_config-0] Notify connected event to listeners.
09:02:48.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a50fcd77-d2d1-4d34-a764-8567c81d1889_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:02:48.218 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a50fcd77-d2d1-4d34-a764-8567c81d1889_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002363d518ad8
09:02:48.427 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:02:54.038 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:02:54.038 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:02:54.038 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:02:54.240 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:02:54.921 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:02:54.921 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:02:54.921 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:03:01.871 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:03:05.438 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 02b21e06-40eb-4ecc-9efa-ae5edd21b1a2
09:03:05.438 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02b21e06-40eb-4ecc-9efa-ae5edd21b1a2] RpcClient init label, labels = {module=naming, source=sdk}
09:03:05.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02b21e06-40eb-4ecc-9efa-ae5edd21b1a2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:03:05.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02b21e06-40eb-4ecc-9efa-ae5edd21b1a2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:03:05.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02b21e06-40eb-4ecc-9efa-ae5edd21b1a2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:03:05.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02b21e06-40eb-4ecc-9efa-ae5edd21b1a2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:03:05.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02b21e06-40eb-4ecc-9efa-ae5edd21b1a2] Success to connect to server [localhost:8848] on start up, connectionId = 1754269385453_127.0.0.1_9391
09:03:05.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02b21e06-40eb-4ecc-9efa-ae5edd21b1a2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:03:05.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02b21e06-40eb-4ecc-9efa-ae5edd21b1a2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002363d518ad8
09:03:05.570 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02b21e06-40eb-4ecc-9efa-ae5edd21b1a2] Notify connected event to listeners.
09:03:05.614 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:03:05.650 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:03:05.805 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 22.652 seconds (JVM running for 26.774)
09:03:05.821 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:03:05.821 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:03:05.821 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:03:06.013 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:03:06.110 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02b21e06-40eb-4ecc-9efa-ae5edd21b1a2] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:03:06.131 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02b21e06-40eb-4ecc-9efa-ae5edd21b1a2] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:13:53.506 [nacos-grpc-client-executor-146] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02b21e06-40eb-4ecc-9efa-ae5edd21b1a2] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:13:53.506 [nacos-grpc-client-executor-146] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02b21e06-40eb-4ecc-9efa-ae5edd21b1a2] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:13:54.585 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:13:54.585 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:13:54.611 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:13:54.611 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
10:09:05.569 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:09:05.574 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:09:05.902 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:09:05.902 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4494ac0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:09:05.902 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754269385453_127.0.0.1_9391
10:09:05.903 [nacos-grpc-client-executor-811] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754269385453_127.0.0.1_9391]Ignore complete event,isRunning:false,isAbandon=false
10:09:05.909 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@24713bea[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 812]
10:09:06.048 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:09:06.054 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:09:06.059 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:09:06.059 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:09:06.060 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:09:06.060 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:09:06.060 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:09:06.061 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:09:11.579 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:09:12.131 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 313bd800-e7d6-4bce-9ebc-f9669b1c2e58_config-0
10:09:12.185 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
10:09:12.214 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
10:09:12.219 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
10:09:12.226 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
10:09:12.233 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
10:09:12.239 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
10:09:12.242 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313bd800-e7d6-4bce-9ebc-f9669b1c2e58_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:09:12.242 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313bd800-e7d6-4bce-9ebc-f9669b1c2e58_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001e4973ceaf8
10:09:12.242 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313bd800-e7d6-4bce-9ebc-f9669b1c2e58_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001e4973ced18
10:09:12.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313bd800-e7d6-4bce-9ebc-f9669b1c2e58_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:09:12.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313bd800-e7d6-4bce-9ebc-f9669b1c2e58_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:09:12.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313bd800-e7d6-4bce-9ebc-f9669b1c2e58_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:09:13.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313bd800-e7d6-4bce-9ebc-f9669b1c2e58_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754273352949_127.0.0.1_12848
10:09:13.157 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313bd800-e7d6-4bce-9ebc-f9669b1c2e58_config-0] Notify connected event to listeners.
10:09:13.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313bd800-e7d6-4bce-9ebc-f9669b1c2e58_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:09:13.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [313bd800-e7d6-4bce-9ebc-f9669b1c2e58_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e497508ad8
10:09:13.245 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:09:15.759 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:09:15.759 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:09:15.759 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:09:15.870 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:09:16.408 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:09:16.409 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:09:16.409 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:09:22.197 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:09:22.302 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:09:22.304 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:09:22.309 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:09:22.309 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:09:22.310 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
10:24:52.225 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:24:52.752 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 27d2565c-4920-4c88-90a4-ddc50d45ee09_config-0
10:24:52.803 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
10:24:52.829 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
10:24:52.837 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
10:24:52.844 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
10:24:52.851 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
10:24:52.857 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
10:24:52.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d2565c-4920-4c88-90a4-ddc50d45ee09_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:24:52.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d2565c-4920-4c88-90a4-ddc50d45ee09_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002b3813c2328
10:24:52.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d2565c-4920-4c88-90a4-ddc50d45ee09_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002b3813c2548
10:24:52.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d2565c-4920-4c88-90a4-ddc50d45ee09_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:24:52.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d2565c-4920-4c88-90a4-ddc50d45ee09_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:24:52.867 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d2565c-4920-4c88-90a4-ddc50d45ee09_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:24:53.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d2565c-4920-4c88-90a4-ddc50d45ee09_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754274293376_127.0.0.1_2542
10:24:53.566 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d2565c-4920-4c88-90a4-ddc50d45ee09_config-0] Notify connected event to listeners.
10:24:53.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d2565c-4920-4c88-90a4-ddc50d45ee09_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:24:53.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d2565c-4920-4c88-90a4-ddc50d45ee09_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002b3814fbff8
10:24:53.645 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:24:56.154 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:24:56.154 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:24:56.154 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:24:56.276 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:24:56.743 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:24:56.745 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:24:56.745 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:25:01.952 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:25:04.028 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 46b86c57-80ca-4363-9785-d539925ece75
10:25:04.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b86c57-80ca-4363-9785-d539925ece75] RpcClient init label, labels = {module=naming, source=sdk}
10:25:04.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b86c57-80ca-4363-9785-d539925ece75] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:25:04.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b86c57-80ca-4363-9785-d539925ece75] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:25:04.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b86c57-80ca-4363-9785-d539925ece75] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:25:04.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b86c57-80ca-4363-9785-d539925ece75] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:25:04.154 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b86c57-80ca-4363-9785-d539925ece75] Success to connect to server [localhost:8848] on start up, connectionId = 1754274304032_127.0.0.1_2579
10:25:04.154 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b86c57-80ca-4363-9785-d539925ece75] Notify connected event to listeners.
10:25:04.154 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b86c57-80ca-4363-9785-d539925ece75] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:25:04.154 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b86c57-80ca-4363-9785-d539925ece75] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002b3814fbff8
10:25:04.192 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:25:04.207 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:25:04.291 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.564 seconds (JVM running for 13.4)
10:25:04.325 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:25:04.326 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:25:04.326 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:25:04.531 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:25:04.736 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b86c57-80ca-4363-9785-d539925ece75] Receive server push request, request = NotifySubscriberRequest, requestId = 21
10:25:04.756 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b86c57-80ca-4363-9785-d539925ece75] Ack server push request, request = NotifySubscriberRequest, requestId = 21
10:32:20.881 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:32:20.881 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:32:21.047 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:32:21.047 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
10:32:21.047 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:32:21.092 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:32:21.092 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:32:21.092 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:32:21.097 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:32:21.097 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:43:24.078 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:43:24.078 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:43:24.423 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:43:24.423 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@268aa326[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:43:24.423 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754274304032_127.0.0.1_2579
10:43:24.423 [nacos-grpc-client-executor-232] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754274304032_127.0.0.1_2579]Ignore complete event,isRunning:false,isAbandon=false
10:43:24.423 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5fff6e71[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 233]
10:43:24.588 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:43:24.588 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
10:43:24.588 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
10:43:24.588 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:43:24.588 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:43:24.588 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:43:28.694 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:43:29.236 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6c0926c5-54bc-4f6d-8e7a-dbf138ca3b9f_config-0
10:43:29.286 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
10:43:29.313 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
10:43:29.319 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
10:43:29.326 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
10:43:29.332 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
10:43:29.341 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
10:43:29.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c0926c5-54bc-4f6d-8e7a-dbf138ca3b9f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:43:29.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c0926c5-54bc-4f6d-8e7a-dbf138ca3b9f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000251013be8d8
10:43:29.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c0926c5-54bc-4f6d-8e7a-dbf138ca3b9f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000251013beaf8
10:43:29.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c0926c5-54bc-4f6d-8e7a-dbf138ca3b9f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:43:29.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c0926c5-54bc-4f6d-8e7a-dbf138ca3b9f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:43:29.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c0926c5-54bc-4f6d-8e7a-dbf138ca3b9f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:43:30.024 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c0926c5-54bc-4f6d-8e7a-dbf138ca3b9f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754275409841_127.0.0.1_6934
10:43:30.025 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c0926c5-54bc-4f6d-8e7a-dbf138ca3b9f_config-0] Notify connected event to listeners.
10:43:30.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c0926c5-54bc-4f6d-8e7a-dbf138ca3b9f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:43:30.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c0926c5-54bc-4f6d-8e7a-dbf138ca3b9f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000251014f8ad8
10:43:30.130 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:43:32.612 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:43:32.612 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:43:32.612 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:43:32.741 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:43:33.302 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:43:33.303 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:43:33.303 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:43:38.978 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:43:41.713 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7740748e-cc96-4206-99fb-3ac012a60396
10:43:41.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7740748e-cc96-4206-99fb-3ac012a60396] RpcClient init label, labels = {module=naming, source=sdk}
10:43:41.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7740748e-cc96-4206-99fb-3ac012a60396] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:43:41.717 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7740748e-cc96-4206-99fb-3ac012a60396] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:43:41.717 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7740748e-cc96-4206-99fb-3ac012a60396] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:43:41.717 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7740748e-cc96-4206-99fb-3ac012a60396] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:43:41.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7740748e-cc96-4206-99fb-3ac012a60396] Success to connect to server [localhost:8848] on start up, connectionId = 1754275421726_127.0.0.1_6982
10:43:41.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7740748e-cc96-4206-99fb-3ac012a60396] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:43:41.843 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7740748e-cc96-4206-99fb-3ac012a60396] Notify connected event to listeners.
10:43:41.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7740748e-cc96-4206-99fb-3ac012a60396] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000251014f8ad8
10:43:41.890 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:43:41.918 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:43:42.035 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.812 seconds (JVM running for 14.602)
10:43:42.049 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:43:42.050 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:43:42.050 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:43:42.440 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7740748e-cc96-4206-99fb-3ac012a60396] Receive server push request, request = NotifySubscriberRequest, requestId = 27
10:43:42.454 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7740748e-cc96-4206-99fb-3ac012a60396] Ack server push request, request = NotifySubscriberRequest, requestId = 27
10:43:42.538 [RMI TCP Connection(10)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:10:39.793 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:10:39.793 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:10:39.945 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
11:10:39.959 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:10:39.960 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:10:39.960 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:43:52.415 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:43:52.415 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:43:52.756 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:43:52.756 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@55b1c7a2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:43:52.756 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754275421726_127.0.0.1_6982
11:43:52.760 [nacos-grpc-client-executor-733] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754275421726_127.0.0.1_6982]Ignore complete event,isRunning:false,isAbandon=false
11:43:52.760 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7e8c1207[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 734]
11:43:52.901 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:43:52.901 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
11:43:52.901 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
11:43:52.901 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:43:52.901 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:43:52.901 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:43:57.813 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:43:58.384 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 760e2fd3-0ca6-4010-a144-61d1f4ee49ec_config-0
11:43:58.433 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 6 values 
11:43:58.463 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
11:43:58.471 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
11:43:58.478 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:43:58.485 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
11:43:58.491 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
11:43:58.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [760e2fd3-0ca6-4010-a144-61d1f4ee49ec_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:43:58.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [760e2fd3-0ca6-4010-a144-61d1f4ee49ec_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001e0813ce480
11:43:58.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [760e2fd3-0ca6-4010-a144-61d1f4ee49ec_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001e0813ce6a0
11:43:58.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [760e2fd3-0ca6-4010-a144-61d1f4ee49ec_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:43:58.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [760e2fd3-0ca6-4010-a144-61d1f4ee49ec_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:43:58.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [760e2fd3-0ca6-4010-a144-61d1f4ee49ec_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:59.224 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [760e2fd3-0ca6-4010-a144-61d1f4ee49ec_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754279039041_127.0.0.1_7531
11:43:59.224 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [760e2fd3-0ca6-4010-a144-61d1f4ee49ec_config-0] Notify connected event to listeners.
11:43:59.224 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [760e2fd3-0ca6-4010-a144-61d1f4ee49ec_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:43:59.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [760e2fd3-0ca6-4010-a144-61d1f4ee49ec_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e081508228
11:43:59.304 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:44:01.959 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:44:01.960 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:44:01.961 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:44:02.076 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:44:02.594 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:44:02.595 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:44:02.595 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:44:11.367 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:44:13.704 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0d481f96-755f-411d-898d-79bc8f227481
11:44:13.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d481f96-755f-411d-898d-79bc8f227481] RpcClient init label, labels = {module=naming, source=sdk}
11:44:13.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d481f96-755f-411d-898d-79bc8f227481] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:44:13.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d481f96-755f-411d-898d-79bc8f227481] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:44:13.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d481f96-755f-411d-898d-79bc8f227481] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:44:13.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d481f96-755f-411d-898d-79bc8f227481] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:44:13.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d481f96-755f-411d-898d-79bc8f227481] Success to connect to server [localhost:8848] on start up, connectionId = 1754279053716_127.0.0.1_7661
11:44:13.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d481f96-755f-411d-898d-79bc8f227481] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:44:13.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d481f96-755f-411d-898d-79bc8f227481] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e081508228
11:44:13.851 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d481f96-755f-411d-898d-79bc8f227481] Notify connected event to listeners.
11:44:13.887 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:44:13.914 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:44:13.998 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.663 seconds (JVM running for 17.532)
11:44:14.004 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:44:14.014 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:44:14.014 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:44:14.403 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:44:14.423 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d481f96-755f-411d-898d-79bc8f227481] Receive server push request, request = NotifySubscriberRequest, requestId = 32
11:44:14.436 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d481f96-755f-411d-898d-79bc8f227481] Ack server push request, request = NotifySubscriberRequest, requestId = 32
11:44:56.080 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:44:56.080 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:35:13.721 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:35:13.731 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:35:14.050 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:35:14.050 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2f413b56[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:35:14.050 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754279053716_127.0.0.1_7661
13:35:14.053 [nacos-grpc-client-executor-1340] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754279053716_127.0.0.1_7661]Ignore complete event,isRunning:false,isAbandon=false
13:35:14.053 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@d52d9b2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1341]
13:35:14.219 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:35:14.223 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:35:14.233 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:35:14.233 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:35:14.234 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:35:14.235 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:35:32.024 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:35:33.016 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 85aefcf3-da45-4f77-ab4b-b07b4feb009f_config-0
13:35:33.115 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 58 ms to scan 1 urls, producing 3 keys and 6 values 
13:35:33.169 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
13:35:33.180 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
13:35:33.192 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
13:35:33.201 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
13:35:33.215 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
13:35:33.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85aefcf3-da45-4f77-ab4b-b07b4feb009f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:35:33.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85aefcf3-da45-4f77-ab4b-b07b4feb009f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000218cf39bda8
13:35:33.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85aefcf3-da45-4f77-ab4b-b07b4feb009f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000218cf39bfc8
13:35:33.221 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85aefcf3-da45-4f77-ab4b-b07b4feb009f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:35:33.221 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85aefcf3-da45-4f77-ab4b-b07b4feb009f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:35:33.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85aefcf3-da45-4f77-ab4b-b07b4feb009f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:35:34.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85aefcf3-da45-4f77-ab4b-b07b4feb009f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754285734116_127.0.0.1_5001
13:35:34.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85aefcf3-da45-4f77-ab4b-b07b4feb009f_config-0] Notify connected event to listeners.
13:35:34.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85aefcf3-da45-4f77-ab4b-b07b4feb009f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:35:34.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85aefcf3-da45-4f77-ab4b-b07b4feb009f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000218cf513db0
13:35:34.525 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:35:38.648 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:35:38.663 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:35:38.663 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:35:38.788 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:35:39.285 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:35:39.286 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:35:39.286 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:35:44.732 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:35:46.799 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 377996c5-cb96-4468-9487-26c28f4273e1
13:35:46.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [377996c5-cb96-4468-9487-26c28f4273e1] RpcClient init label, labels = {module=naming, source=sdk}
13:35:46.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [377996c5-cb96-4468-9487-26c28f4273e1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:35:46.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [377996c5-cb96-4468-9487-26c28f4273e1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:35:46.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [377996c5-cb96-4468-9487-26c28f4273e1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:35:46.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [377996c5-cb96-4468-9487-26c28f4273e1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:35:46.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [377996c5-cb96-4468-9487-26c28f4273e1] Success to connect to server [localhost:8848] on start up, connectionId = 1754285746811_127.0.0.1_5106
13:35:46.940 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [377996c5-cb96-4468-9487-26c28f4273e1] Notify connected event to listeners.
13:35:46.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [377996c5-cb96-4468-9487-26c28f4273e1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:35:46.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [377996c5-cb96-4468-9487-26c28f4273e1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000218cf513db0
13:35:46.982 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:35:47.003 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:35:47.082 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.82 seconds (JVM running for 31.33)
13:35:47.102 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:35:47.102 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:35:47.102 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:35:47.540 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [377996c5-cb96-4468-9487-26c28f4273e1] Receive server push request, request = NotifySubscriberRequest, requestId = 42
13:35:47.547 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [377996c5-cb96-4468-9487-26c28f4273e1] Ack server push request, request = NotifySubscriberRequest, requestId = 42
13:35:59.648 [http-nio-9600-exec-3] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:36:00.621 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:36:00.621 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
13:36:00.621 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:36:00.624 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:36:00.630 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:36:00.630 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:50:00.003 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:50:00.007 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:50:00.338 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:50:00.338 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5f60e086[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:50:00.338 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754285746811_127.0.0.1_5106
18:50:00.342 [nacos-grpc-client-executor-3774] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754285746811_127.0.0.1_5106]Ignore complete event,isRunning:false,isAbandon=false
18:50:00.342 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@166626ec[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 3774]
18:50:00.494 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:50:00.495 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
18:50:00.495 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
18:50:00.495 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:50:00.495 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:50:00.495 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
