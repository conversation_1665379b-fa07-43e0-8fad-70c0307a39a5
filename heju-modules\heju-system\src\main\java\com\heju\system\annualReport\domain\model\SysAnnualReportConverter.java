package com.heju.system.annualReport.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;

import com.heju.system.annualReport.domain.dto.SysAnnualReportDto;
import com.heju.system.annualReport.domain.po.SysAnnualReportPo;
import com.heju.system.annualReport.domain.query.SysAnnualReportQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 工商年报 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysAnnualReportConverter extends BaseConverter<SysAnnualReportQuery, SysAnnualReportDto, SysAnnualReportPo> {
}
