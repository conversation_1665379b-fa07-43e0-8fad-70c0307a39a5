package com.heju.system.phoneinfo.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.heju.system.phoneinfo.domain.po.SysTelephoneCodePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDateTime;


/**
 * 转发短信 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysTelephoneCodeQuery extends SysTelephoneCodePo {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 授权id
     */
    private Long userId;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    /** 剩余次数 */
    private Integer remainingTimes;

    /** 授权次数 */
    private Integer times;

    /** 使用次数 */
    private Integer viewedTimes;

    /**
     * 授权时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime permissionTime;
}
