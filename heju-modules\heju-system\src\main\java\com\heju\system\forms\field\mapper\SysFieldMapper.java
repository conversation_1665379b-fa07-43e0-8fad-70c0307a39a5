package com.heju.system.forms.field.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.po.SysFieldPo;
import com.heju.system.forms.field.domain.query.SysFieldQuery;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 字段管理管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysFieldMapper extends BaseMapper<SysFieldQuery, SysFieldDto, SysFieldPo> {

    @Update("ALTER TABLE ${sheetApiName} " +
            "ADD COLUMN ${apiName} VARCHAR(${fieldLength}) COMMENT '${name}'")
    void createSheetByVarchar(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "ADD COLUMN ${apiName} Bigint(20) COMMENT '${name}'")
    void createSheetByBigintNotLength(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "ADD COLUMN ${apiName} VARCHAR(255) COMMENT '${name}'")
    void createSheetByVarcharNotLength(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "ADD COLUMN ${apiName} TEXT(${fieldLength}) COMMENT '${name}'")
    void createSheetByText(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "ADD COLUMN ${apiName} TEXT COMMENT '${name}'")
    void createSheetByTextNotLength(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
                   "ADD COLUMN ${apiName} INT(${fieldLength}) COMMENT '${name}'")
    void createSheetByInt(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "ADD COLUMN ${apiName} DECIMAL(10,${decimalLength}) COMMENT '${name}'")
    void createSheetByDecimal(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "ADD COLUMN ${apiName} DATE COMMENT '${name}'")
    void createSheetByDate(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "ADD COLUMN ${apiName} DATETIME COMMENT '${name}'")
    void createSheetByDatetime(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "ADD COLUMN ${apiName} BIGINT(11) COMMENT '${name}'")
    void createSheetByBigint(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "CHANGE COLUMN ${apiName} ${apiName} VARCHAR(${fieldLength}) COMMENT '${name}'")
    void updateSheetByVarchar(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "ADD COLUMN ${apiName} INT(1) COMMENT '${name}'")
    void createSheetByJudgment(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "CHANGE COLUMN ${apiName} ${apiName} VARCHAR(255) COMMENT '${name}'")
    void updateSheetByVarcharNotLength(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "CHANGE COLUMN ${apiName} ${apiName} TEXT(${fieldLength}) COMMENT '${name}'")
    void updateSheetByText(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "CHANGE COLUMN ${apiName} ${apiName} TEXT COMMENT '${name}'")
    void updateSheetByTextNotLength(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "CHANGE COLUMN ${apiName} ${apiName} INT(${fieldLength}) COMMENT '${name}'")
    void updateSheetByInt(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "CHANGE COLUMN ${apiName} ${apiName} DECIMAL(10,${decimalLength}) COMMENT '${name}'")
    void updateSheetByDecimal(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "CHANGE COLUMN ${apiName} ${apiName} DATE COMMENT '${name}'")
    void updateSheetByDate(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "CHANGE COLUMN ${apiName} ${apiName} DATETIME COMMENT '${name}'")
    void updateSheetByDatetime(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} " +
            "CHANGE COLUMN ${apiName} ${apiName} BIGINT(11) COMMENT '${name}'")
    void updateSheetByBigint(SysFieldDto dto);

    @Update("ALTER TABLE ${sheetApiName} DROP COLUMN '${fieldName}' " )
    void deleteSheetField(@Param("sheetApiName") String sheetApiName,@Param("fieldName") String fieldName);

    @Update("ALTER TABLE ${sheetApiName} " +
            "CHANGE COLUMN ${apiName} ${apiName} INT(1) COMMENT '${name}'")
    void updateSheetByJudgment(SysFieldDto dto);

//    @Update({
//            "<script>",
//            "DELIMITER $$",
//            "",
//            "CREATE TRIGGER ${triggerName}",
//            "AFTER UPDATE ON ${quoteSheetApiName}",
//            "FOR EACH ROW",
//            "BEGIN",
//            "    UPDATE ${sheetApiName}",
//            "    SET ${apiName} = NEW.${quoteApiName}",
//            "    WHERE ${relationApiName} = NEW.${relationQuoteApiName};",
//            "END$$",
//            "",
//            "DELIMITER ;",
//            "</script>"
//    })
    void updateRelatedTable(@Param("quoteSheetApiName") String quoteSheetApiName,
                            @Param("sheetApiName") String sheetApiName,
                            @Param("apiName") String apiName,
                            @Param("quoteApiName") String quoteApiName,
                            @Param("relationQuoteApiName") String relationQuoteApiName,
                            @Param("relationApiName") String relationApiName,
                            @Param("triggerName") String triggerName);


}
