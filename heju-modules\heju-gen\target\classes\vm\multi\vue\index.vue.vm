<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          :preIcon="IconEnum.ADD"
          v-auth="${BusinessName}Auth.ADD"
          @click="handleCreate"
          type="primary"
        >
          新增
        </a-button>
#if($api.batchRemove || $api.batchRemoveForce)
        <a-button
          :preIcon="IconEnum.DELETE"
          v-auth="${BusinessName}Auth.DELETE"
          @click="handleDelete"
          type="primary"
          color="error"
        >
          删除
        </a-button>
#end
#if($api.export)
        <a-button
          :preIcon="IconEnum.EXPORT"
          v-auth="${BusinessName}Auth.EXPORT"
          @click="handleExport"
          type="primary"
          color="warning"
        >
          导出
        </a-button>
#end
#if($table.tree || $table.subTree)
        <a-button type="primary" @click="expandAll">展开全部</a-button>
        <a-button type="primary" @click="collapseAll">折叠全部</a-button>
#end
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: IconEnum.VIEW,
              tooltip: '查看',
              auth: ${BusinessName}Auth.SINGLE,
              onClick: handleView.bind(null, record),
            },
            {
              icon: IconEnum.EDIT,
              tooltip: '编辑',
              auth: ${BusinessName}Auth.EDIT,
              onClick: handleEdit.bind(null, record),
            },
#if($api.batchRemove || $api.batchRemoveForce)
            {
              icon: IconEnum.DELETE,
              tooltip: '删除',
              auth: ${BusinessName}Auth.DELETE,
              color: 'error',
              onClick: handleDelete.bind(null, record),
            },
#end
          ]"
        />
      </template>
    </BasicTable>
    <${BusinessName}Modal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts">
  import { list${BusinessName}Api, del${BusinessName}Api#if($api.export), export${BusinessName}Api#end } from '/@/api/${moduleName}/${authorityName}/${businessName}';
  import { useModal } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { IconEnum } from '/@/enums/appEnum';
  import { BasicTable, TableAction, useTable } from '/@/components/Table';
  import { ${BusinessName}Auth } from '/@/auth/${moduleName}';
  import { columns, searchFormSchema } from './${businessName}.data';
  import { ${BusinessName}DetailGo } from '/@/enums/${moduleName}';
  import ${BusinessName}Modal from './${BusinessName}Modal.vue';
  import { useUserStore } from '/@/store/modules/user';
#if($api.export)
  import { ${BusinessName}PM } from '/@/model/${moduleName}';
#end

  export default defineComponent({
    name: '${BusinessName}Management',
    components: { BasicTable, ${BusinessName}Modal, TableAction },
    setup() {
      const { createMessage, createConfirm } = useMessage();
      const [registerModal, { openModal }] = useModal();
#if($api.batchRemove || $api.batchRemoveForce)
      const state = reactive<{
        ${pkColumn.javaField}s: (string | number)[]
        ${pkColumn.javaField}Names: string
      }>({
        ${pkColumn.javaField}s: [],
        ${pkColumn.javaField}Names: '',
      });
#end
      const [registerTable, { reload#if($table.tree || $table.subTree), expandAll, collapseAll#end#if($api.export), getForm#end }] = useTable({
        title: '${functionName}列表',
        api: list${BusinessName}Api,
        striped: false,
        useSearchForm: true,
        rowKey: '${pkColumn.javaField}',
        bordered: true,
        showIndexColumn: true,
        columns,
        formConfig: {
          labelWidth: 120,
          schemas: searchFormSchema,
        },
#if($table.tree || $table.subTree)
        isTreeTable: true,
        pagination: false,
        canResize: false,
#end
        showTableSetting: true,
        tableSetting: {
          fullScreen: true,
        },
        actionColumn: {
          width: 220,
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
        },
#if($api.batchRemove || $api.batchRemoveForce)
        rowSelection: {
          onChange: (selectedRowKeys, selectRows) => {
            state.${pkColumn.javaField}s = selectedRowKeys
            state.${pkColumn.javaField}Names = selectRows
              .map((item) => {
                return item.#if($nameColumn)${nameColumn.javaField}#else${pkColumn.javaField}#end
              })
              .join(',')
          },
        },
#end
      });

      /** 查看按钮 */
      function handleView(record: Recordable) {
        useUserStore().getRoutePath(${BusinessName}DetailGo, record.${pkColumn.javaField});
      }

      /** 新增按钮 */
      function handleCreate() {
        openModal(true, {
          isUpdate: false,
        });
      }

      /** 修改按钮 */
      function handleEdit(record: Recordable) {
        openModal(true, {
          record,
          isUpdate: true,
        });
      }
#if($api.batchRemove || $api.batchRemoveForce)

      /** 删除按钮 */
      function handleDelete(record: Recordable) {
        const delIds = record.${pkColumn.javaField} || state.${pkColumn.javaField}s;
        const delNames = record.#if($nameColumn)${nameColumn.javaField}#else${pkColumn.javaField}#end || state.${pkColumn.javaField}Names;
        if (!record.${pkColumn.javaField} && state.${pkColumn.javaField}s.length === 0) {
          createMessage.warning('请选择要操作的数据！');
        } else {
          createConfirm({
            iconType: 'warning',
            title: '提示',
            content: '是否确定要删除' + delNames + '?',
            onOk: () =>
#if($api.batchRemove)
              del${BusinessName}Api(delIds).then(() => {
                createMessage.success('删除' + delNames + '成功！');
                reload();
              }),
#elseif($api.batchRemoveForce)
              delForce${BusinessName}Api(delIds).then(() => {
                createMessage.success('删除' + delNames + '成功！');
                reload();
              }),
#end
          });
        }
      }
#end
#if($api.export)

      /** 导出按钮 */
      function handleExport() {
        const value = getForm().getFieldsValue() as ${BusinessName}PM;
        export${BusinessName}Api(value);
      }
#end

      function handleSuccess() {
        reload();
      }

      return {
        IconEnum,
        ${BusinessName}Auth,
        registerTable,
        registerModal,
        handleView,
        handleCreate,
        handleEdit,
#if($api.batchRemove || $api.batchRemoveForce)
        handleDelete,
#end
#if($api.export)
        handleExport,
#end
        handleSuccess,
#if($table.tree || $table.subTree)
        expandAll,
        collapseAll,
#end
      };
    },
  });
</script>
