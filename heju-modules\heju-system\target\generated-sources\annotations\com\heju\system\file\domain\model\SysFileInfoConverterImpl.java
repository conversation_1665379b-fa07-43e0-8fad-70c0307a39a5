package com.heju.system.file.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.file.domain.dto.SysFileInfoDto;
import com.heju.system.file.domain.po.SysFileInfoPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysFileInfoConverterImpl implements SysFileInfoConverter {

    @Override
    public SysFileInfoDto mapperDto(SysFileInfoPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFileInfoDto sysFileInfoDto = new SysFileInfoDto();

        sysFileInfoDto.setId( arg0.getId() );
        sysFileInfoDto.setSourceName( arg0.getSourceName() );
        sysFileInfoDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFileInfoDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFileInfoDto.setName( arg0.getName() );
        sysFileInfoDto.setStatus( arg0.getStatus() );
        sysFileInfoDto.setSort( arg0.getSort() );
        sysFileInfoDto.setCreateBy( arg0.getCreateBy() );
        sysFileInfoDto.setCreateTime( arg0.getCreateTime() );
        sysFileInfoDto.setUpdateBy( arg0.getUpdateBy() );
        sysFileInfoDto.setUpdateTime( arg0.getUpdateTime() );
        sysFileInfoDto.setCreateName( arg0.getCreateName() );
        sysFileInfoDto.setUpdateName( arg0.getUpdateName() );
        sysFileInfoDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysFileInfoDto.setClassifyId( arg0.getClassifyId() );
        sysFileInfoDto.setPositionId( arg0.getPositionId() );
        sysFileInfoDto.setCode( arg0.getCode() );
        sysFileInfoDto.setSize( arg0.getSize() );
        sysFileInfoDto.setUrl( arg0.getUrl() );
        sysFileInfoDto.setRemark( arg0.getRemark() );
        sysFileInfoDto.setDeleteBy( arg0.getDeleteBy() );
        sysFileInfoDto.setBusinessInfo( arg0.getBusinessInfo() );
        sysFileInfoDto.setDelFlag( arg0.getDelFlag() );
        sysFileInfoDto.setDeleteTime( arg0.getDeleteTime() );

        return sysFileInfoDto;
    }

    @Override
    public List<SysFileInfoDto> mapperDto(Collection<SysFileInfoPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFileInfoDto> list = new ArrayList<SysFileInfoDto>( arg0.size() );
        for ( SysFileInfoPo sysFileInfoPo : arg0 ) {
            list.add( mapperDto( sysFileInfoPo ) );
        }

        return list;
    }

    @Override
    public Page<SysFileInfoDto> mapperPageDto(Collection<SysFileInfoPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFileInfoDto> page = new Page<SysFileInfoDto>();
        for ( SysFileInfoPo sysFileInfoPo : arg0 ) {
            page.add( mapperDto( sysFileInfoPo ) );
        }

        return page;
    }

    @Override
    public SysFileInfoPo mapperPo(SysFileInfoDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFileInfoPo sysFileInfoPo = new SysFileInfoPo();

        sysFileInfoPo.setId( arg0.getId() );
        sysFileInfoPo.setSourceName( arg0.getSourceName() );
        sysFileInfoPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFileInfoPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFileInfoPo.setName( arg0.getName() );
        sysFileInfoPo.setStatus( arg0.getStatus() );
        sysFileInfoPo.setSort( arg0.getSort() );
        sysFileInfoPo.setCreateBy( arg0.getCreateBy() );
        sysFileInfoPo.setCreateTime( arg0.getCreateTime() );
        sysFileInfoPo.setUpdateBy( arg0.getUpdateBy() );
        sysFileInfoPo.setUpdateTime( arg0.getUpdateTime() );
        sysFileInfoPo.setDelFlag( arg0.getDelFlag() );
        sysFileInfoPo.setCreateName( arg0.getCreateName() );
        sysFileInfoPo.setUpdateName( arg0.getUpdateName() );
        sysFileInfoPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysFileInfoPo.setClassifyId( arg0.getClassifyId() );
        sysFileInfoPo.setPositionId( arg0.getPositionId() );
        sysFileInfoPo.setCode( arg0.getCode() );
        sysFileInfoPo.setSize( arg0.getSize() );
        sysFileInfoPo.setUrl( arg0.getUrl() );
        sysFileInfoPo.setRemark( arg0.getRemark() );
        sysFileInfoPo.setDeleteTime( arg0.getDeleteTime() );
        sysFileInfoPo.setDeleteBy( arg0.getDeleteBy() );
        sysFileInfoPo.setBusinessInfo( arg0.getBusinessInfo() );

        return sysFileInfoPo;
    }

    @Override
    public List<SysFileInfoPo> mapperPo(Collection<SysFileInfoDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFileInfoPo> list = new ArrayList<SysFileInfoPo>( arg0.size() );
        for ( SysFileInfoDto sysFileInfoDto : arg0 ) {
            list.add( mapperPo( sysFileInfoDto ) );
        }

        return list;
    }

    @Override
    public Page<SysFileInfoPo> mapperPagePo(Collection<SysFileInfoDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFileInfoPo> page = new Page<SysFileInfoPo>();
        for ( SysFileInfoDto sysFileInfoDto : arg0 ) {
            page.add( mapperPo( sysFileInfoDto ) );
        }

        return page;
    }
}
