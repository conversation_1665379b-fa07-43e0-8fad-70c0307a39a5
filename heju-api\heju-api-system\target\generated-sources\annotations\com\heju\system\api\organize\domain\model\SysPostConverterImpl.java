package com.heju.system.api.organize.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.api.organize.domain.po.SysPostPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:50+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysPostConverterImpl implements SysPostConverter {

    @Override
    public SysPostDto mapperDto(SysPostPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysPostDto sysPostDto = new SysPostDto();

        sysPostDto.setId( arg0.getId() );
        sysPostDto.setSourceName( arg0.getSourceName() );
        sysPostDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysPostDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysPostDto.setStatus( arg0.getStatus() );
        sysPostDto.setSort( arg0.getSort() );
        sysPostDto.setRemark( arg0.getRemark() );
        sysPostDto.setCreateBy( arg0.getCreateBy() );
        sysPostDto.setCreateTime( arg0.getCreateTime() );
        sysPostDto.setUpdateBy( arg0.getUpdateBy() );
        sysPostDto.setUpdateTime( arg0.getUpdateTime() );
        sysPostDto.setDelFlag( arg0.getDelFlag() );
        sysPostDto.setCreateName( arg0.getCreateName() );
        sysPostDto.setUpdateName( arg0.getUpdateName() );
        sysPostDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysPostDto.setDeptId( arg0.getDeptId() );
        sysPostDto.setCode( arg0.getCode() );
        sysPostDto.setName( arg0.getName() );
        sysPostDto.setCompanyId( arg0.getCompanyId() );
        sysPostDto.setDeptName( arg0.getDeptName() );
        sysPostDto.setCompanyName( arg0.getCompanyName() );

        return sysPostDto;
    }

    @Override
    public List<SysPostDto> mapperDto(Collection<SysPostPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysPostDto> list = new ArrayList<SysPostDto>( arg0.size() );
        for ( SysPostPo sysPostPo : arg0 ) {
            list.add( mapperDto( sysPostPo ) );
        }

        return list;
    }

    @Override
    public Page<SysPostDto> mapperPageDto(Collection<SysPostPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysPostDto> page = new Page<SysPostDto>();
        for ( SysPostPo sysPostPo : arg0 ) {
            page.add( mapperDto( sysPostPo ) );
        }

        return page;
    }

    @Override
    public SysPostPo mapperPo(SysPostDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysPostPo sysPostPo = new SysPostPo();

        sysPostPo.setId( arg0.getId() );
        sysPostPo.setSourceName( arg0.getSourceName() );
        sysPostPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysPostPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysPostPo.setStatus( arg0.getStatus() );
        sysPostPo.setSort( arg0.getSort() );
        sysPostPo.setRemark( arg0.getRemark() );
        sysPostPo.setCreateBy( arg0.getCreateBy() );
        sysPostPo.setCreateTime( arg0.getCreateTime() );
        sysPostPo.setUpdateBy( arg0.getUpdateBy() );
        sysPostPo.setUpdateTime( arg0.getUpdateTime() );
        sysPostPo.setDelFlag( arg0.getDelFlag() );
        sysPostPo.setCreateName( arg0.getCreateName() );
        sysPostPo.setUpdateName( arg0.getUpdateName() );
        sysPostPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysPostPo.setDeptId( arg0.getDeptId() );
        sysPostPo.setCode( arg0.getCode() );
        sysPostPo.setName( arg0.getName() );
        sysPostPo.setCompanyId( arg0.getCompanyId() );
        sysPostPo.setDeptName( arg0.getDeptName() );
        sysPostPo.setCompanyName( arg0.getCompanyName() );

        return sysPostPo;
    }

    @Override
    public List<SysPostPo> mapperPo(Collection<SysPostDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysPostPo> list = new ArrayList<SysPostPo>( arg0.size() );
        for ( SysPostDto sysPostDto : arg0 ) {
            list.add( mapperPo( sysPostDto ) );
        }

        return list;
    }

    @Override
    public Page<SysPostPo> mapperPagePo(Collection<SysPostDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysPostPo> page = new Page<SysPostPo>();
        for ( SysPostDto sysPostDto : arg0 ) {
            page.add( mapperPo( sysPostDto ) );
        }

        return page;
    }
}
