package com.heju.system.forms.universal.service;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.universal.domain.query.UniversalQuery;

import java.util.List;

/**
 * 级联管理 服务层
 *
 * <AUTHOR>
 */
public interface UniversalService  {

    AjaxResult list(UniversalQuery query);

    AjaxResult addFieldList(UniversalQuery query);

    AjaxResult searchFieldList(UniversalQuery query);

    AjaxResult getInfo(UniversalQuery query);

    AjaxResult add(UniversalQuery query);

    AjaxResult edit(UniversalQuery query);

    AjaxResult batchRemove(UniversalQuery query);

    AjaxResult option(UniversalQuery query);

    List<SysFieldDto> getFieldByRoleIds(List<Long> sheetIds);

    AjaxResult check(UniversalQuery query);

    AjaxResult getEntityInfo(UniversalQuery query);
}