package com.heju.system.notice.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.notice.domain.dto.SysMessageEntityChangeDto;
import com.heju.system.notice.domain.po.SysMessageEntityChangePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysMessageEntityChangeConverterImpl implements SysMessageEntityChangeConverter {

    @Override
    public SysMessageEntityChangeDto mapperDto(SysMessageEntityChangePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysMessageEntityChangeDto sysMessageEntityChangeDto = new SysMessageEntityChangeDto();

        sysMessageEntityChangeDto.setSourceName( arg0.getSourceName() );
        sysMessageEntityChangeDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysMessageEntityChangeDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysMessageEntityChangeDto.setName( arg0.getName() );
        sysMessageEntityChangeDto.setStatus( arg0.getStatus() );
        sysMessageEntityChangeDto.setSort( arg0.getSort() );
        sysMessageEntityChangeDto.setRemark( arg0.getRemark() );
        sysMessageEntityChangeDto.setCreateBy( arg0.getCreateBy() );
        sysMessageEntityChangeDto.setCreateTime( arg0.getCreateTime() );
        sysMessageEntityChangeDto.setUpdateBy( arg0.getUpdateBy() );
        sysMessageEntityChangeDto.setUpdateTime( arg0.getUpdateTime() );
        sysMessageEntityChangeDto.setDelFlag( arg0.getDelFlag() );
        sysMessageEntityChangeDto.setCreateName( arg0.getCreateName() );
        sysMessageEntityChangeDto.setUpdateName( arg0.getUpdateName() );
        sysMessageEntityChangeDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysMessageEntityChangeDto.setEntityId( arg0.getEntityId() );
        sysMessageEntityChangeDto.setChangeField( arg0.getChangeField() );
        sysMessageEntityChangeDto.setChangeBefore( arg0.getChangeBefore() );
        sysMessageEntityChangeDto.setChangeAfter( arg0.getChangeAfter() );
        sysMessageEntityChangeDto.setId( arg0.getId() );

        return sysMessageEntityChangeDto;
    }

    @Override
    public List<SysMessageEntityChangeDto> mapperDto(Collection<SysMessageEntityChangePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysMessageEntityChangeDto> list = new ArrayList<SysMessageEntityChangeDto>( arg0.size() );
        for ( SysMessageEntityChangePo sysMessageEntityChangePo : arg0 ) {
            list.add( mapperDto( sysMessageEntityChangePo ) );
        }

        return list;
    }

    @Override
    public Page<SysMessageEntityChangeDto> mapperPageDto(Collection<SysMessageEntityChangePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysMessageEntityChangeDto> page = new Page<SysMessageEntityChangeDto>();
        for ( SysMessageEntityChangePo sysMessageEntityChangePo : arg0 ) {
            page.add( mapperDto( sysMessageEntityChangePo ) );
        }

        return page;
    }

    @Override
    public SysMessageEntityChangePo mapperPo(SysMessageEntityChangeDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysMessageEntityChangePo sysMessageEntityChangePo = new SysMessageEntityChangePo();

        sysMessageEntityChangePo.setId( arg0.getId() );
        sysMessageEntityChangePo.setSourceName( arg0.getSourceName() );
        sysMessageEntityChangePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysMessageEntityChangePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysMessageEntityChangePo.setName( arg0.getName() );
        sysMessageEntityChangePo.setStatus( arg0.getStatus() );
        sysMessageEntityChangePo.setSort( arg0.getSort() );
        sysMessageEntityChangePo.setRemark( arg0.getRemark() );
        sysMessageEntityChangePo.setCreateBy( arg0.getCreateBy() );
        sysMessageEntityChangePo.setCreateTime( arg0.getCreateTime() );
        sysMessageEntityChangePo.setUpdateBy( arg0.getUpdateBy() );
        sysMessageEntityChangePo.setUpdateTime( arg0.getUpdateTime() );
        sysMessageEntityChangePo.setDelFlag( arg0.getDelFlag() );
        sysMessageEntityChangePo.setCreateName( arg0.getCreateName() );
        sysMessageEntityChangePo.setUpdateName( arg0.getUpdateName() );
        sysMessageEntityChangePo.setEnterpriseId( arg0.getEnterpriseId() );
        sysMessageEntityChangePo.setEntityId( arg0.getEntityId() );
        sysMessageEntityChangePo.setChangeField( arg0.getChangeField() );
        sysMessageEntityChangePo.setChangeBefore( arg0.getChangeBefore() );
        sysMessageEntityChangePo.setChangeAfter( arg0.getChangeAfter() );

        return sysMessageEntityChangePo;
    }

    @Override
    public List<SysMessageEntityChangePo> mapperPo(Collection<SysMessageEntityChangeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysMessageEntityChangePo> list = new ArrayList<SysMessageEntityChangePo>( arg0.size() );
        for ( SysMessageEntityChangeDto sysMessageEntityChangeDto : arg0 ) {
            list.add( mapperPo( sysMessageEntityChangeDto ) );
        }

        return list;
    }

    @Override
    public Page<SysMessageEntityChangePo> mapperPagePo(Collection<SysMessageEntityChangeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysMessageEntityChangePo> page = new Page<SysMessageEntityChangePo>();
        for ( SysMessageEntityChangeDto sysMessageEntityChangeDto : arg0 ) {
            page.add( mapperPo( sysMessageEntityChangeDto ) );
        }

        return page;
    }
}
