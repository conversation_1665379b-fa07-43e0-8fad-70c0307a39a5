09:21:01.226 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:21:02.189 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7ae8caf8-8c34-4dc8-bac2-a135851a39d8_config-0
09:21:02.270 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 34 ms to scan 1 urls, producing 3 keys and 6 values 
09:21:02.320 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 4 keys and 9 values 
09:21:02.336 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 3 keys and 10 values 
09:21:02.346 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:21:02.362 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 7 values 
09:21:02.370 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:21:02.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8caf8-8c34-4dc8-bac2-a135851a39d8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:21:02.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8caf8-8c34-4dc8-bac2-a135851a39d8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002aec339b650
09:21:02.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8caf8-8c34-4dc8-bac2-a135851a39d8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002aec339b870
09:21:02.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8caf8-8c34-4dc8-bac2-a135851a39d8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:21:02.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8caf8-8c34-4dc8-bac2-a135851a39d8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:21:02.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8caf8-8c34-4dc8-bac2-a135851a39d8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:03.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8caf8-8c34-4dc8-bac2-a135851a39d8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751851263380_127.0.0.1_10558
09:21:03.601 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8caf8-8c34-4dc8-bac2-a135851a39d8_config-0] Notify connected event to listeners.
09:21:03.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8caf8-8c34-4dc8-bac2-a135851a39d8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:03.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ae8caf8-8c34-4dc8-bac2-a135851a39d8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002aec34d4d90
09:21:03.733 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:21:05.992 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:21:05.993 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:21:05.993 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:21:06.225 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:21:08.884 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:21:11.068 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8ecf56f8-1155-41fb-b598-546f37a4a784
09:21:11.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] RpcClient init label, labels = {module=naming, source=sdk}
09:21:11.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:11.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:11.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:11.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:11.206 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] Success to connect to server [localhost:8848] on start up, connectionId = 1751851271087_127.0.0.1_10637
09:21:11.206 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:11.207 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] Notify connected event to listeners.
09:21:11.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002aec34d4d90
09:21:11.274 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:21:11.317 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:21:11.544 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 11.351 seconds (JVM running for 13.668)
09:21:11.558 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:21:11.559 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:21:11.562 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:21:11.756 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:21:11.776 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] Ack server push request, request = NotifySubscriberRequest, requestId = 5
12:23:09.095 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:23:11.260 [nacos-grpc-client-executor-2194] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] Receive server push request, request = NotifySubscriberRequest, requestId = 11
12:23:11.262 [nacos-grpc-client-executor-2194] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] Ack server push request, request = NotifySubscriberRequest, requestId = 11
12:23:25.040 [nacos-grpc-client-executor-2199] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] Receive server push request, request = NotifySubscriberRequest, requestId = 12
12:23:25.043 [nacos-grpc-client-executor-2199] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ecf56f8-1155-41fb-b598-546f37a4a784] Ack server push request, request = NotifySubscriberRequest, requestId = 12
13:30:04.021 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:30:04.026 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:30:04.373 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:30:04.373 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3e7a1707[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:30:04.373 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751851271087_127.0.0.1_10637
13:30:04.373 [nacos-grpc-client-executor-3026] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751851271087_127.0.0.1_10637]Ignore complete event,isRunning:false,isAbandon=false
13:30:04.373 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5a4b584[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 3027]
15:21:28.531 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:21:31.095 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8cde5776-8d3d-4883-a425-e34913493e4b_config-0
15:21:31.357 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 155 ms to scan 1 urls, producing 3 keys and 6 values 
15:21:31.450 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 4 keys and 9 values 
15:21:31.485 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 10 values 
15:21:31.529 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 1 keys and 5 values 
15:21:31.573 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 1 keys and 7 values 
15:21:31.623 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 2 keys and 8 values 
15:21:31.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cde5776-8d3d-4883-a425-e34913493e4b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:21:31.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cde5776-8d3d-4883-a425-e34913493e4b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000288293c9e68
15:21:31.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cde5776-8d3d-4883-a425-e34913493e4b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000288293ca088
15:21:31.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cde5776-8d3d-4883-a425-e34913493e4b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:21:31.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cde5776-8d3d-4883-a425-e34913493e4b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:21:31.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cde5776-8d3d-4883-a425-e34913493e4b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:21:35.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cde5776-8d3d-4883-a425-e34913493e4b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751872894629_127.0.0.1_11890
15:21:35.109 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cde5776-8d3d-4883-a425-e34913493e4b_config-0] Notify connected event to listeners.
15:21:35.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cde5776-8d3d-4883-a425-e34913493e4b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:35.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8cde5776-8d3d-4883-a425-e34913493e4b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000028829506780
15:21:35.467 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:21:42.858 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
15:21:42.860 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:21:42.861 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:21:43.300 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:21:47.472 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:21:50.165 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fbe35d76-6cb7-44e6-bc38-75d9aef0242c
15:21:50.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbe35d76-6cb7-44e6-bc38-75d9aef0242c] RpcClient init label, labels = {module=naming, source=sdk}
15:21:50.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbe35d76-6cb7-44e6-bc38-75d9aef0242c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:21:50.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbe35d76-6cb7-44e6-bc38-75d9aef0242c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:21:50.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbe35d76-6cb7-44e6-bc38-75d9aef0242c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:21:50.172 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbe35d76-6cb7-44e6-bc38-75d9aef0242c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:21:50.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbe35d76-6cb7-44e6-bc38-75d9aef0242c] Success to connect to server [localhost:8848] on start up, connectionId = 1751872910189_127.0.0.1_12003
15:21:50.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbe35d76-6cb7-44e6-bc38-75d9aef0242c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:50.311 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbe35d76-6cb7-44e6-bc38-75d9aef0242c] Notify connected event to listeners.
15:21:50.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbe35d76-6cb7-44e6-bc38-75d9aef0242c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000028829506780
15:21:50.411 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
15:21:50.470 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
15:21:50.811 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 26.398 seconds (JVM running for 31.041)
15:21:50.837 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
15:21:50.840 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
15:21:50.845 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
15:21:50.900 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbe35d76-6cb7-44e6-bc38-75d9aef0242c] Receive server push request, request = NotifySubscriberRequest, requestId = 15
15:21:50.931 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbe35d76-6cb7-44e6-bc38-75d9aef0242c] Ack server push request, request = NotifySubscriberRequest, requestId = 15
15:21:51.515 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:43:55.666 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:43:55.668 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:43:56.011 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:43:56.011 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4a010740[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:43:56.011 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751872910189_127.0.0.1_12003
15:43:56.013 [nacos-grpc-client-executor-275] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751872910189_127.0.0.1_12003]Ignore complete event,isRunning:false,isAbandon=false
15:43:56.015 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@23fc264c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 276]
15:45:56.112 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:46:01.046 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0cc140c2-3e8a-49b6-9bc8-dd4d1fd00188_config-0
15:46:01.386 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 181 ms to scan 1 urls, producing 3 keys and 6 values 
15:46:01.541 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 56 ms to scan 1 urls, producing 4 keys and 9 values 
15:46:01.596 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 10 values 
15:46:01.652 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 1 keys and 5 values 
15:46:01.720 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 1 keys and 7 values 
15:46:01.771 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 2 keys and 8 values 
15:46:01.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cc140c2-3e8a-49b6-9bc8-dd4d1fd00188_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:46:01.792 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cc140c2-3e8a-49b6-9bc8-dd4d1fd00188_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001df513deb60
15:46:01.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cc140c2-3e8a-49b6-9bc8-dd4d1fd00188_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001df513ded80
15:46:01.797 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cc140c2-3e8a-49b6-9bc8-dd4d1fd00188_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:46:01.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cc140c2-3e8a-49b6-9bc8-dd4d1fd00188_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:46:01.849 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cc140c2-3e8a-49b6-9bc8-dd4d1fd00188_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:46:09.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cc140c2-3e8a-49b6-9bc8-dd4d1fd00188_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751874367728_127.0.0.1_3816
15:46:09.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cc140c2-3e8a-49b6-9bc8-dd4d1fd00188_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:46:09.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cc140c2-3e8a-49b6-9bc8-dd4d1fd00188_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001df515169a8
15:46:09.254 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cc140c2-3e8a-49b6-9bc8-dd4d1fd00188_config-0] Notify connected event to listeners.
15:46:11.368 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:46:48.999 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
15:46:49.007 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:46:49.016 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:46:52.246 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:47:26.256 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:47:59.054 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 94e84347-bafc-438a-af05-30de88b3003a
15:47:59.065 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] RpcClient init label, labels = {module=naming, source=sdk}
15:47:59.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:47:59.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:47:59.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:47:59.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:47:59.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Success to connect to server [localhost:8848] on start up, connectionId = 1751874479353_127.0.0.1_4475
15:47:59.771 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Notify connected event to listeners.
15:47:59.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:47:59.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001df515169a8
15:48:01.076 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
15:48:01.246 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Receive server push request, request = NotifySubscriberRequest, requestId = 29
15:48:01.254 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Ack server push request, request = NotifySubscriberRequest, requestId = 29
15:48:01.405 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
15:48:03.157 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Receive server push request, request = NotifySubscriberRequest, requestId = 31
15:48:03.640 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Ack server push request, request = NotifySubscriberRequest, requestId = 31
15:48:05.285 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 133.305 seconds (JVM running for 142.173)
15:48:05.306 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
15:48:05.306 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
15:48:05.327 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
16:29:22.449 [http-nio-9200-exec-5] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:29:24.641 [nacos-grpc-client-executor-505] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Receive server push request, request = NotifySubscriberRequest, requestId = 43
16:29:24.641 [nacos-grpc-client-executor-505] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Ack server push request, request = NotifySubscriberRequest, requestId = 43
16:30:51.310 [nacos-grpc-client-executor-524] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Receive server push request, request = NotifySubscriberRequest, requestId = 44
16:30:51.311 [nacos-grpc-client-executor-524] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Ack server push request, request = NotifySubscriberRequest, requestId = 44
16:39:05.822 [nacos-grpc-client-executor-625] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Receive server push request, request = NotifySubscriberRequest, requestId = 46
16:39:05.840 [nacos-grpc-client-executor-625] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Ack server push request, request = NotifySubscriberRequest, requestId = 46
16:39:24.387 [nacos-grpc-client-executor-629] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Receive server push request, request = NotifySubscriberRequest, requestId = 48
16:39:24.437 [nacos-grpc-client-executor-629] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Ack server push request, request = NotifySubscriberRequest, requestId = 48
17:19:55.072 [nacos-grpc-client-executor-1115] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Receive server push request, request = NotifySubscriberRequest, requestId = 51
17:19:55.090 [nacos-grpc-client-executor-1115] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Ack server push request, request = NotifySubscriberRequest, requestId = 51
17:20:00.916 [nacos-grpc-client-executor-1116] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Receive server push request, request = NotifySubscriberRequest, requestId = 53
17:20:00.929 [nacos-grpc-client-executor-1116] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94e84347-bafc-438a-af05-30de88b3003a] Ack server push request, request = NotifySubscriberRequest, requestId = 53
19:02:19.659 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:02:19.659 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:02:19.985 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:02:19.987 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4957af40[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:02:19.987 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751874479353_127.0.0.1_4475
19:02:19.991 [nacos-grpc-client-executor-2344] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751874479353_127.0.0.1_4475]Ignore complete event,isRunning:false,isAbandon=false
19:02:20.006 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@10564dbd[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2345]
