package com.heju.system.forms.busApply.domain.dto;

import com.heju.system.forms.busApply.domain.po.SysApplyRecordPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 行政申请 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysApplyRecordDto extends SysApplyRecordPo {

    @Serial
    private static final long serialVersionUID = 1L;

    private List<Long> businessIds;

}
