package com.heju.system.forms.busApply.controller;

import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.forms.busApply.domain.dto.SysApplyRecordDto;
import com.heju.system.forms.busApply.domain.query.SysApplyRecordQuery;
import com.heju.system.forms.busApply.domain.query.UniversalApplyQuery;
import com.heju.system.forms.busApply.service.ISysApplyRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 行政申请管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/bus/apply")
public class SysApplyRecordController extends BaseController<SysApplyRecordQuery, SysApplyRecordDto, ISysApplyRecordService> {

    @Autowired
    private ISysApplyRecordService service;

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "行政申请" ;
    }

    private String APPLY_STATUS = "2";
    private String STATUS = "0";

    /**
     * 查询行政申请列表
     */
    @Override
    @GetMapping("/list")
//    @RequiresPermissions(Auth.SYS_APPLY_RECORD_LIST)
    public AjaxResult list(SysApplyRecordQuery applyRecord) {
        return super.list(applyRecord);
    }

    /**
     * 查询行政申请详细
     */
    @Override
    @GetMapping(value = "/{id}")
//    @RequiresPermissions(Auth.SYS_APPLY_RECORD_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 行政申请新增
     */
    @Override
    @PostMapping
//    @RequiresPermissions(Auth.SYS_APPLY_RECORD_ADD)
    @Log(title = "行政申请管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysApplyRecordDto applyRecord) {
        return super.add(applyRecord);
    }

    @PostMapping("/batchInsert")
    public AjaxResult addBatch(@Validated @RequestBody SysApplyRecordDto batchDto) {
        // 转换为多个 dto 实体
        List<SysApplyRecordDto> records = batchDto.getBusinessIds().stream()
                .map(businessId -> {
                    SysApplyRecordDto dto = new SysApplyRecordDto();
                    dto.setBusinessId(businessId);
                    dto.setApiName(batchDto.getApiName());
                    dto.setApplyReason(batchDto.getApplyReason());
                    dto.setApplyTime(new Date());
                    dto.setApplyStatus(APPLY_STATUS);
                    dto.setSort(NumberUtil.Zero);
                    dto.setStatus(STATUS);
                    return dto;
                })
                .collect(Collectors.toList());

        // 批量插入（假设你的 service 支持）
        service.insertBatch(records);
        return AjaxResult.success("批量申请提交成功");
    }

    /**
     * 行政申请修改
     */
    @Override
    @PutMapping
//    @RequiresPermissions(Auth.SYS_APPLY_RECORD_EDIT)
    @Log(title = "行政申请管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysApplyRecordDto applyRecord) {
        return super.edit(applyRecord);
    }

    /**
     * 行政申请修改状态
     */
    @Override
    @PutMapping("/status")
//    @RequiresPermissions(value = {Auth.SYS_APPLY_RECORD_EDIT, Auth.SYS_APPLY_RECORD_ES}, logical = Logical.OR)
    @Log(title = "行政申请管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysApplyRecordDto applyRecord) {
        return super.editStatus(applyRecord);
    }

    /**
     * 更新申请状态
     * @param applyRecord
     * @return
     */
    @PutMapping("/editApplyStatus")
    public AjaxResult editApplyStatus(@Validated({V_E.class}) @RequestBody SysApplyRecordDto applyRecord) {
        return service.editApplyStatus(applyRecord);
    }

    /**
     * 行政申请批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
//    @RequiresPermissions(Auth.SYS_APPLY_RECORD_DEL)
    @Log(title = "行政申请管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取行政申请选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }


    /**
     * 印章申请列表字段
     */
    @GetMapping("/getSealApplyField")
    public AjaxResult getSealApplyField(UniversalApplyQuery query) {
        return service.getSealApplyField(query);
    }

    /**
     * 印章申请列表数据查询
     */
    @GetMapping("/getSealApplyList")
    public AjaxResult getSealApplyList(UniversalApplyQuery query) {
        return service.getSealApplyList(query);
    }

    /**
     * 印章申请新增字段
     */
    @GetMapping("/getAddField")
    public AjaxResult getAddField(UniversalApplyQuery query) {
        return service.getAddField(query);
    }

    /**
     * 印章申请新增列表
     */
    @GetMapping("/getAddList")
    public AjaxResult getAddList(UniversalApplyQuery query) {
        return service.getAddList(query);
    }

    /**
     * 通用查询业务表单列表
     * @param query 搜索条件json
     * @return 业务表单列表
     */
    @GetMapping("/universalList")
    public AjaxResult universalList(UniversalApplyQuery query) {
        return service.universalList(query);
    }

    /**
     * 查询申请管理列表
     * @param query
     * @return
     */
    @GetMapping("/manageList")
    public AjaxResult manageList(UniversalApplyQuery query) {
        return service.manageList(query);
    }

    /**
     * 查询申请管理字段
     * @param query
     * @return
     */
    @GetMapping("/manageFieldList")
    public AjaxResult manageFieldList(UniversalApplyQuery query) {
        return service.manageFieldList(query);
    }

}
