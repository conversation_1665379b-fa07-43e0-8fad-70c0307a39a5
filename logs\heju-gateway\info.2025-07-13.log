10:04:04.891 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:04:05.740 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 547651f4-f50d-4092-991c-8697f6abeeaf_config-0
10:04:05.843 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 33 ms to scan 1 urls, producing 3 keys and 6 values 
10:04:05.877 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 4 keys and 9 values 
10:04:05.895 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 3 keys and 10 values 
10:04:05.900 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 0 ms to scan 1 urls, producing 1 keys and 5 values 
10:04:05.913 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 7 values 
10:04:05.913 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
10:04:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [547651f4-f50d-4092-991c-8697f6abeeaf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:04:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [547651f4-f50d-4092-991c-8697f6abeeaf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000201013b9220
10:04:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [547651f4-f50d-4092-991c-8697f6abeeaf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000201013b9440
10:04:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [547651f4-f50d-4092-991c-8697f6abeeaf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:04:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [547651f4-f50d-4092-991c-8697f6abeeaf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:04:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [547651f4-f50d-4092-991c-8697f6abeeaf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:07.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [547651f4-f50d-4092-991c-8697f6abeeaf_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752372247330_127.0.0.1_8168
10:04:07.608 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [547651f4-f50d-4092-991c-8697f6abeeaf_config-0] Notify connected event to listeners.
10:04:07.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [547651f4-f50d-4092-991c-8697f6abeeaf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:07.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [547651f4-f50d-4092-991c-8697f6abeeaf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000201014f1450
10:04:08.034 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:04:12.143 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:04:13.482 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:04:14.098 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2be7e38e-ff8d-4104-b3e7-a664801ed12a_config-0
10:04:14.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2be7e38e-ff8d-4104-b3e7-a664801ed12a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:04:14.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2be7e38e-ff8d-4104-b3e7-a664801ed12a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000201013b9220
10:04:14.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2be7e38e-ff8d-4104-b3e7-a664801ed12a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000201013b9440
10:04:14.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2be7e38e-ff8d-4104-b3e7-a664801ed12a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:04:14.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2be7e38e-ff8d-4104-b3e7-a664801ed12a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:04:14.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2be7e38e-ff8d-4104-b3e7-a664801ed12a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:14.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2be7e38e-ff8d-4104-b3e7-a664801ed12a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752372254110_127.0.0.1_8180
10:04:14.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2be7e38e-ff8d-4104-b3e7-a664801ed12a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:14.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2be7e38e-ff8d-4104-b3e7-a664801ed12a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000201014f1450
10:04:14.225 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2be7e38e-ff8d-4104-b3e7-a664801ed12a_config-0] Notify connected event to listeners.
10:04:14.327 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1f6330af-6b42-458c-91e4-07af0766f11c
10:04:14.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] RpcClient init label, labels = {module=naming, source=sdk}
10:04:14.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:04:14.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:04:14.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:04:14.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:14.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Success to connect to server [localhost:8848] on start up, connectionId = 1752372254338_127.0.0.1_8182
10:04:14.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:14.461 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Notify connected event to listeners.
10:04:14.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000201014f1450
10:04:14.949 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
10:04:14.988 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 10.88 seconds (JVM running for 19.594)
10:04:14.997 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
10:04:14.998 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
10:04:14.999 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
10:04:15.023 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 2
10:04:15.024 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 2
10:04:15.114 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 3
10:04:15.115 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 3
10:04:45.239 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 8
10:04:45.241 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 8
10:04:45.253 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 7
10:04:45.254 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 7
10:04:45.265 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 9
10:04:45.266 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 9
10:20:12.804 [nacos-grpc-client-executor-343] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 14
10:20:12.826 [nacos-grpc-client-executor-343] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 14
10:20:29.181 [nacos-grpc-client-executor-351] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 17
10:20:29.203 [nacos-grpc-client-executor-351] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 17
10:47:03.777 [nacos-grpc-client-executor-884] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 21
10:47:03.804 [nacos-grpc-client-executor-884] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 21
10:47:29.240 [nacos-grpc-client-executor-893] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 23
10:47:29.257 [nacos-grpc-client-executor-893] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 23
13:48:39.543 [nacos-grpc-client-executor-4474] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 26
13:48:39.556 [nacos-grpc-client-executor-4474] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 26
13:48:57.931 [nacos-grpc-client-executor-4482] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 28
13:48:57.946 [nacos-grpc-client-executor-4482] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 28
14:20:40.231 [nacos-grpc-client-executor-5079] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 31
14:20:40.245 [nacos-grpc-client-executor-5079] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 31
14:20:58.486 [nacos-grpc-client-executor-5084] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 33
14:20:58.501 [nacos-grpc-client-executor-5084] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 33
14:22:07.441 [nacos-grpc-client-executor-5103] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 36
14:22:07.453 [nacos-grpc-client-executor-5103] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 36
14:22:24.180 [nacos-grpc-client-executor-5110] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 38
14:22:24.197 [nacos-grpc-client-executor-5110] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 38
14:23:44.716 [nacos-grpc-client-executor-5132] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 41
14:23:44.733 [nacos-grpc-client-executor-5132] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 41
14:24:11.379 [nacos-grpc-client-executor-5141] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 44
14:24:11.395 [nacos-grpc-client-executor-5141] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 44
15:48:32.097 [nacos-grpc-client-executor-6759] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Receive server push request, request = NotifySubscriberRequest, requestId = 46
15:48:32.100 [nacos-grpc-client-executor-6759] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f6330af-6b42-458c-91e4-07af0766f11c] Ack server push request, request = NotifySubscriberRequest, requestId = 46
19:20:01.414 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:20:01.414 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:20:01.762 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:20:01.762 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4bbe1ed7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:20:01.762 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752372254338_127.0.0.1_8182
19:20:01.762 [nacos-grpc-client-executor-10681] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752372254338_127.0.0.1_8182]Ignore complete event,isRunning:false,isAbandon=false
19:20:01.777 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3ccb5897[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 10682]
19:22:01.373 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:22:04.657 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 64d494c5-8520-4733-ac95-34c90d35a1a9_config-0
19:22:04.932 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 122 ms to scan 1 urls, producing 3 keys and 6 values 
19:22:05.086 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 4 keys and 9 values 
19:22:05.121 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 10 values 
19:22:05.163 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 1 keys and 5 values 
19:22:05.197 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 1 keys and 7 values 
19:22:05.235 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 2 keys and 8 values 
19:22:05.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:22:05.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001f4e63cbda8
19:22:05.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001f4e63cbfc8
19:22:05.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:22:05.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:22:05.284 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:08.223 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:08.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:08.760 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
19:22:08.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:22:08.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001f4e65064e8
19:22:09.307 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:09.560 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:10.670 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:10.793 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:22:11.131 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:11.693 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:12.453 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:13.741 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:14.781 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:15.748 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:16.986 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:18.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:20.008 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:21.699 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:23.596 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Success to connect a server [localhost:8848], connectionId = 1752405743428_127.0.0.1_2831
19:22:23.597 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64d494c5-8520-4733-ac95-34c90d35a1a9_config-0] Notify connected event to listeners.
19:22:24.833 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
19:22:27.510 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
19:22:29.118 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0d65a191-7693-4724-a423-fb5cb4678fe2_config-0
19:22:29.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d65a191-7693-4724-a423-fb5cb4678fe2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:22:29.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d65a191-7693-4724-a423-fb5cb4678fe2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001f4e63cbda8
19:22:29.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d65a191-7693-4724-a423-fb5cb4678fe2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001f4e63cbfc8
19:22:29.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d65a191-7693-4724-a423-fb5cb4678fe2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:22:29.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d65a191-7693-4724-a423-fb5cb4678fe2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:22:29.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d65a191-7693-4724-a423-fb5cb4678fe2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:29.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d65a191-7693-4724-a423-fb5cb4678fe2_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752405749128_127.0.0.1_2856
19:22:29.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d65a191-7693-4724-a423-fb5cb4678fe2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:22:29.240 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d65a191-7693-4724-a423-fb5cb4678fe2_config-0] Notify connected event to listeners.
19:22:29.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d65a191-7693-4724-a423-fb5cb4678fe2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001f4e65064e8
19:22:29.501 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 95aca74e-d547-4db9-bd24-b337d3ea8ccd
19:22:29.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] RpcClient init label, labels = {module=naming, source=sdk}
19:22:29.507 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:22:29.508 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:22:29.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:22:29.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:29.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Success to connect to server [localhost:8848] on start up, connectionId = 1752405749520_127.0.0.1_2857
19:22:29.642 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Notify connected event to listeners.
19:22:29.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:22:29.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001f4e65064e8
19:22:30.188 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Receive server push request, request = NotifySubscriberRequest, requestId = 2
19:22:30.189 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Ack server push request, request = NotifySubscriberRequest, requestId = 2
19:22:30.379 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Receive server push request, request = NotifySubscriberRequest, requestId = 3
19:22:30.380 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Ack server push request, request = NotifySubscriberRequest, requestId = 3
19:22:30.564 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
19:22:30.617 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 31.128 seconds (JVM running for 34.094)
19:22:30.627 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
19:22:30.628 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
19:22:30.629 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
19:22:31.145 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Receive server push request, request = NotifySubscriberRequest, requestId = 4
19:22:31.145 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Ack server push request, request = NotifySubscriberRequest, requestId = 4
19:23:00.236 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Receive server push request, request = NotifySubscriberRequest, requestId = 8
19:23:00.237 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Ack server push request, request = NotifySubscriberRequest, requestId = 8
19:23:00.542 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Receive server push request, request = NotifySubscriberRequest, requestId = 9
19:23:00.542 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Ack server push request, request = NotifySubscriberRequest, requestId = 9
19:23:00.552 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Receive server push request, request = NotifySubscriberRequest, requestId = 10
19:23:00.554 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95aca74e-d547-4db9-bd24-b337d3ea8ccd] Ack server push request, request = NotifySubscriberRequest, requestId = 10
19:34:22.103 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:34:22.121 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:34:22.446 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:34:22.446 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2c147c2d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:34:22.447 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752405749520_127.0.0.1_2857
19:34:22.447 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2b42d74c[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 267]
