package com.heju.system.approval.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.approval.domain.dto.SysApprovalCustomerinfoDto;
import com.heju.system.approval.domain.query.SysApprovalCustomerinfoQuery;
import com.heju.system.approval.manager.ISysApprovalCustomerinfoManager;
import com.heju.system.approval.service.ISysApprovalCustomerinfoService;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.manager.ISysFieldManager;
import com.heju.system.forms.field.manager.impl.SysFieldManager;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.manager.ISysSheetManager;
import com.heju.system.organize.manager.ISysUserManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户信息审核管理 服务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysApprovalCustomerinfoServiceImpl extends BaseServiceImpl<SysApprovalCustomerinfoQuery, SysApprovalCustomerinfoDto, ISysApprovalCustomerinfoManager> implements ISysApprovalCustomerinfoService {

    @Resource
    private ISysSheetManager sheetManager;

    @Resource
    private ISysFieldManager fieldManager;

    @Resource
    private ISysUserManager sysUserManager;

    @Resource
    private SysFieldManager sysFieldManager;

    /**
     * 查询客户信息审核对象列表 | 数据权限
     *
     * @param approvalCustomerinfo 客户信息审核对象
     * @return 客户信息审核对象集合
     */

    @Override
    public List<SysApprovalCustomerinfoDto> selectListScope(SysApprovalCustomerinfoQuery approvalCustomerinfo) {
        // （1）sql 查
        //  return sysApprovalCustomerinfoManager.selectByQuery(approvalCustomerinfo);

        // （2）业务查
        // 1. 取出 sys_approval_Customerinfo 表所有数据
        List<SysApprovalCustomerinfoDto> list = baseManager.selectByQuery(approvalCustomerinfo);

        // 2. 取出所有 fieldId, sheetId, createBy, 并去重
        Set<Long> fieldIds = new HashSet<>();
        Set<Long> sheetIds = new HashSet<>();
        Set<Long> createByIds = new HashSet<>();
        list.forEach(dto -> {
            if (dto.getFieldId() != null) { fieldIds.add(dto.getFieldId()); }
            if (dto.getSheetId() != null) { sheetIds.add(dto.getSheetId()); }
            if (dto.getCreateBy() != null) { createByIds.add(dto.getCreateBy()); }
        });

        // 避免空集合查询，提高效率
        List<SysFieldDto> sysFieldDtos = fieldIds.isEmpty() ? Collections.emptyList() : fieldManager.selectListByIds(fieldIds);
        List<SysSheetDto> sysSheetDtos = sheetIds.isEmpty() ? Collections.emptyList() : sheetManager.selectListByIds(sheetIds);
        List<SysUserDto> sysUserDtos = createByIds.isEmpty() ? Collections.emptyList() : sysUserManager.selectListByIds(createByIds);

        Map<Long, String> fieldMap = sysFieldDtos.stream().
                collect(Collectors.toMap(SysFieldDto::getId, SysFieldDto::getName, (v1, v2) -> v1));
        Map<Long, String> sheetMap = sysSheetDtos.stream()
                .collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getName, (v1, v2) -> v1));
        Map<Long, String> sheetApiMap = sysSheetDtos.stream()
                .collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getApiName, (v1, v2) -> v1));
        Map<Long, String> userMap = sysUserDtos.stream()
                .collect(Collectors.toMap(SysUserDto::getId, SysUserDto::getNickName, (v1, v2) -> v1));

        list.forEach(sysApprovalCustomerinfoDto -> {
            sysApprovalCustomerinfoDto.setSheetName(sheetMap.get(sysApprovalCustomerinfoDto.getSheetId()));
            sysApprovalCustomerinfoDto.setDynamicTableName(sheetApiMap.get(sysApprovalCustomerinfoDto.getSheetId()));
            sysApprovalCustomerinfoDto.setFieldName(fieldMap.get(sysApprovalCustomerinfoDto.getFieldId()));
            sysApprovalCustomerinfoDto.setCreateName(userMap.get(sysApprovalCustomerinfoDto.getCreateBy()));
        });

        Map<String, List<SysApprovalCustomerinfoDto>> sheetApiGroupMap = list.stream()
                .collect(Collectors.groupingBy(SysApprovalCustomerinfoDto::getDynamicTableName));
        List<SysApprovalCustomerinfoDto> result = new ArrayList<>();
        for (Map.Entry<String, List<SysApprovalCustomerinfoDto>> entry : sheetApiGroupMap.entrySet()) {
            List<SysApprovalCustomerinfoDto> value = entry.getValue();
            Set<Long> businessIdSet = value.stream().map(SysApprovalCustomerinfoDto::getBusinessId).collect(Collectors.toSet());
            // // 避免空集合查询
            if (!businessIdSet.isEmpty()) {
                List<SysApprovalCustomerinfoDto> dtos = baseManager.selectBusinessName(entry.getKey(), businessIdSet);
                Map<Long, String> businessNameMap = dtos.stream().collect(
                        Collectors.toMap(SysApprovalCustomerinfoDto::getBusinessId, SysApprovalCustomerinfoDto::getBusinessName, (v1, v2) -> v1));
                value.forEach(sysApprovalCustomerinfoDto ->
                        sysApprovalCustomerinfoDto.setBusinessName(businessNameMap.get(sysApprovalCustomerinfoDto.getBusinessId())));
            }
            result.addAll(value);
        }
        return result;
    }


    /**
     * 查询客户信息审核详细
     *
     * @param id Id 客户信息审核id
     * @return 客户信息审核对象
     */
    @Override
    public SysApprovalCustomerinfoDto selectById(Serializable id) {
        // 业务查
        // 1. 取出 id下 的sys_approval_Customerinfo 表记录
        SysApprovalCustomerinfoDto dto = baseManager.selectById(id);
        if (dto == null) {
            return null;
        }
        // 2. 查出 fieldName, sheetName, dynamicTableName, createName 并赋值给 dto
        dto.setFieldName(sysFieldManager.selectById(dto.getFieldId()).getName());
        dto.setSheetName(sheetManager.selectById(dto.getSheetId()).getName());
        dto.setDynamicTableName(sheetManager.selectById(dto.getSheetId()).getApiName());
        dto.setCreateName(sysUserManager.selectById(dto.getCreateBy()).getNickName());
        // 3. 传入 apiName, businessId 做查询
        SysApprovalCustomerinfoDto businessName = baseManager.selectBusinessNameById(dto.getDynamicTableName(), dto.getBusinessId());
        if (businessName != null) {
            dto.setBusinessName(businessName.getBusinessName());
        }
        return dto;
    }

    /**
     * 通过客户信息审核
     * @param approvalCustomerinfo 客户信息审核对象
     * @return int 影响行数
     */
    @Override
    @DSTransactional
    public int pass(SysApprovalCustomerinfoDto approvalCustomerinfo) {
        SysApprovalCustomerinfoDto dto = baseManager.selectById(approvalCustomerinfo.getId());
        // 1.更新 sys_approval_customerinfo 表的 status 为 1
        if (dto == null) {
            throw new IllegalArgumentException("客户信息审核记录不存在，ID: " + approvalCustomerinfo.getId());
        }
        dto.setStatus("1");
        int result = baseManager.update(dto);

        // 2.更新 sys_field 表 对应 id 的 name 为 after_update
        if (approvalCustomerinfo.getFieldId() != null && approvalCustomerinfo.getAfterUpdate() != null) {
            SysFieldDto sysFieldDto = new SysFieldDto();
            sysFieldDto.setId(approvalCustomerinfo.getFieldId());
            sysFieldDto.setName(approvalCustomerinfo.getAfterUpdate());
            sysFieldManager.update(sysFieldDto);
        }
        return result;
    }

    /**
     * 驳回客户信息审核
     * @param approvalCustomerinfo 客户信息审核对象
     * @return SysApprovalCustomerinfoDto
     */
    @Override
    public int reject(SysApprovalCustomerinfoDto approvalCustomerinfo) {
        SysApprovalCustomerinfoDto dto = baseManager.selectById(approvalCustomerinfo.getId());
        // 更新 sys_approval_customerinfo 表的 status 为 2, remark 为 驳回原因
        if (dto == null) {
            // 如果审核记录不存在，可能需要抛出异常或返回错误码
            throw new IllegalArgumentException("客户信息审核记录不存在，ID: " + approvalCustomerinfo.getId());
        }
        dto.setStatus("2");
        dto.setRemark(approvalCustomerinfo.getRemark());
        return baseManager.update(dto);
    }
}