package com.heju.system.file.domain.query;

import com.heju.system.file.domain.po.SysFileInfoPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 文件信息 数据查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysFileInfoQuery extends SysFileInfoPo {

    @Serial
    private static final long serialVersionUID = 1L;

    List<Long> fileIds;


    private Long[] roleIds;

}
