09:36:29.357 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:36:30.096 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0
09:36:30.163 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 36 ms to scan 1 urls, producing 3 keys and 6 values 
09:36:30.194 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 4 keys and 9 values 
09:36:30.202 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:36:30.212 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:36:30.225 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 7 values 
09:36:30.235 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
09:36:30.238 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:36:30.239 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002b6b23b42b8
09:36:30.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002b6b23b44d8
09:36:30.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:36:30.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:36:30.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:31.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:31.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:31.184 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:36:31.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:36:31.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002b6b24c2228
09:36:31.305 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:31.526 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:31.838 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:32.261 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:32.773 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:32.917 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:36:33.383 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:34.104 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:34.921 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:35.236 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:36:35.237 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:36:35.237 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:36:35.380 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:36:35.834 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:36.938 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:37.114 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:36:38.145 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:38.716 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c0ba2da6-358f-4bcf-869a-5fa9fc406954
09:36:38.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] RpcClient init label, labels = {module=naming, source=sdk}
09:36:38.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:36:38.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:36:38.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:36:38.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:38.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:38.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:38.750 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:36:38.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:36:38.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002b6b24c2228
09:36:38.879 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:39.078 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:36:39.098 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:39.362 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:39.410 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:39.826 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:40.089 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:36:40.089 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@12d28106[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:36:40.089 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ba2da6-358f-4bcf-869a-5fa9fc406954] Client is shutdown, stop reconnect to server
09:36:40.089 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6a3c1b56[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:36:40.093 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fc2e0936-fbea-4d64-b3f0-c23a744522ea
09:36:40.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc2e0936-fbea-4d64-b3f0-c23a744522ea] RpcClient init label, labels = {module=naming, source=sdk}
09:36:40.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc2e0936-fbea-4d64-b3f0-c23a744522ea] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:36:40.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc2e0936-fbea-4d64-b3f0-c23a744522ea] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:36:40.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc2e0936-fbea-4d64-b3f0-c23a744522ea] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:36:40.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc2e0936-fbea-4d64-b3f0-c23a744522ea] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:40.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc2e0936-fbea-4d64-b3f0-c23a744522ea] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:40.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc2e0936-fbea-4d64-b3f0-c23a744522ea] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:40.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc2e0936-fbea-4d64-b3f0-c23a744522ea] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:36:40.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc2e0936-fbea-4d64-b3f0-c23a744522ea] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002b6b24c2228
09:36:40.118 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc2e0936-fbea-4d64-b3f0-c23a744522ea] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:36:40.263 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc2e0936-fbea-4d64-b3f0-c23a744522ea] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:40.490 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc2e0936-fbea-4d64-b3f0-c23a744522ea] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:40.505 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
09:36:40.505 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:36:40.516 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
09:36:40.521 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
09:36:40.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d82d3f-6af1-494e-8832-79af7ccc2bd7_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:40.831 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc2e0936-fbea-4d64-b3f0-c23a744522ea] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:41.254 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc2e0936-fbea-4d64-b3f0-c23a744522ea] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:30.375 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:45:30.972 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a9fdf940-99df-4e34-b740-de3667d77b6b_config-0
09:45:31.032 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 3 keys and 6 values 
09:45:31.052 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
09:45:31.059 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
09:45:31.068 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:45:31.075 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
09:45:31.085 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:45:31.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:45:31.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000029b3d3c9ed0
09:45:31.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000029b3d3ca0f0
09:45:31.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:45:31.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:45:31.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:31.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:31.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:31.982 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:45:31.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:45:31.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000029b3d4d9b70
09:45:32.111 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:32.328 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:32.638 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:33.059 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:33.572 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:33.701 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:45:34.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:34.916 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:35.729 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:36.655 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:36.744 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:45:36.744 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:45:36.744 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:45:37.021 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:45:37.736 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:39.031 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:42.055 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:42.762 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:45:43.513 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:45.088 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:45.229 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 28e77591-776f-4230-803b-2ed363b6fdd6
09:45:45.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e77591-776f-4230-803b-2ed363b6fdd6] RpcClient init label, labels = {module=naming, source=sdk}
09:45:45.231 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e77591-776f-4230-803b-2ed363b6fdd6] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:45:45.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e77591-776f-4230-803b-2ed363b6fdd6] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:45:45.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e77591-776f-4230-803b-2ed363b6fdd6] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:45:45.235 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e77591-776f-4230-803b-2ed363b6fdd6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:45.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e77591-776f-4230-803b-2ed363b6fdd6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:45.269 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e77591-776f-4230-803b-2ed363b6fdd6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:45.280 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e77591-776f-4230-803b-2ed363b6fdd6] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:45:45.281 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e77591-776f-4230-803b-2ed363b6fdd6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:45:45.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e77591-776f-4230-803b-2ed363b6fdd6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000029b3d4d9b70
09:45:45.597 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e77591-776f-4230-803b-2ed363b6fdd6] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:45.628 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:45:45.868 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e77591-776f-4230-803b-2ed363b6fdd6] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:46.284 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e77591-776f-4230-803b-2ed363b6fdd6] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:46.611 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9fdf940-99df-4e34-b740-de3667d77b6b_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:46.615 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:45:46.616 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@51d6c42d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:45:46.616 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28e77591-776f-4230-803b-2ed363b6fdd6] Client is shutdown, stop reconnect to server
09:45:46.616 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@68423388[Running, pool size = 14, active threads = 0, queued tasks = 0, completed tasks = 14]
09:45:46.619 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b6ecdb23-6990-4c4e-890f-a0ab741cab6f
09:45:46.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6ecdb23-6990-4c4e-890f-a0ab741cab6f] RpcClient init label, labels = {module=naming, source=sdk}
09:45:46.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6ecdb23-6990-4c4e-890f-a0ab741cab6f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:45:46.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6ecdb23-6990-4c4e-890f-a0ab741cab6f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:45:46.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6ecdb23-6990-4c4e-890f-a0ab741cab6f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:45:46.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6ecdb23-6990-4c4e-890f-a0ab741cab6f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:46.627 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6ecdb23-6990-4c4e-890f-a0ab741cab6f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:46.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6ecdb23-6990-4c4e-890f-a0ab741cab6f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:46.640 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6ecdb23-6990-4c4e-890f-a0ab741cab6f] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:45:46.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6ecdb23-6990-4c4e-890f-a0ab741cab6f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:45:46.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6ecdb23-6990-4c4e-890f-a0ab741cab6f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000029b3d4d9b70
09:45:46.767 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6ecdb23-6990-4c4e-890f-a0ab741cab6f] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:47.070 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6ecdb23-6990-4c4e-890f-a0ab741cab6f] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:47.079 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
09:45:47.079 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:45:47.095 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
09:45:47.105 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
09:45:47.419 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6ecdb23-6990-4c4e-890f-a0ab741cab6f] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:47.839 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6ecdb23-6990-4c4e-890f-a0ab741cab6f] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:04.885 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:08:05.524 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 008f92b3-d73b-4816-9f79-9851ef011b77_config-0
10:08:05.596 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
10:08:05.617 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 4 keys and 9 values 
10:08:05.634 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
10:08:05.634 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
10:08:05.649 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
10:08:05.665 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
10:08:05.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:08:05.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002b9913caf18
10:08:05.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002b9913cb138
10:08:05.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:08:05.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:08:05.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:06.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:06.538 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:06.550 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:08:06.550 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:06.551 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002b9914daa98
10:08:06.670 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:06.881 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:07.192 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:07.609 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:08.127 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:08.233 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:08:08.739 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:09.452 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:10.276 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:10.681 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
10:08:10.681 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:08:10.681 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:08:10.834 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:08:11.191 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:12.300 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:12.668 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:08:13.489 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:14.564 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7dcdbf73-21d3-4c3f-9b55-e75f689ba49d
10:08:14.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] RpcClient init label, labels = {module=naming, source=sdk}
10:08:14.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:08:14.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:08:14.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:08:14.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:14.615 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:14.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:14.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:14.640 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:08:14.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002b9914daa98
10:08:14.705 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:14.780 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:14.961 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
10:08:14.997 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:15.320 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:15.735 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:15.938 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:08:15.938 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5c8d685e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:08:15.939 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7dcdbf73-21d3-4c3f-9b55-e75f689ba49d] Client is shutdown, stop reconnect to server
10:08:15.939 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4b1ad751[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
10:08:15.945 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fe41a983-147c-4c02-a7d7-8caf18795d00
10:08:15.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe41a983-147c-4c02-a7d7-8caf18795d00] RpcClient init label, labels = {module=naming, source=sdk}
10:08:15.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe41a983-147c-4c02-a7d7-8caf18795d00] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:08:15.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe41a983-147c-4c02-a7d7-8caf18795d00] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:08:15.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe41a983-147c-4c02-a7d7-8caf18795d00] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:08:15.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe41a983-147c-4c02-a7d7-8caf18795d00] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:15.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe41a983-147c-4c02-a7d7-8caf18795d00] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:15.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe41a983-147c-4c02-a7d7-8caf18795d00] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:16.017 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe41a983-147c-4c02-a7d7-8caf18795d00] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:08:16.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe41a983-147c-4c02-a7d7-8caf18795d00] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:16.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe41a983-147c-4c02-a7d7-8caf18795d00] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002b9914daa98
10:08:16.031 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [008f92b3-d73b-4816-9f79-9851ef011b77_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:16.145 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe41a983-147c-4c02-a7d7-8caf18795d00] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:16.369 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe41a983-147c-4c02-a7d7-8caf18795d00] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:16.395 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
10:08:16.395 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
10:08:16.403 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
10:08:16.406 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
10:08:16.698 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe41a983-147c-4c02-a7d7-8caf18795d00] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:17.107 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe41a983-147c-4c02-a7d7-8caf18795d00] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:54.848 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:08:55.743 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5371cd87-0b5d-40e7-8241-76138ca95120_config-0
10:08:55.820 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
10:08:55.858 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
10:08:55.871 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:08:55.885 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
10:08:55.899 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
10:08:55.913 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
10:08:55.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5371cd87-0b5d-40e7-8241-76138ca95120_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:08:55.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5371cd87-0b5d-40e7-8241-76138ca95120_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001b5dc39a328
10:08:55.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5371cd87-0b5d-40e7-8241-76138ca95120_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b5dc39a548
10:08:55.920 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5371cd87-0b5d-40e7-8241-76138ca95120_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:08:55.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5371cd87-0b5d-40e7-8241-76138ca95120_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:08:55.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5371cd87-0b5d-40e7-8241-76138ca95120_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:57.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5371cd87-0b5d-40e7-8241-76138ca95120_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751940537017_127.0.0.1_7905
10:08:57.256 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5371cd87-0b5d-40e7-8241-76138ca95120_config-0] Notify connected event to listeners.
10:08:57.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5371cd87-0b5d-40e7-8241-76138ca95120_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:57.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5371cd87-0b5d-40e7-8241-76138ca95120_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001b5dc514978
10:08:57.421 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:09:00.180 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
10:09:00.181 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:09:00.181 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:09:00.365 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:09:02.134 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:09:03.340 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b5ba54cd-c5aa-472c-a7f5-efea62c74582
10:09:03.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] RpcClient init label, labels = {module=naming, source=sdk}
10:09:03.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:09:03.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:09:03.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:09:03.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:09:03.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] Success to connect to server [localhost:8848] on start up, connectionId = 1751940543353_127.0.0.1_7999
10:09:03.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:09:03.467 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] Notify connected event to listeners.
10:09:03.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001b5dc514978
10:09:03.514 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
10:09:03.548 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
10:09:03.678 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 9.606 seconds (JVM running for 10.765)
10:09:03.691 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
10:09:03.691 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
10:09:03.694 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
10:09:04.062 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] Receive server push request, request = NotifySubscriberRequest, requestId = 2
10:09:04.078 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] Ack server push request, request = NotifySubscriberRequest, requestId = 2
10:09:04.124 [RMI TCP Connection(8)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:49:19.020 [nacos-grpc-client-executor-495] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] Receive server push request, request = NotifySubscriberRequest, requestId = 11
10:49:19.021 [nacos-grpc-client-executor-495] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] Ack server push request, request = NotifySubscriberRequest, requestId = 11
10:49:24.916 [nacos-grpc-client-executor-498] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] Receive server push request, request = NotifySubscriberRequest, requestId = 12
10:49:24.918 [nacos-grpc-client-executor-498] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5ba54cd-c5aa-472c-a7f5-efea62c74582] Ack server push request, request = NotifySubscriberRequest, requestId = 12
13:11:25.104 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:11:25.107 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:11:25.438 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:11:25.438 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6fab78a7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:11:25.438 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751940543353_127.0.0.1_7999
13:11:25.440 [nacos-grpc-client-executor-2203] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751940543353_127.0.0.1_7999]Ignore complete event,isRunning:false,isAbandon=false
13:11:25.444 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@10041093[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 2204]
15:52:34.567 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:52:35.334 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0
15:52:35.398 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
15:52:35.439 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 4 keys and 9 values 
15:52:35.449 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
15:52:35.461 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
15:52:35.472 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
15:52:35.482 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
15:52:35.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:52:35.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001ad813b38c8
15:52:35.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001ad813b3ae8
15:52:35.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:52:35.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:52:35.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:36.321 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:36.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:36.348 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:52:36.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:52:36.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001ad814c1110
15:52:36.459 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:36.679 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:37.001 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:37.487 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:38.058 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:38.085 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:52:38.712 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:39.435 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:40.239 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:41.154 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:41.356 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
15:52:41.357 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:52:41.357 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:52:41.543 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:52:42.266 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:43.465 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:43.718 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:52:44.814 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:46.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:46.402 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a5d2974d-b1ff-471b-b603-2be33f7dd34e
15:52:46.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] RpcClient init label, labels = {module=naming, source=sdk}
15:52:46.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:52:46.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:52:46.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:52:46.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:46.421 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:46.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:46.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:52:46.441 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:52:46.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001ad814c1110
15:52:46.566 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:46.780 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
15:52:46.786 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:47.095 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:47.519 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:47.611 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:47.777 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:52:47.777 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@29f86630[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:52:47.777 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6b8a9e1[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
15:52:47.777 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a5d2974d-b1ff-471b-b603-2be33f7dd34e] Client is shutdown, stop reconnect to server
15:52:47.781 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of db8fa6f7-9a22-4bcd-82e6-814514656c2f
15:52:47.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db8fa6f7-9a22-4bcd-82e6-814514656c2f] RpcClient init label, labels = {module=naming, source=sdk}
15:52:47.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db8fa6f7-9a22-4bcd-82e6-814514656c2f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:52:47.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db8fa6f7-9a22-4bcd-82e6-814514656c2f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:52:47.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db8fa6f7-9a22-4bcd-82e6-814514656c2f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:52:47.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db8fa6f7-9a22-4bcd-82e6-814514656c2f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:47.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db8fa6f7-9a22-4bcd-82e6-814514656c2f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:47.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db8fa6f7-9a22-4bcd-82e6-814514656c2f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:47.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db8fa6f7-9a22-4bcd-82e6-814514656c2f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:52:47.798 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db8fa6f7-9a22-4bcd-82e6-814514656c2f] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:52:47.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db8fa6f7-9a22-4bcd-82e6-814514656c2f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001ad814c1110
15:52:47.929 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db8fa6f7-9a22-4bcd-82e6-814514656c2f] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:48.162 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db8fa6f7-9a22-4bcd-82e6-814514656c2f] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:48.188 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
15:52:48.189 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
15:52:48.198 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
15:52:48.202 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
15:52:48.474 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db8fa6f7-9a22-4bcd-82e6-814514656c2f] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:48.894 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db8fa6f7-9a22-4bcd-82e6-814514656c2f] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:49.129 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6dab6541-102b-46ff-b9dc-c7aaa7a764c5_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:33.683 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:53:34.730 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1b8be36f-c9f6-4c4a-a8bb-633cfa5329af_config-0
15:53:34.874 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 84 ms to scan 1 urls, producing 3 keys and 6 values 
15:53:34.913 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
15:53:34.927 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
15:53:34.999 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 65 ms to scan 1 urls, producing 1 keys and 5 values 
15:53:35.026 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
15:53:35.049 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
15:53:35.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b8be36f-c9f6-4c4a-a8bb-633cfa5329af_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:53:35.057 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b8be36f-c9f6-4c4a-a8bb-633cfa5329af_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000024e1839b188
15:53:35.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b8be36f-c9f6-4c4a-a8bb-633cfa5329af_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000024e1839b3a8
15:53:35.059 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b8be36f-c9f6-4c4a-a8bb-633cfa5329af_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:53:35.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b8be36f-c9f6-4c4a-a8bb-633cfa5329af_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:53:35.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b8be36f-c9f6-4c4a-a8bb-633cfa5329af_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:36.318 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b8be36f-c9f6-4c4a-a8bb-633cfa5329af_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751961216085_127.0.0.1_6457
15:53:36.319 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b8be36f-c9f6-4c4a-a8bb-633cfa5329af_config-0] Notify connected event to listeners.
15:53:36.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b8be36f-c9f6-4c4a-a8bb-633cfa5329af_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:53:36.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b8be36f-c9f6-4c4a-a8bb-633cfa5329af_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000024e18514d90
15:53:36.519 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:53:39.203 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
15:53:39.204 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:53:39.204 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:53:39.354 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:53:40.904 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:53:42.202 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f2d95acd-0faf-4b98-aa98-2c7d6a676775
15:53:42.202 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] RpcClient init label, labels = {module=naming, source=sdk}
15:53:42.204 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:53:42.204 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:53:42.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:53:42.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:42.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] Success to connect to server [localhost:8848] on start up, connectionId = 1751961222216_127.0.0.1_6555
15:53:42.330 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] Notify connected event to listeners.
15:53:42.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:53:42.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000024e18514d90
15:53:42.382 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
15:53:42.411 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ***********:9200 register finished
15:53:42.585 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 9.679 seconds (JVM running for 10.879)
15:53:42.599 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
15:53:42.599 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
15:53:42.602 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
15:53:42.922 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] Receive server push request, request = NotifySubscriberRequest, requestId = 2
15:53:42.942 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] Ack server push request, request = NotifySubscriberRequest, requestId = 2
15:53:42.993 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:19:16.457 [nacos-grpc-client-executor-320] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] Receive server push request, request = NotifySubscriberRequest, requestId = 11
16:19:16.457 [nacos-grpc-client-executor-320] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] Ack server push request, request = NotifySubscriberRequest, requestId = 11
16:21:05.647 [nacos-grpc-client-executor-343] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] Receive server push request, request = NotifySubscriberRequest, requestId = 12
16:21:05.648 [nacos-grpc-client-executor-343] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d95acd-0faf-4b98-aa98-2c7d6a676775] Ack server push request, request = NotifySubscriberRequest, requestId = 12
17:52:45.286 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:52:45.296 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:52:45.619 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:52:45.619 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@69ef317a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:52:45.619 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751961222216_127.0.0.1_6555
17:52:45.626 [nacos-grpc-client-executor-1449] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751961222216_127.0.0.1_6555]Ignore complete event,isRunning:false,isAbandon=false
17:52:45.628 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@68c35019[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 1450]
