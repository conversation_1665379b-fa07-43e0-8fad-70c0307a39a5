package com.heju.system.authority.service;

import com.heju.system.authority.domain.query.SysDisplayInfoQuery;
import com.heju.system.authority.domain.dto.SysDisplayInfoDto;
import com.heju.common.web.entity.service.IBaseService;

/**
 * 显隐列管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysDisplayInfoService extends IBaseService<SysDisplayInfoQuery, SysDisplayInfoDto> {

    /**
     * 新增/修改 数据对象
     *
     * @param dto 数据对象
     * @return 结果
     */
    int addAndModify(SysDisplayInfoDto dto);
}
