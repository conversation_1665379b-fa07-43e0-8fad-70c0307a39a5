package com.heju.system.forms.sheet.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.domain.po.SysSheetPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:36+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysSheetConverterImpl implements SysSheetConverter {

    @Override
    public SysSheetDto mapperDto(SysSheetPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysSheetDto sysSheetDto = new SysSheetDto();

        sysSheetDto.setId( arg0.getId() );
        sysSheetDto.setSourceName( arg0.getSourceName() );
        sysSheetDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysSheetDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysSheetDto.setName( arg0.getName() );
        sysSheetDto.setStatus( arg0.getStatus() );
        sysSheetDto.setSort( arg0.getSort() );
        sysSheetDto.setCreateBy( arg0.getCreateBy() );
        sysSheetDto.setCreateTime( arg0.getCreateTime() );
        sysSheetDto.setUpdateBy( arg0.getUpdateBy() );
        sysSheetDto.setUpdateTime( arg0.getUpdateTime() );
        sysSheetDto.setDelFlag( arg0.getDelFlag() );
        sysSheetDto.setCreateName( arg0.getCreateName() );
        sysSheetDto.setUpdateName( arg0.getUpdateName() );
        sysSheetDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysSheetDto.setSheetType( arg0.getSheetType() );
        sysSheetDto.setApiName( arg0.getApiName() );
        sysSheetDto.setRemark( arg0.getRemark() );
        sysSheetDto.setIsOption( arg0.getIsOption() );

        return sysSheetDto;
    }

    @Override
    public List<SysSheetDto> mapperDto(Collection<SysSheetPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysSheetDto> list = new ArrayList<SysSheetDto>( arg0.size() );
        for ( SysSheetPo sysSheetPo : arg0 ) {
            list.add( mapperDto( sysSheetPo ) );
        }

        return list;
    }

    @Override
    public Page<SysSheetDto> mapperPageDto(Collection<SysSheetPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysSheetDto> page = new Page<SysSheetDto>();
        for ( SysSheetPo sysSheetPo : arg0 ) {
            page.add( mapperDto( sysSheetPo ) );
        }

        return page;
    }

    @Override
    public SysSheetPo mapperPo(SysSheetDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysSheetPo sysSheetPo = new SysSheetPo();

        sysSheetPo.setId( arg0.getId() );
        sysSheetPo.setSourceName( arg0.getSourceName() );
        sysSheetPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysSheetPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysSheetPo.setName( arg0.getName() );
        sysSheetPo.setStatus( arg0.getStatus() );
        sysSheetPo.setSort( arg0.getSort() );
        sysSheetPo.setCreateBy( arg0.getCreateBy() );
        sysSheetPo.setCreateTime( arg0.getCreateTime() );
        sysSheetPo.setUpdateBy( arg0.getUpdateBy() );
        sysSheetPo.setUpdateTime( arg0.getUpdateTime() );
        sysSheetPo.setDelFlag( arg0.getDelFlag() );
        sysSheetPo.setCreateName( arg0.getCreateName() );
        sysSheetPo.setUpdateName( arg0.getUpdateName() );
        sysSheetPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysSheetPo.setSheetType( arg0.getSheetType() );
        sysSheetPo.setApiName( arg0.getApiName() );
        sysSheetPo.setRemark( arg0.getRemark() );
        sysSheetPo.setIsOption( arg0.getIsOption() );

        return sysSheetPo;
    }

    @Override
    public List<SysSheetPo> mapperPo(Collection<SysSheetDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysSheetPo> list = new ArrayList<SysSheetPo>( arg0.size() );
        for ( SysSheetDto sysSheetDto : arg0 ) {
            list.add( mapperPo( sysSheetDto ) );
        }

        return list;
    }

    @Override
    public Page<SysSheetPo> mapperPagePo(Collection<SysSheetDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysSheetPo> page = new Page<SysSheetPo>();
        for ( SysSheetDto sysSheetDto : arg0 ) {
            page.add( mapperPo( sysSheetDto ) );
        }

        return page;
    }
}
