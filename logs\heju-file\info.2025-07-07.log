09:20:36.462 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:20:36.921 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0bf7060f-be63-47d4-ad09-f629e140e407_config-0
09:20:36.980 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 24 ms to scan 1 urls, producing 3 keys and 6 values 
09:20:37.001 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 4 keys and 9 values 
09:20:37.008 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 3 keys and 10 values 
09:20:37.015 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 5 ms to scan 1 urls, producing 1 keys and 5 values 
09:20:37.023 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 7 values 
09:20:37.034 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:20:37.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf7060f-be63-47d4-ad09-f629e140e407_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:20:37.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf7060f-be63-47d4-ad09-f629e140e407_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002ec50396b40
09:20:37.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf7060f-be63-47d4-ad09-f629e140e407_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002ec50396d60
09:20:37.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf7060f-be63-47d4-ad09-f629e140e407_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:20:37.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf7060f-be63-47d4-ad09-f629e140e407_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:20:37.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf7060f-be63-47d4-ad09-f629e140e407_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:20:37.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf7060f-be63-47d4-ad09-f629e140e407_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751851237737_127.0.0.1_10305
09:20:37.994 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf7060f-be63-47d4-ad09-f629e140e407_config-0] Notify connected event to listeners.
09:20:37.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf7060f-be63-47d4-ad09-f629e140e407_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:20:37.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0bf7060f-be63-47d4-ad09-f629e140e407_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002ec50510ad8
09:20:38.099 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:20:40.560 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:20:40.560 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:20:40.560 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:20:40.722 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:20:42.199 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:20:44.455 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 98971fce-b342-484f-b88d-308fdcae09f6
09:20:44.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98971fce-b342-484f-b88d-308fdcae09f6] RpcClient init label, labels = {module=naming, source=sdk}
09:20:44.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98971fce-b342-484f-b88d-308fdcae09f6] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:20:44.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98971fce-b342-484f-b88d-308fdcae09f6] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:20:44.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98971fce-b342-484f-b88d-308fdcae09f6] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:20:44.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98971fce-b342-484f-b88d-308fdcae09f6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:20:44.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98971fce-b342-484f-b88d-308fdcae09f6] Success to connect to server [localhost:8848] on start up, connectionId = 1751851244465_127.0.0.1_10368
09:20:44.581 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98971fce-b342-484f-b88d-308fdcae09f6] Notify connected event to listeners.
09:20:44.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98971fce-b342-484f-b88d-308fdcae09f6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:20:44.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98971fce-b342-484f-b88d-308fdcae09f6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002ec50510ad8
09:20:44.631 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:20:44.657 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:20:44.785 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 8.935 seconds (JVM running for 10.255)
09:20:44.803 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:20:44.804 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:20:44.807 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:20:45.027 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:20:45.153 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98971fce-b342-484f-b88d-308fdcae09f6] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:20:45.171 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [98971fce-b342-484f-b88d-308fdcae09f6] Ack server push request, request = NotifySubscriberRequest, requestId = 3
13:30:04.277 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:30:04.291 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:30:04.629 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:30:04.629 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@75c9ed09[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:30:04.634 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751851244465_127.0.0.1_10368
13:30:04.638 [nacos-grpc-client-executor-3005] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751851244465_127.0.0.1_10368]Ignore complete event,isRunning:false,isAbandon=false
13:30:04.638 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@774f3387[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3006]
15:21:41.339 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:21:43.485 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 89f56b50-1bca-4dcd-86f4-fc0b99cd0b93_config-0
15:21:43.704 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 108 ms to scan 1 urls, producing 3 keys and 6 values 
15:21:43.792 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 4 keys and 9 values 
15:21:43.832 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 10 values 
15:21:43.866 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 1 keys and 5 values 
15:21:43.902 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 1 keys and 7 values 
15:21:43.953 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 2 keys and 8 values 
15:21:43.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89f56b50-1bca-4dcd-86f4-fc0b99cd0b93_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:21:43.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89f56b50-1bca-4dcd-86f4-fc0b99cd0b93_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002e2813af918
15:21:43.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89f56b50-1bca-4dcd-86f4-fc0b99cd0b93_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002e2813afb38
15:21:43.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89f56b50-1bca-4dcd-86f4-fc0b99cd0b93_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:21:43.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89f56b50-1bca-4dcd-86f4-fc0b99cd0b93_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:21:43.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89f56b50-1bca-4dcd-86f4-fc0b99cd0b93_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:21:46.615 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89f56b50-1bca-4dcd-86f4-fc0b99cd0b93_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751872906215_127.0.0.1_11960
15:21:46.617 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89f56b50-1bca-4dcd-86f4-fc0b99cd0b93_config-0] Notify connected event to listeners.
15:21:46.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89f56b50-1bca-4dcd-86f4-fc0b99cd0b93_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:46.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89f56b50-1bca-4dcd-86f4-fc0b99cd0b93_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002e2814e9a90
15:21:46.912 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:21:53.149 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
15:21:53.151 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:21:53.151 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:21:53.932 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:21:58.288 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:22:06.048 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a940f75c-03e0-4072-ad73-4cf71049745b
15:22:06.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a940f75c-03e0-4072-ad73-4cf71049745b] RpcClient init label, labels = {module=naming, source=sdk}
15:22:06.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a940f75c-03e0-4072-ad73-4cf71049745b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:22:06.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a940f75c-03e0-4072-ad73-4cf71049745b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:22:06.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a940f75c-03e0-4072-ad73-4cf71049745b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:22:06.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a940f75c-03e0-4072-ad73-4cf71049745b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:22:06.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a940f75c-03e0-4072-ad73-4cf71049745b] Success to connect to server [localhost:8848] on start up, connectionId = 1751872926070_127.0.0.1_12085
15:22:06.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a940f75c-03e0-4072-ad73-4cf71049745b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:22:06.197 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a940f75c-03e0-4072-ad73-4cf71049745b] Notify connected event to listeners.
15:22:06.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a940f75c-03e0-4072-ad73-4cf71049745b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002e2814e9a90
15:22:06.282 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
15:22:06.348 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
15:22:06.782 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a940f75c-03e0-4072-ad73-4cf71049745b] Receive server push request, request = NotifySubscriberRequest, requestId = 19
15:22:06.807 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a940f75c-03e0-4072-ad73-4cf71049745b] Ack server push request, request = NotifySubscriberRequest, requestId = 19
15:22:06.848 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 27.842 seconds (JVM running for 32.003)
15:22:06.877 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
15:22:06.879 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
15:22:06.882 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
15:43:55.684 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:43:55.684 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:43:56.015 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:43:56.015 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@62203b17[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:43:56.015 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751872926070_127.0.0.1_12085
15:43:56.015 [nacos-grpc-client-executor-275] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751872926070_127.0.0.1_12085]Ignore complete event,isRunning:false,isAbandon=false
15:43:56.019 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@f04ff11[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 276]
15:46:01.449 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:46:06.592 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0
15:46:06.948 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 201 ms to scan 1 urls, producing 3 keys and 6 values 
15:46:07.096 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 4 keys and 9 values 
15:46:07.143 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 10 values 
15:46:07.200 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 1 keys and 5 values 
15:46:07.260 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 1 keys and 7 values 
15:46:07.314 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 2 keys and 8 values 
15:46:07.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:46:07.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001acca3fd7c8
15:46:07.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001acca3fd9e8
15:46:07.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:46:07.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:46:07.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:46:22.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:46:25.270 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751874383815_127.0.0.1_3920
15:46:25.276 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Notify connected event to listeners.
15:46:25.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:46:25.283 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001acca539ca0
15:46:26.567 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:47:09.122 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
15:47:09.130 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:47:09.136 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:47:11.758 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:47:47.668 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:48:12.844 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Server healthy check fail, currentConnection = 1751874383815_127.0.0.1_3920
15:48:12.846 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:48:16.875 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Success to connect a server [localhost:8848], connectionId = 1751874492958_127.0.0.1_4580
15:48:16.875 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751874383815_127.0.0.1_3920
15:48:16.875 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751874383815_127.0.0.1_3920
15:48:16.876 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Notify disconnected event to listeners
15:48:16.877 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aafbd8fb-9c4b-4dd8-8f36-91bdbb121b9d_config-0] Notify connected event to listeners.
15:48:19.623 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of de305873-be4e-486a-a25d-956f52e4e036
15:48:19.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de305873-be4e-486a-a25d-956f52e4e036] RpcClient init label, labels = {module=naming, source=sdk}
15:48:19.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de305873-be4e-486a-a25d-956f52e4e036] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:48:19.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de305873-be4e-486a-a25d-956f52e4e036] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:48:19.627 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de305873-be4e-486a-a25d-956f52e4e036] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:48:19.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de305873-be4e-486a-a25d-956f52e4e036] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:48:19.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de305873-be4e-486a-a25d-956f52e4e036] Success to connect to server [localhost:8848] on start up, connectionId = 1751874499644_127.0.0.1_4642
15:48:19.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de305873-be4e-486a-a25d-956f52e4e036] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:48:19.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de305873-be4e-486a-a25d-956f52e4e036] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001acca539ca0
15:48:19.763 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de305873-be4e-486a-a25d-956f52e4e036] Notify connected event to listeners.
15:48:19.817 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
15:48:19.863 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
15:48:20.262 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 143.288 seconds (JVM running for 149.436)
15:48:21.532 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
15:48:21.533 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
15:48:21.550 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de305873-be4e-486a-a25d-956f52e4e036] Receive server push request, request = NotifySubscriberRequest, requestId = 34
15:48:21.551 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de305873-be4e-486a-a25d-956f52e4e036] Ack server push request, request = NotifySubscriberRequest, requestId = 34
15:48:21.559 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
19:02:19.716 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:02:19.724 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:02:20.070 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:02:20.071 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@630e5c39[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:02:20.071 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751874499644_127.0.0.1_4642
19:02:20.074 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7cdfaf4a[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 2332]
19:02:20.079 [nacos-grpc-client-executor-2332] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751874499644_127.0.0.1_4642]Ignore complete event,isRunning:false,isAbandon=false
