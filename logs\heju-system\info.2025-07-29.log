09:13:53.566 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:13:57.981 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f73260ce-aae0-4efb-a2fd-6de8a1c0ba6f_config-0
09:13:58.314 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 138 ms to scan 1 urls, producing 3 keys and 6 values 
09:13:58.510 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 38 ms to scan 1 urls, producing 4 keys and 9 values 
09:13:58.548 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 32 ms to scan 1 urls, producing 3 keys and 10 values 
09:13:58.585 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 26 ms to scan 1 urls, producing 1 keys and 5 values 
09:13:58.622 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 29 ms to scan 1 urls, producing 1 keys and 7 values 
09:13:58.669 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 2 keys and 8 values 
09:13:58.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f73260ce-aae0-4efb-a2fd-6de8a1c0ba6f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:13:58.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f73260ce-aae0-4efb-a2fd-6de8a1c0ba6f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000017a2b39e480
09:13:58.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f73260ce-aae0-4efb-a2fd-6de8a1c0ba6f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000017a2b39e6a0
09:13:58.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f73260ce-aae0-4efb-a2fd-6de8a1c0ba6f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:13:58.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f73260ce-aae0-4efb-a2fd-6de8a1c0ba6f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:13:58.717 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f73260ce-aae0-4efb-a2fd-6de8a1c0ba6f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:02.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f73260ce-aae0-4efb-a2fd-6de8a1c0ba6f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753751641657_127.0.0.1_10549
09:14:02.131 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f73260ce-aae0-4efb-a2fd-6de8a1c0ba6f_config-0] Notify connected event to listeners.
09:14:02.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f73260ce-aae0-4efb-a2fd-6de8a1c0ba6f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:02.136 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f73260ce-aae0-4efb-a2fd-6de8a1c0ba6f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017a2b51ab60
09:14:02.582 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:14:12.315 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:14:12.315 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:14:12.316 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:14:12.604 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:14:14.028 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:14:14.029 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:14:14.030 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:14:26.298 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:14:30.489 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 21bd28ad-4bac-4f5c-97d3-798e978aaace
09:14:30.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21bd28ad-4bac-4f5c-97d3-798e978aaace] RpcClient init label, labels = {module=naming, source=sdk}
09:14:30.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21bd28ad-4bac-4f5c-97d3-798e978aaace] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:14:30.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21bd28ad-4bac-4f5c-97d3-798e978aaace] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:14:30.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21bd28ad-4bac-4f5c-97d3-798e978aaace] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:14:30.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21bd28ad-4bac-4f5c-97d3-798e978aaace] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:30.619 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21bd28ad-4bac-4f5c-97d3-798e978aaace] Success to connect to server [localhost:8848] on start up, connectionId = 1753751670500_127.0.0.1_10857
09:14:30.620 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21bd28ad-4bac-4f5c-97d3-798e978aaace] Notify connected event to listeners.
09:14:30.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21bd28ad-4bac-4f5c-97d3-798e978aaace] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:30.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21bd28ad-4bac-4f5c-97d3-798e978aaace] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017a2b51ab60
09:14:30.679 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:14:30.718 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:14:30.897 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 40.308 seconds (JVM running for 42.694)
09:14:30.915 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:14:30.916 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:14:30.917 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:14:31.055 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:14:31.412 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21bd28ad-4bac-4f5c-97d3-798e978aaace] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:14:31.452 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21bd28ad-4bac-4f5c-97d3-798e978aaace] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:15:09.161 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21bd28ad-4bac-4f5c-97d3-798e978aaace] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:15:09.162 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [21bd28ad-4bac-4f5c-97d3-798e978aaace] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:15:09.955 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:15:09.956 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:15:10.151 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:15:10.152 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
10:38:33.007 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:38:33.007 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:38:33.357 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:38:33.358 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@701ed906[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:38:33.358 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753751670500_127.0.0.1_10857
10:38:33.360 [nacos-grpc-client-executor-1026] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753751670500_127.0.0.1_10857]Ignore complete event,isRunning:false,isAbandon=false
10:38:33.371 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@69cbc23f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1027]
10:38:33.540 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:38:33.559 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:38:33.565 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:38:33.565 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:38:33.565 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:38:33.565 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:38:33.565 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:38:33.567 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:38:39.068 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:38:40.068 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 568ad456-cf0f-4eac-8798-89ad35c2451d_config-0
10:38:40.159 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
10:38:40.206 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
10:38:40.219 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
10:38:40.221 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
10:38:40.235 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
10:38:40.256 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 2 keys and 8 values 
10:38:40.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:38:40.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f88139e230
10:38:40.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001f88139e450
10:38:40.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:38:40.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:38:40.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:38:41.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753756721171_127.0.0.1_9560
10:38:41.419 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] Notify connected event to listeners.
10:38:41.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:38:41.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f881518228
10:38:41.589 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:38:48.159 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:38:48.161 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:38:48.161 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:38:48.940 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:38:52.086 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:38:52.087 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:38:52.088 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:39:01.159 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:39:04.929 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2af66dd3-5ceb-4a53-847d-72af28e1ddf0
10:39:04.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] RpcClient init label, labels = {module=naming, source=sdk}
10:39:04.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:39:04.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:39:04.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:39:04.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:39:05.065 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Success to connect to server [localhost:8848] on start up, connectionId = 1753756744940_127.0.0.1_9801
10:39:05.066 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Notify connected event to listeners.
10:39:05.066 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:39:05.066 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f881518228
10:39:05.120 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:39:05.152 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:39:05.325 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 27.006 seconds (JVM running for 27.999)
10:39:05.337 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:39:05.337 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:39:05.337 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:39:05.603 [RMI TCP Connection(11)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:39:05.647 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Receive server push request, request = NotifySubscriberRequest, requestId = 18
10:39:05.663 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Ack server push request, request = NotifySubscriberRequest, requestId = 18
10:39:48.785 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:39:48.785 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:46:17.626 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] Server healthy check fail, currentConnection = 1753756721171_127.0.0.1_9560
10:46:17.626 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Server healthy check fail, currentConnection = 1753756744940_127.0.0.1_9801
10:46:17.627 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:46:17.627 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:46:17.749 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] Success to connect a server [localhost:8848], connectionId = 1753757177635_127.0.0.1_10856
10:46:17.749 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Success to connect a server [localhost:8848], connectionId = 1753757177635_127.0.0.1_10855
10:46:17.749 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Abandon prev connection, server is localhost:8848, connectionId is 1753756744940_127.0.0.1_9801
10:46:17.749 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1753756721171_127.0.0.1_9560
10:46:17.749 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753756744940_127.0.0.1_9801
10:46:17.749 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753756721171_127.0.0.1_9560
10:46:17.752 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753756721171_127.0.0.1_9560]Ignore complete event,isRunning:false,isAbandon=true
10:46:17.752 [nacos-grpc-client-executor-65] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753756744940_127.0.0.1_9801]Ignore complete event,isRunning:false,isAbandon=true
10:46:17.752 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Notify disconnected event to listeners
10:46:17.754 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Notify connected event to listeners.
10:46:17.754 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] Notify disconnected event to listeners
10:46:17.755 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [568ad456-cf0f-4eac-8798-89ad35c2451d_config-0] Notify connected event to listeners.
10:46:17.755 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Server check success, currentServer is localhost:8848 
10:49:54.894 [nacos-grpc-client-executor-70] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Receive server push request, request = NotifySubscriberRequest, requestId = 25
10:49:54.910 [nacos-grpc-client-executor-70] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2af66dd3-5ceb-4a53-847d-72af28e1ddf0] Ack server push request, request = NotifySubscriberRequest, requestId = 25
11:08:49.460 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:08:49.464 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:08:49.790 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:08:49.791 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@529e320b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:08:49.791 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753757177635_127.0.0.1_10855
11:08:49.792 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1b22dca8[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 246]
11:08:49.954 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:08:49.956 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:08:49.967 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:08:49.968 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:08:49.968 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:08:49.968 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:08:55.187 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:08:55.737 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dfd20f06-a565-4549-ac04-582e7685f6a0_config-0
11:08:55.782 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
11:08:55.807 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
11:08:55.812 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
11:08:55.818 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
11:08:55.825 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
11:08:55.833 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
11:08:55.835 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfd20f06-a565-4549-ac04-582e7685f6a0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:08:55.835 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfd20f06-a565-4549-ac04-582e7685f6a0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000024f813be480
11:08:55.835 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfd20f06-a565-4549-ac04-582e7685f6a0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000024f813be6a0
11:08:55.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfd20f06-a565-4549-ac04-582e7685f6a0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:08:55.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfd20f06-a565-4549-ac04-582e7685f6a0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:08:55.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfd20f06-a565-4549-ac04-582e7685f6a0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:08:56.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfd20f06-a565-4549-ac04-582e7685f6a0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753758536345_127.0.0.1_1593
11:08:56.528 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfd20f06-a565-4549-ac04-582e7685f6a0_config-0] Notify connected event to listeners.
11:08:56.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfd20f06-a565-4549-ac04-582e7685f6a0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:08:56.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfd20f06-a565-4549-ac04-582e7685f6a0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024f814f8228
11:08:56.612 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:08:59.895 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:08:59.896 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:08:59.896 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:09:00.034 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:09:01.051 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:09:01.052 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:09:01.052 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:09:09.110 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:09:11.207 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5be6d781-2b28-4bd0-b9f5-cb5ae278088f
11:09:11.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5be6d781-2b28-4bd0-b9f5-cb5ae278088f] RpcClient init label, labels = {module=naming, source=sdk}
11:09:11.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5be6d781-2b28-4bd0-b9f5-cb5ae278088f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:09:11.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5be6d781-2b28-4bd0-b9f5-cb5ae278088f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:09:11.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5be6d781-2b28-4bd0-b9f5-cb5ae278088f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:09:11.210 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5be6d781-2b28-4bd0-b9f5-cb5ae278088f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:09:11.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5be6d781-2b28-4bd0-b9f5-cb5ae278088f] Success to connect to server [localhost:8848] on start up, connectionId = 1753758551217_127.0.0.1_1777
11:09:11.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5be6d781-2b28-4bd0-b9f5-cb5ae278088f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:09:11.337 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5be6d781-2b28-4bd0-b9f5-cb5ae278088f] Notify connected event to listeners.
11:09:11.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5be6d781-2b28-4bd0-b9f5-cb5ae278088f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024f814f8228
11:09:11.382 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:09:11.409 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:09:11.502 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.797 seconds (JVM running for 17.605)
11:09:11.515 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:09:11.516 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:09:11.516 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:09:11.607 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:09:11.890 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5be6d781-2b28-4bd0-b9f5-cb5ae278088f] Receive server push request, request = NotifySubscriberRequest, requestId = 32
11:09:11.908 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5be6d781-2b28-4bd0-b9f5-cb5ae278088f] Ack server push request, request = NotifySubscriberRequest, requestId = 32
11:09:16.128 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:09:16.128 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:41:03.911 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:41:03.914 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:41:04.256 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:41:04.256 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@60e28bfe[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:41:04.256 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753758551217_127.0.0.1_1777
11:41:04.260 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@46cc4bd8[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 394]
11:41:04.261 [nacos-grpc-client-executor-394] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753758551217_127.0.0.1_1777]Ignore complete event,isRunning:false,isAbandon=false
11:41:04.442 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:41:04.444 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:41:04.450 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:41:04.450 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:41:04.451 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:41:04.451 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:45:07.382 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:45:08.026 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 498b788f-e70d-4d83-bc44-29067c00d87b_config-0
11:45:08.081 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
11:45:08.113 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
11:45:08.122 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:45:08.133 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
11:45:08.141 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
11:45:08.154 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
11:45:08.156 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:45:08.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002371a39eaf8
11:45:08.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002371a39ed18
11:45:08.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:45:08.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:45:08.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:45:09.063 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753760708864_127.0.0.1_7317
11:45:09.063 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] Notify connected event to listeners.
11:45:09.063 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:45:09.065 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002371a518ad8
11:45:09.153 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:45:12.497 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:45:12.497 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:45:12.497 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:45:12.627 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:45:13.300 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:45:13.301 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:45:13.301 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:45:19.993 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:45:22.937 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e0cea274-3000-47ce-a53f-00f84f370dd1
11:45:22.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] RpcClient init label, labels = {module=naming, source=sdk}
11:45:22.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:45:22.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:45:22.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:45:22.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:45:23.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Success to connect to server [localhost:8848] on start up, connectionId = 1753760722950_127.0.0.1_7357
11:45:23.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:45:23.061 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Notify connected event to listeners.
11:45:23.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002371a518ad8
11:45:23.103 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:45:23.126 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:45:23.235 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.44 seconds (JVM running for 17.687)
11:45:23.248 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:45:23.249 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:45:23.249 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:45:23.548 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:45:23.604 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Receive server push request, request = NotifySubscriberRequest, requestId = 40
11:45:23.619 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Ack server push request, request = NotifySubscriberRequest, requestId = 40
11:45:31.884 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:45:31.885 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:09:04.123 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] Server healthy check fail, currentConnection = 1753760708864_127.0.0.1_7317
13:09:04.123 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Server healthy check fail, currentConnection = 1753760722950_127.0.0.1_7357
13:09:04.124 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:09:04.124 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:09:21.092 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] Success to connect a server [localhost:8848], connectionId = 1753765759593_127.0.0.1_5959
13:09:21.094 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1753760708864_127.0.0.1_7317
13:09:21.095 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753760708864_127.0.0.1_7317
13:09:21.098 [nacos-grpc-client-executor-449] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753760708864_127.0.0.1_7317]Ignore complete event,isRunning:false,isAbandon=true
13:09:21.100 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] Notify disconnected event to listeners
13:09:21.101 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [498b788f-e70d-4d83-bc44-29067c00d87b_config-0] Notify connected event to listeners.
13:14:53.826 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
13:15:29.267 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Success to connect a server [localhost:8848], connectionId = 1753766094038_127.0.0.1_7240
13:15:29.267 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Abandon prev connection, server is localhost:8848, connectionId is 1753760722950_127.0.0.1_7357
13:15:29.267 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753760722950_127.0.0.1_7357
13:15:29.274 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Notify disconnected event to listeners
13:15:29.274 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Server check success, currentServer is localhost:8848 
13:15:29.277 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Notify connected event to listeners.
13:15:32.880 [nacos-grpc-client-executor-452] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Receive server push request, request = NotifySubscriberRequest, requestId = 46
13:15:32.880 [nacos-grpc-client-executor-452] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0cea274-3000-47ce-a53f-00f84f370dd1] Ack server push request, request = NotifySubscriberRequest, requestId = 46
13:46:05.741 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:46:05.744 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:46:06.077 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:46:06.077 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2f57fc66[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:46:06.078 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753766094038_127.0.0.1_7240
13:46:06.078 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@36a8a3b9[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 618]
13:46:06.252 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:46:06.255 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:46:06.261 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:46:06.261 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:46:06.262 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:46:06.262 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:46:14.862 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:46:15.485 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0
13:46:15.533 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 6 values 
13:46:15.561 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
13:46:15.571 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
13:46:15.578 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
13:46:15.586 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
13:46:15.595 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
13:46:15.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:46:15.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001cf9d39ed38
13:46:15.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001cf9d39ef58
13:46:15.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:46:15.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:46:15.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:46:16.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753767976182_127.0.0.1_13733
13:46:16.376 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] Notify connected event to listeners.
13:46:16.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:46:16.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001cf9d518668
13:46:16.518 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:46:19.267 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:46:19.267 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:46:19.268 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:46:19.415 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:46:20.030 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:46:20.031 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:46:20.031 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:46:26.228 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:46:28.839 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-cab5-43d3-9a93-e3469beedfa9
13:46:28.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] RpcClient init label, labels = {module=naming, source=sdk}
13:46:28.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:46:28.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:46:28.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:46:28.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:46:28.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Success to connect to server [localhost:8848] on start up, connectionId = 1753767988850_127.0.0.1_13841
13:46:28.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:46:28.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001cf9d518668
13:46:28.965 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Notify connected event to listeners.
13:46:29.014 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:46:29.037 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:46:29.148 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 14.781 seconds (JVM running for 15.927)
13:46:29.161 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:46:29.161 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:46:29.162 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:46:29.438 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:46:29.568 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Receive server push request, request = NotifySubscriberRequest, requestId = 49
13:46:29.581 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Ack server push request, request = NotifySubscriberRequest, requestId = 49
13:46:41.815 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:46:41.815 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:46:52.105 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Server healthy check fail, currentConnection = 1753767988850_127.0.0.1_13841
13:46:52.105 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] Server healthy check fail, currentConnection = 1753767976182_127.0.0.1_13733
13:46:52.106 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:46:52.106 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:46:52.225 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] Success to connect a server [localhost:8848], connectionId = 1753768012115_127.0.0.1_14001
13:46:52.225 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Success to connect a server [localhost:8848], connectionId = 1753768012115_127.0.0.1_14000
13:46:52.225 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1753767976182_127.0.0.1_13733
13:46:52.225 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Abandon prev connection, server is localhost:8848, connectionId is 1753767988850_127.0.0.1_13841
13:46:52.225 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753767976182_127.0.0.1_13733
13:46:52.225 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753767988850_127.0.0.1_13841
13:46:52.227 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753767988850_127.0.0.1_13841]Ignore complete event,isRunning:false,isAbandon=true
13:46:52.227 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753767976182_127.0.0.1_13733]Ignore complete event,isRunning:false,isAbandon=true
13:46:52.230 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Notify disconnected event to listeners
13:46:52.230 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] Notify disconnected event to listeners
13:46:52.231 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb2af259-4f7d-4aaf-a11a-6d60b19b601c_config-0] Notify connected event to listeners.
13:46:52.232 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Notify connected event to listeners.
13:47:13.042 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Receive server push request, request = NotifySubscriberRequest, requestId = 53
13:47:13.044 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Ack server push request, request = NotifySubscriberRequest, requestId = 53
13:47:13.047 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Receive server push request, request = NotifySubscriberRequest, requestId = 54
13:47:13.047 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-cab5-43d3-9a93-e3469beedfa9] Ack server push request, request = NotifySubscriberRequest, requestId = 54
13:47:23.972 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:47:23.975 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:47:24.313 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:47:24.314 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7d5b6b70[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:47:24.314 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753768012115_127.0.0.1_14000
13:47:24.315 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 25]
13:47:24.460 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:47:24.464 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:47:24.470 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:47:24.470 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:47:24.471 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:47:24.471 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:47:29.402 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:47:29.962 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f9ee36e6-fae7-47de-b92e-a102df74438b_config-0
13:47:30.015 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 6 values 
13:47:30.046 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
13:47:30.054 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
13:47:30.061 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 3 ms to scan 1 urls, producing 1 keys and 5 values 
13:47:30.067 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
13:47:30.074 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
13:47:30.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:47:30.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000299823cdd00
13:47:30.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000299823cdf20
13:47:30.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:47:30.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:47:30.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:47:30.874 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753768050683_127.0.0.1_14169
13:47:30.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:47:30.875 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Notify connected event to listeners.
13:47:30.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000029982507b88
13:47:30.949 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:47:33.576 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:47:33.576 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:47:33.576 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:47:33.715 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:47:34.535 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:47:34.535 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:47:34.535 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:47:44.423 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:47:49.093 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 397e6d18-d70c-4696-85f4-be6cfa72021f
13:47:49.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [397e6d18-d70c-4696-85f4-be6cfa72021f] RpcClient init label, labels = {module=naming, source=sdk}
13:47:49.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [397e6d18-d70c-4696-85f4-be6cfa72021f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:47:49.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [397e6d18-d70c-4696-85f4-be6cfa72021f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:47:49.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [397e6d18-d70c-4696-85f4-be6cfa72021f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:47:49.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [397e6d18-d70c-4696-85f4-be6cfa72021f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:47:49.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [397e6d18-d70c-4696-85f4-be6cfa72021f] Success to connect to server [localhost:8848] on start up, connectionId = 1753768069119_127.0.0.1_14284
13:47:49.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [397e6d18-d70c-4696-85f4-be6cfa72021f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:47:49.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [397e6d18-d70c-4696-85f4-be6cfa72021f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000029982507b88
13:47:49.241 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [397e6d18-d70c-4696-85f4-be6cfa72021f] Notify connected event to listeners.
13:47:49.292 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:47:49.325 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:47:49.464 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.536 seconds (JVM running for 21.392)
13:47:49.479 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:47:49.480 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:47:49.480 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:47:49.803 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [397e6d18-d70c-4696-85f4-be6cfa72021f] Receive server push request, request = NotifySubscriberRequest, requestId = 57
13:47:49.818 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [397e6d18-d70c-4696-85f4-be6cfa72021f] Ack server push request, request = NotifySubscriberRequest, requestId = 57
13:47:49.859 [RMI TCP Connection(11)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:49:16.266 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:49:16.266 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:50:07.389 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Server healthy check fail, currentConnection = 1753768050683_127.0.0.1_14169
13:50:08.477 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:50:21.475 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
13:50:22.194 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Success to connect a server [localhost:8848], connectionId = 1753768221698_127.0.0.1_14714
13:50:22.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1753768050683_127.0.0.1_14169
13:50:22.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753768050683_127.0.0.1_14169
13:50:22.196 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Notify disconnected event to listeners
13:50:22.198 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9ee36e6-fae7-47de-b92e-a102df74438b_config-0] Notify connected event to listeners.
13:58:07.356 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:58:07.361 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:58:07.697 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:58:07.698 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3a814e4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:58:07.698 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753768069119_127.0.0.1_14284
13:58:07.698 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@727ab9d2[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 125]
13:58:07.830 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:58:07.830 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:58:07.846 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:58:07.846 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:58:07.847 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:58:07.847 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:58:12.343 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:58:12.918 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 84ffc973-55d9-483d-821c-e8b7d47b6dbd_config-0
13:58:12.965 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
13:58:12.992 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
13:58:12.998 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
13:58:13.006 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
13:58:13.013 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
13:58:13.022 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
13:58:13.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84ffc973-55d9-483d-821c-e8b7d47b6dbd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:58:13.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84ffc973-55d9-483d-821c-e8b7d47b6dbd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002131239eaf8
13:58:13.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84ffc973-55d9-483d-821c-e8b7d47b6dbd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002131239ed18
13:58:13.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84ffc973-55d9-483d-821c-e8b7d47b6dbd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:58:13.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84ffc973-55d9-483d-821c-e8b7d47b6dbd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:58:13.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84ffc973-55d9-483d-821c-e8b7d47b6dbd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:58:13.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84ffc973-55d9-483d-821c-e8b7d47b6dbd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753768693539_127.0.0.1_2009
13:58:13.718 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84ffc973-55d9-483d-821c-e8b7d47b6dbd_config-0] Notify connected event to listeners.
13:58:13.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84ffc973-55d9-483d-821c-e8b7d47b6dbd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:58:13.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84ffc973-55d9-483d-821c-e8b7d47b6dbd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021312518fd0
13:58:13.798 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:58:16.332 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:58:16.332 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:58:16.332 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:58:16.451 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:58:17.115 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:58:17.116 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:58:17.116 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:58:22.263 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:58:24.396 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 64601e47-f2e3-487f-aa10-276a77432c5d
13:58:24.396 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64601e47-f2e3-487f-aa10-276a77432c5d] RpcClient init label, labels = {module=naming, source=sdk}
13:58:24.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64601e47-f2e3-487f-aa10-276a77432c5d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:58:24.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64601e47-f2e3-487f-aa10-276a77432c5d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:58:24.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64601e47-f2e3-487f-aa10-276a77432c5d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:58:24.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64601e47-f2e3-487f-aa10-276a77432c5d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:58:24.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64601e47-f2e3-487f-aa10-276a77432c5d] Success to connect to server [localhost:8848] on start up, connectionId = 1753768704406_127.0.0.1_2066
13:58:24.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64601e47-f2e3-487f-aa10-276a77432c5d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:58:24.514 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64601e47-f2e3-487f-aa10-276a77432c5d] Notify connected event to listeners.
13:58:24.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64601e47-f2e3-487f-aa10-276a77432c5d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021312518fd0
13:58:24.549 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:58:24.572 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:58:24.664 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.83 seconds (JVM running for 13.704)
13:58:24.674 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:58:24.674 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:58:24.674 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:58:24.920 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:58:25.035 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64601e47-f2e3-487f-aa10-276a77432c5d] Receive server push request, request = NotifySubscriberRequest, requestId = 59
13:58:25.047 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64601e47-f2e3-487f-aa10-276a77432c5d] Ack server push request, request = NotifySubscriberRequest, requestId = 59
13:58:30.834 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:58:30.834 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:59:10.017 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:59:10.020 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:59:10.342 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:59:10.342 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@58854af7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:59:10.344 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753768704406_127.0.0.1_2066
13:59:10.346 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753768704406_127.0.0.1_2066]Ignore complete event,isRunning:false,isAbandon=false
13:59:10.349 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@47946a05[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 23]
13:59:10.500 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:59:10.502 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:59:10.507 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:59:10.507 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:59:10.508 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:59:10.508 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:59:16.328 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:59:17.472 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f27fa7be-53fe-4eb5-9f2e-c2eae8117789_config-0
13:59:17.581 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
13:59:17.628 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
13:59:17.641 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
13:59:17.652 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
13:59:17.664 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
13:59:17.675 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
13:59:17.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f27fa7be-53fe-4eb5-9f2e-c2eae8117789_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:59:17.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f27fa7be-53fe-4eb5-9f2e-c2eae8117789_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001a98139e8d8
13:59:17.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f27fa7be-53fe-4eb5-9f2e-c2eae8117789_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001a98139eaf8
13:59:17.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f27fa7be-53fe-4eb5-9f2e-c2eae8117789_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:59:17.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f27fa7be-53fe-4eb5-9f2e-c2eae8117789_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:59:17.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f27fa7be-53fe-4eb5-9f2e-c2eae8117789_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:59:18.774 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f27fa7be-53fe-4eb5-9f2e-c2eae8117789_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753768758535_127.0.0.1_2415
13:59:18.774 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f27fa7be-53fe-4eb5-9f2e-c2eae8117789_config-0] Notify connected event to listeners.
13:59:18.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f27fa7be-53fe-4eb5-9f2e-c2eae8117789_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:59:18.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f27fa7be-53fe-4eb5-9f2e-c2eae8117789_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a981518668
13:59:18.896 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:59:22.224 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:59:22.224 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:59:22.224 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:59:22.370 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:59:23.110 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:59:23.112 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:59:23.113 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:59:32.370 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:59:38.889 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 112eb5ef-**************-caf0951c9684
13:59:38.891 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [112eb5ef-**************-caf0951c9684] RpcClient init label, labels = {module=naming, source=sdk}
13:59:38.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [112eb5ef-**************-caf0951c9684] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:59:38.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [112eb5ef-**************-caf0951c9684] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:59:38.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [112eb5ef-**************-caf0951c9684] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:59:38.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [112eb5ef-**************-caf0951c9684] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:59:39.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [112eb5ef-**************-caf0951c9684] Success to connect to server [localhost:8848] on start up, connectionId = 1753768778913_127.0.0.1_2538
13:59:39.038 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [112eb5ef-**************-caf0951c9684] Notify connected event to listeners.
13:59:39.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [112eb5ef-**************-caf0951c9684] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:59:39.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [112eb5ef-**************-caf0951c9684] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a981518668
13:59:39.105 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:59:39.161 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:59:39.345 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.978 seconds (JVM running for 25.337)
13:59:39.367 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:59:39.368 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:59:39.369 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:59:39.612 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [112eb5ef-**************-caf0951c9684] Receive server push request, request = NotifySubscriberRequest, requestId = 63
13:59:39.630 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [112eb5ef-**************-caf0951c9684] Ack server push request, request = NotifySubscriberRequest, requestId = 63
13:59:39.839 [RMI TCP Connection(20)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:59:46.247 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:59:46.248 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:18:42.333 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:18:42.337 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:18:42.688 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:18:42.689 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3e18ad09[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:18:42.690 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753768778913_127.0.0.1_2538
15:18:42.694 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@606ffd76[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 400]
15:18:42.851 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:18:42.853 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:18:42.860 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:18:42.861 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:18:42.862 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:18:42.862 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:18:48.462 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:18:49.005 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 09008bcc-76aa-4e27-b637-e2df73306886_config-0
15:18:49.060 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
15:18:49.086 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
15:18:49.093 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
15:18:49.100 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
15:18:49.106 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
15:18:49.113 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
15:18:49.115 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09008bcc-76aa-4e27-b637-e2df73306886_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:18:49.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09008bcc-76aa-4e27-b637-e2df73306886_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001902d3beaf8
15:18:49.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09008bcc-76aa-4e27-b637-e2df73306886_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001902d3bed18
15:18:49.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09008bcc-76aa-4e27-b637-e2df73306886_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:18:49.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09008bcc-76aa-4e27-b637-e2df73306886_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:18:49.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09008bcc-76aa-4e27-b637-e2df73306886_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:18:49.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09008bcc-76aa-4e27-b637-e2df73306886_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753773529627_127.0.0.1_2340
15:18:49.810 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09008bcc-76aa-4e27-b637-e2df73306886_config-0] Notify connected event to listeners.
15:18:49.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09008bcc-76aa-4e27-b637-e2df73306886_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:18:49.812 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09008bcc-76aa-4e27-b637-e2df73306886_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001902d4f8fb0
15:18:49.894 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:18:52.367 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:18:52.367 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:18:52.367 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:18:52.486 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:18:52.999 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:18:53.000 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:18:53.001 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:18:58.156 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:19:00.250 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7c0c618b-3cb5-4205-8a95-b8bb57ba3f38
15:19:00.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c0c618b-3cb5-4205-8a95-b8bb57ba3f38] RpcClient init label, labels = {module=naming, source=sdk}
15:19:00.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c0c618b-3cb5-4205-8a95-b8bb57ba3f38] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:19:00.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c0c618b-3cb5-4205-8a95-b8bb57ba3f38] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:19:00.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c0c618b-3cb5-4205-8a95-b8bb57ba3f38] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:19:00.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c0c618b-3cb5-4205-8a95-b8bb57ba3f38] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:19:00.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c0c618b-3cb5-4205-8a95-b8bb57ba3f38] Success to connect to server [localhost:8848] on start up, connectionId = 1753773540260_127.0.0.1_2404
15:19:00.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c0c618b-3cb5-4205-8a95-b8bb57ba3f38] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:19:00.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c0c618b-3cb5-4205-8a95-b8bb57ba3f38] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001902d4f8fb0
15:19:00.372 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c0c618b-3cb5-4205-8a95-b8bb57ba3f38] Notify connected event to listeners.
15:19:00.407 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:19:00.429 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:19:00.522 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.552 seconds (JVM running for 13.463)
15:19:00.533 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:19:00.534 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:19:00.534 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:19:00.653 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:19:00.957 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c0c618b-3cb5-4205-8a95-b8bb57ba3f38] Receive server push request, request = NotifySubscriberRequest, requestId = 66
15:19:00.971 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c0c618b-3cb5-4205-8a95-b8bb57ba3f38] Ack server push request, request = NotifySubscriberRequest, requestId = 66
15:19:06.693 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:19:06.693 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:11:08.801 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:11:08.806 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:11:09.143 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:11:09.143 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3f731bbc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:11:09.143 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753773540260_127.0.0.1_2404
16:11:09.143 [nacos-grpc-client-executor-614] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753773540260_127.0.0.1_2404]Ignore complete event,isRunning:false,isAbandon=false
16:11:09.149 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6068a68[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 615]
16:11:09.312 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:11:09.318 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:11:09.318 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:11:09.318 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:11:09.318 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:11:09.318 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:11:17.049 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:11:18.142 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 979ad3d5-fe56-43c4-832e-6ca4be6391ec_config-0
16:11:18.216 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
16:11:18.270 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
16:11:18.274 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
16:11:18.291 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
16:11:18.301 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
16:11:18.318 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
16:11:18.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [979ad3d5-fe56-43c4-832e-6ca4be6391ec_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:11:18.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [979ad3d5-fe56-43c4-832e-6ca4be6391ec_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000260b039ed38
16:11:18.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [979ad3d5-fe56-43c4-832e-6ca4be6391ec_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000260b039ef58
16:11:18.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [979ad3d5-fe56-43c4-832e-6ca4be6391ec_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:11:18.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [979ad3d5-fe56-43c4-832e-6ca4be6391ec_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:11:18.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [979ad3d5-fe56-43c4-832e-6ca4be6391ec_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:11:19.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [979ad3d5-fe56-43c4-832e-6ca4be6391ec_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753776679051_127.0.0.1_4080
16:11:19.260 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [979ad3d5-fe56-43c4-832e-6ca4be6391ec_config-0] Notify connected event to listeners.
16:11:19.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [979ad3d5-fe56-43c4-832e-6ca4be6391ec_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:11:19.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [979ad3d5-fe56-43c4-832e-6ca4be6391ec_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000260b0518fb0
16:11:19.465 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:11:23.895 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:11:23.896 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:11:23.896 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:11:24.085 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:11:24.837 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:11:24.840 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:11:24.841 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:11:33.576 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:11:36.853 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 835a5518-ad01-4371-aad8-2de1330437b5
16:11:36.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [835a5518-ad01-4371-aad8-2de1330437b5] RpcClient init label, labels = {module=naming, source=sdk}
16:11:36.855 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [835a5518-ad01-4371-aad8-2de1330437b5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:11:36.855 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [835a5518-ad01-4371-aad8-2de1330437b5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:11:36.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [835a5518-ad01-4371-aad8-2de1330437b5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:11:36.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [835a5518-ad01-4371-aad8-2de1330437b5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:11:36.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [835a5518-ad01-4371-aad8-2de1330437b5] Success to connect to server [localhost:8848] on start up, connectionId = 1753776696865_127.0.0.1_4171
16:11:36.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [835a5518-ad01-4371-aad8-2de1330437b5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:11:36.992 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [835a5518-ad01-4371-aad8-2de1330437b5] Notify connected event to listeners.
16:11:36.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [835a5518-ad01-4371-aad8-2de1330437b5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000260b0518fb0
16:11:37.063 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:11:37.097 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:11:37.275 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.87 seconds (JVM running for 21.969)
16:11:37.293 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:11:37.293 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:11:37.294 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:11:37.803 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [835a5518-ad01-4371-aad8-2de1330437b5] Receive server push request, request = NotifySubscriberRequest, requestId = 69
16:11:37.828 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [835a5518-ad01-4371-aad8-2de1330437b5] Ack server push request, request = NotifySubscriberRequest, requestId = 69
16:11:38.338 [RMI TCP Connection(11)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:11:49.665 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:11:49.665 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:16:49.406 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:16:49.422 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:16:49.753 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:16:49.754 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3393e60e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:16:49.754 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753776696865_127.0.0.1_4171
16:16:49.759 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2b3a57d2[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 77]
16:16:49.917 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:16:49.924 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:16:49.924 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:16:49.924 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:16:49.924 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:16:49.924 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:16:56.251 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:16:57.125 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9416c443-8ed5-4d3b-ab60-aa1f31c0eab3_config-0
16:16:57.268 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
16:16:57.311 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
16:16:57.333 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
16:16:57.353 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 5 values 
16:16:57.372 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
16:16:57.395 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
16:16:57.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9416c443-8ed5-4d3b-ab60-aa1f31c0eab3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:16:57.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9416c443-8ed5-4d3b-ab60-aa1f31c0eab3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002181139f1c0
16:16:57.403 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9416c443-8ed5-4d3b-ab60-aa1f31c0eab3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002181139f3e0
16:16:57.403 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9416c443-8ed5-4d3b-ab60-aa1f31c0eab3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:16:57.403 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9416c443-8ed5-4d3b-ab60-aa1f31c0eab3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:16:57.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9416c443-8ed5-4d3b-ab60-aa1f31c0eab3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:16:58.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9416c443-8ed5-4d3b-ab60-aa1f31c0eab3_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753777018256_127.0.0.1_5564
16:16:58.454 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9416c443-8ed5-4d3b-ab60-aa1f31c0eab3_config-0] Notify connected event to listeners.
16:16:58.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9416c443-8ed5-4d3b-ab60-aa1f31c0eab3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:16:58.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9416c443-8ed5-4d3b-ab60-aa1f31c0eab3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021811518ad8
16:16:58.649 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:17:02.922 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:17:02.923 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:17:02.923 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:17:03.148 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:17:04.053 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:17:04.056 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:17:04.056 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:17:12.204 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:17:15.265 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0a504f24-d5ac-4ff6-a446-a89d9db389b0
16:17:15.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a504f24-d5ac-4ff6-a446-a89d9db389b0] RpcClient init label, labels = {module=naming, source=sdk}
16:17:15.268 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a504f24-d5ac-4ff6-a446-a89d9db389b0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:17:15.268 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a504f24-d5ac-4ff6-a446-a89d9db389b0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:17:15.268 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a504f24-d5ac-4ff6-a446-a89d9db389b0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:17:15.269 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a504f24-d5ac-4ff6-a446-a89d9db389b0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:17:15.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a504f24-d5ac-4ff6-a446-a89d9db389b0] Success to connect to server [localhost:8848] on start up, connectionId = 1753777035280_127.0.0.1_5698
16:17:15.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a504f24-d5ac-4ff6-a446-a89d9db389b0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:17:15.406 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a504f24-d5ac-4ff6-a446-a89d9db389b0] Notify connected event to listeners.
16:17:15.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a504f24-d5ac-4ff6-a446-a89d9db389b0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021811518ad8
16:17:15.477 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:17:15.518 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:17:15.676 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.032 seconds (JVM running for 21.035)
16:17:15.697 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:17:15.698 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:17:15.698 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:17:15.926 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:17:15.975 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a504f24-d5ac-4ff6-a446-a89d9db389b0] Receive server push request, request = NotifySubscriberRequest, requestId = 72
16:17:15.995 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a504f24-d5ac-4ff6-a446-a89d9db389b0] Ack server push request, request = NotifySubscriberRequest, requestId = 72
16:17:33.567 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:17:33.574 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:27:08.001 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:27:08.001 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:27:08.334 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:27:08.334 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2f24145[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:27:08.334 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753777035280_127.0.0.1_5698
20:27:08.334 [nacos-grpc-client-executor-3000] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753777035280_127.0.0.1_5698]Ignore complete event,isRunning:false,isAbandon=false
20:27:08.345 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@496d6aa4[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3001]
20:27:08.527 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:27:08.532 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:27:08.543 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:27:08.543 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:27:08.543 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:27:08.543 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
