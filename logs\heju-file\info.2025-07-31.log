09:43:49.419 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:43:50.593 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3bc2923b-e828-466b-8231-198ca3eb9427_config-0
09:43:50.748 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 70 ms to scan 1 urls, producing 3 keys and 6 values 
09:43:50.819 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 24 ms to scan 1 urls, producing 4 keys and 9 values 
09:43:50.842 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 3 keys and 10 values 
09:43:50.859 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:43:50.887 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 1 keys and 7 values 
09:43:50.908 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 2 keys and 8 values 
09:43:50.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3bc2923b-e828-466b-8231-198ca3eb9427_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:43:50.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3bc2923b-e828-466b-8231-198ca3eb9427_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000019c503b3678
09:43:50.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3bc2923b-e828-466b-8231-198ca3eb9427_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000019c503b3898
09:43:50.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3bc2923b-e828-466b-8231-198ca3eb9427_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:43:50.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3bc2923b-e828-466b-8231-198ca3eb9427_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:43:50.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3bc2923b-e828-466b-8231-198ca3eb9427_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:43:52.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3bc2923b-e828-466b-8231-198ca3eb9427_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753926232591_127.0.0.1_5988
09:43:52.919 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3bc2923b-e828-466b-8231-198ca3eb9427_config-0] Notify connected event to listeners.
09:43:52.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3bc2923b-e828-466b-8231-198ca3eb9427_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:43:52.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3bc2923b-e828-466b-8231-198ca3eb9427_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000019c504efcb0
09:43:53.151 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:43:59.541 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:43:59.543 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:43:59.543 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:44:00.017 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:44:06.656 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:44:20.508 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ffaa5e07-1de4-4b4d-8633-b768c7c2a84e
09:44:20.515 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] RpcClient init label, labels = {module=naming, source=sdk}
09:44:20.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:44:20.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:44:20.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:44:20.542 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:20.717 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Success to connect to server [localhost:8848] on start up, connectionId = 1753926260573_127.0.0.1_6457
09:44:20.717 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Notify connected event to listeners.
09:44:20.717 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:20.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000019c504efcb0
09:44:20.846 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:44:20.938 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:44:21.311 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:44:21.339 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:44:21.479 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 32.802 seconds (JVM running for 40.763)
09:44:21.511 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:44:21.516 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:44:21.520 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
10:02:37.363 [http-nio-9300-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:02:38.761 [nacos-grpc-client-executor-228] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Receive server push request, request = NotifySubscriberRequest, requestId = 13
10:02:38.762 [nacos-grpc-client-executor-228] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Ack server push request, request = NotifySubscriberRequest, requestId = 13
11:36:05.667 [nacos-grpc-client-executor-1349] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Receive server push request, request = NotifySubscriberRequest, requestId = 17
11:36:05.680 [nacos-grpc-client-executor-1349] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Ack server push request, request = NotifySubscriberRequest, requestId = 17
11:36:43.046 [nacos-grpc-client-executor-1357] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Receive server push request, request = NotifySubscriberRequest, requestId = 19
11:36:43.065 [nacos-grpc-client-executor-1357] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Ack server push request, request = NotifySubscriberRequest, requestId = 19
14:14:04.135 [nacos-grpc-client-executor-3243] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Receive server push request, request = NotifySubscriberRequest, requestId = 24
14:14:04.153 [nacos-grpc-client-executor-3243] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Ack server push request, request = NotifySubscriberRequest, requestId = 24
14:14:24.978 [nacos-grpc-client-executor-3248] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Receive server push request, request = NotifySubscriberRequest, requestId = 27
14:14:24.993 [nacos-grpc-client-executor-3248] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Ack server push request, request = NotifySubscriberRequest, requestId = 27
14:36:10.446 [nacos-grpc-client-executor-3509] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Receive server push request, request = NotifySubscriberRequest, requestId = 31
14:36:10.464 [nacos-grpc-client-executor-3509] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Ack server push request, request = NotifySubscriberRequest, requestId = 31
14:36:26.835 [nacos-grpc-client-executor-3512] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Receive server push request, request = NotifySubscriberRequest, requestId = 34
14:36:26.845 [nacos-grpc-client-executor-3512] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffaa5e07-1de4-4b4d-8633-b768c7c2a84e] Ack server push request, request = NotifySubscriberRequest, requestId = 34
14:59:26.660 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:59:26.672 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:59:27.010 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:59:27.011 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@612100a0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:59:27.012 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753926260573_127.0.0.1_6457
14:59:27.014 [nacos-grpc-client-executor-3790] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753926260573_127.0.0.1_6457]Ignore complete event,isRunning:false,isAbandon=false
14:59:27.019 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@33a149d5[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 3791]
15:07:12.733 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:07:14.648 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0071d22a-50bf-40a8-ae4f-4ceee90a7eeb_config-0
15:07:14.821 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 95 ms to scan 1 urls, producing 3 keys and 6 values 
15:07:14.885 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 4 keys and 9 values 
15:07:14.910 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 10 values 
15:07:14.934 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
15:07:14.962 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 1 keys and 7 values 
15:07:14.997 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 2 keys and 8 values 
15:07:15.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0071d22a-50bf-40a8-ae4f-4ceee90a7eeb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:07:15.009 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0071d22a-50bf-40a8-ae4f-4ceee90a7eeb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000209013b0200
15:07:15.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0071d22a-50bf-40a8-ae4f-4ceee90a7eeb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000209013b0420
15:07:15.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0071d22a-50bf-40a8-ae4f-4ceee90a7eeb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:07:15.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0071d22a-50bf-40a8-ae4f-4ceee90a7eeb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:07:15.040 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0071d22a-50bf-40a8-ae4f-4ceee90a7eeb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:07:17.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0071d22a-50bf-40a8-ae4f-4ceee90a7eeb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753945637438_127.0.0.1_9267
15:07:17.805 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0071d22a-50bf-40a8-ae4f-4ceee90a7eeb_config-0] Notify connected event to listeners.
15:07:17.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0071d22a-50bf-40a8-ae4f-4ceee90a7eeb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:17.807 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0071d22a-50bf-40a8-ae4f-4ceee90a7eeb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000209014eab60
15:07:18.021 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:07:23.704 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
15:07:23.705 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:07:23.705 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:07:23.924 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:07:26.200 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:07:30.277 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 64439c7d-281f-44a7-a2f5-02c3c8bc4104
15:07:30.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64439c7d-281f-44a7-a2f5-02c3c8bc4104] RpcClient init label, labels = {module=naming, source=sdk}
15:07:30.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64439c7d-281f-44a7-a2f5-02c3c8bc4104] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:07:30.283 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64439c7d-281f-44a7-a2f5-02c3c8bc4104] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:07:30.284 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64439c7d-281f-44a7-a2f5-02c3c8bc4104] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:07:30.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64439c7d-281f-44a7-a2f5-02c3c8bc4104] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:07:30.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64439c7d-281f-44a7-a2f5-02c3c8bc4104] Success to connect to server [localhost:8848] on start up, connectionId = 1753945650309_127.0.0.1_9305
15:07:30.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64439c7d-281f-44a7-a2f5-02c3c8bc4104] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:30.432 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64439c7d-281f-44a7-a2f5-02c3c8bc4104] Notify connected event to listeners.
15:07:30.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64439c7d-281f-44a7-a2f5-02c3c8bc4104] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000209014eab60
15:07:30.494 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
15:07:31.065 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64439c7d-281f-44a7-a2f5-02c3c8bc4104] Receive server push request, request = NotifySubscriberRequest, requestId = 41
15:07:31.066 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64439c7d-281f-44a7-a2f5-02c3c8bc4104] Ack server push request, request = NotifySubscriberRequest, requestId = 41
15:07:31.195 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:07:31.196 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@490b8f1e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:07:31.196 [main] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753945650309_127.0.0.1_9305
15:07:31.198 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753945650309_127.0.0.1_9305]Ignore complete event,isRunning:false,isAbandon=false
15:07:31.199 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4d7f3adf[Running, pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 5]
15:07:31.203 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6ab0fa9c-3d9b-488c-859e-53f6dfa96c3d
15:07:31.204 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ab0fa9c-3d9b-488c-859e-53f6dfa96c3d] RpcClient init label, labels = {module=naming, source=sdk}
15:07:31.204 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ab0fa9c-3d9b-488c-859e-53f6dfa96c3d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:07:31.204 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ab0fa9c-3d9b-488c-859e-53f6dfa96c3d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:07:31.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ab0fa9c-3d9b-488c-859e-53f6dfa96c3d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:07:31.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ab0fa9c-3d9b-488c-859e-53f6dfa96c3d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:07:31.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ab0fa9c-3d9b-488c-859e-53f6dfa96c3d] Success to connect to server [localhost:8848] on start up, connectionId = 1753945651221_127.0.0.1_9312
15:07:31.349 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ab0fa9c-3d9b-488c-859e-53f6dfa96c3d] Notify connected event to listeners.
15:07:31.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ab0fa9c-3d9b-488c-859e-53f6dfa96c3d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:31.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6ab0fa9c-3d9b-488c-859e-53f6dfa96c3d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000209014eab60
15:07:31.371 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9300"]
15:07:31.371 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
15:07:31.381 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9300"]
15:07:31.383 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9300"]
16:32:32.649 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:32:33.070 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9a63e5d8-5ee9-4af1-b92b-54837d2285c6_config-0
16:32:33.120 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
16:32:33.137 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
16:32:33.143 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
16:32:33.150 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
16:32:33.156 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
16:32:33.164 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
16:32:33.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a63e5d8-5ee9-4af1-b92b-54837d2285c6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:32:33.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a63e5d8-5ee9-4af1-b92b-54837d2285c6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000025be23b6d88
16:32:33.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a63e5d8-5ee9-4af1-b92b-54837d2285c6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000025be23b6fa8
16:32:33.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a63e5d8-5ee9-4af1-b92b-54837d2285c6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:32:33.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a63e5d8-5ee9-4af1-b92b-54837d2285c6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:32:33.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a63e5d8-5ee9-4af1-b92b-54837d2285c6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:32:33.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a63e5d8-5ee9-4af1-b92b-54837d2285c6_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753950753642_127.0.0.1_2764
16:32:33.820 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a63e5d8-5ee9-4af1-b92b-54837d2285c6_config-0] Notify connected event to listeners.
16:32:33.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a63e5d8-5ee9-4af1-b92b-54837d2285c6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:32:33.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a63e5d8-5ee9-4af1-b92b-54837d2285c6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025be24f0668
16:32:33.890 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:32:35.572 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
16:32:35.572 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:32:35.573 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:32:35.690 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:32:37.163 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:32:39.357 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bf8af50a-005e-4ba8-870b-34a72cd6a9f2
16:32:39.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] RpcClient init label, labels = {module=naming, source=sdk}
16:32:39.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:32:39.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:32:39.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:32:39.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:32:39.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] Success to connect to server [localhost:8848] on start up, connectionId = 1753950759372_127.0.0.1_2810
16:32:39.495 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] Notify connected event to listeners.
16:32:39.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:32:39.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025be24f0668
16:32:39.536 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
16:32:39.565 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
16:32:39.725 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 7.521 seconds (JVM running for 8.361)
16:32:39.734 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
16:32:39.734 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
16:32:39.736 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
16:32:40.020 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] Receive server push request, request = NotifySubscriberRequest, requestId = 49
16:32:40.036 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] Ack server push request, request = NotifySubscriberRequest, requestId = 49
16:32:40.249 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:43:15.555 [nacos-grpc-client-executor-861] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] Receive server push request, request = NotifySubscriberRequest, requestId = 71
17:43:15.556 [nacos-grpc-client-executor-861] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] Ack server push request, request = NotifySubscriberRequest, requestId = 71
20:46:46.207 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:46:46.211 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:46:46.454 [nacos-grpc-client-executor-3063] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] Receive server push request, request = NotifySubscriberRequest, requestId = 73
20:46:46.472 [nacos-grpc-client-executor-3063] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf8af50a-005e-4ba8-870b-34a72cd6a9f2] Ack server push request, request = NotifySubscriberRequest, requestId = 73
20:46:46.544 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:46:46.544 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7b9604aa[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:46:46.545 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753950759372_127.0.0.1_2810
20:46:46.546 [nacos-grpc-client-executor-3064] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753950759372_127.0.0.1_2810]Ignore complete event,isRunning:false,isAbandon=false
20:46:46.552 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2d19e252[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 3065]
