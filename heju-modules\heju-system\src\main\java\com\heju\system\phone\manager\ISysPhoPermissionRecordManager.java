package com.heju.system.phone.manager;

import com.heju.system.phone.domain.dto.SysPhoPermissionRecordDto;
import com.heju.system.phone.domain.query.SysPhoPermissionRecordQuery;
import com.heju.common.web.entity.manager.IBaseManager;

import java.util.List;

/**
 * 手机号授权管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysPhoPermissionRecordManager extends IBaseManager<SysPhoPermissionRecordQuery, SysPhoPermissionRecordDto> {

    /**
     * 查询数据对象列表
     *
     * @param query 数据查询对象
     * @return 数据对象集合
     */
    List<SysPhoPermissionRecordDto> selectPermList(SysPhoPermissionRecordQuery query);

    /**
     * 查授权记录列表
     * @param query 数据查询对象
     * @return
     */
    List<SysPhoPermissionRecordDto> selectOverTimeList(SysPhoPermissionRecordQuery query);

    /**
     * 短信查询条件获取
     *
     * @param query 数据查询对象
     * @return 数据对象集合
     */
    List<SysPhoPermissionRecordDto> selectRecordForMsg(SysPhoPermissionRecordQuery query);

    List<SysPhoPermissionRecordDto> selectRecordForTimes(SysPhoPermissionRecordQuery query);

}
