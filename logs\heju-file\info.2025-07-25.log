09:09:03.731 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:09:04.455 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 64fabe65-0e0d-4299-b3de-67720c370b63_config-0
09:09:04.544 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 40 ms to scan 1 urls, producing 3 keys and 6 values 
09:09:04.585 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:09:04.596 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:09:04.611 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 5 values 
09:09:04.620 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 7 values 
09:09:04.639 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
09:09:04.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64fabe65-0e0d-4299-b3de-67720c370b63_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:09:04.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64fabe65-0e0d-4299-b3de-67720c370b63_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000020e81396b40
09:09:04.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64fabe65-0e0d-4299-b3de-67720c370b63_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000020e81396d60
09:09:04.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64fabe65-0e0d-4299-b3de-67720c370b63_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:09:04.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64fabe65-0e0d-4299-b3de-67720c370b63_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:09:04.655 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64fabe65-0e0d-4299-b3de-67720c370b63_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:09:05.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64fabe65-0e0d-4299-b3de-67720c370b63_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753405745430_127.0.0.1_6375
09:09:05.644 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64fabe65-0e0d-4299-b3de-67720c370b63_config-0] Notify connected event to listeners.
09:09:05.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64fabe65-0e0d-4299-b3de-67720c370b63_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:09:05.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64fabe65-0e0d-4299-b3de-67720c370b63_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000020e81510668
09:09:05.785 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:09:09.151 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:09:09.152 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:09:09.152 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:09:09.514 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:09:12.728 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:09:17.945 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fa6fa931-ce48-4a19-ac20-a8ac9cb0b92e
09:09:17.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa6fa931-ce48-4a19-ac20-a8ac9cb0b92e] RpcClient init label, labels = {module=naming, source=sdk}
09:09:17.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa6fa931-ce48-4a19-ac20-a8ac9cb0b92e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:09:17.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa6fa931-ce48-4a19-ac20-a8ac9cb0b92e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:09:17.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa6fa931-ce48-4a19-ac20-a8ac9cb0b92e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:09:17.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa6fa931-ce48-4a19-ac20-a8ac9cb0b92e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:09:18.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa6fa931-ce48-4a19-ac20-a8ac9cb0b92e] Success to connect to server [localhost:8848] on start up, connectionId = 1753405757961_127.0.0.1_6461
09:09:18.091 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa6fa931-ce48-4a19-ac20-a8ac9cb0b92e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:09:18.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa6fa931-ce48-4a19-ac20-a8ac9cb0b92e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000020e81510668
09:09:18.092 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa6fa931-ce48-4a19-ac20-a8ac9cb0b92e] Notify connected event to listeners.
09:09:18.160 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:09:18.210 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:09:18.562 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 15.553 seconds (JVM running for 17.325)
09:09:18.587 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:09:18.587 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:09:18.592 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:09:18.710 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa6fa931-ce48-4a19-ac20-a8ac9cb0b92e] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:09:18.739 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa6fa931-ce48-4a19-ac20-a8ac9cb0b92e] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:09:19.231 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:02:07.208 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:02:07.215 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:02:07.568 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:02:07.568 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7630661a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:02:07.568 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753405757961_127.0.0.1_6461
19:02:07.573 [nacos-grpc-client-executor-7114] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753405757961_127.0.0.1_6461]Ignore complete event,isRunning:false,isAbandon=false
19:02:07.587 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7046d1ce[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 7115]
