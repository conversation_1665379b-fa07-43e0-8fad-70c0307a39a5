package com.heju.system.api.organize.feign.factory;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.feign.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户服务 降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<RemoteUserService> {

    @Override
    public RemoteUserService create(Throwable throwable) {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteUserService() {
            @Override
            public R<SysUserDto> addInner(SysUserDto user, Long enterpriseId, String sourceName, String source) {
                return R.fail("新增用户失败:" + throwable.getMessage());
            }

            @Override
            public R<SysUserDto> addInnerUser(SysUserDto user,Long enterpriseId, String sourceName, String source) {
                return R.fail("新增用户失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult remoteOption(String sourceMame, String source) {
                return AjaxResult.error("调用岗位树失败:" + throwable.getMessage());
            }

            @Override
            public R<List<T>> remoteGetOrganizeScope(String sourceMame, String source) {
                return R.fail("调用岗位树失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult innerEditRoleAuth(SysUserDto user, String source, Long enterpriseId, String sourceName) {
                return AjaxResult.error("调用修改用户关联的角色Id集:" + throwable.getMessage());
            }

            @Override
            public R<SysUserDto> innerGetInfo(String openId, String sourceName, String source) {
                return R.fail("调用通过openId查询失败:" + throwable.getMessage());
            }

            @Override
            public R<SysUserDto> getInfoInner(Long id, String sourceName, String source) {
                return R.fail("调用通过id查询失败:" + throwable.getMessage());
            }
        };
    }
}