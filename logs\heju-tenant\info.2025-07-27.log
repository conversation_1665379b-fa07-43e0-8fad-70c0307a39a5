14:21:41.595 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:21:42.820 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 52fcd3bc-91c8-4430-9c40-75493d845321_config-0
14:21:42.895 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 33 ms to scan 1 urls, producing 3 keys and 6 values 
14:21:42.943 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 4 keys and 9 values 
14:21:42.960 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 3 keys and 10 values 
14:21:42.975 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 5 values 
14:21:42.988 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 7 values 
14:21:43.001 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
14:21:43.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52fcd3bc-91c8-4430-9c40-75493d845321_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:21:43.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52fcd3bc-91c8-4430-9c40-75493d845321_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001b80939cfb8
14:21:43.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52fcd3bc-91c8-4430-9c40-75493d845321_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001b80939d1d8
14:21:43.009 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52fcd3bc-91c8-4430-9c40-75493d845321_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:21:43.009 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52fcd3bc-91c8-4430-9c40-75493d845321_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:21:43.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52fcd3bc-91c8-4430-9c40-75493d845321_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:21:44.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52fcd3bc-91c8-4430-9c40-75493d845321_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753597304077_127.0.0.1_4553
14:21:44.339 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52fcd3bc-91c8-4430-9c40-75493d845321_config-0] Notify connected event to listeners.
14:21:44.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52fcd3bc-91c8-4430-9c40-75493d845321_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:21:44.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52fcd3bc-91c8-4430-9c40-75493d845321_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001b809515418
14:21:44.550 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:21:48.653 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:21:48.653 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:21:48.653 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:21:48.827 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:21:49.727 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:21:49.728 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:21:49.728 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:21:51.846 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:21:54.117 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b497596d-dfce-464e-9b3e-44ee51aa1a54
14:21:54.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] RpcClient init label, labels = {module=naming, source=sdk}
14:21:54.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:21:54.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:21:54.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:21:54.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:21:54.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Success to connect to server [localhost:8848] on start up, connectionId = 1753597314131_127.0.0.1_4564
14:21:54.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:21:54.256 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Notify connected event to listeners.
14:21:54.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001b809515418
14:21:54.298 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:21:54.323 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
14:21:54.409 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 13.925 seconds (JVM running for 17.414)
14:21:54.425 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
14:21:54.425 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
14:21:54.425 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
14:21:54.878 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 5
14:21:54.893 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 5
14:36:52.485 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:36:57.533 [nacos-grpc-client-executor-191] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 13
14:36:57.533 [nacos-grpc-client-executor-191] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 13
16:28:35.100 [nacos-grpc-client-executor-1586] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 16
16:28:35.120 [nacos-grpc-client-executor-1586] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 16
16:28:37.745 [nacos-grpc-client-executor-1587] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 21
16:28:37.761 [nacos-grpc-client-executor-1587] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 21
17:03:24.817 [nacos-grpc-client-executor-2015] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 24
17:03:24.837 [nacos-grpc-client-executor-2015] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 24
17:03:42.124 [nacos-grpc-client-executor-2019] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 27
17:03:42.140 [nacos-grpc-client-executor-2019] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 27
17:10:15.816 [nacos-grpc-client-executor-2103] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 31
17:10:15.835 [nacos-grpc-client-executor-2103] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 31
17:10:31.562 [nacos-grpc-client-executor-2106] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 35
17:10:31.574 [nacos-grpc-client-executor-2106] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 35
17:12:07.232 [nacos-grpc-client-executor-2127] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 38
17:12:07.247 [nacos-grpc-client-executor-2127] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 38
17:12:27.359 [nacos-grpc-client-executor-2131] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 41
17:12:27.375 [nacos-grpc-client-executor-2131] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 41
17:13:31.380 [nacos-grpc-client-executor-2143] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 45
17:13:31.383 [nacos-grpc-client-executor-2143] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 45
17:13:48.719 [nacos-grpc-client-executor-2147] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 49
17:13:48.735 [nacos-grpc-client-executor-2147] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 49
17:15:52.508 [nacos-grpc-client-executor-2173] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 52
17:15:52.530 [nacos-grpc-client-executor-2173] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 52
17:16:10.189 [nacos-grpc-client-executor-2177] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 56
17:16:10.202 [nacos-grpc-client-executor-2177] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 56
19:48:19.629 [nacos-grpc-client-executor-4003] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 59
19:48:19.653 [nacos-grpc-client-executor-4003] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 59
19:48:38.301 [nacos-grpc-client-executor-4007] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 62
19:48:38.315 [nacos-grpc-client-executor-4007] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 62
19:51:57.652 [nacos-grpc-client-executor-4047] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 66
19:51:57.668 [nacos-grpc-client-executor-4047] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 66
19:52:16.980 [nacos-grpc-client-executor-4052] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 70
19:52:16.995 [nacos-grpc-client-executor-4052] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 70
19:53:46.137 [nacos-grpc-client-executor-4069] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 73
19:53:46.153 [nacos-grpc-client-executor-4069] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 73
19:54:02.907 [nacos-grpc-client-executor-4073] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 76
19:54:02.920 [nacos-grpc-client-executor-4073] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 76
19:54:27.591 [nacos-grpc-client-executor-4078] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 80
19:54:27.612 [nacos-grpc-client-executor-4078] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 80
19:54:47.668 [nacos-grpc-client-executor-4082] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Receive server push request, request = NotifySubscriberRequest, requestId = 84
19:54:47.683 [nacos-grpc-client-executor-4082] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b497596d-dfce-464e-9b3e-44ee51aa1a54] Ack server push request, request = NotifySubscriberRequest, requestId = 84
19:59:06.283 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:59:06.286 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:59:06.626 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:59:06.627 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3b5675bf[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:59:06.627 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753597314131_127.0.0.1_4564
19:59:06.629 [nacos-grpc-client-executor-4135] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753597314131_127.0.0.1_4564]Ignore complete event,isRunning:false,isAbandon=false
19:59:06.633 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@29f06182[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 4136]
19:59:06.787 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:59:06.791 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:59:06.798 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:59:06.798 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
