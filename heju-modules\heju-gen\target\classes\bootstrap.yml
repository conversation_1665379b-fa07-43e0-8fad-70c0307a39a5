# Tomcat
server:
  port: 9400

# Spring
spring: 
  application:
    # 应用名称
    name: heju-gen
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: localhost:8848
#        server-addr: *************:8848
      config:
        # 配置中心地址
        server-addr: localhost:8848
#        server-addr: *************:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-secret-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-datasource-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}