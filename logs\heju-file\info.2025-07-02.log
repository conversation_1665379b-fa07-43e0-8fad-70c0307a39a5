09:20:35.092 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:20:37.626 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0
09:20:37.886 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 129 ms to scan 1 urls, producing 3 keys and 6 values 
09:20:37.990 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 32 ms to scan 1 urls, producing 4 keys and 9 values 
09:20:38.017 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 3 keys and 10 values 
09:20:38.050 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 1 keys and 5 values 
09:20:38.084 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 29 ms to scan 1 urls, producing 1 keys and 7 values 
09:20:38.121 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 2 keys and 8 values 
09:20:38.129 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:20:38.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000110c5396b40
09:20:38.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000110c5396d60
09:20:38.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:20:38.135 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:20:38.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:20:40.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:20:40.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:20:40.566 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:20:40.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:20:40.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000110c54e1000
09:20:40.769 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:40.995 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:41.326 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:41.761 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:42.297 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:42.553 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:20:42.930 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:43.661 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:44.482 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:45.414 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:46.446 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:47.829 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:49.272 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:50.859 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:51.396 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:20:51.398 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:20:51.400 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:20:52.085 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:20:52.551 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:54.468 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:56.505 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:20:58.943 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:01.039 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:21:01.101 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:03.836 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:06.198 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:08.651 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:11.204 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:14.002 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:16.384 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ad11bb46-ad1f-43b0-b1eb-00704769e69a
09:21:16.385 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] RpcClient init label, labels = {module=naming, source=sdk}
09:21:16.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:16.395 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:16.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:16.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:16.840 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24e2ea75-90d1-4021-9d1e-f08603e02a26_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:16.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:16.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:17.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:17.000 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:21:17.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000110c54e1000
09:21:17.189 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:17.362 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:21:17.472 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:17.823 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:18.280 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:18.363 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:21:18.364 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3a3f1703[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:21:18.364 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@25d6ae3[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:21:18.364 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad11bb46-ad1f-43b0-b1eb-00704769e69a] Client is shutdown, stop reconnect to server
09:21:18.376 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 269daaae-77c8-4a17-ac22-22ca81c7f61c
09:21:18.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269daaae-77c8-4a17-ac22-22ca81c7f61c] RpcClient init label, labels = {module=naming, source=sdk}
09:21:18.390 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269daaae-77c8-4a17-ac22-22ca81c7f61c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:18.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269daaae-77c8-4a17-ac22-22ca81c7f61c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:18.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269daaae-77c8-4a17-ac22-22ca81c7f61c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:18.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269daaae-77c8-4a17-ac22-22ca81c7f61c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:18.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269daaae-77c8-4a17-ac22-22ca81c7f61c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:18.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269daaae-77c8-4a17-ac22-22ca81c7f61c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:21:18.530 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269daaae-77c8-4a17-ac22-22ca81c7f61c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:18.530 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269daaae-77c8-4a17-ac22-22ca81c7f61c] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:21:18.531 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269daaae-77c8-4a17-ac22-22ca81c7f61c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000110c54e1000
09:21:18.682 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269daaae-77c8-4a17-ac22-22ca81c7f61c] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:18.922 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269daaae-77c8-4a17-ac22-22ca81c7f61c] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:21:18.983 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9300"]
09:21:18.984 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:21:19.014 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9300"]
09:21:19.027 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9300"]
09:22:34.830 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:22:37.118 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0
09:22:37.341 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 109 ms to scan 1 urls, producing 3 keys and 6 values 
09:22:37.424 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 4 keys and 9 values 
09:22:37.448 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 10 values 
09:22:37.474 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 5 values 
09:22:37.500 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 7 values 
09:22:37.533 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 2 keys and 8 values 
09:22:37.540 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:22:37.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000237a03b0200
09:22:37.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000237a03b0420
09:22:37.546 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:22:37.551 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:22:37.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:22:40.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751419360013_127.0.0.1_3508
09:22:40.408 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] Notify connected event to listeners.
09:22:40.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:22:40.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000237a04ea6e0
09:22:40.739 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:22:48.522 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:22:48.523 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:22:48.524 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:22:49.138 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:22:53.783 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:23:03.243 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 19e1ac9a-7814-407f-afc8-4cd8359e9fca
09:23:03.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] RpcClient init label, labels = {module=naming, source=sdk}
09:23:03.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:23:03.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:23:03.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:23:03.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:03.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Success to connect to server [localhost:8848] on start up, connectionId = 1751419383287_127.0.0.1_3549
09:23:03.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:03.419 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Notify connected event to listeners.
09:23:03.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000237a04ea6e0
09:23:03.595 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:23:03.680 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:23:04.038 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:23:04.080 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:23:04.229 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 31.341 seconds (JVM running for 34.522)
09:23:04.271 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:23:04.272 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:23:04.283 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:42:53.488 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Server healthy check fail, currentConnection = 1751419383287_127.0.0.1_3549
09:42:53.490 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:42:53.890 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] Server healthy check fail, currentConnection = 1751419360013_127.0.0.1_3508
09:42:53.890 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:42:59.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] Success to connect a server [localhost:8848], connectionId = 1751420579303_127.0.0.1_5605
09:42:59.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751419360013_127.0.0.1_3508
09:42:59.516 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751419360013_127.0.0.1_3508
09:42:59.517 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] Notify disconnected event to listeners
09:42:59.519 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8b7ab7-944a-4482-b6cd-80c011d94a3c_config-0] Notify connected event to listeners.
09:42:59.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Success to connect a server [localhost:8848], connectionId = 1751420579377_127.0.0.1_5606
09:42:59.733 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Abandon prev connection, server is localhost:8848, connectionId is 1751419383287_127.0.0.1_3549
09:42:59.733 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751419383287_127.0.0.1_3549
09:42:59.753 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Notify disconnected event to listeners
09:42:59.755 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Notify connected event to listeners.
09:43:02.376 [nacos-grpc-client-executor-250] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Receive server push request, request = NotifySubscriberRequest, requestId = 19
09:43:02.376 [nacos-grpc-client-executor-250] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19e1ac9a-7814-407f-afc8-4cd8359e9fca] Ack server push request, request = NotifySubscriberRequest, requestId = 19
13:40:38.056 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:40:38.064 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:40:38.410 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:40:38.410 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@66ba5d92[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:40:38.410 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751420579377_127.0.0.1_5606
13:40:38.417 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3ab55be[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 3097]
13:40:38.417 [nacos-grpc-client-executor-3097] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751420579377_127.0.0.1_5606]Ignore complete event,isRunning:false,isAbandon=false
14:14:44.907 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:14:45.719 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 93623d12-903d-4556-9a21-e7cc4b950ad0_config-0
14:14:45.802 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
14:14:45.833 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
14:14:45.844 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
14:14:45.857 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:14:45.868 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
14:14:45.880 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:14:45.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93623d12-903d-4556-9a21-e7cc4b950ad0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:14:45.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93623d12-903d-4556-9a21-e7cc4b950ad0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000025901397220
14:14:45.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93623d12-903d-4556-9a21-e7cc4b950ad0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000025901397440
14:14:45.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93623d12-903d-4556-9a21-e7cc4b950ad0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:14:45.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93623d12-903d-4556-9a21-e7cc4b950ad0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:14:45.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93623d12-903d-4556-9a21-e7cc4b950ad0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:14:47.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93623d12-903d-4556-9a21-e7cc4b950ad0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751436886890_127.0.0.1_9396
14:14:47.162 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93623d12-903d-4556-9a21-e7cc4b950ad0_config-0] Notify connected event to listeners.
14:14:47.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93623d12-903d-4556-9a21-e7cc4b950ad0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:14:47.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93623d12-903d-4556-9a21-e7cc4b950ad0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025901510fb0
14:14:47.296 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:14:50.298 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
14:14:50.299 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:14:50.299 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:14:50.555 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:14:52.574 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:14:55.759 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 55c332d3-af0d-479d-80b8-100292b4270e
14:14:55.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55c332d3-af0d-479d-80b8-100292b4270e] RpcClient init label, labels = {module=naming, source=sdk}
14:14:55.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55c332d3-af0d-479d-80b8-100292b4270e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:14:55.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55c332d3-af0d-479d-80b8-100292b4270e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:14:55.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55c332d3-af0d-479d-80b8-100292b4270e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:14:55.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55c332d3-af0d-479d-80b8-100292b4270e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:14:55.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55c332d3-af0d-479d-80b8-100292b4270e] Success to connect to server [localhost:8848] on start up, connectionId = 1751436895776_127.0.0.1_9421
14:14:55.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55c332d3-af0d-479d-80b8-100292b4270e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:14:55.895 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55c332d3-af0d-479d-80b8-100292b4270e] Notify connected event to listeners.
14:14:55.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55c332d3-af0d-479d-80b8-100292b4270e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025901510fb0
14:14:55.964 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
14:14:56.008 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
14:14:56.213 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 12.11 seconds (JVM running for 13.622)
14:14:56.231 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
14:14:56.232 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
14:14:56.253 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
14:14:56.482 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55c332d3-af0d-479d-80b8-100292b4270e] Receive server push request, request = NotifySubscriberRequest, requestId = 73
14:14:56.503 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55c332d3-af0d-479d-80b8-100292b4270e] Ack server push request, request = NotifySubscriberRequest, requestId = 73
14:14:56.670 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:20:06.442 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:20:06.449 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:20:06.783 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:20:06.784 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@74b2bb5b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:20:06.784 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751436895776_127.0.0.1_9421
14:20:06.787 [nacos-grpc-client-executor-70] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751436895776_127.0.0.1_9421]Ignore complete event,isRunning:false,isAbandon=false
14:20:06.791 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@49158a5c[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 71]
17:35:23.443 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:35:26.028 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0
17:35:26.198 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 74 ms to scan 1 urls, producing 3 keys and 6 values 
17:35:26.278 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 4 keys and 9 values 
17:35:26.292 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
17:35:26.315 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 5 values 
17:35:26.338 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 7 values 
17:35:26.354 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
17:35:26.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:35:26.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001514d3aed88
17:35:26.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001514d3aefa8
17:35:26.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:35:26.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:35:26.384 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:35:29.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751448928757_127.0.0.1_13028
17:35:29.089 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Notify connected event to listeners.
17:35:29.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:29.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001514d4e8d48
17:35:29.319 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:35:33.413 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
17:35:33.416 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:35:33.416 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:35:33.645 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:35:35.930 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:35:39.435 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2c292f27-cba4-4e73-bfa2-bab9bea35788
17:35:39.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] RpcClient init label, labels = {module=naming, source=sdk}
17:35:39.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:35:39.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:35:39.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:35:39.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:35:39.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Success to connect to server [localhost:8848] on start up, connectionId = 1751448939461_127.0.0.1_13057
17:35:39.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:39.579 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Notify connected event to listeners.
17:35:39.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001514d4e8d48
17:35:39.668 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
17:35:39.717 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
17:35:39.941 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 17.752 seconds (JVM running for 21.397)
17:35:39.971 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
17:35:39.973 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
17:35:39.977 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
17:35:40.162 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Receive server push request, request = NotifySubscriberRequest, requestId = 95
17:35:40.189 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Ack server push request, request = NotifySubscriberRequest, requestId = 95
21:12:22.463 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:12:22.463 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:12:22.604 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:22.604 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:22.822 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:22.825 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:23.156 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:23.158 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:23.578 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:23.598 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:24.106 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:24.136 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:24.736 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:24.751 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:25.454 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c585ee54-b6bb-466a-85b1-1e4aed8f0d4c_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:25.461 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
21:12:25.511 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
21:12:25.836 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
21:12:26.157 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
21:12:26.157 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4208e339[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
21:12:26.157 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751448939461_127.0.0.1_13057
21:12:26.157 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@23ced74e[Running, pool size = 18, active threads = 0, queued tasks = 0, completed tasks = 2622]
21:12:26.160 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Notify disconnected event to listeners
21:12:26.160 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c292f27-cba4-4e73-bfa2-bab9bea35788] Client is shutdown, stop reconnect to server
