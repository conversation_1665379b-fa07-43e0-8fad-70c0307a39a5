package com.heju.system.api.organize.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.api.organize.domain.dto.SysEnterpriseDto;
import com.heju.system.api.organize.domain.po.SysEnterprisePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:36:51+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysEnterpriseConverterImpl implements SysEnterpriseConverter {

    @Override
    public SysEnterpriseDto mapperDto(SysEnterprisePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEnterpriseDto sysEnterpriseDto = new SysEnterpriseDto();

        sysEnterpriseDto.setId( arg0.getId() );
        sysEnterpriseDto.setSourceName( arg0.getSourceName() );
        sysEnterpriseDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEnterpriseDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEnterpriseDto.setStatus( arg0.getStatus() );
        sysEnterpriseDto.setSort( arg0.getSort() );
        sysEnterpriseDto.setRemark( arg0.getRemark() );
        sysEnterpriseDto.setCreateBy( arg0.getCreateBy() );
        sysEnterpriseDto.setCreateTime( arg0.getCreateTime() );
        sysEnterpriseDto.setUpdateBy( arg0.getUpdateBy() );
        sysEnterpriseDto.setUpdateTime( arg0.getUpdateTime() );
        sysEnterpriseDto.setDelFlag( arg0.getDelFlag() );
        sysEnterpriseDto.setCreateName( arg0.getCreateName() );
        sysEnterpriseDto.setUpdateName( arg0.getUpdateName() );
        sysEnterpriseDto.setStrategyId( arg0.getStrategyId() );
        sysEnterpriseDto.setName( arg0.getName() );
        sysEnterpriseDto.setSystemName( arg0.getSystemName() );
        sysEnterpriseDto.setNick( arg0.getNick() );
        sysEnterpriseDto.setLogo( arg0.getLogo() );
        sysEnterpriseDto.setIsLessor( arg0.getIsLessor() );
        sysEnterpriseDto.setNameFrequency( arg0.getNameFrequency() );
        sysEnterpriseDto.setIsDefault( arg0.getIsDefault() );

        return sysEnterpriseDto;
    }

    @Override
    public List<SysEnterpriseDto> mapperDto(Collection<SysEnterprisePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEnterpriseDto> list = new ArrayList<SysEnterpriseDto>( arg0.size() );
        for ( SysEnterprisePo sysEnterprisePo : arg0 ) {
            list.add( mapperDto( sysEnterprisePo ) );
        }

        return list;
    }

    @Override
    public Page<SysEnterpriseDto> mapperPageDto(Collection<SysEnterprisePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEnterpriseDto> page = new Page<SysEnterpriseDto>();
        for ( SysEnterprisePo sysEnterprisePo : arg0 ) {
            page.add( mapperDto( sysEnterprisePo ) );
        }

        return page;
    }

    @Override
    public SysEnterprisePo mapperPo(SysEnterpriseDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEnterprisePo sysEnterprisePo = new SysEnterprisePo();

        sysEnterprisePo.setId( arg0.getId() );
        sysEnterprisePo.setSourceName( arg0.getSourceName() );
        sysEnterprisePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEnterprisePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEnterprisePo.setStatus( arg0.getStatus() );
        sysEnterprisePo.setSort( arg0.getSort() );
        sysEnterprisePo.setRemark( arg0.getRemark() );
        sysEnterprisePo.setCreateBy( arg0.getCreateBy() );
        sysEnterprisePo.setCreateTime( arg0.getCreateTime() );
        sysEnterprisePo.setUpdateBy( arg0.getUpdateBy() );
        sysEnterprisePo.setUpdateTime( arg0.getUpdateTime() );
        sysEnterprisePo.setDelFlag( arg0.getDelFlag() );
        sysEnterprisePo.setCreateName( arg0.getCreateName() );
        sysEnterprisePo.setUpdateName( arg0.getUpdateName() );
        sysEnterprisePo.setStrategyId( arg0.getStrategyId() );
        sysEnterprisePo.setName( arg0.getName() );
        sysEnterprisePo.setSystemName( arg0.getSystemName() );
        sysEnterprisePo.setNick( arg0.getNick() );
        sysEnterprisePo.setLogo( arg0.getLogo() );
        sysEnterprisePo.setIsLessor( arg0.getIsLessor() );
        sysEnterprisePo.setNameFrequency( arg0.getNameFrequency() );
        sysEnterprisePo.setIsDefault( arg0.getIsDefault() );

        return sysEnterprisePo;
    }

    @Override
    public List<SysEnterprisePo> mapperPo(Collection<SysEnterpriseDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEnterprisePo> list = new ArrayList<SysEnterprisePo>( arg0.size() );
        for ( SysEnterpriseDto sysEnterpriseDto : arg0 ) {
            list.add( mapperPo( sysEnterpriseDto ) );
        }

        return list;
    }

    @Override
    public Page<SysEnterprisePo> mapperPagePo(Collection<SysEnterpriseDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEnterprisePo> page = new Page<SysEnterprisePo>();
        for ( SysEnterpriseDto sysEnterpriseDto : arg0 ) {
            page.add( mapperPo( sysEnterpriseDto ) );
        }

        return page;
    }
}
