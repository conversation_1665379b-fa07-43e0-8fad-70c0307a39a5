package com.heju.system.notice.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.notice.domain.dto.SysNoticeDto;
import com.heju.system.notice.domain.po.SysNoticePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysNoticeConverterImpl implements SysNoticeConverter {

    @Override
    public SysNoticeDto mapperDto(SysNoticePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysNoticeDto sysNoticeDto = new SysNoticeDto();

        sysNoticeDto.setId( arg0.getId() );
        sysNoticeDto.setSourceName( arg0.getSourceName() );
        sysNoticeDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysNoticeDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysNoticeDto.setName( arg0.getName() );
        sysNoticeDto.setSort( arg0.getSort() );
        sysNoticeDto.setRemark( arg0.getRemark() );
        sysNoticeDto.setCreateBy( arg0.getCreateBy() );
        sysNoticeDto.setCreateTime( arg0.getCreateTime() );
        sysNoticeDto.setUpdateBy( arg0.getUpdateBy() );
        sysNoticeDto.setUpdateTime( arg0.getUpdateTime() );
        sysNoticeDto.setDelFlag( arg0.getDelFlag() );
        sysNoticeDto.setCreateName( arg0.getCreateName() );
        sysNoticeDto.setUpdateName( arg0.getUpdateName() );
        sysNoticeDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysNoticeDto.setType( arg0.getType() );
        sysNoticeDto.setContent( arg0.getContent() );
        sysNoticeDto.setStatus( arg0.getStatus() );

        return sysNoticeDto;
    }

    @Override
    public List<SysNoticeDto> mapperDto(Collection<SysNoticePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysNoticeDto> list = new ArrayList<SysNoticeDto>( arg0.size() );
        for ( SysNoticePo sysNoticePo : arg0 ) {
            list.add( mapperDto( sysNoticePo ) );
        }

        return list;
    }

    @Override
    public Page<SysNoticeDto> mapperPageDto(Collection<SysNoticePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysNoticeDto> page = new Page<SysNoticeDto>();
        for ( SysNoticePo sysNoticePo : arg0 ) {
            page.add( mapperDto( sysNoticePo ) );
        }

        return page;
    }

    @Override
    public SysNoticePo mapperPo(SysNoticeDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysNoticePo sysNoticePo = new SysNoticePo();

        sysNoticePo.setId( arg0.getId() );
        sysNoticePo.setSourceName( arg0.getSourceName() );
        sysNoticePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysNoticePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysNoticePo.setName( arg0.getName() );
        sysNoticePo.setSort( arg0.getSort() );
        sysNoticePo.setRemark( arg0.getRemark() );
        sysNoticePo.setCreateBy( arg0.getCreateBy() );
        sysNoticePo.setCreateTime( arg0.getCreateTime() );
        sysNoticePo.setUpdateBy( arg0.getUpdateBy() );
        sysNoticePo.setUpdateTime( arg0.getUpdateTime() );
        sysNoticePo.setDelFlag( arg0.getDelFlag() );
        sysNoticePo.setCreateName( arg0.getCreateName() );
        sysNoticePo.setUpdateName( arg0.getUpdateName() );
        sysNoticePo.setEnterpriseId( arg0.getEnterpriseId() );
        sysNoticePo.setType( arg0.getType() );
        sysNoticePo.setContent( arg0.getContent() );
        sysNoticePo.setStatus( arg0.getStatus() );

        return sysNoticePo;
    }

    @Override
    public List<SysNoticePo> mapperPo(Collection<SysNoticeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysNoticePo> list = new ArrayList<SysNoticePo>( arg0.size() );
        for ( SysNoticeDto sysNoticeDto : arg0 ) {
            list.add( mapperPo( sysNoticeDto ) );
        }

        return list;
    }

    @Override
    public Page<SysNoticePo> mapperPagePo(Collection<SysNoticeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysNoticePo> page = new Page<SysNoticePo>();
        for ( SysNoticeDto sysNoticeDto : arg0 ) {
            page.add( mapperPo( sysNoticeDto ) );
        }

        return page;
    }
}
