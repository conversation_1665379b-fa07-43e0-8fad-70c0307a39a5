09:05:56.446 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:05:57.497 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dd5e1bce-6673-440d-b3df-35e3bc65347e_config-0
09:05:57.608 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 41 ms to scan 1 urls, producing 3 keys and 6 values 
09:05:57.669 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:05:57.680 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:05:57.694 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:05:57.708 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:05:57.724 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:05:57.729 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd5e1bce-6673-440d-b3df-35e3bc65347e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:05:57.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd5e1bce-6673-440d-b3df-35e3bc65347e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002ad1139f1c0
09:05:57.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd5e1bce-6673-440d-b3df-35e3bc65347e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002ad1139f3e0
09:05:57.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd5e1bce-6673-440d-b3df-35e3bc65347e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:05:57.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd5e1bce-6673-440d-b3df-35e3bc65347e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:05:57.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd5e1bce-6673-440d-b3df-35e3bc65347e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:05:59.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd5e1bce-6673-440d-b3df-35e3bc65347e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754528759072_127.0.0.1_1736
09:05:59.300 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd5e1bce-6673-440d-b3df-35e3bc65347e_config-0] Notify connected event to listeners.
09:05:59.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd5e1bce-6673-440d-b3df-35e3bc65347e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:05:59.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd5e1bce-6673-440d-b3df-35e3bc65347e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002ad11518fb0
09:05:59.450 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:06:06.969 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:06:06.971 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:06:06.972 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:06:07.400 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:06:08.626 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:06:08.628 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:06:08.628 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:06:18.037 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:06:24.208 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f63fffe8-7f5c-48f3-9463-918f69923e8b
09:06:24.208 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63fffe8-7f5c-48f3-9463-918f69923e8b] RpcClient init label, labels = {module=naming, source=sdk}
09:06:24.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63fffe8-7f5c-48f3-9463-918f69923e8b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:06:24.214 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63fffe8-7f5c-48f3-9463-918f69923e8b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:06:24.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63fffe8-7f5c-48f3-9463-918f69923e8b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:06:24.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63fffe8-7f5c-48f3-9463-918f69923e8b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:24.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63fffe8-7f5c-48f3-9463-918f69923e8b] Success to connect to server [localhost:8848] on start up, connectionId = 1754528784229_127.0.0.1_2221
09:06:24.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63fffe8-7f5c-48f3-9463-918f69923e8b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:24.363 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63fffe8-7f5c-48f3-9463-918f69923e8b] Notify connected event to listeners.
09:06:24.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63fffe8-7f5c-48f3-9463-918f69923e8b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002ad11518fb0
09:06:24.445 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:06:24.513 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:06:24.795 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 29.027 seconds (JVM running for 30.405)
09:06:24.844 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:06:24.844 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:06:24.844 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:06:24.985 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63fffe8-7f5c-48f3-9463-918f69923e8b] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:06:25.007 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63fffe8-7f5c-48f3-9463-918f69923e8b] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:06:25.207 [RMI TCP Connection(12)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:15:07.333 [nacos-grpc-client-executor-115] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63fffe8-7f5c-48f3-9463-918f69923e8b] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:15:07.333 [nacos-grpc-client-executor-115] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f63fffe8-7f5c-48f3-9463-918f69923e8b] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:15:08.963 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:15:08.975 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:15:09.363 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:15:09.363 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
10:08:31.788 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:08:31.795 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:08:32.126 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:08:32.126 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@29793c3a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:08:32.127 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754528784229_127.0.0.1_2221
10:08:32.130 [nacos-grpc-client-executor-596] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754528784229_127.0.0.1_2221]Ignore complete event,isRunning:false,isAbandon=false
10:08:32.140 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6242b82c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 597]
10:08:32.357 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:08:32.381 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:08:32.396 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:08:32.397 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:08:32.400 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:08:32.401 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:08:32.403 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:08:32.403 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:09:04.706 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:09:05.687 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d8afe242-28fa-45dc-871e-6f3487b765b5_config-0
10:09:05.771 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
10:09:05.819 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
10:09:05.819 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
10:09:05.841 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 1 keys and 5 values 
10:09:05.851 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
10:09:05.853 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
10:09:05.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8afe242-28fa-45dc-871e-6f3487b765b5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:09:05.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8afe242-28fa-45dc-871e-6f3487b765b5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001cc013b8fc8
10:09:05.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8afe242-28fa-45dc-871e-6f3487b765b5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001cc013b91e8
10:09:05.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8afe242-28fa-45dc-871e-6f3487b765b5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:09:05.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8afe242-28fa-45dc-871e-6f3487b765b5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:09:05.880 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8afe242-28fa-45dc-871e-6f3487b765b5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:09:07.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8afe242-28fa-45dc-871e-6f3487b765b5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754532546776_127.0.0.1_6930
10:09:07.049 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8afe242-28fa-45dc-871e-6f3487b765b5_config-0] Notify connected event to listeners.
10:09:07.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8afe242-28fa-45dc-871e-6f3487b765b5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:09:07.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8afe242-28fa-45dc-871e-6f3487b765b5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001cc014f0fb0
10:09:07.312 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:09:11.038 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:09:11.038 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:09:11.038 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:09:11.163 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:09:11.721 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:09:11.721 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:09:11.721 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:09:18.980 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:09:21.217 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d565f271-93cb-4d76-9151-3edb14a94053
10:09:21.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d565f271-93cb-4d76-9151-3edb14a94053] RpcClient init label, labels = {module=naming, source=sdk}
10:09:21.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d565f271-93cb-4d76-9151-3edb14a94053] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:09:21.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d565f271-93cb-4d76-9151-3edb14a94053] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:09:21.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d565f271-93cb-4d76-9151-3edb14a94053] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:09:21.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d565f271-93cb-4d76-9151-3edb14a94053] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:09:21.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d565f271-93cb-4d76-9151-3edb14a94053] Success to connect to server [localhost:8848] on start up, connectionId = 1754532561231_127.0.0.1_7028
10:09:21.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d565f271-93cb-4d76-9151-3edb14a94053] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:09:21.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d565f271-93cb-4d76-9151-3edb14a94053] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001cc014f0fb0
10:09:21.343 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d565f271-93cb-4d76-9151-3edb14a94053] Notify connected event to listeners.
10:09:21.391 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:09:21.416 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:09:21.504 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 17.63 seconds (JVM running for 19.892)
10:09:21.520 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:09:21.520 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:09:21.520 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:09:21.913 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d565f271-93cb-4d76-9151-3edb14a94053] Receive server push request, request = NotifySubscriberRequest, requestId = 19
10:09:21.929 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d565f271-93cb-4d76-9151-3edb14a94053] Ack server push request, request = NotifySubscriberRequest, requestId = 19
10:09:53.213 [http-nio-9600-exec-6] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:09:55.213 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:09:55.213 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:11:30.348 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:11:30.348 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:11:30.689 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:11:30.689 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@66c7d23a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:11:30.689 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754532561231_127.0.0.1_7028
10:11:30.691 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754532561231_127.0.0.1_7028]Ignore complete event,isRunning:false,isAbandon=false
10:11:30.696 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@699a9388[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 37]
10:11:30.868 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:11:30.868 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:11:30.876 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:11:30.876 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:11:30.876 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:11:30.876 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:11:35.984 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:11:36.542 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f828e8c7-04ee-4527-9eda-153959d83a5f_config-0
10:11:36.588 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 6 values 
10:11:36.613 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
10:11:36.620 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
10:11:36.627 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
10:11:36.636 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
10:11:36.644 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
10:11:36.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:11:36.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001a9c03cdd70
10:11:36.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001a9c03cdf90
10:11:36.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:11:36.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:11:36.657 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:11:37.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754532697155_127.0.0.1_8058
10:11:37.335 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] Notify connected event to listeners.
10:11:37.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:11:37.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a9c0507b88
10:11:37.416 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:11:39.937 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:11:39.939 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:11:39.939 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:11:40.055 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:11:40.693 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:11:40.694 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:11:40.694 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:11:50.232 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:11:54.599 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e8fb11e1-dd3b-4c84-9606-f669b1cb839b
10:11:54.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] RpcClient init label, labels = {module=naming, source=sdk}
10:11:54.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:11:54.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:11:54.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:11:54.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:11:54.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Success to connect to server [localhost:8848] on start up, connectionId = 1754532714612_127.0.0.1_8159
10:11:54.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:11:54.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a9c0507b88
10:11:54.738 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Notify connected event to listeners.
10:11:54.795 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:11:54.839 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:11:55.058 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.559 seconds (JVM running for 20.388)
10:11:55.081 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:11:55.083 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:11:55.083 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:11:55.267 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 26
10:11:55.284 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 26
10:11:55.704 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:12:10.017 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:12:10.017 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:36:30.489 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Server healthy check fail, currentConnection = 1754532714612_127.0.0.1_8159
11:36:30.489 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] Server healthy check fail, currentConnection = 1754532697155_127.0.0.1_8058
11:36:30.493 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:36:30.493 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:36:32.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Success to connect a server [localhost:8848], connectionId = 1754537792058_127.0.0.1_6021
11:36:32.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] Success to connect a server [localhost:8848], connectionId = 1754537792058_127.0.0.1_6020
11:36:32.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Abandon prev connection, server is localhost:8848, connectionId is 1754532714612_127.0.0.1_8159
11:36:32.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1754532697155_127.0.0.1_8058
11:36:32.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754532697155_127.0.0.1_8058
11:36:32.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754532714612_127.0.0.1_8159
11:36:32.178 [nacos-grpc-client-executor-932] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754532697155_127.0.0.1_8058]Ignore complete event,isRunning:false,isAbandon=true
11:36:32.178 [nacos-grpc-client-executor-924] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754532714612_127.0.0.1_8159]Ignore complete event,isRunning:false,isAbandon=true
11:36:32.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] Notify disconnected event to listeners
11:36:32.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f828e8c7-04ee-4527-9eda-153959d83a5f_config-0] Notify connected event to listeners.
11:36:32.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Notify disconnected event to listeners
11:36:32.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Notify connected event to listeners.
11:36:35.533 [nacos-grpc-client-executor-927] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 33
11:37:17.995 [nacos-grpc-client-executor-927] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 33
11:37:18.012 [nacos-grpc-client-executor-930] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 35
11:37:18.012 [nacos-grpc-client-executor-930] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 35
11:44:15.034 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Server healthy check fail, currentConnection = 1754537792058_127.0.0.1_6021
11:44:15.034 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:44:15.045 [nacos-grpc-client-executor-931] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 36
11:44:15.047 [nacos-grpc-client-executor-931] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 36
11:44:15.053 [nacos-grpc-client-executor-932] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 37
11:44:15.054 [nacos-grpc-client-executor-932] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 37
11:44:15.059 [nacos-grpc-client-executor-934] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 38
11:44:15.059 [nacos-grpc-client-executor-934] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 38
11:44:15.062 [nacos-grpc-client-executor-937] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 39
11:44:15.065 [nacos-grpc-client-executor-937] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 39
11:44:15.070 [nacos-grpc-client-executor-938] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 40
11:44:15.071 [nacos-grpc-client-executor-938] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 40
11:44:15.071 [nacos-grpc-client-executor-939] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 41
11:44:15.071 [nacos-grpc-client-executor-939] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 41
11:44:15.071 [nacos-grpc-client-executor-940] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 42
11:44:15.071 [nacos-grpc-client-executor-940] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 42
11:44:15.088 [nacos-grpc-client-executor-941] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 43
11:44:15.089 [nacos-grpc-client-executor-941] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 43
11:44:15.091 [nacos-grpc-client-executor-942] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 44
11:44:15.091 [nacos-grpc-client-executor-942] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 44
11:44:15.099 [nacos-grpc-client-executor-943] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 45
11:44:15.099 [nacos-grpc-client-executor-943] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 45
11:44:15.103 [nacos-grpc-client-executor-944] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 46
11:44:15.103 [nacos-grpc-client-executor-944] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 46
11:44:15.103 [nacos-grpc-client-executor-945] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 47
11:44:15.103 [nacos-grpc-client-executor-945] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 47
11:44:15.114 [nacos-grpc-client-executor-946] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 48
11:44:15.114 [nacos-grpc-client-executor-946] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 48
11:44:15.114 [nacos-grpc-client-executor-947] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 49
11:44:15.114 [nacos-grpc-client-executor-947] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 49
11:44:15.125 [nacos-grpc-client-executor-948] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 50
11:44:15.125 [nacos-grpc-client-executor-948] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 50
11:44:15.125 [nacos-grpc-client-executor-949] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 51
11:44:15.125 [nacos-grpc-client-executor-949] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 51
11:44:15.125 [nacos-grpc-client-executor-950] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 52
11:44:15.125 [nacos-grpc-client-executor-950] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 52
11:44:15.142 [nacos-grpc-client-executor-951] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 53
11:44:15.142 [nacos-grpc-client-executor-951] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 53
11:44:15.148 [nacos-grpc-client-executor-952] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 54
11:44:15.148 [nacos-grpc-client-executor-952] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 54
11:44:15.154 [nacos-grpc-client-executor-953] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 55
11:44:15.154 [nacos-grpc-client-executor-953] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 55
11:44:15.159 [nacos-grpc-client-executor-954] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 56
11:44:15.159 [nacos-grpc-client-executor-954] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 56
11:44:15.166 [nacos-grpc-client-executor-955] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 57
11:44:15.166 [nacos-grpc-client-executor-955] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 57
11:44:15.171 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Success to connect a server [localhost:8848], connectionId = 1754538255054_127.0.0.1_8183
11:44:15.171 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Abandon prev connection, server is localhost:8848, connectionId is 1754537792058_127.0.0.1_6021
11:44:15.171 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754537792058_127.0.0.1_6021
11:44:15.171 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Notify disconnected event to listeners
11:44:15.171 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Notify connected event to listeners.
11:44:15.171 [nacos-grpc-client-executor-956] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 58
11:44:15.175 [nacos-grpc-client-executor-956] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 58
11:44:15.175 [nacos-grpc-client-executor-957] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 59
11:44:15.175 [nacos-grpc-client-executor-957] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 59
11:44:15.175 [nacos-grpc-client-executor-958] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 60
11:44:15.175 [nacos-grpc-client-executor-958] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 60
11:44:15.175 [nacos-grpc-client-executor-959] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 61
11:44:15.175 [nacos-grpc-client-executor-959] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 61
11:44:15.175 [nacos-grpc-client-executor-960] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 62
11:44:15.175 [nacos-grpc-client-executor-960] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 62
11:44:15.175 [nacos-grpc-client-executor-961] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 63
11:44:15.175 [nacos-grpc-client-executor-961] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 63
11:44:15.175 [nacos-grpc-client-executor-962] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 64
11:44:15.175 [nacos-grpc-client-executor-962] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 64
11:44:15.182 [nacos-grpc-client-executor-963] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 65
11:44:15.182 [nacos-grpc-client-executor-963] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 65
11:44:15.183 [nacos-grpc-client-executor-964] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 66
11:44:15.183 [nacos-grpc-client-executor-964] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 66
11:44:15.183 [nacos-grpc-client-executor-965] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 67
11:44:15.183 [nacos-grpc-client-executor-965] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 67
11:44:15.183 [nacos-grpc-client-executor-966] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 68
11:44:15.183 [nacos-grpc-client-executor-966] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 68
11:44:15.183 [nacos-grpc-client-executor-967] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 69
11:44:15.183 [nacos-grpc-client-executor-967] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 69
11:44:15.183 [nacos-grpc-client-executor-968] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 70
11:44:15.183 [nacos-grpc-client-executor-968] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 70
11:44:15.183 [nacos-grpc-client-executor-969] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 71
11:44:15.183 [nacos-grpc-client-executor-969] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 71
11:44:15.188 [nacos-grpc-client-executor-970] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 72
11:44:15.188 [nacos-grpc-client-executor-970] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 72
11:44:15.189 [nacos-grpc-client-executor-971] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 73
11:44:15.189 [nacos-grpc-client-executor-971] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 73
11:44:15.189 [nacos-grpc-client-executor-972] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 74
11:44:15.189 [nacos-grpc-client-executor-972] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 74
11:44:15.189 [nacos-grpc-client-executor-973] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 75
11:44:15.189 [nacos-grpc-client-executor-973] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 75
11:44:15.189 [nacos-grpc-client-executor-974] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 76
11:44:15.189 [nacos-grpc-client-executor-974] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 76
11:44:15.189 [nacos-grpc-client-executor-975] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 77
11:44:15.194 [nacos-grpc-client-executor-975] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 77
11:44:15.194 [nacos-grpc-client-executor-976] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 78
11:44:15.194 [nacos-grpc-client-executor-976] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 78
11:44:15.194 [nacos-grpc-client-executor-977] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 79
11:44:15.194 [nacos-grpc-client-executor-977] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 79
11:44:15.196 [nacos-grpc-client-executor-978] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 80
11:44:15.196 [nacos-grpc-client-executor-978] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 80
11:44:15.196 [nacos-grpc-client-executor-979] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 81
11:44:15.196 [nacos-grpc-client-executor-979] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 81
11:44:15.199 [nacos-grpc-client-executor-980] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 82
11:44:15.199 [nacos-grpc-client-executor-980] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 82
11:44:15.199 [nacos-grpc-client-executor-981] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 83
11:44:15.199 [nacos-grpc-client-executor-981] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 83
11:44:15.203 [nacos-grpc-client-executor-982] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 84
11:44:15.203 [nacos-grpc-client-executor-982] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 84
11:44:15.203 [nacos-grpc-client-executor-983] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 85
11:44:15.203 [nacos-grpc-client-executor-983] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 85
11:44:15.203 [nacos-grpc-client-executor-984] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 86
11:44:15.203 [nacos-grpc-client-executor-984] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 86
11:44:15.207 [nacos-grpc-client-executor-985] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 87
11:44:15.207 [nacos-grpc-client-executor-985] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 87
11:44:15.207 [nacos-grpc-client-executor-986] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 88
11:44:15.207 [nacos-grpc-client-executor-986] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 88
11:44:15.207 [nacos-grpc-client-executor-987] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 89
11:44:15.207 [nacos-grpc-client-executor-987] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 89
11:44:15.207 [nacos-grpc-client-executor-988] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 90
11:44:15.210 [nacos-grpc-client-executor-988] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 90
11:44:15.210 [nacos-grpc-client-executor-989] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 91
11:44:15.210 [nacos-grpc-client-executor-989] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 91
11:44:15.210 [nacos-grpc-client-executor-990] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 92
11:44:15.211 [nacos-grpc-client-executor-990] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 92
11:44:15.211 [nacos-grpc-client-executor-991] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 93
11:44:15.211 [nacos-grpc-client-executor-991] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 93
11:44:15.211 [nacos-grpc-client-executor-992] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 94
11:44:15.211 [nacos-grpc-client-executor-992] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 94
11:44:15.211 [nacos-grpc-client-executor-993] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 95
11:44:15.211 [nacos-grpc-client-executor-993] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 95
11:44:15.211 [nacos-grpc-client-executor-994] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 96
11:44:15.215 [nacos-grpc-client-executor-994] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 96
11:44:15.215 [nacos-grpc-client-executor-995] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 97
11:44:15.215 [nacos-grpc-client-executor-995] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 97
11:44:15.215 [nacos-grpc-client-executor-996] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 98
11:44:15.215 [nacos-grpc-client-executor-996] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 98
11:44:15.215 [nacos-grpc-client-executor-997] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 99
11:44:15.215 [nacos-grpc-client-executor-997] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 99
11:44:15.215 [nacos-grpc-client-executor-998] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 100
11:44:15.215 [nacos-grpc-client-executor-998] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 100
11:44:15.215 [nacos-grpc-client-executor-999] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 101
11:44:15.215 [nacos-grpc-client-executor-999] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 101
11:44:15.219 [nacos-grpc-client-executor-1000] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 102
11:44:15.219 [nacos-grpc-client-executor-1000] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 102
11:44:15.219 [nacos-grpc-client-executor-1001] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 103
11:44:15.219 [nacos-grpc-client-executor-1001] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 103
11:44:15.219 [nacos-grpc-client-executor-1002] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 104
11:44:15.219 [nacos-grpc-client-executor-1002] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 104
11:44:15.222 [nacos-grpc-client-executor-1003] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 105
11:44:15.222 [nacos-grpc-client-executor-1003] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 105
11:44:15.222 [nacos-grpc-client-executor-1004] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 106
11:44:15.222 [nacos-grpc-client-executor-1004] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 106
11:44:15.222 [nacos-grpc-client-executor-1005] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 107
11:44:15.222 [nacos-grpc-client-executor-1005] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 107
11:44:15.222 [nacos-grpc-client-executor-1006] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 108
11:44:15.222 [nacos-grpc-client-executor-1006] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 108
11:44:15.222 [nacos-grpc-client-executor-1007] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 109
11:44:15.222 [nacos-grpc-client-executor-1007] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 109
11:44:15.222 [nacos-grpc-client-executor-1008] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 110
11:44:15.222 [nacos-grpc-client-executor-1008] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 110
11:44:15.222 [nacos-grpc-client-executor-1008] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754537792058_127.0.0.1_6021]Ignore complete event,isRunning:true,isAbandon=true
11:44:18.579 [nacos-grpc-client-executor-1011] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Receive server push request, request = NotifySubscriberRequest, requestId = 114
11:44:18.579 [nacos-grpc-client-executor-1011] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8fb11e1-dd3b-4c84-9606-f669b1cb839b] Ack server push request, request = NotifySubscriberRequest, requestId = 114
11:55:32.839 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:55:32.839 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:55:33.171 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:55:33.171 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4099bc0b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:55:33.171 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754538255054_127.0.0.1_8183
11:55:33.171 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 1148]
11:55:33.326 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:55:33.326 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:55:33.343 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:55:33.356 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:55:33.359 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:55:33.359 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:55:58.760 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:55:59.399 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4a796ff6-867e-42ed-8ccd-3e17c5bc9511_config-0
11:55:59.472 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 3 keys and 6 values 
11:55:59.500 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 4 keys and 9 values 
11:55:59.508 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
11:55:59.517 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
11:55:59.526 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
11:55:59.533 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
11:55:59.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a796ff6-867e-42ed-8ccd-3e17c5bc9511_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:55:59.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a796ff6-867e-42ed-8ccd-3e17c5bc9511_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000025db43b9e68
11:55:59.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a796ff6-867e-42ed-8ccd-3e17c5bc9511_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000025db43ba088
11:55:59.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a796ff6-867e-42ed-8ccd-3e17c5bc9511_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:55:59.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a796ff6-867e-42ed-8ccd-3e17c5bc9511_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:55:59.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a796ff6-867e-42ed-8ccd-3e17c5bc9511_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:56:00.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a796ff6-867e-42ed-8ccd-3e17c5bc9511_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754538960000_127.0.0.1_9712
11:56:00.211 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a796ff6-867e-42ed-8ccd-3e17c5bc9511_config-0] Notify connected event to listeners.
11:56:00.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a796ff6-867e-42ed-8ccd-3e17c5bc9511_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:56:00.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a796ff6-867e-42ed-8ccd-3e17c5bc9511_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000025db44f20a0
11:56:00.308 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:56:02.855 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:56:02.855 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:56:02.855 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:56:02.971 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:56:03.500 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:56:03.500 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:56:03.500 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:56:08.739 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:56:10.777 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 27b7a097-7772-4894-9e09-3c527f8aff55
11:56:10.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27b7a097-7772-4894-9e09-3c527f8aff55] RpcClient init label, labels = {module=naming, source=sdk}
11:56:10.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27b7a097-7772-4894-9e09-3c527f8aff55] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:56:10.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27b7a097-7772-4894-9e09-3c527f8aff55] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:56:10.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27b7a097-7772-4894-9e09-3c527f8aff55] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:56:10.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27b7a097-7772-4894-9e09-3c527f8aff55] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:56:10.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27b7a097-7772-4894-9e09-3c527f8aff55] Success to connect to server [localhost:8848] on start up, connectionId = 1754538970785_127.0.0.1_9728
11:56:10.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27b7a097-7772-4894-9e09-3c527f8aff55] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:56:10.895 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27b7a097-7772-4894-9e09-3c527f8aff55] Notify connected event to listeners.
11:56:10.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27b7a097-7772-4894-9e09-3c527f8aff55] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000025db44f20a0
11:56:10.933 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:56:10.949 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:56:11.042 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.829 seconds (JVM running for 14.792)
11:56:11.047 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:56:11.047 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:56:11.047 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:56:11.500 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27b7a097-7772-4894-9e09-3c527f8aff55] Receive server push request, request = NotifySubscriberRequest, requestId = 121
11:56:11.517 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27b7a097-7772-4894-9e09-3c527f8aff55] Ack server push request, request = NotifySubscriberRequest, requestId = 121
11:56:31.712 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:56:32.782 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:56:32.782 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:12:07.178 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:12:07.180 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:12:07.502 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:12:07.502 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@128fd8a0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:12:07.503 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754538970785_127.0.0.1_9728
14:12:07.504 [nacos-grpc-client-executor-1636] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754538970785_127.0.0.1_9728]Ignore complete event,isRunning:false,isAbandon=false
14:12:07.505 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@25e9d33[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1637]
14:12:07.674 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:12:07.676 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:12:07.687 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:12:07.687 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:12:07.687 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:12:07.687 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:12:13.091 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:12:13.650 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7ecff078-f88a-4091-9a44-56360ec0932e_config-0
14:12:13.704 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 3 keys and 6 values 
14:12:13.732 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
14:12:13.738 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
14:12:13.745 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
14:12:13.751 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
14:12:13.759 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
14:12:13.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ecff078-f88a-4091-9a44-56360ec0932e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:12:13.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ecff078-f88a-4091-9a44-56360ec0932e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000176113ceaf8
14:12:13.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ecff078-f88a-4091-9a44-56360ec0932e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000176113ced18
14:12:13.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ecff078-f88a-4091-9a44-56360ec0932e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:12:13.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ecff078-f88a-4091-9a44-56360ec0932e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:12:13.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ecff078-f88a-4091-9a44-56360ec0932e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:12:14.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ecff078-f88a-4091-9a44-56360ec0932e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754547134303_127.0.0.1_6319
14:12:14.490 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ecff078-f88a-4091-9a44-56360ec0932e_config-0] Notify connected event to listeners.
14:12:14.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ecff078-f88a-4091-9a44-56360ec0932e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:12:14.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ecff078-f88a-4091-9a44-56360ec0932e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017611508668
14:12:14.578 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:12:17.097 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:12:17.097 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:12:17.097 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:12:17.212 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:12:17.745 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:12:17.745 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:12:17.746 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:12:26.978 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:12:32.018 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a8689318-7bba-479f-b1d7-0974cb71a3b1
14:12:32.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8689318-7bba-479f-b1d7-0974cb71a3b1] RpcClient init label, labels = {module=naming, source=sdk}
14:12:32.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8689318-7bba-479f-b1d7-0974cb71a3b1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:12:32.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8689318-7bba-479f-b1d7-0974cb71a3b1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:12:32.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8689318-7bba-479f-b1d7-0974cb71a3b1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:12:32.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8689318-7bba-479f-b1d7-0974cb71a3b1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:12:32.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8689318-7bba-479f-b1d7-0974cb71a3b1] Success to connect to server [localhost:8848] on start up, connectionId = 1754547152035_127.0.0.1_6345
14:12:32.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8689318-7bba-479f-b1d7-0974cb71a3b1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:12:32.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8689318-7bba-479f-b1d7-0974cb71a3b1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017611508668
14:12:32.170 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8689318-7bba-479f-b1d7-0974cb71a3b1] Notify connected event to listeners.
14:12:32.238 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:12:32.278 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:12:32.456 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.862 seconds (JVM running for 20.834)
14:12:32.478 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:12:32.479 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:12:32.480 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:12:32.801 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8689318-7bba-479f-b1d7-0974cb71a3b1] Receive server push request, request = NotifySubscriberRequest, requestId = 130
14:12:32.817 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a8689318-7bba-479f-b1d7-0974cb71a3b1] Ack server push request, request = NotifySubscriberRequest, requestId = 130
14:12:32.981 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:12:45.863 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:12:45.863 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:33:39.868 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:33:39.880 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:33:40.194 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:33:40.194 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3ec61fd2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:33:40.199 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754547152035_127.0.0.1_6345
14:33:40.201 [nacos-grpc-client-executor-104] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754547152035_127.0.0.1_6345]Ignore complete event,isRunning:false,isAbandon=false
14:33:40.203 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7d5706a1[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 105]
14:33:40.386 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:33:40.391 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:33:40.392 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:33:40.392 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:33:40.392 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:33:40.392 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:33:47.038 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:33:48.095 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0
14:33:48.151 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
14:33:48.193 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
14:33:48.215 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
14:33:48.233 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:33:48.244 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:33:48.255 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:33:48.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:33:48.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d90139ed38
14:33:48.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001d90139ef58
14:33:48.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:33:48.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:33:48.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:33:49.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754548429487_127.0.0.1_8124
14:33:49.770 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] Notify connected event to listeners.
14:33:49.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:33:49.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d901518668
14:33:49.949 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:33:55.762 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:33:55.763 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:33:55.763 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:33:56.050 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:33:56.961 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:33:56.963 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:33:56.963 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:34:10.348 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:34:15.706 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3b363e3d-17a1-4b4d-a953-35e4d06fb152
14:34:15.706 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] RpcClient init label, labels = {module=naming, source=sdk}
14:34:15.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:34:15.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:34:15.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:34:15.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:34:15.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Success to connect to server [localhost:8848] on start up, connectionId = 1754548455724_127.0.0.1_8191
14:34:15.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:15.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d901518668
14:34:15.848 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Notify connected event to listeners.
14:34:15.919 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:34:15.970 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:34:16.269 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 30.259 seconds (JVM running for 31.289)
14:34:16.307 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:34:16.308 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:34:16.309 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:34:16.397 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Receive server push request, request = NotifySubscriberRequest, requestId = 137
14:34:16.415 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Ack server push request, request = NotifySubscriberRequest, requestId = 137
14:34:16.526 [RMI TCP Connection(23)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:34:40.546 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:34:40.546 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:35:59.140 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Server healthy check fail, currentConnection = 1754548455724_127.0.0.1_8191
14:35:59.140 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] Server healthy check fail, currentConnection = 1754548429487_127.0.0.1_8124
14:36:00.470 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:36:00.470 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:36:07.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] Success to connect a server [localhost:8848], connectionId = 1754548567229_127.0.0.1_8378
14:36:07.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1754548429487_127.0.0.1_8124
14:36:07.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Success to connect a server [localhost:8848], connectionId = 1754548567228_127.0.0.1_8377
14:36:07.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754548429487_127.0.0.1_8124
14:36:07.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Abandon prev connection, server is localhost:8848, connectionId is 1754548455724_127.0.0.1_8191
14:36:07.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754548455724_127.0.0.1_8191
14:36:07.345 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Notify disconnected event to listeners
14:36:07.345 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] Notify disconnected event to listeners
14:36:07.345 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0d6ebe3a-a44e-4582-9b0f-9a57a0f754fc_config-0] Notify connected event to listeners.
14:36:07.347 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Notify connected event to listeners.
14:36:07.930 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Receive server push request, request = NotifySubscriberRequest, requestId = 140
14:36:07.930 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b363e3d-17a1-4b4d-a953-35e4d06fb152] Ack server push request, request = NotifySubscriberRequest, requestId = 140
14:38:54.142 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:38:54.143 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:38:54.487 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:38:54.487 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7e050f71[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:38:54.488 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754548567228_127.0.0.1_8377
14:38:54.489 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1aae98a6[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 54]
14:38:54.635 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:38:54.639 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:38:54.646 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:38:54.646 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:38:54.652 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:38:54.652 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:38:59.979 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:39:00.553 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0
14:39:00.603 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
14:39:00.631 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
14:39:00.637 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
14:39:00.644 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
14:39:00.653 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:39:00.664 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:39:00.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:39:00.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000269df3cf1c0
14:39:00.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000269df3cf3e0
14:39:00.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:39:00.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:39:00.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:39:01.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754548741201_127.0.0.1_8651
14:39:01.380 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] Notify connected event to listeners.
14:39:01.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:39:01.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000269df508668
14:39:01.485 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:39:04.742 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:39:04.743 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:39:04.743 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:39:04.916 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:39:05.532 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:39:05.533 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:39:05.534 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:39:13.978 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:39:16.999 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8f5c429d-1163-4dbc-8480-84c7cd3e292b
14:39:16.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] RpcClient init label, labels = {module=naming, source=sdk}
14:39:17.001 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:39:17.001 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:39:17.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:39:17.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:39:17.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Success to connect to server [localhost:8848] on start up, connectionId = 1754548757011_127.0.0.1_8682
14:39:17.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:39:17.127 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Notify connected event to listeners.
14:39:17.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000269df508668
14:39:17.180 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:39:17.204 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:39:17.311 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 17.859 seconds (JVM running for 18.85)
14:39:17.326 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:39:17.326 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:39:17.327 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:39:17.668 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Receive server push request, request = NotifySubscriberRequest, requestId = 147
14:39:17.682 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Ack server push request, request = NotifySubscriberRequest, requestId = 147
14:39:17.821 [RMI TCP Connection(13)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:39:48.603 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:39:48.604 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:42:50.069 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Server healthy check fail, currentConnection = 1754548757011_127.0.0.1_8682
14:42:50.069 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] Server healthy check fail, currentConnection = 1754548741201_127.0.0.1_8651
14:42:50.836 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:42:50.837 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:42:54.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Success to connect a server [localhost:8848], connectionId = 1754548973582_127.0.0.1_8992
14:42:54.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] Success to connect a server [localhost:8848], connectionId = 1754548974262_127.0.0.1_8991
14:42:54.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Abandon prev connection, server is localhost:8848, connectionId is 1754548757011_127.0.0.1_8682
14:42:54.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1754548741201_127.0.0.1_8651
14:42:54.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754548757011_127.0.0.1_8682
14:42:54.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754548741201_127.0.0.1_8651
14:42:54.379 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754548757011_127.0.0.1_8682]Ignore complete event,isRunning:false,isAbandon=true
14:42:54.379 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754548741201_127.0.0.1_8651]Ignore complete event,isRunning:false,isAbandon=true
14:42:54.384 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Notify disconnected event to listeners
14:42:54.384 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] Notify disconnected event to listeners
14:42:54.385 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9da6044d-77c7-41b8-9e1d-adb7b42f974a_config-0] Notify connected event to listeners.
14:42:54.385 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Notify connected event to listeners.
14:43:01.474 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Receive server push request, request = NotifySubscriberRequest, requestId = 156
14:43:01.475 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5c429d-1163-4dbc-8480-84c7cd3e292b] Ack server push request, request = NotifySubscriberRequest, requestId = 156
