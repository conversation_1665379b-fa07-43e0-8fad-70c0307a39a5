package com.heju.system.file.controller;

import com.alibaba.fastjson.JSONObject;
import com.heju.common.core.constant.basic.BaseConstants;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.InnerAuth;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.file.domain.dto.SysFileInfoDto;
import com.heju.system.file.domain.dto.SysFileRecordDto;
import com.heju.system.file.domain.query.SysFileInfoQuery;
import com.heju.system.file.service.ISysFileInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * 文件信息管理 业务处理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/file/info")
public class SysFileInfoController extends BaseController<SysFileInfoQuery, SysFileInfoDto, ISysFileInfoService> {

    @Resource
    private ISysFileInfoService fileInfoService;


    /**
     * 定义节点名称
     */
    @Override
    protected String getNodeName() {
        return "文件信息";
    }

    /**
     * 查询文件信息列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_FILE_INFO_LIST)
    public AjaxResult list(SysFileInfoQuery fileInfo) {
        startPage();
        List<SysFileInfoDto> list = baseService.selectListScope(fileInfo);
        return getDataTable(list);
    }


    /**
     * 查询文件信息详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_FILE_INFO_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 文件信息新增
     */
    @PostMapping
    @RequiresPermissions(Auth.SYS_FILE_INFO_ADD)
    @Log(title = "文件信息管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@RequestPart("file") MultipartFile file, @RequestPart("fileInfoJson") String fileInfoJson) {
        JSONObject jsonObject = JSONObject.parseObject(fileInfoJson);
        SysFileInfoDto fileInfo = jsonObject.toJavaObject(SysFileInfoDto.class);
        fileInfo.initOperate(BaseConstants.Operate.ADD);
        AEHandle(fileInfo.getOperate(), fileInfo);
        return toAjax(fileInfoService.insertFile(file, fileInfo));
    }

    /**
     * 文件编码生成
     *
     * @param fileclass
     * @return
     */
    @GetMapping("/batch/file/class")
    @Log(title = "文件信息管理", businessType = BusinessType.REFRESH)
    public AjaxResult batchRemove(@RequestParam String fileclass) {
        return AjaxResult.success(FileCode.generateSummaryString(fileclass));
    }

    /**
     * 文件信息修改
     */
    @Override
    @PutMapping("/update")
    @RequiresPermissions(Auth.SYS_FILE_INFO_EDIT)
    @Log(title = "文件信息管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysFileInfoDto fileInfo) {
        fileInfo.initOperate(BaseConstants.Operate.EDIT);
        AEHandle(fileInfo.getOperate(), fileInfo);
        return toAjax(baseService.update(fileInfo));
    }

    /**
     * 文件信息修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_FILE_INFO_EDIT, Auth.SYS_FILE_INFO_ES}, logical = Logical.OR)
    @Log(title = "文件信息管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysFileInfoDto fileInfo) {
        return super.editStatus(fileInfo);
    }

    /**
     * 文件信息批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_FILE_INFO_DEL)
    @Log(title = "文件信息管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        RHandleEmpty(idList);
        RHandle(BaseConstants.Operate.DELETE, idList);
        return toAjax(baseService.deleteByIds(idList));
    }

    /**
     * 获取文件信息选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 查询暂存文件信息列表
     */
    @GetMapping("/tempStorage/list")
    public AjaxResult tempStorageList(SysFileInfoQuery fileInfo) {
        startPage();
        List<SysFileInfoDto> list = fileInfoService.selectTempStorageList(fileInfo);
        return getDataTable(list);
    }


    /**
     * 查询回收站信息列表
     */
    @GetMapping("/recyclStation/list")
    public AjaxResult recyclStationList(SysFileInfoQuery fileInfo) {
        startPage();
        List<SysFileInfoDto> list = fileInfoService.selectrecyclList(fileInfo);
        return getDataTable(list);
    }

    /**
     * 回收站批量删除
     */
    @DeleteMapping("/batch/delete/{idList}")
    @Log(title = "文件信息管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemoveRecyclList(@PathVariable List<Long> idList) {
        RHandleEmpty(idList);
        RHandle(BaseConstants.Operate.DELETE, idList);
        return fileInfoService.batchDeleteByIds(idList);
    }

    /**
     * 回收站批量恢复
     */
    @PutMapping("/batch/reply/{idList}")
    @Log(title = "文件信息管理", businessType = BusinessType.UPDATE)
    public AjaxResult batchReplyList(@PathVariable List<Long> idList) {
        return fileInfoService.batchReplyList(idList);
    }

    /**
     * 暂存管理批量删除
     */
    @DeleteMapping("/batch/deleteStorage/{idList}")
    @Log(title = "文件信息管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemoveStorageList(@PathVariable List<Long> idList) {
        return fileInfoService.removeStorageByIds(idList);
    }

    /**
     * 查询操作 新增操作记录
     * id   文件id
     */
    @PutMapping("/insertView/{id}")
    public AjaxResult insertView(@PathVariable Long id) {
        return fileInfoService.insertView(id);
    }

    /**
     * 定时任务删除过期文件
     * @return 删除文件数
     */
    @PostMapping("/deleteExpiredFiles")
    @InnerAuth
    public R<Integer> deleteExpiredFiles() {
        int count = fileInfoService.deleteExpiredFiles();
        return R.ok(count);
    }

    /**
     * 单文件下载
     */
    @GetMapping("/download/{id}")
//    @RequiresPermissions(Auth.SYS_FILE_DOWNLOAD)
    @Log(title = "单文件下载", businessType = BusinessType.EXPORT)
    public void download(@PathVariable Long id, HttpServletResponse response) {
        fileInfoService.download(id, response);
    }

    /**
     * 多文件文件批量下载
     */
    @GetMapping("/batch/download/{idList}")
//    @RequiresPermissions(Auth.SYS_FILE_DOWNLOAD)
    @Log(title = "文件批量下载", businessType = BusinessType.EXPORT)
    public void batchDownload(@PathVariable List<Long> idList, HttpServletResponse response) {
        fileInfoService.batchDownload(idList, response);
    }
}
