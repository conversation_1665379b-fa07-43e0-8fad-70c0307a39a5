package com.heju.system.api.annualReport.feign.factory;

import com.heju.common.core.web.result.R;
import com.heju.system.api.annualReport.feign.RemoteAnnualReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 年度报表服务 降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteAnnualReportFallbackFactory implements FallbackFactory<RemoteAnnualReportService> {
    @Override
    public RemoteAnnualReportService create(Throwable cause) {
        return new RemoteAnnualReportService() {
            @Override
            public R<Integer> check(Long enterpriseId, String sourceName, String source) {
                return R.fail("年度报表服务调用失败:" + cause.getMessage());
            }
        };
    }
}
