package com.heju.system.file.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.file.domain.dto.SysFileRecordDto;
import com.heju.system.file.domain.query.SysFileRecordQuery;

import java.io.Serializable;
import java.util.List;

/**
 * 文件操作记录管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysFileRecordManager extends IBaseManager<SysFileRecordQuery, SysFileRecordDto> {

    /**
     * 文件管理操作列表
     * @param fileId
     * @return
     */
    List<SysFileRecordDto> selectByFileId(Serializable fileId);


}
