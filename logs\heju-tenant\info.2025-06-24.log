17:06:15.566 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:06:16.597 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0
17:06:16.742 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 62 ms to scan 1 urls, producing 3 keys and 6 values 
17:06:16.790 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
17:06:16.817 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 3 keys and 10 values 
17:06:16.833 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
17:06:16.853 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 7 values 
17:06:16.874 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
17:06:16.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:06:16.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001dfdc3b8200
17:06:16.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001dfdc3b8420
17:06:16.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:06:16.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:06:16.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:06:18.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750755977844_127.0.0.1_4730
17:06:18.092 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Notify connected event to listeners.
17:06:18.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:06:18.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001dfdc4f26e0
17:06:18.296 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:06:23.999 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
17:06:24.001 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:06:24.003 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:06:24.417 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:06:26.595 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:06:26.599 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:06:26.609 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:06:31.920 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:06:35.590 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-4f15-4a8f-acc5-d62fa00cdaab
17:06:35.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] RpcClient init label, labels = {module=naming, source=sdk}
17:06:35.595 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:06:35.595 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:06:35.595 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:06:35.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:06:35.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750755995612_127.0.0.1_4835
17:06:35.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:06:35.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001dfdc4f26e0
17:06:35.730 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Notify connected event to listeners.
17:06:35.812 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
17:06:35.853 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant 192.168.2.43:9700 register finished
17:06:36.020 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 21.355 seconds (JVM running for 22.769)
17:06:36.040 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
17:06:36.044 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
17:06:36.046 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
17:06:36.316 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Receive server push request, request = NotifySubscriberRequest, requestId = 24
17:06:36.340 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Ack server push request, request = NotifySubscriberRequest, requestId = 24
18:33:18.070 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Server healthy check fail, currentConnection = 1750755977844_127.0.0.1_4730
18:33:18.365 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
18:34:00.309 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1750761239906_127.0.0.1_3144
18:34:00.310 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1750755977844_127.0.0.1_4730
18:34:00.324 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750755977844_127.0.0.1_4730
18:34:00.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Notify disconnected event to listeners
18:34:00.345 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Notify connected event to listeners.
18:34:00.375 [nacos-grpc-client-executor-1059] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750755977844_127.0.0.1_4730]Ignore complete event,isRunning:true,isAbandon=true
18:40:14.430 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Server healthy check fail, currentConnection = 1750755995612_127.0.0.1_4835
18:40:14.431 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Try to reconnect to a new server, server is  not appointed, will choose a random server.
18:40:16.744 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Success to connect a server [127.0.0.1:8848], connectionId = 1750761616375_127.0.0.1_3659
18:40:16.826 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1750755995612_127.0.0.1_4835
18:40:16.827 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750755995612_127.0.0.1_4835
18:40:16.844 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Notify disconnected event to listeners
18:40:16.874 [nacos-grpc-client-executor-1125] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750755995612_127.0.0.1_4835]Ignore complete event,isRunning:true,isAbandon=true
18:40:16.908 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Notify connected event to listeners.
18:40:18.765 [nacos-grpc-client-executor-1128] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Receive server push request, request = NotifySubscriberRequest, requestId = 27
18:40:18.773 [nacos-grpc-client-executor-1128] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Ack server push request, request = NotifySubscriberRequest, requestId = 27
18:50:48.279 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Server healthy check fail, currentConnection = 1750761239906_127.0.0.1_3144
18:50:48.280 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
18:50:48.551 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1750762248431_127.0.0.1_4542
18:50:48.553 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1750761239906_127.0.0.1_3144
18:50:48.553 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750761239906_127.0.0.1_3144
18:50:48.553 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Notify disconnected event to listeners
18:50:48.554 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Notify connected event to listeners.
19:12:39.983 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Server healthy check fail, currentConnection = 1750761616375_127.0.0.1_3659
19:12:39.983 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Try to reconnect to a new server, server is  not appointed, will choose a random server.
19:12:56.131 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
19:12:57.232 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Server healthy check fail, currentConnection = 1750762248431_127.0.0.1_4542
19:12:57.232 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
19:13:03.896 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Success to connect a server [127.0.0.1:8848], connectionId = 1750763581992_127.0.0.1_6227
19:13:03.896 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1750761616375_127.0.0.1_3659
19:13:03.896 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750761616375_127.0.0.1_3659
19:13:03.971 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Notify disconnected event to listeners
19:13:04.115 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Notify connected event to listeners.
19:13:08.886 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Server check success, currentServer is 127.0.0.1:8848 
19:13:12.125 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1750763591675_127.0.0.1_6247
19:13:12.125 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1750762248431_127.0.0.1_4542
19:13:12.125 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750762248431_127.0.0.1_4542
19:13:12.128 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Notify disconnected event to listeners
19:13:12.128 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Notify connected event to listeners.
19:13:12.465 [nacos-grpc-client-executor-1520] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Receive server push request, request = NotifySubscriberRequest, requestId = 29
19:13:12.466 [nacos-grpc-client-executor-1520] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-4f15-4a8f-acc5-d62fa00cdaab] Ack server push request, request = NotifySubscriberRequest, requestId = 29
19:32:42.051 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Server healthy check fail, currentConnection = 1750763591675_127.0.0.1_6247
19:32:42.051 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
19:32:43.990 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1750764763382_127.0.0.1_7884
19:32:43.990 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1750763591675_127.0.0.1_6247
19:32:43.990 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750763591675_127.0.0.1_6247
19:32:44.077 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Notify disconnected event to listeners
19:32:44.077 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Notify connected event to listeners.
19:58:21.992 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Server healthy check fail, currentConnection = 1750764763382_127.0.0.1_7884
19:58:21.992 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
19:58:25.392 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1750766305245_127.0.0.1_10271
19:58:25.394 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1750764763382_127.0.0.1_7884
19:58:25.394 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750764763382_127.0.0.1_7884
19:58:25.394 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Notify disconnected event to listeners
19:58:25.394 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35e58bee-6f2c-4677-b8c7-60cc6c2eefdf_config-0] Notify connected event to listeners.
20:50:13.211 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:50:13.218 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:50:13.555 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:50:13.556 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@c1877ae[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:50:13.556 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750763581992_127.0.0.1_6227
20:50:13.558 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2088212a[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 2675]
20:50:13.558 [nacos-grpc-client-executor-2675] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750763581992_127.0.0.1_6227]Ignore complete event,isRunning:false,isAbandon=false
20:50:13.621 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:50:13.630 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:50:13.656 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:50:13.657 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
