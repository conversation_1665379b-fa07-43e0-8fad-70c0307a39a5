14:21:09.365 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:21:10.000 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 372141a0-07ed-4038-a131-c5d0b0be9d89_config-0
14:21:10.094 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 47 ms to scan 1 urls, producing 3 keys and 6 values 
14:21:10.122 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 2 ms to scan 1 urls, producing 4 keys and 9 values 
14:21:10.136 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 3 keys and 10 values 
14:21:10.148 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
14:21:10.158 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:21:10.169 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:21:10.172 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [372141a0-07ed-4038-a131-c5d0b0be9d89_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:21:10.172 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [372141a0-07ed-4038-a131-c5d0b0be9d89_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000020a913b94f0
14:21:10.172 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [372141a0-07ed-4038-a131-c5d0b0be9d89_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000020a913b9710
14:21:10.172 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [372141a0-07ed-4038-a131-c5d0b0be9d89_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:21:10.172 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [372141a0-07ed-4038-a131-c5d0b0be9d89_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:21:10.180 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [372141a0-07ed-4038-a131-c5d0b0be9d89_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:21:11.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [372141a0-07ed-4038-a131-c5d0b0be9d89_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753597271008_127.0.0.1_4482
14:21:11.276 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [372141a0-07ed-4038-a131-c5d0b0be9d89_config-0] Notify connected event to listeners.
14:21:11.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [372141a0-07ed-4038-a131-c5d0b0be9d89_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:21:11.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [372141a0-07ed-4038-a131-c5d0b0be9d89_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000020a914f0fb0
14:21:11.468 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:21:14.725 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:21:15.773 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:21:16.308 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 11d46be5-8fe7-4aee-ba09-37cf3d965efb_config-0
14:21:16.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11d46be5-8fe7-4aee-ba09-37cf3d965efb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:21:16.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11d46be5-8fe7-4aee-ba09-37cf3d965efb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000020a913b94f0
14:21:16.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11d46be5-8fe7-4aee-ba09-37cf3d965efb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000020a913b9710
14:21:16.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11d46be5-8fe7-4aee-ba09-37cf3d965efb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:21:16.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11d46be5-8fe7-4aee-ba09-37cf3d965efb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:21:16.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11d46be5-8fe7-4aee-ba09-37cf3d965efb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:21:16.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11d46be5-8fe7-4aee-ba09-37cf3d965efb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753597276320_127.0.0.1_4485
14:21:16.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11d46be5-8fe7-4aee-ba09-37cf3d965efb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:21:16.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11d46be5-8fe7-4aee-ba09-37cf3d965efb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000020a914f0fb0
14:21:16.435 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11d46be5-8fe7-4aee-ba09-37cf3d965efb_config-0] Notify connected event to listeners.
14:21:16.539 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4125447d-2697-45ac-8831-4d1b545f1e0c
14:21:16.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] RpcClient init label, labels = {module=naming, source=sdk}
14:21:16.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:21:16.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:21:16.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:21:16.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:21:16.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Success to connect to server [localhost:8848] on start up, connectionId = 1753597276553_127.0.0.1_4486
14:21:16.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:21:16.668 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Notify connected event to listeners.
14:21:16.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000020a914f0fb0
14:21:17.121 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
14:21:17.165 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 8.351 seconds (JVM running for 21.807)
14:21:17.182 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
14:21:17.182 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
14:21:17.184 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
14:21:17.594 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 1
14:21:17.594 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 1
14:21:47.340 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 3
14:21:47.344 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 3
14:22:17.384 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 6
14:22:17.384 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 6
14:22:17.384 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 7
14:22:17.384 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 7
14:32:47.614 [nacos-grpc-client-executor-236] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 9
14:32:47.615 [nacos-grpc-client-executor-236] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 9
16:28:35.097 [nacos-grpc-client-executor-2532] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 15
16:28:35.120 [nacos-grpc-client-executor-2532] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 15
16:28:37.745 [nacos-grpc-client-executor-2533] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 20
16:28:37.761 [nacos-grpc-client-executor-2533] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 20
17:03:24.817 [nacos-grpc-client-executor-3224] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 23
17:03:24.839 [nacos-grpc-client-executor-3224] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 23
17:03:42.124 [nacos-grpc-client-executor-3227] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 26
17:03:42.140 [nacos-grpc-client-executor-3227] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 26
17:10:15.816 [nacos-grpc-client-executor-3356] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 30
17:10:15.835 [nacos-grpc-client-executor-3356] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 30
17:10:31.559 [nacos-grpc-client-executor-3364] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 34
17:10:31.574 [nacos-grpc-client-executor-3364] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 34
17:12:07.232 [nacos-grpc-client-executor-3393] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 37
17:12:07.247 [nacos-grpc-client-executor-3393] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 37
17:12:27.359 [nacos-grpc-client-executor-3402] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 40
17:12:27.374 [nacos-grpc-client-executor-3402] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 40
17:13:31.380 [nacos-grpc-client-executor-3422] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 44
17:13:31.383 [nacos-grpc-client-executor-3422] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 44
17:13:48.719 [nacos-grpc-client-executor-3425] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 47
17:13:48.735 [nacos-grpc-client-executor-3425] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 47
17:15:52.513 [nacos-grpc-client-executor-3469] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 51
17:15:52.530 [nacos-grpc-client-executor-3469] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 51
17:16:10.188 [nacos-grpc-client-executor-3473] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 54
17:16:10.202 [nacos-grpc-client-executor-3473] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 54
19:48:19.629 [nacos-grpc-client-executor-6442] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 58
19:48:19.653 [nacos-grpc-client-executor-6442] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 58
19:48:38.287 [nacos-grpc-client-executor-6451] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 61
19:48:38.302 [nacos-grpc-client-executor-6451] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 61
19:51:57.651 [nacos-grpc-client-executor-6510] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 65
19:51:57.668 [nacos-grpc-client-executor-6510] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 65
19:52:16.980 [nacos-grpc-client-executor-6514] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 68
19:52:16.995 [nacos-grpc-client-executor-6514] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 68
19:53:46.137 [nacos-grpc-client-executor-6543] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 72
19:53:46.160 [nacos-grpc-client-executor-6543] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 72
19:54:02.906 [nacos-grpc-client-executor-6549] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 75
19:54:02.921 [nacos-grpc-client-executor-6549] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 75
19:54:27.590 [nacos-grpc-client-executor-6559] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 79
19:54:27.612 [nacos-grpc-client-executor-6559] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 79
19:54:47.668 [nacos-grpc-client-executor-6563] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 83
19:54:47.683 [nacos-grpc-client-executor-6563] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 83
19:59:06.628 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:59:06.632 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:59:06.733 [nacos-grpc-client-executor-6648] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 85
19:59:06.751 [nacos-grpc-client-executor-6648] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 85
19:59:06.842 [nacos-grpc-client-executor-6649] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 86
19:59:06.858 [nacos-grpc-client-executor-6649] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 86
19:59:06.860 [nacos-grpc-client-executor-6650] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Receive server push request, request = NotifySubscriberRequest, requestId = 87
19:59:06.872 [nacos-grpc-client-executor-6650] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4125447d-2697-45ac-8831-4d1b545f1e0c] Ack server push request, request = NotifySubscriberRequest, requestId = 87
19:59:06.971 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:59:06.972 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6e469716[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:59:06.972 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753597276553_127.0.0.1_4486
19:59:06.973 [nacos-grpc-client-executor-6651] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753597276553_127.0.0.1_4486]Ignore complete event,isRunning:false,isAbandon=false
19:59:06.977 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1c241e1a[Running, pool size = 9, active threads = 0, queued tasks = 0, completed tasks = 6652]
