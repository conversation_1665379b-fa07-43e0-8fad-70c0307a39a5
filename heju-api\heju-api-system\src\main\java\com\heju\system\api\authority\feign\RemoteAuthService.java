package com.heju.system.api.authority.feign;

import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.constant.basic.ServiceConstants;
import com.heju.common.core.web.result.R;
import com.heju.system.api.authority.domain.dto.SysTenantMenuDto;
import com.heju.system.api.authority.feign.factory.RemoteAuthFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteAuthService", value = ServiceConstants.SYSTEM_SERVICE, fallbackFactory = RemoteAuthFallbackFactory.class)
public interface RemoteAuthService {

    /**
     * 新增租户权限
     *
     * @param enterpriseId 企业Id
     * @param sourceName   策略源
     * @param source       请求来源
     * @return 结果
     */
    @GetMapping("/auth/inner/getTenantAuth")
    R<Long[]> getTenantAuthInner(@RequestHeader(SecurityConstants.ENTERPRISE_ID) Long enterpriseId, @RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 新增租户权限
     *
     * @param authIdsString      权限Ids字符串
     * @param enterpriseId 企业Id
     * @param sourceName   策略源
     * @param source       请求来源
     * @return 结果
     */
    @PostMapping("/auth/inner/addTenantAuth")
    R<Boolean> addTenantAuthInner(@RequestBody String authIdsString, @RequestHeader(SecurityConstants.ENTERPRISE_ID) Long enterpriseId, @RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 修改租户权限
     *
     * @param authIds      权限Ids
     * @param enterpriseId 企业Id
     * @param sourceName   策略源
     * @param source       请求来源
     * @return 结果
     */
    @PostMapping("/auth/inner/editTenantAuth")
    R<Boolean> editTenantAuthInner(@RequestBody Long[] authIds, @RequestHeader(SecurityConstants.ENTERPRISE_ID) Long enterpriseId, @RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 租户删除菜单
     */
    @DeleteMapping("/auth/inner/tenantDeleteMenus")
    R<Boolean> tenantDeleteMenus(@RequestBody List<Long> menuIdList, @RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 租户新增插入菜单
     */
    @PostMapping("/auth/inner/tenantUpdateMenus")
    R<Boolean> tenantUpdateMenus(@RequestBody SysTenantMenuDto sysTenantMenuDto, @RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询租户已有菜单
     */
    @GetMapping("/auth/inner/tenantHasMenuIds")
    List<Long> tenantHasMenuIds(@RequestParam(value = "tenantId") Long tenantId , @RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}