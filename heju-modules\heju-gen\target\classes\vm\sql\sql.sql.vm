#set($subLevel=$level + 1)
insert into sys_menu (id, parent_id, name, title, ancestors, level, path, frame_src, component, param_path, transition_name, ignore_route, is_cache, is_affix, is_disabled, frame_type, menu_type, hide_tab, hide_menu, hide_breadcrumb, hide_children, hide_path_for_children, dynamic_level, real_path, perms, icon, sort, remark, is_common, is_default, module_id, tenant_id)
values
           (${menuId0}, ${parentMenuId}, '${menuName0}', '${functionName}管理', '${parentMenuAncestors}', ${level}, '${businessName}', null, '${moduleName}/${authorityName}/${businessName}/index', null, null, 'N', 'N', 'N', 'N', '0', 'C', '0', '0', '0', '0', '0', 1, null, '${authorityName}:${businessName}:list', 'xy_organization', 1, '菜单:${functionName}管理', '0', 'Y', ${parentModuleId}, 0),
               (${menuId1}, ${menuId0}, '${menuName1}', '${functionName}详情', '${parentMenuAncestors},${menuId0}', ${subLevel}, '${businessName}Detail/:id', null, '${moduleName}/${authorityName}/${businessName}/${BusinessName}Detail', null, null, 'N', 'Y', 'N', 'N', '0', 'X', '0', '1', '0', '0', '0', 5, null, '${authorityName}:${businessName}:single', null, 2, '详情:${functionName}详情', '0', 'Y', ${parentModuleId}, 0),
               (${menuId2}, ${menuId0}, '${menuName2}', '${functionName}新增', '${parentMenuAncestors},${menuId0}', ${subLevel}, null, null, null, null, null, 'N', 'N', 'N', 'N', '0', 'F', '0', '0', '0', '0', '0', 1, null, '${authorityName}:${businessName}:add', null, 3, '按钮:${functionName}新增', '0', 'Y', ${parentModuleId}, 0),
               (${menuId3}, ${menuId0}, '${menuName3}', '${functionName}修改', '${parentMenuAncestors},${menuId0}', ${subLevel}, null, null, null, null, null, 'N', 'N', 'N', 'N', '0', 'F', '0', '0', '0', '0', '0', 1, null, '${authorityName}:${businessName}:edit', null, 4, '按钮:${functionName}修改', '0', 'Y', ${parentModuleId}, 0),
               (${menuId4}, ${menuId0}, '${menuName4}', '${functionName}修改状态', '${parentMenuAncestors},${menuId0}', ${subLevel}, null, null, null, null, null, 'N', 'N', 'N', 'N', '0', 'F', '0', '0', '0', '0', '0', 1, null, '${authorityName}:${businessName}:es', null, 5, '按钮:${functionName}修改状态', '0', 'Y', ${parentModuleId}, 0),
               (${menuId5}, ${menuId0}, '${menuName5}', '${functionName}删除', '${parentMenuAncestors},${menuId0}', ${subLevel}, null, null, null, null, null, 'N', 'N', 'N', 'N', '0', 'F', '0', '0', '0', '0', '0', 1, null, '${authorityName}:${businessName}:delete', null, 6, '按钮:${functionName}删除', '0', 'Y', ${parentModuleId}, 0),
               (${menuId6}, ${menuId0}, '${menuName6}', '${functionName}导入', '${parentMenuAncestors},${menuId0}', ${subLevel}, null, null, null, null, null, 'N', 'N', 'N', 'N', '0', 'F', '0', '0', '0', '0', '0', 1, null, '${authorityName}:${businessName}:import', null, 7, '按钮:${functionName}导入', '0', 'Y', ${parentModuleId}, 0),
               (${menuId7}, ${menuId0}, '${menuName7}', '${functionName}导出', '${parentMenuAncestors},${menuId0}', ${subLevel}, null, null, null, null, null, 'N', 'N', 'N', 'N', '0', 'F', '0', '0', '0', '0', '0', 1, null, '${authorityName}:${businessName}:export', null, 8, '按钮:${functionName}导出', '0', 'Y', ${parentModuleId}, 0);