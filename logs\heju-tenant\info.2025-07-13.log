10:04:18.025 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:04:18.771 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9a717116-99dc-4b8f-a8d7-6fc421fe44f9_config-0
10:04:18.831 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 3 keys and 6 values 
10:04:18.857 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 4 keys and 9 values 
10:04:18.866 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 3 keys and 10 values 
10:04:18.877 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
10:04:18.892 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 7 values 
10:04:18.900 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
10:04:18.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a717116-99dc-4b8f-a8d7-6fc421fe44f9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:04:18.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a717116-99dc-4b8f-a8d7-6fc421fe44f9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001aa8139fd80
10:04:18.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a717116-99dc-4b8f-a8d7-6fc421fe44f9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001aa813a0000
10:04:18.905 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a717116-99dc-4b8f-a8d7-6fc421fe44f9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:04:18.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a717116-99dc-4b8f-a8d7-6fc421fe44f9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:04:18.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a717116-99dc-4b8f-a8d7-6fc421fe44f9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:19.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a717116-99dc-4b8f-a8d7-6fc421fe44f9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752372259705_127.0.0.1_8193
10:04:19.899 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a717116-99dc-4b8f-a8d7-6fc421fe44f9_config-0] Notify connected event to listeners.
10:04:19.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a717116-99dc-4b8f-a8d7-6fc421fe44f9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:19.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a717116-99dc-4b8f-a8d7-6fc421fe44f9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001aa8151a0a0
10:04:20.102 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:04:23.161 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
10:04:23.162 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:04:23.162 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:04:23.330 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:04:24.024 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:04:24.027 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:04:24.027 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:04:26.712 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:04:29.818 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d7900f69-2eef-4bf0-a834-bfaa2e467a92
10:04:29.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7900f69-2eef-4bf0-a834-bfaa2e467a92] RpcClient init label, labels = {module=naming, source=sdk}
10:04:29.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7900f69-2eef-4bf0-a834-bfaa2e467a92] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:04:29.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7900f69-2eef-4bf0-a834-bfaa2e467a92] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:04:29.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7900f69-2eef-4bf0-a834-bfaa2e467a92] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:04:29.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7900f69-2eef-4bf0-a834-bfaa2e467a92] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:29.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7900f69-2eef-4bf0-a834-bfaa2e467a92] Success to connect to server [localhost:8848] on start up, connectionId = 1752372269833_127.0.0.1_8237
10:04:29.963 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7900f69-2eef-4bf0-a834-bfaa2e467a92] Notify connected event to listeners.
10:04:29.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7900f69-2eef-4bf0-a834-bfaa2e467a92] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:29.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7900f69-2eef-4bf0-a834-bfaa2e467a92] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001aa8151a0a0
10:04:30.027 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
10:04:30.057 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
10:04:30.183 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 12.773 seconds (JVM running for 14.209)
10:04:30.198 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
10:04:30.203 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
10:04:30.204 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
10:04:30.571 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:04:30.579 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7900f69-2eef-4bf0-a834-bfaa2e467a92] Receive server push request, request = NotifySubscriberRequest, requestId = 5
10:04:30.596 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7900f69-2eef-4bf0-a834-bfaa2e467a92] Ack server push request, request = NotifySubscriberRequest, requestId = 5
10:13:32.681 [nacos-grpc-client-executor-124] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7900f69-2eef-4bf0-a834-bfaa2e467a92] Receive server push request, request = NotifySubscriberRequest, requestId = 13
10:13:32.683 [nacos-grpc-client-executor-124] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7900f69-2eef-4bf0-a834-bfaa2e467a92] Ack server push request, request = NotifySubscriberRequest, requestId = 13
10:20:12.248 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:20:12.266 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:20:12.621 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:20:12.621 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@234032a3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:20:12.621 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752372269833_127.0.0.1_8237
10:20:12.623 [nacos-grpc-client-executor-210] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752372269833_127.0.0.1_8237]Ignore complete event,isRunning:false,isAbandon=false
10:20:12.631 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5e0d59ff[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 211]
10:20:12.770 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:20:12.770 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:20:12.788 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:20:12.788 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:20:16.340 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:20:16.991 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5cc3564b-be24-48f7-9e85-23e3988f6f00_config-0
10:20:17.051 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
10:20:17.075 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
10:20:17.084 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
10:20:17.093 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
10:20:17.110 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
10:20:17.119 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
10:20:17.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc3564b-be24-48f7-9e85-23e3988f6f00_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:20:17.124 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc3564b-be24-48f7-9e85-23e3988f6f00_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000156593ceb60
10:20:17.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc3564b-be24-48f7-9e85-23e3988f6f00_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000156593ced80
10:20:17.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc3564b-be24-48f7-9e85-23e3988f6f00_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:20:17.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc3564b-be24-48f7-9e85-23e3988f6f00_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:20:17.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc3564b-be24-48f7-9e85-23e3988f6f00_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:20:18.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc3564b-be24-48f7-9e85-23e3988f6f00_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752373217968_127.0.0.1_10789
10:20:18.168 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc3564b-be24-48f7-9e85-23e3988f6f00_config-0] Notify connected event to listeners.
10:20:18.168 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc3564b-be24-48f7-9e85-23e3988f6f00_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:20:18.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5cc3564b-be24-48f7-9e85-23e3988f6f00_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000015659508668
10:20:18.345 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:20:21.772 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
10:20:21.773 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:20:21.773 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:20:21.971 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:20:22.776 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:20:22.777 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:20:22.778 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:20:25.754 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:20:28.489 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of efc82637-afef-48ec-97cc-bd18decb624f
10:20:28.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efc82637-afef-48ec-97cc-bd18decb624f] RpcClient init label, labels = {module=naming, source=sdk}
10:20:28.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efc82637-afef-48ec-97cc-bd18decb624f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:20:28.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efc82637-afef-48ec-97cc-bd18decb624f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:20:28.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efc82637-afef-48ec-97cc-bd18decb624f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:20:28.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efc82637-afef-48ec-97cc-bd18decb624f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:20:28.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efc82637-afef-48ec-97cc-bd18decb624f] Success to connect to server [localhost:8848] on start up, connectionId = 1752373228500_127.0.0.1_10800
10:20:28.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efc82637-afef-48ec-97cc-bd18decb624f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:20:28.614 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efc82637-afef-48ec-97cc-bd18decb624f] Notify connected event to listeners.
10:20:28.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efc82637-afef-48ec-97cc-bd18decb624f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000015659508668
10:20:28.683 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
10:20:28.731 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
10:20:28.864 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 13.228 seconds (JVM running for 14.33)
10:20:28.880 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
10:20:28.900 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
10:20:28.904 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
10:20:29.211 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efc82637-afef-48ec-97cc-bd18decb624f] Receive server push request, request = NotifySubscriberRequest, requestId = 20
10:20:29.236 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efc82637-afef-48ec-97cc-bd18decb624f] Ack server push request, request = NotifySubscriberRequest, requestId = 20
10:20:29.269 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:20:01.556 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:20:01.559 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:20:01.904 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:20:01.904 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@43433496[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:20:01.904 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752373228500_127.0.0.1_10800
19:20:01.904 [nacos-grpc-client-executor-6473] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752373228500_127.0.0.1_10800]Ignore complete event,isRunning:false,isAbandon=false
19:20:01.919 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@f648ad[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6474]
19:20:02.117 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:20:02.128 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:20:02.132 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:20:02.132 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:22:12.318 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:22:16.214 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0
19:22:16.421 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 117 ms to scan 1 urls, producing 3 keys and 6 values 
19:22:16.518 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 4 keys and 9 values 
19:22:16.544 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
19:22:16.571 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 5 values 
19:22:16.594 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
19:22:16.616 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
19:22:16.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:22:16.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002473239f8e0
19:22:16.630 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002473239fb00
19:22:16.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:22:16.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:22:16.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:19.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:20.040 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:20.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:22:20.103 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
19:22:20.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002473251e658
19:22:20.430 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:20.761 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:21.165 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:21.670 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:22.563 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Success to connect a server [localhost:8848], connectionId = 1752405742296_127.0.0.1_2823
19:22:22.565 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Notify connected event to listeners.
19:22:22.630 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84b52c8c-f1ee-4d09-87c7-b802fa78f47b_config-0] Server check success, currentServer is localhost:8848 
19:22:22.634 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:22:31.047 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
19:22:31.048 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:22:31.048 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:22:31.362 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:22:33.767 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:22:33.771 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:22:33.771 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:22:42.291 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:22:53.479 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 77ecca8a-3bc8-4474-940c-1146431cef95
19:22:53.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [77ecca8a-3bc8-4474-940c-1146431cef95] RpcClient init label, labels = {module=naming, source=sdk}
19:22:53.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [77ecca8a-3bc8-4474-940c-1146431cef95] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:22:53.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [77ecca8a-3bc8-4474-940c-1146431cef95] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:22:53.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [77ecca8a-3bc8-4474-940c-1146431cef95] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:22:53.486 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [77ecca8a-3bc8-4474-940c-1146431cef95] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:53.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [77ecca8a-3bc8-4474-940c-1146431cef95] Success to connect to server [localhost:8848] on start up, connectionId = 1752405773500_127.0.0.1_3053
19:22:53.623 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [77ecca8a-3bc8-4474-940c-1146431cef95] Notify connected event to listeners.
19:22:53.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [77ecca8a-3bc8-4474-940c-1146431cef95] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:22:53.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [77ecca8a-3bc8-4474-940c-1146431cef95] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002473251e658
19:22:53.703 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
19:22:53.774 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
19:22:54.135 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 43.837 seconds (JVM running for 47.666)
19:22:54.161 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
19:22:54.164 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
19:22:54.165 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
19:22:54.210 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [77ecca8a-3bc8-4474-940c-1146431cef95] Receive server push request, request = NotifySubscriberRequest, requestId = 6
19:22:54.238 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [77ecca8a-3bc8-4474-940c-1146431cef95] Ack server push request, request = NotifySubscriberRequest, requestId = 6
19:22:54.257 [RMI TCP Connection(17)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:24:22.995 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [77ecca8a-3bc8-4474-940c-1146431cef95] Receive server push request, request = NotifySubscriberRequest, requestId = 12
19:24:22.997 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [77ecca8a-3bc8-4474-940c-1146431cef95] Ack server push request, request = NotifySubscriberRequest, requestId = 12
19:34:22.170 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:34:22.172 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:34:22.508 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:34:22.509 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3ffcdc88[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:34:22.509 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752405773500_127.0.0.1_3053
19:34:22.510 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1e0246fb[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 155]
19:34:22.642 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:34:22.646 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:34:22.653 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:34:22.654 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
