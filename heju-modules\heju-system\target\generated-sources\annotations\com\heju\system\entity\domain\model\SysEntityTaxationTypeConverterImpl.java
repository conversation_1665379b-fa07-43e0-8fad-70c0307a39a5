package com.heju.system.entity.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.entity.domain.dto.SysEntityTaxationTypeDto;
import com.heju.system.entity.domain.po.SysEntityTaxationTypePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysEntityTaxationTypeConverterImpl implements SysEntityTaxationTypeConverter {

    @Override
    public SysEntityTaxationTypeDto mapperDto(SysEntityTaxationTypePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntityTaxationTypeDto sysEntityTaxationTypeDto = new SysEntityTaxationTypeDto();

        sysEntityTaxationTypeDto.setId( arg0.getId() );
        sysEntityTaxationTypeDto.setSourceName( arg0.getSourceName() );
        sysEntityTaxationTypeDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntityTaxationTypeDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntityTaxationTypeDto.setName( arg0.getName() );
        sysEntityTaxationTypeDto.setStatus( arg0.getStatus() );
        sysEntityTaxationTypeDto.setSort( arg0.getSort() );
        sysEntityTaxationTypeDto.setRemark( arg0.getRemark() );
        sysEntityTaxationTypeDto.setCreateBy( arg0.getCreateBy() );
        sysEntityTaxationTypeDto.setCreateTime( arg0.getCreateTime() );
        sysEntityTaxationTypeDto.setUpdateBy( arg0.getUpdateBy() );
        sysEntityTaxationTypeDto.setUpdateTime( arg0.getUpdateTime() );
        sysEntityTaxationTypeDto.setDelFlag( arg0.getDelFlag() );
        sysEntityTaxationTypeDto.setCreateName( arg0.getCreateName() );
        sysEntityTaxationTypeDto.setUpdateName( arg0.getUpdateName() );
        sysEntityTaxationTypeDto.setCollectionProject( arg0.getCollectionProject() );
        sysEntityTaxationTypeDto.setCollectionItem( arg0.getCollectionItem() );
        sysEntityTaxationTypeDto.setTaxPaymenPeriod( arg0.getTaxPaymenPeriod() );
        sysEntityTaxationTypeDto.setStarttime( arg0.getStarttime() );
        sysEntityTaxationTypeDto.setEndtime( arg0.getEndtime() );
        sysEntityTaxationTypeDto.setEntityId( arg0.getEntityId() );

        return sysEntityTaxationTypeDto;
    }

    @Override
    public List<SysEntityTaxationTypeDto> mapperDto(Collection<SysEntityTaxationTypePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntityTaxationTypeDto> list = new ArrayList<SysEntityTaxationTypeDto>( arg0.size() );
        for ( SysEntityTaxationTypePo sysEntityTaxationTypePo : arg0 ) {
            list.add( mapperDto( sysEntityTaxationTypePo ) );
        }

        return list;
    }

    @Override
    public Page<SysEntityTaxationTypeDto> mapperPageDto(Collection<SysEntityTaxationTypePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntityTaxationTypeDto> page = new Page<SysEntityTaxationTypeDto>();
        for ( SysEntityTaxationTypePo sysEntityTaxationTypePo : arg0 ) {
            page.add( mapperDto( sysEntityTaxationTypePo ) );
        }

        return page;
    }

    @Override
    public SysEntityTaxationTypePo mapperPo(SysEntityTaxationTypeDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntityTaxationTypePo sysEntityTaxationTypePo = new SysEntityTaxationTypePo();

        sysEntityTaxationTypePo.setId( arg0.getId() );
        sysEntityTaxationTypePo.setSourceName( arg0.getSourceName() );
        sysEntityTaxationTypePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntityTaxationTypePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntityTaxationTypePo.setName( arg0.getName() );
        sysEntityTaxationTypePo.setStatus( arg0.getStatus() );
        sysEntityTaxationTypePo.setSort( arg0.getSort() );
        sysEntityTaxationTypePo.setRemark( arg0.getRemark() );
        sysEntityTaxationTypePo.setCreateBy( arg0.getCreateBy() );
        sysEntityTaxationTypePo.setCreateTime( arg0.getCreateTime() );
        sysEntityTaxationTypePo.setUpdateBy( arg0.getUpdateBy() );
        sysEntityTaxationTypePo.setUpdateTime( arg0.getUpdateTime() );
        sysEntityTaxationTypePo.setDelFlag( arg0.getDelFlag() );
        sysEntityTaxationTypePo.setCreateName( arg0.getCreateName() );
        sysEntityTaxationTypePo.setUpdateName( arg0.getUpdateName() );
        sysEntityTaxationTypePo.setCollectionProject( arg0.getCollectionProject() );
        sysEntityTaxationTypePo.setCollectionItem( arg0.getCollectionItem() );
        sysEntityTaxationTypePo.setTaxPaymenPeriod( arg0.getTaxPaymenPeriod() );
        sysEntityTaxationTypePo.setStarttime( arg0.getStarttime() );
        sysEntityTaxationTypePo.setEndtime( arg0.getEndtime() );
        sysEntityTaxationTypePo.setEntityId( arg0.getEntityId() );

        return sysEntityTaxationTypePo;
    }

    @Override
    public List<SysEntityTaxationTypePo> mapperPo(Collection<SysEntityTaxationTypeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntityTaxationTypePo> list = new ArrayList<SysEntityTaxationTypePo>( arg0.size() );
        for ( SysEntityTaxationTypeDto sysEntityTaxationTypeDto : arg0 ) {
            list.add( mapperPo( sysEntityTaxationTypeDto ) );
        }

        return list;
    }

    @Override
    public Page<SysEntityTaxationTypePo> mapperPagePo(Collection<SysEntityTaxationTypeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntityTaxationTypePo> page = new Page<SysEntityTaxationTypePo>();
        for ( SysEntityTaxationTypeDto sysEntityTaxationTypeDto : arg0 ) {
            page.add( mapperPo( sysEntityTaxationTypeDto ) );
        }

        return page;
    }
}
