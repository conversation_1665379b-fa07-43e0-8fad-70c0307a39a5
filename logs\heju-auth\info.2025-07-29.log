09:14:09.439 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:14:10.596 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0515a0ab-3342-4a1e-9782-2e498390bba0_config-0
09:14:10.718 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 69 ms to scan 1 urls, producing 3 keys and 6 values 
09:14:10.765 [main] INFO  o.r.Reflections - [scan,232] - <PERSON>flections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
09:14:10.782 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 3 keys and 10 values 
09:14:10.804 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 17 ms to scan 1 urls, producing 1 keys and 5 values 
09:14:10.823 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 7 values 
09:14:10.839 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:14:10.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0515a0ab-3342-4a1e-9782-2e498390bba0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:14:10.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0515a0ab-3342-4a1e-9782-2e498390bba0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000297163b44e8
09:14:10.845 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0515a0ab-3342-4a1e-9782-2e498390bba0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000297163b4708
09:14:10.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0515a0ab-3342-4a1e-9782-2e498390bba0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:14:10.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0515a0ab-3342-4a1e-9782-2e498390bba0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:14:10.866 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0515a0ab-3342-4a1e-9782-2e498390bba0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:12.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0515a0ab-3342-4a1e-9782-2e498390bba0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753751652188_127.0.0.1_10665
09:14:12.489 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0515a0ab-3342-4a1e-9782-2e498390bba0_config-0] Notify connected event to listeners.
09:14:12.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0515a0ab-3342-4a1e-9782-2e498390bba0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:12.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0515a0ab-3342-4a1e-9782-2e498390bba0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000297164ec200
09:14:12.718 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:14:16.112 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:14:16.113 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:14:16.113 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:14:16.383 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:14:18.891 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:14:20.645 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 97307038-f5e4-4abf-9624-aa72085dffce
09:14:20.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] RpcClient init label, labels = {module=naming, source=sdk}
09:14:20.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:14:20.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:14:20.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:14:20.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:20.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Success to connect to server [localhost:8848] on start up, connectionId = 1753751660663_127.0.0.1_10756
09:14:20.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:20.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000297164ec200
09:14:20.789 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Notify connected event to listeners.
09:14:20.857 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:14:20.900 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:14:21.151 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 12.981 seconds (JVM running for 17.669)
09:14:21.173 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:14:21.174 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:14:21.178 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:14:21.410 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:14:21.427 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:14:54.917 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:15:05.241 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:15:05.242 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:15:07.744 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:15:07.744 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Ack server push request, request = NotifySubscriberRequest, requestId = 12
10:38:33.595 [nacos-grpc-client-executor-1062] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Receive server push request, request = NotifySubscriberRequest, requestId = 16
10:38:33.612 [nacos-grpc-client-executor-1062] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Ack server push request, request = NotifySubscriberRequest, requestId = 16
10:39:05.621 [nacos-grpc-client-executor-1068] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Receive server push request, request = NotifySubscriberRequest, requestId = 20
10:39:05.640 [nacos-grpc-client-executor-1068] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Ack server push request, request = NotifySubscriberRequest, requestId = 20
10:46:18.273 [nacos-grpc-client-executor-1160] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Receive server push request, request = NotifySubscriberRequest, requestId = 23
10:46:18.274 [nacos-grpc-client-executor-1160] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Ack server push request, request = NotifySubscriberRequest, requestId = 23
10:49:54.894 [nacos-grpc-client-executor-1204] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Receive server push request, request = NotifySubscriberRequest, requestId = 27
10:49:54.910 [nacos-grpc-client-executor-1204] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Ack server push request, request = NotifySubscriberRequest, requestId = 27
11:08:50.071 [nacos-grpc-client-executor-1431] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Receive server push request, request = NotifySubscriberRequest, requestId = 30
11:08:50.080 [nacos-grpc-client-executor-1431] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Ack server push request, request = NotifySubscriberRequest, requestId = 30
11:09:11.870 [nacos-grpc-client-executor-1435] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Receive server push request, request = NotifySubscriberRequest, requestId = 34
11:09:11.888 [nacos-grpc-client-executor-1435] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97307038-f5e4-4abf-9624-aa72085dffce] Ack server push request, request = NotifySubscriberRequest, requestId = 34
11:41:03.893 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:41:03.895 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:41:04.205 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:41:04.206 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@59365572[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:41:04.206 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753751660663_127.0.0.1_10756
11:41:04.213 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4f597d37[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 1819]
11:41:04.213 [nacos-grpc-client-executor-1819] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753751660663_127.0.0.1_10756]Ignore complete event,isRunning:false,isAbandon=false
11:44:58.036 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:44:58.543 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a26d758b-9352-4b09-865e-deef68e10f38_config-0
11:44:58.601 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
11:44:58.624 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
11:44:58.634 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:44:58.645 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
11:44:58.657 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
11:44:58.673 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
11:44:58.678 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a26d758b-9352-4b09-865e-deef68e10f38_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:44:58.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a26d758b-9352-4b09-865e-deef68e10f38_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001bc453c9ed0
11:44:58.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a26d758b-9352-4b09-865e-deef68e10f38_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001bc453ca0f0
11:44:58.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a26d758b-9352-4b09-865e-deef68e10f38_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:44:58.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a26d758b-9352-4b09-865e-deef68e10f38_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:44:58.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a26d758b-9352-4b09-865e-deef68e10f38_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:44:59.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a26d758b-9352-4b09-865e-deef68e10f38_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753760699513_127.0.0.1_7237
11:44:59.740 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a26d758b-9352-4b09-865e-deef68e10f38_config-0] Notify connected event to listeners.
11:44:59.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a26d758b-9352-4b09-865e-deef68e10f38_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:44:59.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a26d758b-9352-4b09-865e-deef68e10f38_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001bc455042a8
11:44:59.829 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:45:02.390 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
11:45:02.391 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:45:02.391 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:45:02.571 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:45:03.955 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:45:05.473 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 13d3a8ae-9214-4a4e-8853-506bcc0b757a
11:45:05.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13d3a8ae-9214-4a4e-8853-506bcc0b757a] RpcClient init label, labels = {module=naming, source=sdk}
11:45:05.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13d3a8ae-9214-4a4e-8853-506bcc0b757a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:45:05.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13d3a8ae-9214-4a4e-8853-506bcc0b757a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:45:05.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13d3a8ae-9214-4a4e-8853-506bcc0b757a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:45:05.478 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13d3a8ae-9214-4a4e-8853-506bcc0b757a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:45:05.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13d3a8ae-9214-4a4e-8853-506bcc0b757a] Success to connect to server [localhost:8848] on start up, connectionId = 1753760705488_127.0.0.1_7296
11:45:05.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13d3a8ae-9214-4a4e-8853-506bcc0b757a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:45:05.604 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13d3a8ae-9214-4a4e-8853-506bcc0b757a] Notify connected event to listeners.
11:45:05.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13d3a8ae-9214-4a4e-8853-506bcc0b757a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001bc455042a8
11:45:05.651 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
11:45:05.673 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
11:45:05.803 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 8.327 seconds (JVM running for 9.246)
11:45:05.813 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
11:45:05.813 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
11:45:05.815 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
11:45:06.156 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:45:06.163 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13d3a8ae-9214-4a4e-8853-506bcc0b757a] Receive server push request, request = NotifySubscriberRequest, requestId = 38
11:45:06.182 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13d3a8ae-9214-4a4e-8853-506bcc0b757a] Ack server push request, request = NotifySubscriberRequest, requestId = 38
20:27:07.996 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:27:08.001 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:27:08.334 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:27:08.334 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@d15ca2a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:27:08.334 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753760705488_127.0.0.1_7296
20:27:08.334 [nacos-grpc-client-executor-6269] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753760705488_127.0.0.1_7296]Ignore complete event,isRunning:false,isAbandon=false
20:27:08.349 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1c5b8d63[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6270]
