package com.heju.system.file.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.file.domain.dto.SysFileRoleMergeDto;
import com.heju.system.file.domain.query.SysFileRoleMergeQuery;
import com.heju.system.file.service.ISysFileRoleMergeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 文件角色权限关联管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/merge")
public class SysFileRoleMergeController extends BaseController<SysFileRoleMergeQuery, SysFileRoleMergeDto, ISysFileRoleMergeService> {

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "文件角色权限关联" ;
    }

    /**
     * 查询文件角色权限关联列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.SYS_FILE_ROLE_MERGE_LIST)
    public AjaxResult list(SysFileRoleMergeQuery fileRoleMerge) {
        return super.list(fileRoleMerge);
    }

    /**
     * 查询文件角色权限关联详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_FILE_ROLE_MERGE_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 文件角色权限关联新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_FILE_ROLE_MERGE_ADD)
    @Log(title = "文件角色权限关联管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysFileRoleMergeDto fileRoleMerge) {
        return super.add(fileRoleMerge);
    }

    /**
     * 文件角色权限关联修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_FILE_ROLE_MERGE_EDIT)
    @Log(title = "文件角色权限关联管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysFileRoleMergeDto fileRoleMerge) {
        return super.edit(fileRoleMerge);
    }

    /**
     * 文件角色权限关联修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_FILE_ROLE_MERGE_EDIT, Auth.SYS_FILE_ROLE_MERGE_ES}, logical = Logical.OR)
    @Log(title = "文件角色权限关联管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysFileRoleMergeDto fileRoleMerge) {
        return super.editStatus(fileRoleMerge);
    }

    /**
     * 文件角色权限关联批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_FILE_ROLE_MERGE_DEL)
    @Log(title = "文件角色权限关联管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取文件角色权限关联选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

}
