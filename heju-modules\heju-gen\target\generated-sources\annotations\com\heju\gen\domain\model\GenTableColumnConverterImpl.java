package com.heju.gen.domain.model;

import com.github.pagehelper.Page;
import com.heju.gen.domain.dto.GenTableColumnDto;
import com.heju.gen.domain.po.GenTableColumnPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:44+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class GenTableColumnConverterImpl implements GenTableColumnConverter {

    @Override
    public GenTableColumnDto mapperDto(GenTableColumnPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        GenTableColumnDto genTableColumnDto = new GenTableColumnDto();

        genTableColumnDto.setId( arg0.getId() );
        genTableColumnDto.setSourceName( arg0.getSourceName() );
        genTableColumnDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            genTableColumnDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        genTableColumnDto.setName( arg0.getName() );
        genTableColumnDto.setStatus( arg0.getStatus() );
        genTableColumnDto.setSort( arg0.getSort() );
        genTableColumnDto.setRemark( arg0.getRemark() );
        genTableColumnDto.setCreateBy( arg0.getCreateBy() );
        genTableColumnDto.setCreateTime( arg0.getCreateTime() );
        genTableColumnDto.setUpdateBy( arg0.getUpdateBy() );
        genTableColumnDto.setUpdateTime( arg0.getUpdateTime() );
        genTableColumnDto.setDelFlag( arg0.getDelFlag() );
        genTableColumnDto.setCreateName( arg0.getCreateName() );
        genTableColumnDto.setUpdateName( arg0.getUpdateName() );
        genTableColumnDto.setTableId( arg0.getTableId() );
        genTableColumnDto.setComment( arg0.getComment() );
        genTableColumnDto.setType( arg0.getType() );
        genTableColumnDto.setJavaType( arg0.getJavaType() );
        genTableColumnDto.setJavaField( arg0.getJavaField() );
        genTableColumnDto.setIsPk( arg0.getIsPk() );
        genTableColumnDto.setIsIncrement( arg0.getIsIncrement() );
        genTableColumnDto.setIsRequired( arg0.getIsRequired() );
        genTableColumnDto.setIsView( arg0.getIsView() );
        genTableColumnDto.setIsInsert( arg0.getIsInsert() );
        genTableColumnDto.setIsEdit( arg0.getIsEdit() );
        genTableColumnDto.setIsList( arg0.getIsList() );
        genTableColumnDto.setIsQuery( arg0.getIsQuery() );
        genTableColumnDto.setIsUnique( arg0.getIsUnique() );
        genTableColumnDto.setIsImport( arg0.getIsImport() );
        genTableColumnDto.setIsExport( arg0.getIsExport() );
        genTableColumnDto.setIsHide( arg0.getIsHide() );
        genTableColumnDto.setIsCover( arg0.getIsCover() );
        genTableColumnDto.setQueryType( arg0.getQueryType() );
        genTableColumnDto.setHtmlType( arg0.getHtmlType() );
        genTableColumnDto.setDictType( arg0.getDictType() );

        return genTableColumnDto;
    }

    @Override
    public List<GenTableColumnDto> mapperDto(Collection<GenTableColumnPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<GenTableColumnDto> list = new ArrayList<GenTableColumnDto>( arg0.size() );
        for ( GenTableColumnPo genTableColumnPo : arg0 ) {
            list.add( mapperDto( genTableColumnPo ) );
        }

        return list;
    }

    @Override
    public Page<GenTableColumnDto> mapperPageDto(Collection<GenTableColumnPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<GenTableColumnDto> page = new Page<GenTableColumnDto>();
        for ( GenTableColumnPo genTableColumnPo : arg0 ) {
            page.add( mapperDto( genTableColumnPo ) );
        }

        return page;
    }

    @Override
    public GenTableColumnPo mapperPo(GenTableColumnDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        GenTableColumnPo genTableColumnPo = new GenTableColumnPo();

        genTableColumnPo.setId( arg0.getId() );
        genTableColumnPo.setSourceName( arg0.getSourceName() );
        genTableColumnPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            genTableColumnPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        genTableColumnPo.setName( arg0.getName() );
        genTableColumnPo.setStatus( arg0.getStatus() );
        genTableColumnPo.setSort( arg0.getSort() );
        genTableColumnPo.setRemark( arg0.getRemark() );
        genTableColumnPo.setCreateBy( arg0.getCreateBy() );
        genTableColumnPo.setCreateTime( arg0.getCreateTime() );
        genTableColumnPo.setUpdateBy( arg0.getUpdateBy() );
        genTableColumnPo.setUpdateTime( arg0.getUpdateTime() );
        genTableColumnPo.setDelFlag( arg0.getDelFlag() );
        genTableColumnPo.setCreateName( arg0.getCreateName() );
        genTableColumnPo.setUpdateName( arg0.getUpdateName() );
        genTableColumnPo.setTableId( arg0.getTableId() );
        genTableColumnPo.setComment( arg0.getComment() );
        genTableColumnPo.setType( arg0.getType() );
        genTableColumnPo.setJavaType( arg0.getJavaType() );
        genTableColumnPo.setJavaField( arg0.getJavaField() );
        genTableColumnPo.setIsPk( arg0.getIsPk() );
        genTableColumnPo.setIsIncrement( arg0.getIsIncrement() );
        genTableColumnPo.setIsRequired( arg0.getIsRequired() );
        genTableColumnPo.setIsView( arg0.getIsView() );
        genTableColumnPo.setIsInsert( arg0.getIsInsert() );
        genTableColumnPo.setIsEdit( arg0.getIsEdit() );
        genTableColumnPo.setIsList( arg0.getIsList() );
        genTableColumnPo.setIsQuery( arg0.getIsQuery() );
        genTableColumnPo.setIsUnique( arg0.getIsUnique() );
        genTableColumnPo.setIsImport( arg0.getIsImport() );
        genTableColumnPo.setIsExport( arg0.getIsExport() );
        genTableColumnPo.setIsHide( arg0.getIsHide() );
        genTableColumnPo.setIsCover( arg0.getIsCover() );
        genTableColumnPo.setQueryType( arg0.getQueryType() );
        genTableColumnPo.setHtmlType( arg0.getHtmlType() );
        genTableColumnPo.setDictType( arg0.getDictType() );

        return genTableColumnPo;
    }

    @Override
    public List<GenTableColumnPo> mapperPo(Collection<GenTableColumnDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<GenTableColumnPo> list = new ArrayList<GenTableColumnPo>( arg0.size() );
        for ( GenTableColumnDto genTableColumnDto : arg0 ) {
            list.add( mapperPo( genTableColumnDto ) );
        }

        return list;
    }

    @Override
    public Page<GenTableColumnPo> mapperPagePo(Collection<GenTableColumnDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<GenTableColumnPo> page = new Page<GenTableColumnPo>();
        for ( GenTableColumnDto genTableColumnDto : arg0 ) {
            page.add( mapperPo( genTableColumnDto ) );
        }

        return page;
    }
}
