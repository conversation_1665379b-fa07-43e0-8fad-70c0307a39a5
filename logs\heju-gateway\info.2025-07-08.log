09:36:27.138 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:36:27.725 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0
09:36:27.826 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 41 ms to scan 1 urls, producing 3 keys and 6 values 
09:36:27.860 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:36:27.869 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:36:27.883 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 11 ms to scan 1 urls, producing 1 keys and 5 values 
09:36:27.894 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:36:27.907 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:36:27.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:36:27.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000021c013b9748
09:36:27.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000021c013b9968
09:36:27.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:36:27.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:36:27.927 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:28.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:28.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:28.955 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:36:28.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:36:28.956 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000021c014c1650
09:36:29.075 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:29.298 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:29.611 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:30.027 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:30.541 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:30.703 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:36:31.171 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:31.886 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:32.694 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:33.617 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:34.628 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:35.090 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:36:35.856 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:36.353 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:36:37.025 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0
09:36:37.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:36:37.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000021c013b9748
09:36:37.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000021c013b9968
09:36:37.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:36:37.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:36:37.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:37.104 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:37.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:37.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:37.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:36:37.120 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:36:37.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000021c014c1650
09:36:37.243 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:37.461 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:37.563 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 51f6cf85-0c5d-4eef-8a1c-246c48431618
09:36:37.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] RpcClient init label, labels = {module=naming, source=sdk}
09:36:37.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:36:37.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:36:37.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:36:37.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:37.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:37.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:36:37.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:36:37.632 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:36:37.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000021c014c1650
09:36:37.766 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:37.769 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:37.977 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:38.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:38.286 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:38.432 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:38.699 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:38.706 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:39.220 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:39.312 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:39.839 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:39.855 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:39.991 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:36:39.991 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@318353[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:36:39.991 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51f6cf85-0c5d-4eef-8a1c-246c48431618] Client is shutdown, stop reconnect to server
09:36:39.991 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2cd6fae8[Running, pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 20]
09:36:40.052 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:40.875 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:41.376 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:41.807 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:42.918 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b53ceece-55a8-4e83-ad95-16a5baa372bf_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:36:42.995 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [724ffe7e-0a5a-4496-8ce5-43fbe6d13970_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:26.238 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:45:26.810 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0
09:45:26.904 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
09:45:26.934 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:45:26.943 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:45:26.953 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:45:26.966 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
09:45:26.975 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
09:45:26.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:45:26.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000151df3bc4e8
09:45:26.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000151df3bc708
09:45:26.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:45:26.981 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:45:26.989 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:27.825 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:27.833 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:27.840 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:45:27.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:45:27.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000151df4cc6e8
09:45:27.960 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:28.181 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:28.491 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:28.915 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:29.429 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:29.560 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:45:30.043 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:30.754 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:31.565 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:32.482 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:33.452 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:45:33.494 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:34.683 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:34.765 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:45:35.584 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c64ffe9c-5dac-4537-b865-c051b517e024_config-0
09:45:35.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:45:35.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000151df3bc4e8
09:45:35.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000151df3bc708
09:45:35.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:45:35.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:45:35.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:35.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:35.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:35.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:45:35.695 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:45:35.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000151df4cc6e8
09:45:35.842 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:35.910 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:36.069 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:36.203 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9
09:45:36.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] RpcClient init label, labels = {module=naming, source=sdk}
09:45:36.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:45:36.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:45:36.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:45:36.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:36.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:36.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:36.266 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:45:36.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:45:36.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x00000151df4cc6e8
09:45:36.393 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:36.402 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:36.630 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:36.840 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:36.963 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:37.239 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:37.484 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:37.492 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:38.032 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:38.095 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:38.650 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:38.652 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:38.901 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:39.375 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:39.802 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:39.992 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:45:39.992 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@37bac0f4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:45:39.993 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe40db89-6629-4e4d-9fdc-d6ff3f1fa5a9] Client is shutdown, stop reconnect to server
09:45:39.993 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@20c55658[Running, pool size = 22, active threads = 0, queued tasks = 0, completed tasks = 22]
09:45:42.087 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c64ffe9c-5dac-4537-b865-c051b517e024_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:45:42.119 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eaf7694c-40f7-467f-a5a0-ee6111dc972b_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:02.861 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:08:03.467 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0
10:08:03.535 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
10:08:03.569 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
10:08:03.582 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:08:03.593 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
10:08:03.606 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
10:08:03.616 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
10:08:03.619 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:08:03.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001fb6339c2b8
10:08:03.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001fb6339c4d8
10:08:03.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:08:03.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:08:03.630 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:04.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:04.436 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:04.443 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:08:04.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:04.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001fb634ce228
10:08:04.574 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:04.784 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:05.112 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:05.535 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:06.041 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:06.133 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:08:06.650 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:07.369 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:08.172 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:09.085 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:09.871 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:08:10.105 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:11.201 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:08:11.302 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:11.971 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0
10:08:11.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:08:11.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001fb6339c2b8
10:08:11.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001fb6339c4d8
10:08:11.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:08:11.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:08:11.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:11.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:11.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:12.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:12.000 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:08:12.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001fb634ce228
10:08:12.116 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:12.339 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:12.457 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b2d967bc-935e-4cdd-bb15-4ddad62e7b34
10:08:12.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] RpcClient init label, labels = {module=naming, source=sdk}
10:08:12.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:08:12.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:08:12.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:08:12.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:12.468 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:12.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:08:12.482 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:08:12.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:08:12.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001fb634ce228
10:08:12.530 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:12.613 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:12.647 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:12.832 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:13.285 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:13.285 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:13.695 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:13.793 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:13.844 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:14.211 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:14.411 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:14.824 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:15.092 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:08:15.092 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@218c6d3e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:08:15.092 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@63f819a6[Running, pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 20]
10:08:15.092 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2d967bc-935e-4cdd-bb15-4ddad62e7b34] Client is shutdown, stop reconnect to server
10:08:15.120 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:15.279 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:15.940 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:16.800 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:16.858 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:17.939 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68bf1c94-dea3-4c38-a965-aa744ebf24db_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:18.424 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f6e8eb7-29e6-4235-befb-61f3d69739ca_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
10:08:58.498 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:08:59.204 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3779c9a2-3930-4171-bae1-44155b427cef_config-0
10:08:59.274 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
10:08:59.301 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
10:08:59.311 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
10:08:59.322 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
10:08:59.332 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
10:08:59.347 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
10:08:59.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3779c9a2-3930-4171-bae1-44155b427cef_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:08:59.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3779c9a2-3930-4171-bae1-44155b427cef_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000158013cb8c8
10:08:59.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3779c9a2-3930-4171-bae1-44155b427cef_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000158013cbae8
10:08:59.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3779c9a2-3930-4171-bae1-44155b427cef_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:08:59.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3779c9a2-3930-4171-bae1-44155b427cef_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:08:59.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3779c9a2-3930-4171-bae1-44155b427cef_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:09:00.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3779c9a2-3930-4171-bae1-44155b427cef_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751940540227_127.0.0.1_7930
10:09:00.451 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3779c9a2-3930-4171-bae1-44155b427cef_config-0] Notify connected event to listeners.
10:09:00.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3779c9a2-3930-4171-bae1-44155b427cef_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:09:00.452 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3779c9a2-3930-4171-bae1-44155b427cef_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000015801505418
10:09:00.610 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:09:04.689 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:09:05.916 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:09:06.429 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0cecfd7d-f0f7-4232-a2b2-459edc579074_config-0
10:09:06.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cecfd7d-f0f7-4232-a2b2-459edc579074_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:09:06.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cecfd7d-f0f7-4232-a2b2-459edc579074_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000158013cb8c8
10:09:06.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cecfd7d-f0f7-4232-a2b2-459edc579074_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000158013cbae8
10:09:06.430 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cecfd7d-f0f7-4232-a2b2-459edc579074_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:09:06.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cecfd7d-f0f7-4232-a2b2-459edc579074_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:09:06.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cecfd7d-f0f7-4232-a2b2-459edc579074_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:09:06.558 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cecfd7d-f0f7-4232-a2b2-459edc579074_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751940546441_127.0.0.1_8035
10:09:06.558 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cecfd7d-f0f7-4232-a2b2-459edc579074_config-0] Notify connected event to listeners.
10:09:06.559 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cecfd7d-f0f7-4232-a2b2-459edc579074_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:09:06.559 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0cecfd7d-f0f7-4232-a2b2-459edc579074_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000015801505418
10:09:06.683 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3
10:09:06.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] RpcClient init label, labels = {module=naming, source=sdk}
10:09:06.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:09:06.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:09:06.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:09:06.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:09:06.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Success to connect to server [localhost:8848] on start up, connectionId = 1751940546698_127.0.0.1_8037
10:09:06.828 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Notify connected event to listeners.
10:09:06.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:09:06.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000015801505418
10:09:07.431 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Receive server push request, request = NotifySubscriberRequest, requestId = 3
10:09:07.432 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Ack server push request, request = NotifySubscriberRequest, requestId = 3
10:09:07.511 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Receive server push request, request = NotifySubscriberRequest, requestId = 4
10:09:07.511 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Ack server push request, request = NotifySubscriberRequest, requestId = 4
10:09:07.520 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Receive server push request, request = NotifySubscriberRequest, requestId = 5
10:09:07.520 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Ack server push request, request = NotifySubscriberRequest, requestId = 5
10:09:07.555 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
10:09:07.594 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 10.085 seconds (JVM running for 11.435)
10:09:07.601 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
10:09:07.601 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
10:09:07.603 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
10:09:08.017 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Receive server push request, request = NotifySubscriberRequest, requestId = 6
10:09:08.019 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Ack server push request, request = NotifySubscriberRequest, requestId = 6
10:09:08.139 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Receive server push request, request = NotifySubscriberRequest, requestId = 8
10:09:08.155 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Ack server push request, request = NotifySubscriberRequest, requestId = 8
10:09:37.543 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Receive server push request, request = NotifySubscriberRequest, requestId = 10
10:09:37.545 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Ack server push request, request = NotifySubscriberRequest, requestId = 10
12:03:39.803 [nacos-grpc-client-executor-2300] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Receive server push request, request = NotifySubscriberRequest, requestId = 13
12:03:39.804 [nacos-grpc-client-executor-2300] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Ack server push request, request = NotifySubscriberRequest, requestId = 13
13:11:16.962 [nacos-grpc-client-executor-3705] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Receive server push request, request = NotifySubscriberRequest, requestId = 14
13:11:16.975 [nacos-grpc-client-executor-3705] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f337ee0-bb1f-42e9-bb44-7d1f9d72d6a3] Ack server push request, request = NotifySubscriberRequest, requestId = 14
13:11:25.040 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:11:25.044 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:11:25.376 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:11:25.376 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@34099075[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:11:25.376 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751940546698_127.0.0.1_8037
13:11:25.378 [nacos-grpc-client-executor-3709] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751940546698_127.0.0.1_8037]Ignore complete event,isRunning:false,isAbandon=false
13:11:25.382 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5b17d0c9[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3710]
15:52:33.082 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:52:33.854 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f97d9c57-2980-485e-983e-8a81eb934732_config-0
15:52:33.929 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 
15:52:33.971 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
15:52:33.979 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
15:52:33.996 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
15:52:34.005 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
15:52:34.010 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
15:52:34.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:52:34.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001e3543b9478
15:52:34.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001e3543b9698
15:52:34.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:52:34.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:52:34.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:35.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:35.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:35.152 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:52:35.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:52:35.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001e3544c1870
15:52:35.277 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:35.497 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:35.812 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:36.228 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:36.745 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:36.902 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:52:37.444 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:38.206 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:39.050 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:39.967 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:40.981 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:42.205 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:43.427 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
15:52:43.532 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:45.194 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:45.815 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
15:52:46.636 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0
15:52:46.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:52:46.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001e3543b9478
15:52:46.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001e3543b9698
15:52:46.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:52:46.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:52:46.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:46.679 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:46.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:46.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:46.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:52:46.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001e3544c1870
15:52:46.698 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:52:46.825 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:47.035 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:47.139 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a
15:52:47.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] RpcClient init label, labels = {module=naming, source=sdk}
15:52:47.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:52:47.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:52:47.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:52:47.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:47.154 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:47.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:47.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:52:47.173 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:52:47.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001e3544c1870
15:52:47.299 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:47.346 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:47.517 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:47.762 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:47.836 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:48.189 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:48.257 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:48.273 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:48.771 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:48.882 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:49.327 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:52:49.327 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1deca369[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:52:49.327 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d40a1b-b7f6-4d6d-87cd-a26f27b8b25a] Client is shutdown, stop reconnect to server
15:52:49.327 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2d0ce8a1[Running, pool size = 18, active threads = 0, queued tasks = 0, completed tasks = 18]
15:52:49.597 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:49.798 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f97d9c57-2980-485e-983e-8a81eb934732_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:50.410 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:52:51.317 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb3f8bd8-bc62-4686-a204-65a745e9f7a3_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
15:53:37.433 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:53:38.163 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f9c211c1-69ab-4f3e-bc3d-99239e877058_config-0
15:53:38.253 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
15:53:38.293 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
15:53:38.308 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
15:53:38.326 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
15:53:38.348 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
15:53:38.362 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
15:53:38.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9c211c1-69ab-4f3e-bc3d-99239e877058_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:53:38.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9c211c1-69ab-4f3e-bc3d-99239e877058_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000234373ccfb8
15:53:38.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9c211c1-69ab-4f3e-bc3d-99239e877058_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000234373cd1d8
15:53:38.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9c211c1-69ab-4f3e-bc3d-99239e877058_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:53:38.373 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9c211c1-69ab-4f3e-bc3d-99239e877058_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:53:38.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9c211c1-69ab-4f3e-bc3d-99239e877058_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:39.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9c211c1-69ab-4f3e-bc3d-99239e877058_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751961219373_127.0.0.1_6524
15:53:39.589 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9c211c1-69ab-4f3e-bc3d-99239e877058_config-0] Notify connected event to listeners.
15:53:39.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9c211c1-69ab-4f3e-bc3d-99239e877058_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:53:39.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9c211c1-69ab-4f3e-bc3d-99239e877058_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000023437507b78
15:53:39.723 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:53:43.933 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
15:53:45.336 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
15:53:46.049 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 83a3491e-97b6-4371-aee1-637b247d39a6_config-0
15:53:46.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a3491e-97b6-4371-aee1-637b247d39a6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:53:46.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a3491e-97b6-4371-aee1-637b247d39a6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x00000234373ccfb8
15:53:46.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a3491e-97b6-4371-aee1-637b247d39a6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x00000234373cd1d8
15:53:46.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a3491e-97b6-4371-aee1-637b247d39a6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:53:46.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a3491e-97b6-4371-aee1-637b247d39a6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:53:46.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a3491e-97b6-4371-aee1-637b247d39a6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:46.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a3491e-97b6-4371-aee1-637b247d39a6_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751961226062_127.0.0.1_6564
15:53:46.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a3491e-97b6-4371-aee1-637b247d39a6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:53:46.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a3491e-97b6-4371-aee1-637b247d39a6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000023437507b78
15:53:46.175 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a3491e-97b6-4371-aee1-637b247d39a6_config-0] Notify connected event to listeners.
15:53:46.290 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b6f5eaa9-d5c2-464f-a788-65d8fd0060a0
15:53:46.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] RpcClient init label, labels = {module=naming, source=sdk}
15:53:46.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:53:46.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:53:46.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:53:46.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:46.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Success to connect to server [localhost:8848] on start up, connectionId = 1751961226305_127.0.0.1_6565
15:53:46.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:53:46.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000023437507b78
15:53:46.432 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Notify connected event to listeners.
15:53:46.941 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
15:53:46.984 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 10.343 seconds (JVM running for 11.663)
15:53:47.000 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
15:53:47.000 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
15:53:47.001 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
15:53:47.060 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Receive server push request, request = NotifySubscriberRequest, requestId = 3
15:53:47.060 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Ack server push request, request = NotifySubscriberRequest, requestId = 3
15:53:47.156 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Receive server push request, request = NotifySubscriberRequest, requestId = 4
15:53:47.157 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Ack server push request, request = NotifySubscriberRequest, requestId = 4
15:53:47.170 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Receive server push request, request = NotifySubscriberRequest, requestId = 5
15:53:47.172 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Ack server push request, request = NotifySubscriberRequest, requestId = 5
15:53:47.374 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Receive server push request, request = NotifySubscriberRequest, requestId = 7
15:53:47.374 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Ack server push request, request = NotifySubscriberRequest, requestId = 7
15:54:17.290 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Receive server push request, request = NotifySubscriberRequest, requestId = 9
15:54:17.293 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Ack server push request, request = NotifySubscriberRequest, requestId = 9
15:54:17.303 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Receive server push request, request = NotifySubscriberRequest, requestId = 10
15:54:17.305 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6f5eaa9-d5c2-464f-a788-65d8fd0060a0] Ack server push request, request = NotifySubscriberRequest, requestId = 10
17:35:06.303 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:35:06.316 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:35:06.650 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:35:06.650 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1a22cfda[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:35:06.650 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751961226305_127.0.0.1_6565
17:35:06.653 [nacos-grpc-client-executor-2141] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751961226305_127.0.0.1_6565]Ignore complete event,isRunning:false,isAbandon=false
17:35:06.657 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@52e5cc4c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2142]
17:35:23.824 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:35:25.075 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 465a449a-3258-41fb-9bd3-a0f4fc1857c4_config-0
17:35:25.237 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 95 ms to scan 1 urls, producing 3 keys and 6 values 
17:35:25.291 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 4 keys and 9 values 
17:35:25.315 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 10 values 
17:35:25.341 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 5 values 
17:35:25.363 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 7 values 
17:35:25.393 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 2 keys and 8 values 
17:35:25.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [465a449a-3258-41fb-9bd3-a0f4fc1857c4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:35:25.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [465a449a-3258-41fb-9bd3-a0f4fc1857c4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001f0163b9bf8
17:35:25.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [465a449a-3258-41fb-9bd3-a0f4fc1857c4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001f0163b9e18
17:35:25.403 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [465a449a-3258-41fb-9bd3-a0f4fc1857c4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:35:25.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [465a449a-3258-41fb-9bd3-a0f4fc1857c4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:35:25.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [465a449a-3258-41fb-9bd3-a0f4fc1857c4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:35:27.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [465a449a-3258-41fb-9bd3-a0f4fc1857c4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751967327075_127.0.0.1_7949
17:35:27.394 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [465a449a-3258-41fb-9bd3-a0f4fc1857c4_config-0] Notify connected event to listeners.
17:35:27.395 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [465a449a-3258-41fb-9bd3-a0f4fc1857c4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:27.396 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [465a449a-3258-41fb-9bd3-a0f4fc1857c4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001f0164f1a90
17:35:27.696 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:35:33.454 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
17:35:34.460 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
17:35:34.994 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 86f7a042-9299-4c15-8e10-71934a641177_config-0
17:35:34.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86f7a042-9299-4c15-8e10-71934a641177_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:35:34.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86f7a042-9299-4c15-8e10-71934a641177_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001f0163b9bf8
17:35:34.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86f7a042-9299-4c15-8e10-71934a641177_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001f0163b9e18
17:35:34.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86f7a042-9299-4c15-8e10-71934a641177_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:35:34.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86f7a042-9299-4c15-8e10-71934a641177_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:35:34.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86f7a042-9299-4c15-8e10-71934a641177_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:35:35.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86f7a042-9299-4c15-8e10-71934a641177_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751967335006_127.0.0.1_7983
17:35:35.123 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86f7a042-9299-4c15-8e10-71934a641177_config-0] Notify connected event to listeners.
17:35:35.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86f7a042-9299-4c15-8e10-71934a641177_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:35.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86f7a042-9299-4c15-8e10-71934a641177_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001f0164f1a90
17:35:35.268 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 130a492c-789d-48cf-aa95-08928c66d2cb
17:35:35.268 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] RpcClient init label, labels = {module=naming, source=sdk}
17:35:35.270 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:35:35.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:35:35.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:35:35.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:35:35.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Success to connect to server [localhost:8848] on start up, connectionId = 1751967335280_127.0.0.1_7984
17:35:35.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:35.400 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Notify connected event to listeners.
17:35:35.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001f0164f1a90
17:35:35.905 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
17:35:35.954 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Receive server push request, request = NotifySubscriberRequest, requestId = 13
17:35:35.978 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 13.395 seconds (JVM running for 25.307)
17:35:35.982 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Ack server push request, request = NotifySubscriberRequest, requestId = 13
17:35:35.997 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
17:35:35.997 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
17:35:36.006 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
17:35:36.023 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Receive server push request, request = NotifySubscriberRequest, requestId = 14
17:35:36.023 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Ack server push request, request = NotifySubscriberRequest, requestId = 14
17:35:36.138 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Receive server push request, request = NotifySubscriberRequest, requestId = 15
17:35:36.142 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Ack server push request, request = NotifySubscriberRequest, requestId = 15
17:35:36.172 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Receive server push request, request = NotifySubscriberRequest, requestId = 17
17:35:36.172 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Ack server push request, request = NotifySubscriberRequest, requestId = 17
17:35:36.188 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Receive server push request, request = NotifySubscriberRequest, requestId = 16
17:35:36.188 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Ack server push request, request = NotifySubscriberRequest, requestId = 16
17:35:36.201 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Receive server push request, request = NotifySubscriberRequest, requestId = 18
17:35:36.201 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [130a492c-789d-48cf-aa95-08928c66d2cb] Ack server push request, request = NotifySubscriberRequest, requestId = 18
17:42:29.352 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:42:29.357 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:42:29.703 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:42:29.703 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@598cd771[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:42:29.703 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751967335280_127.0.0.1_7984
17:42:29.706 [nacos-grpc-client-executor-188] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751967335280_127.0.0.1_7984]Ignore complete event,isRunning:false,isAbandon=false
17:42:29.707 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@158378f0[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 189]
17:42:35.159 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:42:35.695 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cb6cc3d8-d65e-4efe-ad97-96b18d402ea2_config-0
17:42:35.760 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 
17:42:35.786 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
17:42:35.793 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
17:42:35.805 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
17:42:35.814 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
17:42:35.831 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
17:42:35.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb6cc3d8-d65e-4efe-ad97-96b18d402ea2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:42:35.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb6cc3d8-d65e-4efe-ad97-96b18d402ea2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001cdc73bc2b8
17:42:35.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb6cc3d8-d65e-4efe-ad97-96b18d402ea2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001cdc73bc4d8
17:42:35.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb6cc3d8-d65e-4efe-ad97-96b18d402ea2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:42:35.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb6cc3d8-d65e-4efe-ad97-96b18d402ea2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:42:35.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb6cc3d8-d65e-4efe-ad97-96b18d402ea2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:42:36.943 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb6cc3d8-d65e-4efe-ad97-96b18d402ea2_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751967756697_127.0.0.1_8994
17:42:36.944 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb6cc3d8-d65e-4efe-ad97-96b18d402ea2_config-0] Notify connected event to listeners.
17:42:36.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb6cc3d8-d65e-4efe-ad97-96b18d402ea2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:42:36.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb6cc3d8-d65e-4efe-ad97-96b18d402ea2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001cdc74f6350
17:42:37.084 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:42:40.811 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
17:42:42.421 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
17:42:43.623 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 51cfb5e9-7f8c-413b-9a72-b99b04bb3c0c_config-0
17:42:43.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51cfb5e9-7f8c-413b-9a72-b99b04bb3c0c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:42:43.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51cfb5e9-7f8c-413b-9a72-b99b04bb3c0c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001cdc73bc2b8
17:42:43.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51cfb5e9-7f8c-413b-9a72-b99b04bb3c0c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001cdc73bc4d8
17:42:43.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51cfb5e9-7f8c-413b-9a72-b99b04bb3c0c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:42:43.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51cfb5e9-7f8c-413b-9a72-b99b04bb3c0c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:42:43.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51cfb5e9-7f8c-413b-9a72-b99b04bb3c0c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:42:43.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51cfb5e9-7f8c-413b-9a72-b99b04bb3c0c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751967763645_127.0.0.1_9023
17:42:43.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51cfb5e9-7f8c-413b-9a72-b99b04bb3c0c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:42:43.769 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51cfb5e9-7f8c-413b-9a72-b99b04bb3c0c_config-0] Notify connected event to listeners.
17:42:43.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [51cfb5e9-7f8c-413b-9a72-b99b04bb3c0c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001cdc74f6350
17:42:43.987 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 83a5f573-6678-489c-b2c5-085588570e02
17:42:43.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] RpcClient init label, labels = {module=naming, source=sdk}
17:42:43.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:42:43.995 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:42:43.995 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:42:43.995 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:42:44.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Success to connect to server [localhost:8848] on start up, connectionId = 1751967764010_127.0.0.1_9025
17:42:44.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:42:44.140 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Notify connected event to listeners.
17:42:44.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001cdc74f6350
17:42:44.725 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Receive server push request, request = NotifySubscriberRequest, requestId = 19
17:42:44.727 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Ack server push request, request = NotifySubscriberRequest, requestId = 19
17:42:44.914 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Receive server push request, request = NotifySubscriberRequest, requestId = 22
17:42:44.916 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Ack server push request, request = NotifySubscriberRequest, requestId = 22
17:42:44.931 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Receive server push request, request = NotifySubscriberRequest, requestId = 20
17:42:44.940 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Ack server push request, request = NotifySubscriberRequest, requestId = 20
17:42:44.955 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Receive server push request, request = NotifySubscriberRequest, requestId = 23
17:42:44.957 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Ack server push request, request = NotifySubscriberRequest, requestId = 23
17:42:44.970 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Receive server push request, request = NotifySubscriberRequest, requestId = 21
17:42:44.970 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Ack server push request, request = NotifySubscriberRequest, requestId = 21
17:42:44.982 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Receive server push request, request = NotifySubscriberRequest, requestId = 24
17:42:44.982 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Ack server push request, request = NotifySubscriberRequest, requestId = 24
17:42:45.228 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
17:42:45.315 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 10.712 seconds (JVM running for 11.752)
17:42:45.335 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
17:42:45.337 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
17:42:45.338 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
17:42:45.757 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Receive server push request, request = NotifySubscriberRequest, requestId = 25
17:42:45.785 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [83a5f573-6678-489c-b2c5-085588570e02] Ack server push request, request = NotifySubscriberRequest, requestId = 25
17:47:59.595 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb6cc3d8-d65e-4efe-ad97-96b18d402ea2_config-0] Receive server push request, request = ConfigChangeNotifyRequest, requestId = 26
17:47:59.595 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb6cc3d8-d65e-4efe-ad97-96b18d402ea2_config-0] Ack server push request, request = ConfigChangeNotifyRequest, requestId = 26
17:52:45.246 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:52:45.250 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:52:45.568 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:52:45.568 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1cc8ca69[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:52:45.568 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751967764010_127.0.0.1_9025
17:52:45.571 [nacos-grpc-client-executor-275] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751967764010_127.0.0.1_9025]Ignore complete event,isRunning:false,isAbandon=false
17:52:45.571 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5e96c51a[Running, pool size = 12, active threads = 1, queued tasks = 0, completed tasks = 275]
19:27:22.348 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:27:22.771 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 214f355b-b543-448f-ac28-c4edea2001e8_config-0
19:27:22.824 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
19:27:22.842 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
19:27:22.848 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
19:27:22.855 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
19:27:22.862 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
19:27:22.870 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
19:27:22.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [214f355b-b543-448f-ac28-c4edea2001e8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:27:22.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [214f355b-b543-448f-ac28-c4edea2001e8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000023c8b3bb8c8
19:27:22.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [214f355b-b543-448f-ac28-c4edea2001e8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000023c8b3bbae8
19:27:22.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [214f355b-b543-448f-ac28-c4edea2001e8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:27:22.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [214f355b-b543-448f-ac28-c4edea2001e8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:27:22.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [214f355b-b543-448f-ac28-c4edea2001e8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:27:23.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [214f355b-b543-448f-ac28-c4edea2001e8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751974043379_127.0.0.1_10386
19:27:23.566 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [214f355b-b543-448f-ac28-c4edea2001e8_config-0] Notify connected event to listeners.
19:27:23.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [214f355b-b543-448f-ac28-c4edea2001e8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:27:23.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [214f355b-b543-448f-ac28-c4edea2001e8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000023c8b4f5418
19:27:23.649 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:27:25.865 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
19:27:26.664 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
19:27:27.012 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of db1c9723-e11e-4a4a-a0d6-5f4f7c784217_config-0
19:27:27.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c9723-e11e-4a4a-a0d6-5f4f7c784217_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:27:27.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c9723-e11e-4a4a-a0d6-5f4f7c784217_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x0000023c8b3bb8c8
19:27:27.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c9723-e11e-4a4a-a0d6-5f4f7c784217_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x0000023c8b3bbae8
19:27:27.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c9723-e11e-4a4a-a0d6-5f4f7c784217_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:27:27.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c9723-e11e-4a4a-a0d6-5f4f7c784217_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:27:27.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c9723-e11e-4a4a-a0d6-5f4f7c784217_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:27:27.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c9723-e11e-4a4a-a0d6-5f4f7c784217_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751974047021_127.0.0.1_10389
19:27:27.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c9723-e11e-4a4a-a0d6-5f4f7c784217_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:27:27.131 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c9723-e11e-4a4a-a0d6-5f4f7c784217_config-0] Notify connected event to listeners.
19:27:27.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db1c9723-e11e-4a4a-a0d6-5f4f7c784217_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000023c8b4f5418
19:27:27.205 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c933707a-ecb6-4182-a842-6751a9524e2d
19:27:27.206 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c933707a-ecb6-4182-a842-6751a9524e2d] RpcClient init label, labels = {module=naming, source=sdk}
19:27:27.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c933707a-ecb6-4182-a842-6751a9524e2d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:27:27.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c933707a-ecb6-4182-a842-6751a9524e2d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:27:27.208 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c933707a-ecb6-4182-a842-6751a9524e2d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:27:27.208 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c933707a-ecb6-4182-a842-6751a9524e2d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:27:27.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c933707a-ecb6-4182-a842-6751a9524e2d] Success to connect to server [localhost:8848] on start up, connectionId = 1751974047215_127.0.0.1_10390
19:27:27.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c933707a-ecb6-4182-a842-6751a9524e2d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:27:27.338 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c933707a-ecb6-4182-a842-6751a9524e2d] Notify connected event to listeners.
19:27:27.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c933707a-ecb6-4182-a842-6751a9524e2d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x0000023c8b4f5418
19:27:27.622 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
19:27:27.651 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 5.753 seconds (JVM running for 6.616)
19:27:27.672 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
19:27:27.672 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
19:27:27.672 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
19:27:27.923 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c933707a-ecb6-4182-a842-6751a9524e2d] Receive server push request, request = NotifySubscriberRequest, requestId = 27
19:27:27.923 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c933707a-ecb6-4182-a842-6751a9524e2d] Ack server push request, request = NotifySubscriberRequest, requestId = 27
19:44:13.144 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:44:13.152 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:44:13.479 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:44:13.479 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@13feef5a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:44:13.479 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751974047215_127.0.0.1_10390
19:44:13.480 [nacos-grpc-client-executor-280] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751974047215_127.0.0.1_10390]Ignore complete event,isRunning:false,isAbandon=false
19:44:13.482 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2872ae6e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 281]
19:44:19.176 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:44:19.558 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3b75ed3a-eed6-4a99-ada4-9e46a625bee1_config-0
19:44:19.606 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 3 keys and 6 values 
19:44:19.622 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
19:44:19.629 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
19:44:19.634 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
19:44:19.641 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
19:44:19.649 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
19:44:19.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b75ed3a-eed6-4a99-ada4-9e46a625bee1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:44:19.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b75ed3a-eed6-4a99-ada4-9e46a625bee1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001ebd93bc2b8
19:44:19.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b75ed3a-eed6-4a99-ada4-9e46a625bee1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001ebd93bc4d8
19:44:19.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b75ed3a-eed6-4a99-ada4-9e46a625bee1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:44:19.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b75ed3a-eed6-4a99-ada4-9e46a625bee1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:44:19.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b75ed3a-eed6-4a99-ada4-9e46a625bee1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:44:20.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b75ed3a-eed6-4a99-ada4-9e46a625bee1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751975060125_127.0.0.1_11888
19:44:20.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b75ed3a-eed6-4a99-ada4-9e46a625bee1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:44:20.312 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b75ed3a-eed6-4a99-ada4-9e46a625bee1_config-0] Notify connected event to listeners.
19:44:20.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b75ed3a-eed6-4a99-ada4-9e46a625bee1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001ebd94f6350
19:44:20.394 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:44:23.059 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
19:44:24.034 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
19:44:24.561 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9a058bad-b254-4020-8c6c-ba38dc2cf630_config-0
19:44:24.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a058bad-b254-4020-8c6c-ba38dc2cf630_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:44:24.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a058bad-b254-4020-8c6c-ba38dc2cf630_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001ebd93bc2b8
19:44:24.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a058bad-b254-4020-8c6c-ba38dc2cf630_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001ebd93bc4d8
19:44:24.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a058bad-b254-4020-8c6c-ba38dc2cf630_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:44:24.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a058bad-b254-4020-8c6c-ba38dc2cf630_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:44:24.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a058bad-b254-4020-8c6c-ba38dc2cf630_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:44:24.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a058bad-b254-4020-8c6c-ba38dc2cf630_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751975064570_127.0.0.1_11895
19:44:24.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a058bad-b254-4020-8c6c-ba38dc2cf630_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:44:24.680 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a058bad-b254-4020-8c6c-ba38dc2cf630_config-0] Notify connected event to listeners.
19:44:24.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a058bad-b254-4020-8c6c-ba38dc2cf630_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001ebd94f6350
19:44:24.790 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5e120694-fbc0-40ac-a1a0-00d5ffce073a
19:44:24.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e120694-fbc0-40ac-a1a0-00d5ffce073a] RpcClient init label, labels = {module=naming, source=sdk}
19:44:24.792 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e120694-fbc0-40ac-a1a0-00d5ffce073a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:44:24.792 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e120694-fbc0-40ac-a1a0-00d5ffce073a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:44:24.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e120694-fbc0-40ac-a1a0-00d5ffce073a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:44:24.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e120694-fbc0-40ac-a1a0-00d5ffce073a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:44:24.927 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e120694-fbc0-40ac-a1a0-00d5ffce073a] Success to connect to server [localhost:8848] on start up, connectionId = 1751975064803_127.0.0.1_11896
19:44:24.927 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e120694-fbc0-40ac-a1a0-00d5ffce073a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:44:24.927 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e120694-fbc0-40ac-a1a0-00d5ffce073a] Notify connected event to listeners.
19:44:24.927 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e120694-fbc0-40ac-a1a0-00d5ffce073a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001ebd94f6350
19:44:25.299 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ***********:8081 register finished
19:44:25.330 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 6.597 seconds (JVM running for 7.358)
19:44:25.338 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
19:44:25.338 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
19:44:25.338 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
19:44:25.456 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e120694-fbc0-40ac-a1a0-00d5ffce073a] Receive server push request, request = NotifySubscriberRequest, requestId = 28
19:44:25.473 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e120694-fbc0-40ac-a1a0-00d5ffce073a] Ack server push request, request = NotifySubscriberRequest, requestId = 28
19:51:26.746 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:51:26.748 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:51:27.087 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:51:27.087 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1b126d7c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:51:27.087 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751975064803_127.0.0.1_11896
19:51:27.089 [nacos-grpc-client-executor-132] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751975064803_127.0.0.1_11896]Ignore complete event,isRunning:false,isAbandon=false
19:51:27.089 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4da719ec[Running, pool size = 8, active threads = 0, queued tasks = 0, completed tasks = 133]
