09:12:17.410 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:12:18.398 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 99e259f5-13de-431a-8c63-c508c2cb7be5_config-0
09:12:18.482 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 36 ms to scan 1 urls, producing 3 keys and 6 values 
09:12:18.523 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:12:18.541 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 3 keys and 10 values 
09:12:18.552 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:12:18.570 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 7 values 
09:12:18.581 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:12:18.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99e259f5-13de-431a-8c63-c508c2cb7be5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:12:18.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99e259f5-13de-431a-8c63-c508c2cb7be5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000123363bf6a0
09:12:18.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99e259f5-13de-431a-8c63-c508c2cb7be5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000123363bf8c0
09:12:18.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99e259f5-13de-431a-8c63-c508c2cb7be5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:12:18.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99e259f5-13de-431a-8c63-c508c2cb7be5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:12:18.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99e259f5-13de-431a-8c63-c508c2cb7be5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:19.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99e259f5-13de-431a-8c63-c508c2cb7be5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752541939561_127.0.0.1_5619
09:12:19.817 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99e259f5-13de-431a-8c63-c508c2cb7be5_config-0] Notify connected event to listeners.
09:12:19.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99e259f5-13de-431a-8c63-c508c2cb7be5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:19.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [99e259f5-13de-431a-8c63-c508c2cb7be5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000123364f8fb0
09:12:19.997 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:12:26.927 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:12:26.928 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:12:26.929 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:12:27.393 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:12:29.201 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:12:29.204 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:12:29.205 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:12:37.019 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:12:41.418 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4e24943b-5aaf-4ccc-af9a-e6f03c0e10de
09:12:41.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e24943b-5aaf-4ccc-af9a-e6f03c0e10de] RpcClient init label, labels = {module=naming, source=sdk}
09:12:41.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e24943b-5aaf-4ccc-af9a-e6f03c0e10de] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:12:41.421 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e24943b-5aaf-4ccc-af9a-e6f03c0e10de] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:12:41.421 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e24943b-5aaf-4ccc-af9a-e6f03c0e10de] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:12:41.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e24943b-5aaf-4ccc-af9a-e6f03c0e10de] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:41.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e24943b-5aaf-4ccc-af9a-e6f03c0e10de] Success to connect to server [localhost:8848] on start up, connectionId = 1752541961428_127.0.0.1_5761
09:12:41.545 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e24943b-5aaf-4ccc-af9a-e6f03c0e10de] Notify connected event to listeners.
09:12:41.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e24943b-5aaf-4ccc-af9a-e6f03c0e10de] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:41.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e24943b-5aaf-4ccc-af9a-e6f03c0e10de] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000123364f8fb0
09:12:41.606 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:12:41.637 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
09:12:41.835 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 25.324 seconds (JVM running for 27.192)
09:12:41.853 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:12:41.856 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:12:41.856 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:12:42.115 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e24943b-5aaf-4ccc-af9a-e6f03c0e10de] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:12:42.134 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e24943b-5aaf-4ccc-af9a-e6f03c0e10de] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:12:42.176 [RMI TCP Connection(20)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:13:53.639 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e24943b-5aaf-4ccc-af9a-e6f03c0e10de] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:13:53.640 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e24943b-5aaf-4ccc-af9a-e6f03c0e10de] Ack server push request, request = NotifySubscriberRequest, requestId = 13
11:24:20.579 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:24:20.585 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:24:20.912 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:24:20.912 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3190cd70[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:24:20.913 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752541961428_127.0.0.1_5761
11:24:20.913 [nacos-grpc-client-executor-1595] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752541961428_127.0.0.1_5761]Ignore complete event,isRunning:false,isAbandon=false
11:24:20.922 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@79179b2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1596]
11:24:21.063 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:24:21.067 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:24:21.087 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:24:21.087 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:24:28.900 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:24:29.731 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of aac0e32c-faca-48c0-a858-098e12df099c_config-0
11:24:29.808 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
11:24:29.838 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
11:24:29.847 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
11:24:29.856 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:24:29.872 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
11:24:29.881 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
11:24:29.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aac0e32c-faca-48c0-a858-098e12df099c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:24:29.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aac0e32c-faca-48c0-a858-098e12df099c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000020d013bf8e0
11:24:29.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aac0e32c-faca-48c0-a858-098e12df099c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000020d013bfb00
11:24:29.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aac0e32c-faca-48c0-a858-098e12df099c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:24:29.887 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aac0e32c-faca-48c0-a858-098e12df099c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:24:29.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aac0e32c-faca-48c0-a858-098e12df099c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:24:30.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aac0e32c-faca-48c0-a858-098e12df099c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752549870732_127.0.0.1_9449
11:24:30.931 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aac0e32c-faca-48c0-a858-098e12df099c_config-0] Notify connected event to listeners.
11:24:30.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aac0e32c-faca-48c0-a858-098e12df099c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:24:30.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aac0e32c-faca-48c0-a858-098e12df099c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000020d014f9450
11:24:31.077 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:24:34.820 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
11:24:34.821 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:24:34.821 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:24:35.044 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:24:35.907 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:24:35.909 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:24:35.909 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:24:39.484 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:24:43.054 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0639e215-fb89-43e8-a8fa-ddfb0c4ab5f7
11:24:43.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0639e215-fb89-43e8-a8fa-ddfb0c4ab5f7] RpcClient init label, labels = {module=naming, source=sdk}
11:24:43.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0639e215-fb89-43e8-a8fa-ddfb0c4ab5f7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:24:43.057 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0639e215-fb89-43e8-a8fa-ddfb0c4ab5f7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:24:43.057 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0639e215-fb89-43e8-a8fa-ddfb0c4ab5f7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:24:43.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0639e215-fb89-43e8-a8fa-ddfb0c4ab5f7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:24:43.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0639e215-fb89-43e8-a8fa-ddfb0c4ab5f7] Success to connect to server [localhost:8848] on start up, connectionId = 1752549883070_127.0.0.1_9505
11:24:43.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0639e215-fb89-43e8-a8fa-ddfb0c4ab5f7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:24:43.182 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0639e215-fb89-43e8-a8fa-ddfb0c4ab5f7] Notify connected event to listeners.
11:24:43.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0639e215-fb89-43e8-a8fa-ddfb0c4ab5f7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000020d014f9450
11:24:43.231 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
11:24:43.265 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
11:24:43.514 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 15.25 seconds (JVM running for 16.327)
11:24:43.531 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
11:24:43.554 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
11:24:43.555 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
11:24:43.713 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0639e215-fb89-43e8-a8fa-ddfb0c4ab5f7] Receive server push request, request = NotifySubscriberRequest, requestId = 16
11:24:43.732 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0639e215-fb89-43e8-a8fa-ddfb0c4ab5f7] Ack server push request, request = NotifySubscriberRequest, requestId = 16
11:24:43.963 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:25:36.323 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:25:36.323 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:25:36.669 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:25:36.670 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4b3f37b2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:25:36.671 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752549883070_127.0.0.1_9505
15:25:36.672 [nacos-grpc-client-executor-2898] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752549883070_127.0.0.1_9505]Ignore complete event,isRunning:false,isAbandon=false
15:25:36.676 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4596f0a1[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2899]
15:25:36.819 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:25:36.824 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:25:36.835 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:25:36.836 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:29:47.247 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:29:48.870 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2aa4c5fd-b51c-4586-bad9-e93faea5b4be_config-0
15:29:48.979 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 59 ms to scan 1 urls, producing 3 keys and 6 values 
15:29:49.024 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
15:29:49.042 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
15:29:49.058 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
15:29:49.085 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 1 keys and 7 values 
15:29:49.100 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
15:29:49.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aa4c5fd-b51c-4586-bad9-e93faea5b4be_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:29:49.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aa4c5fd-b51c-4586-bad9-e93faea5b4be_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001d6e23a0200
15:29:49.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aa4c5fd-b51c-4586-bad9-e93faea5b4be_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d6e23a0420
15:29:49.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aa4c5fd-b51c-4586-bad9-e93faea5b4be_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:29:49.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aa4c5fd-b51c-4586-bad9-e93faea5b4be_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:29:49.156 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aa4c5fd-b51c-4586-bad9-e93faea5b4be_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:29:50.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aa4c5fd-b51c-4586-bad9-e93faea5b4be_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752564590500_127.0.0.1_8469
15:29:50.757 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aa4c5fd-b51c-4586-bad9-e93faea5b4be_config-0] Notify connected event to listeners.
15:29:50.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aa4c5fd-b51c-4586-bad9-e93faea5b4be_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:29:50.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aa4c5fd-b51c-4586-bad9-e93faea5b4be_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001d6e251a6e0
15:29:50.925 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:29:55.187 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
15:29:55.188 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:29:55.188 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:29:55.528 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:29:56.789 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:29:56.790 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:29:56.791 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:29:59.872 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:30:02.769 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1737bd01-2a42-45e2-8c9c-d517c994234b
15:30:02.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1737bd01-2a42-45e2-8c9c-d517c994234b] RpcClient init label, labels = {module=naming, source=sdk}
15:30:02.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1737bd01-2a42-45e2-8c9c-d517c994234b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:30:02.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1737bd01-2a42-45e2-8c9c-d517c994234b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:30:02.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1737bd01-2a42-45e2-8c9c-d517c994234b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:30:02.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1737bd01-2a42-45e2-8c9c-d517c994234b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:30:02.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1737bd01-2a42-45e2-8c9c-d517c994234b] Success to connect to server [localhost:8848] on start up, connectionId = 1752564602786_127.0.0.1_8523
15:30:02.902 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1737bd01-2a42-45e2-8c9c-d517c994234b] Notify connected event to listeners.
15:30:02.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1737bd01-2a42-45e2-8c9c-d517c994234b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:30:02.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1737bd01-2a42-45e2-8c9c-d517c994234b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001d6e251a6e0
15:30:02.966 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
15:30:03.000 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
15:30:03.126 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 17.256 seconds (JVM running for 19.95)
15:30:03.139 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
15:30:03.143 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
15:30:03.144 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
15:30:03.528 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1737bd01-2a42-45e2-8c9c-d517c994234b] Receive server push request, request = NotifySubscriberRequest, requestId = 72
15:30:03.561 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1737bd01-2a42-45e2-8c9c-d517c994234b] Ack server push request, request = NotifySubscriberRequest, requestId = 72
15:30:03.589 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:18:04.708 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:18:04.713 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:18:05.062 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:18:05.062 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3f95d47f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:18:05.063 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752564602786_127.0.0.1_8523
19:18:05.074 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@520a2424[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 2738]
19:18:05.250 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:18:05.254 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:18:05.264 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:18:05.264 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
