package com.heju.system.phoneinfo.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.phoneinfo.domain.dto.SysTelephoneCodeDto;
import com.heju.system.phoneinfo.domain.po.SysTelephoneCodePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:35+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysTelephoneCodeConverterImpl implements SysTelephoneCodeConverter {

    @Override
    public SysTelephoneCodeDto mapperDto(SysTelephoneCodePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysTelephoneCodeDto sysTelephoneCodeDto = new SysTelephoneCodeDto();

        sysTelephoneCodeDto.setId( arg0.getId() );
        sysTelephoneCodeDto.setSourceName( arg0.getSourceName() );
        sysTelephoneCodeDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysTelephoneCodeDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysTelephoneCodeDto.setName( arg0.getName() );
        sysTelephoneCodeDto.setStatus( arg0.getStatus() );
        sysTelephoneCodeDto.setSort( arg0.getSort() );
        sysTelephoneCodeDto.setRemark( arg0.getRemark() );
        sysTelephoneCodeDto.setCreateBy( arg0.getCreateBy() );
        sysTelephoneCodeDto.setCreateTime( arg0.getCreateTime() );
        sysTelephoneCodeDto.setUpdateBy( arg0.getUpdateBy() );
        sysTelephoneCodeDto.setUpdateTime( arg0.getUpdateTime() );
        sysTelephoneCodeDto.setDelFlag( arg0.getDelFlag() );
        sysTelephoneCodeDto.setCreateName( arg0.getCreateName() );
        sysTelephoneCodeDto.setUpdateName( arg0.getUpdateName() );
        sysTelephoneCodeDto.setSource( arg0.getSource() );
        sysTelephoneCodeDto.setMsgContent( arg0.getMsgContent() );
        sysTelephoneCodeDto.setReceive( arg0.getReceive() );
        sysTelephoneCodeDto.setVerificationCode( arg0.getVerificationCode() );

        return sysTelephoneCodeDto;
    }

    @Override
    public List<SysTelephoneCodeDto> mapperDto(Collection<SysTelephoneCodePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysTelephoneCodeDto> list = new ArrayList<SysTelephoneCodeDto>( arg0.size() );
        for ( SysTelephoneCodePo sysTelephoneCodePo : arg0 ) {
            list.add( mapperDto( sysTelephoneCodePo ) );
        }

        return list;
    }

    @Override
    public Page<SysTelephoneCodeDto> mapperPageDto(Collection<SysTelephoneCodePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysTelephoneCodeDto> page = new Page<SysTelephoneCodeDto>();
        for ( SysTelephoneCodePo sysTelephoneCodePo : arg0 ) {
            page.add( mapperDto( sysTelephoneCodePo ) );
        }

        return page;
    }

    @Override
    public SysTelephoneCodePo mapperPo(SysTelephoneCodeDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysTelephoneCodePo sysTelephoneCodePo = new SysTelephoneCodePo();

        sysTelephoneCodePo.setId( arg0.getId() );
        sysTelephoneCodePo.setSourceName( arg0.getSourceName() );
        sysTelephoneCodePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysTelephoneCodePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysTelephoneCodePo.setName( arg0.getName() );
        sysTelephoneCodePo.setStatus( arg0.getStatus() );
        sysTelephoneCodePo.setSort( arg0.getSort() );
        sysTelephoneCodePo.setRemark( arg0.getRemark() );
        sysTelephoneCodePo.setCreateBy( arg0.getCreateBy() );
        sysTelephoneCodePo.setCreateTime( arg0.getCreateTime() );
        sysTelephoneCodePo.setUpdateBy( arg0.getUpdateBy() );
        sysTelephoneCodePo.setUpdateTime( arg0.getUpdateTime() );
        sysTelephoneCodePo.setDelFlag( arg0.getDelFlag() );
        sysTelephoneCodePo.setCreateName( arg0.getCreateName() );
        sysTelephoneCodePo.setUpdateName( arg0.getUpdateName() );
        sysTelephoneCodePo.setSource( arg0.getSource() );
        sysTelephoneCodePo.setMsgContent( arg0.getMsgContent() );
        sysTelephoneCodePo.setReceive( arg0.getReceive() );
        sysTelephoneCodePo.setVerificationCode( arg0.getVerificationCode() );

        return sysTelephoneCodePo;
    }

    @Override
    public List<SysTelephoneCodePo> mapperPo(Collection<SysTelephoneCodeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysTelephoneCodePo> list = new ArrayList<SysTelephoneCodePo>( arg0.size() );
        for ( SysTelephoneCodeDto sysTelephoneCodeDto : arg0 ) {
            list.add( mapperPo( sysTelephoneCodeDto ) );
        }

        return list;
    }

    @Override
    public Page<SysTelephoneCodePo> mapperPagePo(Collection<SysTelephoneCodeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysTelephoneCodePo> page = new Page<SysTelephoneCodePo>();
        for ( SysTelephoneCodeDto sysTelephoneCodeDto : arg0 ) {
            page.add( mapperPo( sysTelephoneCodeDto ) );
        }

        return page;
    }
}
