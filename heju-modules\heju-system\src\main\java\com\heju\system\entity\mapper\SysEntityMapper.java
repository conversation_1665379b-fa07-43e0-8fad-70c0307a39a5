package com.heju.system.entity.mapper;


import com.alibaba.fastjson.JSONObject;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.system.entity.domain.dto.SysEntityFieldDto;
import com.heju.system.entity.domain.po.SysEntity;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.domain.query.SysEntityQuery;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


/**
 * 实体字段管理管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysEntityMapper {

    @Update("<script>" +
            "ALTER TABLE " +
            "<if test='fieldBelong==1 or fieldBelong==4'>" +
            "sys_entity " +
            "</if> " +
            "<if test='fieldBelong==2 or fieldBelong==3 or fieldBelong==5'>" +
            "sys_entity_taxation " +
            "</if> " +
            " ADD COLUMN ${fieldName} " +
            "<choose>" +
            "<when test='fieldType==1'>" +
            " varchar(255)" +
            "</when>" +
            "<when test='fieldType==2'>" +
            " int(11)" +
            "</when>" +
            "<when test='fieldType==3'>" +
            " datetime" +
            "</when>" +
            "<when test='fieldType==4'>" +
            " date" +
            "</when>" +
            "<when test='fieldType==5'>" +
            " varchar(255)" +
            "</when>" +
            "<when test='fieldType==6'>" +
            " text" +
            "</when>" +
            "</choose>" +
            "</script>")
    void addNewColumn(SysEntityFieldDto Dto);

    @Update("<script>" +
            "ALTER TABLE " +
            "<if test='fieldBelong==1 or fieldBelong==4'>" +
            "sys_entity " +
            "</if> " +
            "<if test='fieldBelong==2 or fieldBelong==3 or fieldBelong==5'>" +
            "sys_entity_taxation " +
            "</if> " +
            " DROP COLUMN ${fieldName} " +
            "</script>")
    void deleteNewColumn(SysEntityFieldDto Dto);

    @Update("<script>" +
            "ALTER TABLE " +
            "<if test='fieldBelong==1 or fieldBelong==4'>" +
            "sys_entity " +
            "</if> " +
            "<if test='fieldBelong==2 or fieldBelong==3 or fieldBelong==5'>" +
            "sys_entity_taxation " +
            "</if> " +
            " CHANGE COLUMN ${oldfieldName} ${fieldName} " +
            "<choose>" +
            "<when test='fieldType==1'>" +
            " varchar(255)" +
            "</when>" +
            "<when test='fieldType==2'>" +
            " int(11)" +
            "</when>" +
            "<when test='fieldType==3'>" +
            " datetime" +
            "</when>" +
            "<when test='fieldType==4'>" +
            " date" +
            "</when>" +
            "<when test='fieldType==5'>" +
            " varchar(255)" +
            "</when>" +
            "<when test='fieldType==6'>" +
            " text" +
            "</when>" +
            "</choose>" +
            "</script>")
    void editNewColumn(SysEntityFieldDto Dto);


    @Select("<script>" +
            "select a.*,b.* from sys_entity a left join sys_entity_taxation b on a.`id`=b.entity_id " +
            "<where>" +
            " <if test='name != null'> " +
            "  a.`name` like concat('%',#{name},'%') and" +
            " </if>" +
            " del_flag=0" +
            "</where>" +
            " limit #{page},#{pageSize} " +
            "<if test='sort != null'>" +
            " order by ${sort} DESC" +
            "</if>" +
            "</script>")
    List<Map<String, Object>> list(SysEntityQuery entityQuery);

    @Select("<script>" +
            "select count(*) from sys_entity" +
            " <where> " +
            " <if test='name!=null'> " +
            " `name` like concat('%',#{name},'%')" +
            " </if>" +
            " </where>" +
            "</script>")
    Integer countList(SysEntityQuery entityQuery);

    @Select("select a.*,b.* from sys_entity a left join sys_entity_taxation b on a.`id`=b.entity_id where a.`id`=#{id}")
    Map<String, Object> getInfo(Serializable id);


    @Insert("insert into sys_entity (" +
            "`id`,`code`, `name` ,`legal_person_name` ,`credit_no` ,`register_capital` ,`register_capital_currency` ,`reg_type`, `business_scope` ,`address`,`business_status` ,`business_term` ,`belong_org`,`register_no`,`start_date` ,`partner_total` ,`branch_total` ,`employee_total` ,`status`,`create_by`" +
            ") values (" +
            "#{id},#{code},#{name},#{legalPersonName},#{creditNo},#{registerCapital},#{registerCapitalCurrency},#{regType},#{businessScope},#{address},#{businessStatus},#{businessTerm},#{belongOrg},#{registerNo},#{startDate},#{partnerTotal},#{branchTotal},#{employeeTotal},1,#{createBy}" +
            ")")
    int insertEntity(SysEntityPo sysEntityPo);

//    @Insert("<script>" +
//            "insert into sys_entity (" +
//            "`id`,`code`, `name` ,`legal_person_name` ,`credit_no` ,`register_capital` ,`register_capital_currency` ," +
//            "`reg_type`, `business_scope` ,`address`,`business_status` ,`business_term` ,`belong_org`,`register_no`," +
//            "`start_date` ,`partner_total` ,`branch_total` ,`employee_total` ,`status`,`create_by`) values " +
//            " <foreach collection='entityList' index='index' item='item' open='' separator=',' close=''>#{item}</foreach>" +
//            "(#{item.id},#{item.code},#{item.name},#{item.legalPersonName},#{item.creditNo},#{item.registerCapital}," +
//            "#{item.registerCapitalCurrency},#{item.regType},#{item.businessScope},#{item.address},#{item.businessStatus}," +
//            "#{item.businessTerm},#{item.belongOrg},#{item.registerNo},#{item.startDate},#{item.partnerTotal}," +
//            "#{item.branchTotal},#{item.employeeTotal},1,#{item.createBy})" +
//            "</script>")
//    int insertBatchEntity(@Param("entityList") List<SysEntityPo> entityList);


    @Insert("insert into sys_entity_taxation (" +
            "`entity_id`) values (#{id})")
    int insertEntityTaxation(SysEntityPo sysEntityPo);

    @Update("<script>" +
            "update sys_entity set del_flag=1 where `id` in " +
            " <foreach collection='idList' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>" +
            "</script>")
    int batchRemove(@Param("idList") List<Long> idList);

    @Update("<script>" +
            "update sys_entity set " +
            "<foreach collection='jsonObject' open='' separator=',' item='jsonValue' index='jsonKey'>" +
            " ${jsonKey} = #{jsonValue}" +
            "</foreach>" +
            " where id=#{id}" +
            "</script>")
    int updateSAIC(@Param("jsonObject") JSONObject jsonObject, @Param("id") Long id);


    @Update("<script>" +
            "update sys_entity_taxation set " +
            "<foreach collection='jsonObject' open='' separator=',' item='jsonValue' index='jsonKey'>" +
            " ${jsonKey} = #{jsonValue}" +
            "</foreach>" +
            " where entity_id=#{id}" +
            "</script>")
    int updateTaxation(@Param("jsonObject") JSONObject jsonObject, @Param("id") Long id);


    @Update("update sys_entity set ${fieldName} = #{afterText} where `id`=#{entityId}" )
    int updateSAICExamine(SysEntityExamineDto entityExamine);

    @Update("update sys_entity_taxation set ${fieldName} = #{afterText} where entity_id=#{entityId}" )
    int updateTaxationExamine(SysEntityExamineDto entityExamine);

    @Select("select `id`,`name` from sys_entity where del_flag=0")
    List<Map<String,Object>> option();

    @Select("<script>" +
            "select credit_no from sys_entity where credit_no in " +
            " <foreach collection='entityList' index='index' item='item' open='(' separator=',' close=')'>#{item.creditNo}</foreach>" +
            "</script>")
    List<String> getIdByCreditNo(@Param("entityList") List<SysEntity> entityList);

    @Select("select * from sys_entity where id = #{entityId}")
    SysEntityPo selectById(Long entityId);

    @Select("select * from sys_entity")
    SysEntityPo[] drop();

    @Select("select * from sys_entity where case when #{entityId} is not null then id = #{entityId} else 1=1 end ")
    List<SysEntityPo> findAllEntityIdAndName(Object query);

    @Select("select * from sys_entity where del_flag = 0")
    List<SysEntityPo> select();
}