package com.heju.common.flowable.utils;

import cn.hutool.core.util.ObjectUtil;
import com.heju.common.flowable.common.constant.TaskConstants;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.system.api.model.LoginUser;
import com.heju.system.api.organize.domain.dto.SysPostDto;
import com.heju.system.api.organize.domain.dto.SysUserDto;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 工作流任务工具类
 *
 * <AUTHOR>
 * @createTime 2022/4/24 12:42
 */
public class TaskUtils {

    public static String getUserId() {
        return String.valueOf(SecurityUtils.getUserId());
    }

    /**
     * 获取用户组信息
     *
     * @return candidateGroup
     */
    public static List<String> getCandidateGroup() {
        List<String> list = new ArrayList<>();
        SysUserDto user = SecurityUtils.getUser();
        if (ObjectUtil.isNotNull(user)) {
            if (ObjectUtil.isNotEmpty(user.getRoles())) {
                user.getRoles().forEach(role -> list.add(TaskConstants.ROLE_GROUP_PREFIX + role.getId()));
            }
            if (ObjectUtil.isNotNull(user.getPosts())) {
                Set<String> deptSet=new HashSet<>();
                user.getPosts().forEach(post -> deptSet.add(TaskConstants.DEPT_GROUP_PREFIX + post.getDeptId()));
                list.addAll(deptSet.stream().toList());
            }
        }
        return list;
    }
}
