package com.heju.system.forms.field.domain.dto;

import cn.hutool.core.lang.tree.Tree;
import com.heju.system.forms.field.domain.po.SysFieldPo;
import com.heju.system.forms.optionValue.domain.dto.SysOptionValueDto;
import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;
import java.util.Map;

/**
 * 字段管理 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysFieldDto extends SysFieldPo {

    @Serial
    private static final long serialVersionUID = 1L;

    //表单API名称
    private String sheetApiName;

    //引用表API名称
    private String quoteSheetApiName;

    //引用字段API名称
    private String quoteSheetFieldApiName;

    //触发器名称
    private String triggerName;

    //角色id列表
    private List<Long> roleIds;

    //关联表名
    private String relationSheetName;

    //关联字段名
    private String relationSheetFieldName;

    //表名称
    private String sheetName;

    //引用表名称
    private String quoteSheetName;

    //引用字段名称
    private String quoteSheetFieldName;

    //引用字段类型
    private String quoteFieldType;

    //选项列表
    private List<SysOptionValueDto> optionValueList;


    private List<SysOptionValuePo> optionValuePoList;

    private List<Tree<String>> casecadeTreeList;

    private List<Map<String,Object>> sheetOptionList;
}