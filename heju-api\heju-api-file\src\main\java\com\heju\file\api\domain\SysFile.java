package com.heju.file.api.domain;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 文件信息
 *
 * <AUTHOR>
 */
@Data
public class SysFile implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** id */
    protected Long id;

    /** 名称 */
    protected String name;

    /** 分类Id */
    protected Long folderId;

    /** 文件别名 */
    protected String nick;

    /** 文件地址 */
    protected String url;

    /** 文件大小 */
    protected Long size;

    /** 文件类型（0默认 1系统） */
    protected String type;

}
