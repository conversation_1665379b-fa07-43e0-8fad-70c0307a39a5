package com.heju.system.api.authority.feign.factory;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.system.api.authority.domain.dto.SysRoleDto;
import com.heju.system.api.authority.domain.dto.SysRoleGroupDto;
import com.heju.system.api.authority.feign.RemoteRoleService;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 角色服务 降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteRoleFallbackFactory implements FallbackFactory<RemoteRoleService> {

    @Override
    public RemoteRoleService create(Throwable throwable) {
        log.error("角色服务调用失败:{}", throwable.getMessage());
        return new RemoteRoleService() {
            @Override
            public AjaxResult addInner(SysRoleDto role, String source,String sourceName,Long enterpriseId) {
                return AjaxResult.error("添加角色失败:" + throwable.getMessage());
            }

            @Override
            public R<SysUserDto> getRoleIdsByRoleKey(String roleKey, String source, String sourceName, Long enterpriseId) {
                return R.fail("查询角色id失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult addInner(SysRoleGroupDto roleGroup, String source, String sourceName) {
                return AjaxResult.error("添加角色组失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult getInfoInner(Long id, String source, String sourceName) {
                return AjaxResult.error("查询角色信息失败:" + throwable.getMessage());
            }
        };
    }
}
