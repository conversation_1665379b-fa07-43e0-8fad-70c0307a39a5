09:20:56.613 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:21:00.511 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0
09:21:00.967 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 224 ms to scan 1 urls, producing 3 keys and 6 values 
09:21:01.163 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 82 ms to scan 1 urls, producing 4 keys and 9 values 
09:21:01.222 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 49 ms to scan 1 urls, producing 3 keys and 10 values 
09:21:01.276 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 39 ms to scan 1 urls, producing 1 keys and 5 values 
09:21:01.335 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 50 ms to scan 1 urls, producing 1 keys and 7 values 
09:21:01.393 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 2 keys and 8 values 
09:21:01.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:21:01.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001ffb53b8fc8
09:21:01.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001ffb53b91e8
09:21:01.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:21:01.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:21:01.469 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:07.024 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:07.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:07.095 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:21:07.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:07.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001ffb54c1650
09:21:07.268 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:07.524 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:07.860 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:08.292 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:08.840 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:09.334 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:21:09.475 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:10.218 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:11.096 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:12.071 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:13.188 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:15.101 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:16.885 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:18.551 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:20.390 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:22.264 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:24.242 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:26.300 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:28.442 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:30.587 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:31.337 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:21:32.889 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:35.268 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:36.052 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:21:37.674 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:38.109 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0
09:21:38.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:21:38.115 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001ffb53b8fc8
09:21:38.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001ffb53b91e8
09:21:38.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:21:38.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:21:38.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:38.134 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:38.149 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:38.167 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:21:38.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:38.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001ffb54c1650
09:21:38.313 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:38.542 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:38.867 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:38.890 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 383cbf81-0bb4-4dd4-9b29-df081f643817
09:21:38.892 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] RpcClient init label, labels = {module=naming, source=sdk}
09:21:38.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:38.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:38.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:38.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:38.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:38.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:38.956 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:21:38.956 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:38.956 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001ffb54c1650
09:21:39.101 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:39.322 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:39.341 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:39.667 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:39.931 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:40.008 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:40.080 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:40.594 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:40.661 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:41.325 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:41.325 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:42.051 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:42.170 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:42.424 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:42.872 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:43.088 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:43.362 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:21:43.363 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2b8f5aa4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:21:43.363 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7d0e46dd[Running, pool size = 24, active threads = 0, queued tasks = 0, completed tasks = 24]
09:21:43.364 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [383cbf81-0bb4-4dd4-9b29-df081f643817] Client is shutdown, stop reconnect to server
09:21:44.111 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:44.952 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddd8e31-b908-47cf-9dbb-65e7a965852c_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:45.237 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:21:46.690 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5d6a0cd8-2c1f-466d-8747-579e269dc1e2_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:22:51.085 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:22:53.047 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 269d9e19-8019-471d-964c-b9d02781c6da_config-0
09:22:53.210 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 85 ms to scan 1 urls, producing 3 keys and 6 values 
09:22:53.281 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 4 keys and 9 values 
09:22:53.306 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 10 values 
09:22:53.339 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 1 keys and 5 values 
09:22:53.373 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 1 keys and 7 values 
09:22:53.392 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
09:22:53.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269d9e19-8019-471d-964c-b9d02781c6da_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:22:53.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269d9e19-8019-471d-964c-b9d02781c6da_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001b8cc3b42b8
09:22:53.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269d9e19-8019-471d-964c-b9d02781c6da_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001b8cc3b44d8
09:22:53.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269d9e19-8019-471d-964c-b9d02781c6da_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:22:53.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269d9e19-8019-471d-964c-b9d02781c6da_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:22:53.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269d9e19-8019-471d-964c-b9d02781c6da_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:22:56.522 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269d9e19-8019-471d-964c-b9d02781c6da_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751419376052_127.0.0.1_3542
09:22:56.523 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269d9e19-8019-471d-964c-b9d02781c6da_config-0] Notify connected event to listeners.
09:22:56.524 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269d9e19-8019-471d-964c-b9d02781c6da_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:22:56.524 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [269d9e19-8019-471d-964c-b9d02781c6da_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001b8cc4ee848
09:22:56.975 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:23:10.555 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:23:13.034 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:23:13.948 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0
09:23:13.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:23:13.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001b8cc3b42b8
09:23:13.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001b8cc3b44d8
09:23:13.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:23:13.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:23:13.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:23:14.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751419393958_127.0.0.1_3562
09:23:14.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:14.077 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] Notify connected event to listeners.
09:23:14.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001b8cc4ee848
09:23:14.266 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5c204bf3-8d13-4d35-b5bb-0d1c75851f50
09:23:14.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] RpcClient init label, labels = {module=naming, source=sdk}
09:23:14.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:23:14.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:23:14.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:23:14.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:23:14.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751419394283_127.0.0.1_3563
09:23:14.424 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Notify connected event to listeners.
09:23:14.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:14.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001b8cc4ee848
09:23:15.046 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:23:15.046 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:23:15.119 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:23:15.119 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:23:15.189 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
09:23:15.239 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:23:15.239 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:23:15.263 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:23:15.275 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:23:15.291 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 26.552 seconds (JVM running for 30.419)
09:23:15.291 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:23:15.300 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:23:15.300 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:23:15.796 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:23:15.796 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:23:45.354 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:23:45.354 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:37:46.096 [nacos-grpc-client-executor-314] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:37:46.096 [nacos-grpc-client-executor-314] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:42:55.241 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] Server healthy check fail, currentConnection = 1751419393958_127.0.0.1_3562
09:42:55.345 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:42:59.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1751420579396_127.0.0.1_5608
09:42:59.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1751419393958_127.0.0.1_3562
09:42:59.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751419393958_127.0.0.1_3562
09:42:59.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] Notify disconnected event to listeners
09:42:59.731 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01d95ffe-6bbe-42fc-9624-8c2fc3a3f292_config-0] Notify connected event to listeners.
09:43:00.109 [nacos-grpc-client-executor-423] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:43:00.124 [nacos-grpc-client-executor-423] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 14
09:43:00.246 [nacos-grpc-client-executor-424] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 15
09:43:00.267 [nacos-grpc-client-executor-424] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 15
09:43:00.419 [nacos-grpc-client-executor-425] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 16
09:43:00.435 [nacos-grpc-client-executor-425] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 16
09:43:00.646 [nacos-grpc-client-executor-426] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 17
09:43:00.660 [nacos-grpc-client-executor-426] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 17
09:43:02.374 [nacos-grpc-client-executor-427] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 18
09:43:02.384 [nacos-grpc-client-executor-427] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 18
09:43:02.478 [nacos-grpc-client-executor-428] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 20
09:43:02.493 [nacos-grpc-client-executor-428] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 20
09:43:02.593 [nacos-grpc-client-executor-429] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 22
09:43:02.611 [nacos-grpc-client-executor-429] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 22
09:43:03.153 [nacos-grpc-client-executor-430] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 24
09:43:03.170 [nacos-grpc-client-executor-430] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 24
09:53:59.010 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Server healthy check fail, currentConnection = 1751419394283_127.0.0.1_3563
09:53:59.010 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:54:02.101 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Success to connect a server [127.0.0.1:8848], connectionId = 1751421240860_127.0.0.1_6325
09:54:02.102 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1751419394283_127.0.0.1_3563
09:54:02.102 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751419394283_127.0.0.1_3563
09:54:02.110 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Notify disconnected event to listeners
09:54:02.144 [nacos-grpc-client-executor-650] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751419394283_127.0.0.1_3563]Ignore complete event,isRunning:true,isAbandon=true
09:54:02.149 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Notify connected event to listeners.
09:54:04.629 [nacos-grpc-client-executor-659] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 31
09:54:04.630 [nacos-grpc-client-executor-659] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 31
09:54:04.633 [nacos-grpc-client-executor-660] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 30
09:54:04.634 [nacos-grpc-client-executor-660] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 30
09:54:04.635 [nacos-grpc-client-executor-661] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 27
09:54:04.636 [nacos-grpc-client-executor-661] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 27
09:54:04.637 [nacos-grpc-client-executor-662] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 28
09:54:04.637 [nacos-grpc-client-executor-662] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 28
09:54:04.639 [nacos-grpc-client-executor-663] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 32
09:54:04.654 [nacos-grpc-client-executor-663] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 32
09:54:04.656 [nacos-grpc-client-executor-664] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 29
09:54:04.657 [nacos-grpc-client-executor-664] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 29
09:54:05.493 [nacos-grpc-client-executor-665] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 34
09:54:05.507 [nacos-grpc-client-executor-665] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 34
10:17:54.627 [nacos-grpc-client-executor-1144] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 35
10:17:54.675 [nacos-grpc-client-executor-1144] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 35
10:17:56.861 [nacos-grpc-client-executor-1145] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 37
10:17:56.879 [nacos-grpc-client-executor-1145] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 37
10:35:54.282 [nacos-grpc-client-executor-1502] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 38
10:35:54.301 [nacos-grpc-client-executor-1502] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 38
10:36:14.973 [nacos-grpc-client-executor-1507] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 40
10:36:14.990 [nacos-grpc-client-executor-1507] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 40
10:40:48.419 [nacos-grpc-client-executor-1604] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 42
10:40:48.446 [nacos-grpc-client-executor-1604] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 42
10:41:16.007 [nacos-grpc-client-executor-1611] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 44
10:41:16.036 [nacos-grpc-client-executor-1611] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 44
11:41:36.138 [nacos-grpc-client-executor-2806] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 45
11:41:36.157 [nacos-grpc-client-executor-2806] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 45
11:42:00.949 [nacos-grpc-client-executor-2812] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 47
11:42:00.976 [nacos-grpc-client-executor-2812] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 47
11:51:50.602 [nacos-grpc-client-executor-2999] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 48
11:51:50.620 [nacos-grpc-client-executor-2999] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 48
11:52:19.498 [nacos-grpc-client-executor-3012] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 50
11:52:19.518 [nacos-grpc-client-executor-3012] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 50
12:12:45.304 [nacos-grpc-client-executor-3417] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 51
12:12:45.332 [nacos-grpc-client-executor-3417] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 51
12:15:19.785 [nacos-grpc-client-executor-3473] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 53
12:15:19.808 [nacos-grpc-client-executor-3473] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 53
12:16:44.521 [nacos-grpc-client-executor-3500] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 55
12:16:44.551 [nacos-grpc-client-executor-3500] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 55
12:17:10.516 [nacos-grpc-client-executor-3507] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 57
12:17:10.552 [nacos-grpc-client-executor-3507] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 57
12:17:36.324 [nacos-grpc-client-executor-3518] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 58
12:17:36.350 [nacos-grpc-client-executor-3518] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 58
12:18:00.285 [nacos-grpc-client-executor-3524] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 60
12:18:00.313 [nacos-grpc-client-executor-3524] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 60
12:20:06.058 [nacos-grpc-client-executor-3565] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 61
12:20:06.096 [nacos-grpc-client-executor-3565] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 61
12:20:50.455 [nacos-grpc-client-executor-3584] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 63
12:20:50.485 [nacos-grpc-client-executor-3584] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 63
12:31:26.286 [nacos-grpc-client-executor-3781] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 64
12:31:26.298 [nacos-grpc-client-executor-3781] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 64
12:31:52.957 [nacos-grpc-client-executor-3788] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 66
12:31:52.988 [nacos-grpc-client-executor-3788] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 66
13:27:49.698 [nacos-grpc-client-executor-4887] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 67
13:27:49.749 [nacos-grpc-client-executor-4887] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 67
13:28:25.802 [nacos-grpc-client-executor-4903] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 69
13:28:25.839 [nacos-grpc-client-executor-4903] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 69
13:35:27.530 [nacos-grpc-client-executor-5046] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Receive server push request, request = NotifySubscriberRequest, requestId = 70
13:35:27.621 [nacos-grpc-client-executor-5046] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c204bf3-8d13-4d35-b5bb-0d1c75851f50] Ack server push request, request = NotifySubscriberRequest, requestId = 70
13:40:37.787 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:40:37.801 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:40:38.135 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:40:38.135 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@623817c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:40:38.135 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751421240860_127.0.0.1_6325
13:40:38.139 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1eaff0a6[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 5151]
14:14:42.525 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:14:43.453 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0152e84a-5e70-4a95-8477-6a14235f4ba7_config-0
14:14:43.553 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
14:14:43.591 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
14:14:43.605 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:14:43.626 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
14:14:43.652 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
14:14:43.663 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
14:14:43.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0152e84a-5e70-4a95-8477-6a14235f4ba7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:14:43.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0152e84a-5e70-4a95-8477-6a14235f4ba7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000002a4c43bb650
14:14:43.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0152e84a-5e70-4a95-8477-6a14235f4ba7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000002a4c43bb870
14:14:43.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0152e84a-5e70-4a95-8477-6a14235f4ba7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:14:43.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0152e84a-5e70-4a95-8477-6a14235f4ba7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:14:43.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0152e84a-5e70-4a95-8477-6a14235f4ba7_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:14:45.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0152e84a-5e70-4a95-8477-6a14235f4ba7_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751436884770_127.0.0.1_9395
14:14:45.031 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0152e84a-5e70-4a95-8477-6a14235f4ba7_config-0] Notify connected event to listeners.
14:14:45.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0152e84a-5e70-4a95-8477-6a14235f4ba7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:14:45.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0152e84a-5e70-4a95-8477-6a14235f4ba7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002a4c44f4d90
14:14:45.187 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:14:49.911 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:14:51.337 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:14:52.059 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 511af534-e92c-4b00-8878-6d0ceb7c4f97_config-0
14:14:52.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [511af534-e92c-4b00-8878-6d0ceb7c4f97_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:14:52.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [511af534-e92c-4b00-8878-6d0ceb7c4f97_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000002a4c43bb650
14:14:52.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [511af534-e92c-4b00-8878-6d0ceb7c4f97_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000002a4c43bb870
14:14:52.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [511af534-e92c-4b00-8878-6d0ceb7c4f97_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:14:52.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [511af534-e92c-4b00-8878-6d0ceb7c4f97_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:14:52.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [511af534-e92c-4b00-8878-6d0ceb7c4f97_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:14:52.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [511af534-e92c-4b00-8878-6d0ceb7c4f97_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751436892071_127.0.0.1_9405
14:14:52.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [511af534-e92c-4b00-8878-6d0ceb7c4f97_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:14:52.184 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [511af534-e92c-4b00-8878-6d0ceb7c4f97_config-0] Notify connected event to listeners.
14:14:52.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [511af534-e92c-4b00-8878-6d0ceb7c4f97_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002a4c44f4d90
14:14:52.322 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0aad78e9-9da0-460d-8d7c-8e16c8440c62
14:14:52.323 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] RpcClient init label, labels = {module=naming, source=sdk}
14:14:52.326 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:14:52.326 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:14:52.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:14:52.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:14:52.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751436892338_127.0.0.1_9406
14:14:52.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:14:52.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002a4c44f4d90
14:14:52.467 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Notify connected event to listeners.
14:14:53.018 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Receive server push request, request = NotifySubscriberRequest, requestId = 71
14:14:53.019 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Ack server push request, request = NotifySubscriberRequest, requestId = 71
14:14:53.257 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
14:14:53.310 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 11.663 seconds (JVM running for 13.985)
14:14:53.348 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
14:14:53.349 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
14:14:53.350 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
14:14:53.761 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Receive server push request, request = NotifySubscriberRequest, requestId = 72
14:14:53.763 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Ack server push request, request = NotifySubscriberRequest, requestId = 72
14:15:23.223 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Receive server push request, request = NotifySubscriberRequest, requestId = 77
14:15:23.224 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Ack server push request, request = NotifySubscriberRequest, requestId = 77
14:15:23.234 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Receive server push request, request = NotifySubscriberRequest, requestId = 76
14:15:23.236 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Ack server push request, request = NotifySubscriberRequest, requestId = 76
14:15:23.247 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Receive server push request, request = NotifySubscriberRequest, requestId = 78
14:15:23.247 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Ack server push request, request = NotifySubscriberRequest, requestId = 78
14:15:53.217 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Receive server push request, request = NotifySubscriberRequest, requestId = 80
14:15:53.219 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0aad78e9-9da0-460d-8d7c-8e16c8440c62] Ack server push request, request = NotifySubscriberRequest, requestId = 80
14:20:06.233 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:20:06.239 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:20:06.592 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:20:06.593 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3a6070d0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:20:06.593 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751436892338_127.0.0.1_9406
14:20:06.602 [nacos-grpc-client-executor-132] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751436892338_127.0.0.1_9406]Ignore complete event,isRunning:false,isAbandon=false
14:20:06.602 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@637e43ec[Running, pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 132]
17:34:52.169 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:34:53.461 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9524364b-adf2-49fa-8147-19f076903f8f_config-0
17:34:53.601 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 74 ms to scan 1 urls, producing 3 keys and 6 values 
17:34:53.670 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
17:34:53.689 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
17:34:53.709 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 5 values 
17:34:53.727 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
17:34:53.749 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
17:34:53.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:34:53.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000020aad3b9748
17:34:53.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000020aad3b9968
17:34:53.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:34:53.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:34:53.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:34:55.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751448895575_127.0.0.1_12962
17:34:55.954 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Notify connected event to listeners.
17:34:55.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:34:55.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000020aad4f1450
17:34:56.305 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:35:06.519 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
17:35:09.349 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
17:35:10.595 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 411ee505-30ae-48fd-baa9-620cbc937112_config-0
17:35:10.595 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:35:10.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000020aad3b9748
17:35:10.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000020aad3b9968
17:35:10.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:35:10.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:35:10.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:35:10.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751448910619_127.0.0.1_12987
17:35:10.741 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Notify connected event to listeners.
17:35:10.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:10.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000020aad4f1450
17:35:11.005 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d
17:35:11.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] RpcClient init label, labels = {module=naming, source=sdk}
17:35:11.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:35:11.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:35:11.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:35:11.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:35:11.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751448911043_127.0.0.1_12988
17:35:11.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:11.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000020aad4f1450
17:35:11.171 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Notify connected event to listeners.
17:35:11.785 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 92
17:35:11.787 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 92
17:35:11.975 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 93
17:35:11.977 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 93
17:35:12.197 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.2.43:8081 register finished
17:35:12.278 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 21.259 seconds (JVM running for 53.178)
17:35:12.293 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
17:35:12.296 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
17:35:12.297 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
17:35:12.743 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 94
17:35:12.743 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 94
17:35:42.071 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 96
17:35:42.071 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 96
17:36:12.156 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 100
17:36:12.156 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 100
17:36:12.173 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 99
17:36:12.173 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 99
17:36:27.225 [nacos-grpc-client-executor-66] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 102
17:36:27.227 [nacos-grpc-client-executor-66] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 102
17:44:54.578 [nacos-grpc-client-executor-224] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 103
17:44:54.608 [nacos-grpc-client-executor-224] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 103
17:45:24.355 [nacos-grpc-client-executor-235] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 105
17:45:24.380 [nacos-grpc-client-executor-235] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 105
17:55:02.787 [nacos-grpc-client-executor-408] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 106
17:55:02.808 [nacos-grpc-client-executor-408] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 106
17:55:05.736 [nacos-grpc-client-executor-409] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 107
17:55:05.753 [nacos-grpc-client-executor-409] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 107
18:17:16.786 [nacos-grpc-client-executor-845] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 108
18:17:16.812 [nacos-grpc-client-executor-845] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 108
18:26:07.626 [nacos-grpc-client-executor-1017] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 110
18:26:07.651 [nacos-grpc-client-executor-1017] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 110
18:29:23.555 [nacos-grpc-client-executor-1078] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 111
18:29:23.583 [nacos-grpc-client-executor-1078] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 111
18:29:57.283 [nacos-grpc-client-executor-1087] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 113
18:29:57.310 [nacos-grpc-client-executor-1087] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 113
19:08:57.221 [nacos-grpc-client-executor-1802] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 114
19:08:57.245 [nacos-grpc-client-executor-1802] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 114
19:09:33.984 [nacos-grpc-client-executor-1817] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 116
19:09:34.013 [nacos-grpc-client-executor-1817] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 116
19:10:07.898 [nacos-grpc-client-executor-1826] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 120
19:10:07.924 [nacos-grpc-client-executor-1826] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 120
19:13:54.467 [nacos-grpc-client-executor-1898] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 122
19:13:54.499 [nacos-grpc-client-executor-1898] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 122
19:14:29.579 [nacos-grpc-client-executor-1913] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 125
19:14:29.606 [nacos-grpc-client-executor-1913] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 125
19:22:32.041 [nacos-grpc-client-executor-2062] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 127
19:22:32.062 [nacos-grpc-client-executor-2062] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 127
19:23:02.725 [nacos-grpc-client-executor-2072] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 130
19:23:02.752 [nacos-grpc-client-executor-2072] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 130
19:24:42.662 [nacos-grpc-client-executor-2102] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 132
19:24:42.695 [nacos-grpc-client-executor-2102] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 132
19:25:14.405 [nacos-grpc-client-executor-2115] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 135
19:25:14.454 [nacos-grpc-client-executor-2115] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 135
19:39:06.451 [nacos-grpc-client-executor-2365] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 137
19:39:06.475 [nacos-grpc-client-executor-2365] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 137
19:39:39.567 [nacos-grpc-client-executor-2377] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 140
19:39:39.608 [nacos-grpc-client-executor-2377] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 140
20:28:31.900 [nacos-grpc-client-executor-3304] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 141
20:28:31.934 [nacos-grpc-client-executor-3304] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 141
20:28:35.805 [nacos-grpc-client-executor-3305] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 143
20:28:35.823 [nacos-grpc-client-executor-3305] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 143
20:34:45.966 [nacos-grpc-client-executor-3425] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 146
20:34:45.992 [nacos-grpc-client-executor-3425] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 146
20:40:15.605 [nacos-grpc-client-executor-3530] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 148
20:40:15.625 [nacos-grpc-client-executor-3530] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 148
20:40:47.077 [nacos-grpc-client-executor-3542] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 151
20:40:47.097 [nacos-grpc-client-executor-3542] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 151
20:47:23.022 [nacos-grpc-client-executor-3668] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 153
20:47:23.052 [nacos-grpc-client-executor-3668] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 153
20:48:01.199 [nacos-grpc-client-executor-3679] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 156
20:48:01.238 [nacos-grpc-client-executor-3679] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 156
20:50:05.076 [nacos-grpc-client-executor-3716] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 158
20:50:05.102 [nacos-grpc-client-executor-3716] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 158
20:50:43.511 [nacos-grpc-client-executor-3728] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 161
20:50:43.543 [nacos-grpc-client-executor-3728] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 161
21:12:22.427 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:12:22.427 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:12:22.427 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:12:22.566 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:22.567 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:22.567 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:22.784 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:22.784 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:22.798 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:23.098 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:23.101 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:23.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:23.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:23.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:23.522 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:24.021 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:24.024 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:24.029 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:24.635 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:24.635 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:24.667 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:25.392 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:25.393 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:25.393 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:25.427 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
21:12:25.752 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
21:12:26.089 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
21:12:26.089 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2b25c22c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
21:12:26.090 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751448911043_127.0.0.1_12988
21:12:26.090 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be7ddbfb-76d4-4c2d-9ea0-8bceaf4f7f9d] Client is shutdown, stop reconnect to server
21:12:26.090 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@59793dd2[Running, pool size = 19, active threads = 0, queued tasks = 0, completed tasks = 4139]
21:12:26.198 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:26.225 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:27.104 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:27.133 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:28.118 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9524364b-adf2-49fa-8147-19f076903f8f_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
21:12:28.137 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [411ee505-30ae-48fd-baa9-620cbc937112_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
