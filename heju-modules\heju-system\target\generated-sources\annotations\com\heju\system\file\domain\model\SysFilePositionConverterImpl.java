package com.heju.system.file.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.file.domain.dto.SysFilePositionDto;
import com.heju.system.file.domain.po.SysFilePositionPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:36+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysFilePositionConverterImpl implements SysFilePositionConverter {

    @Override
    public Page<SysFilePositionDto> mapperPageDto(Collection<SysFilePositionPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFilePositionDto> page = new Page<SysFilePositionDto>();
        for ( SysFilePositionPo sysFilePositionPo : arg0 ) {
            page.add( mapperDto( sysFilePositionPo ) );
        }

        return page;
    }

    @Override
    public Page<SysFilePositionPo> mapperPagePo(Collection<SysFilePositionDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFilePositionPo> page = new Page<SysFilePositionPo>();
        for ( SysFilePositionDto sysFilePositionDto : arg0 ) {
            page.add( mapperPo( sysFilePositionDto ) );
        }

        return page;
    }

    @Override
    public SysFilePositionDto mapperDto(SysFilePositionPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFilePositionDto sysFilePositionDto = new SysFilePositionDto();

        sysFilePositionDto.setId( arg0.getId() );
        sysFilePositionDto.setSourceName( arg0.getSourceName() );
        sysFilePositionDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFilePositionDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFilePositionDto.setName( arg0.getName() );
        sysFilePositionDto.setStatus( arg0.getStatus() );
        sysFilePositionDto.setSort( arg0.getSort() );
        sysFilePositionDto.setCreateBy( arg0.getCreateBy() );
        sysFilePositionDto.setCreateTime( arg0.getCreateTime() );
        sysFilePositionDto.setUpdateBy( arg0.getUpdateBy() );
        sysFilePositionDto.setUpdateTime( arg0.getUpdateTime() );
        sysFilePositionDto.setDelFlag( arg0.getDelFlag() );
        sysFilePositionDto.setCreateName( arg0.getCreateName() );
        sysFilePositionDto.setUpdateName( arg0.getUpdateName() );
        sysFilePositionDto.setParentId( arg0.getParentId() );
        sysFilePositionDto.setParentName( arg0.getParentName() );
        sysFilePositionDto.setAncestors( arg0.getAncestors() );
        sysFilePositionDto.setLevel( arg0.getLevel() );
        sysFilePositionDto.setDefaultNode( arg0.getDefaultNode() );
        sysFilePositionDto.setOldAncestors( arg0.getOldAncestors() );
        sysFilePositionDto.setOldLevel( arg0.getOldLevel() );
        sysFilePositionDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysFilePositionDto.setCode( arg0.getCode() );
        sysFilePositionDto.setRemark( arg0.getRemark() );

        return sysFilePositionDto;
    }

    @Override
    public List<SysFilePositionDto> mapperDto(Collection<SysFilePositionPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFilePositionDto> list = new ArrayList<SysFilePositionDto>( arg0.size() );
        for ( SysFilePositionPo sysFilePositionPo : arg0 ) {
            list.add( mapperDto( sysFilePositionPo ) );
        }

        return list;
    }

    @Override
    public SysFilePositionPo mapperPo(SysFilePositionDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFilePositionPo sysFilePositionPo = new SysFilePositionPo();

        sysFilePositionPo.setId( arg0.getId() );
        sysFilePositionPo.setSourceName( arg0.getSourceName() );
        sysFilePositionPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFilePositionPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFilePositionPo.setName( arg0.getName() );
        sysFilePositionPo.setStatus( arg0.getStatus() );
        sysFilePositionPo.setSort( arg0.getSort() );
        sysFilePositionPo.setCreateBy( arg0.getCreateBy() );
        sysFilePositionPo.setCreateTime( arg0.getCreateTime() );
        sysFilePositionPo.setUpdateBy( arg0.getUpdateBy() );
        sysFilePositionPo.setUpdateTime( arg0.getUpdateTime() );
        sysFilePositionPo.setDelFlag( arg0.getDelFlag() );
        sysFilePositionPo.setCreateName( arg0.getCreateName() );
        sysFilePositionPo.setUpdateName( arg0.getUpdateName() );
        sysFilePositionPo.setParentId( arg0.getParentId() );
        sysFilePositionPo.setParentName( arg0.getParentName() );
        sysFilePositionPo.setAncestors( arg0.getAncestors() );
        sysFilePositionPo.setLevel( arg0.getLevel() );
        sysFilePositionPo.setDefaultNode( arg0.getDefaultNode() );
        sysFilePositionPo.setOldAncestors( arg0.getOldAncestors() );
        sysFilePositionPo.setOldLevel( arg0.getOldLevel() );
        sysFilePositionPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysFilePositionPo.setCode( arg0.getCode() );
        sysFilePositionPo.setRemark( arg0.getRemark() );

        return sysFilePositionPo;
    }

    @Override
    public List<SysFilePositionPo> mapperPo(Collection<SysFilePositionDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFilePositionPo> list = new ArrayList<SysFilePositionPo>( arg0.size() );
        for ( SysFilePositionDto sysFilePositionDto : arg0 ) {
            list.add( mapperPo( sysFilePositionDto ) );
        }

        return list;
    }
}
