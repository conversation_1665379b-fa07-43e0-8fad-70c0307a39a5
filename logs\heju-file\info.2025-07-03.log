09:41:04.213 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:41:05.032 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 709b2cdc-c84e-46ef-8d2e-dfeef0ca213c_config-0
09:41:05.160 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 52 ms to scan 1 urls, producing 3 keys and 6 values 
09:41:05.206 [main] INFO  o.r.Reflections - [scan,232] - <PERSON>flections took 21 ms to scan 1 urls, producing 4 keys and 9 values 
09:41:05.220 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 3 keys and 10 values 
09:41:05.226 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 1 ms to scan 1 urls, producing 1 keys and 5 values 
09:41:05.241 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 7 values 
09:41:05.265 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 2 keys and 8 values 
09:41:05.270 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [709b2cdc-c84e-46ef-8d2e-dfeef0ca213c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:41:05.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [709b2cdc-c84e-46ef-8d2e-dfeef0ca213c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001cd9d396b40
09:41:05.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [709b2cdc-c84e-46ef-8d2e-dfeef0ca213c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001cd9d396d60
09:41:05.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [709b2cdc-c84e-46ef-8d2e-dfeef0ca213c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:41:05.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [709b2cdc-c84e-46ef-8d2e-dfeef0ca213c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:41:05.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [709b2cdc-c84e-46ef-8d2e-dfeef0ca213c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:41:06.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [709b2cdc-c84e-46ef-8d2e-dfeef0ca213c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751506866384_127.0.0.1_14957
09:41:06.626 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [709b2cdc-c84e-46ef-8d2e-dfeef0ca213c_config-0] Notify connected event to listeners.
09:41:06.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [709b2cdc-c84e-46ef-8d2e-dfeef0ca213c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:41:06.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [709b2cdc-c84e-46ef-8d2e-dfeef0ca213c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001cd9d510668
09:41:06.902 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:41:10.271 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:41:10.271 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:41:10.271 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:41:10.491 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:41:12.755 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:41:16.357 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 984e054d-d265-4d6a-b32c-edb06ed115cc
09:41:16.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [984e054d-d265-4d6a-b32c-edb06ed115cc] RpcClient init label, labels = {module=naming, source=sdk}
09:41:16.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [984e054d-d265-4d6a-b32c-edb06ed115cc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:41:16.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [984e054d-d265-4d6a-b32c-edb06ed115cc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:41:16.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [984e054d-d265-4d6a-b32c-edb06ed115cc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:41:16.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [984e054d-d265-4d6a-b32c-edb06ed115cc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:41:16.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [984e054d-d265-4d6a-b32c-edb06ed115cc] Success to connect to server [localhost:8848] on start up, connectionId = 1751506876399_127.0.0.1_1071
09:41:16.520 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [984e054d-d265-4d6a-b32c-edb06ed115cc] Notify connected event to listeners.
09:41:16.520 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [984e054d-d265-4d6a-b32c-edb06ed115cc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:41:16.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [984e054d-d265-4d6a-b32c-edb06ed115cc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001cd9d510668
09:41:16.608 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:41:16.643 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:41:16.809 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 13.455 seconds (JVM running for 14.922)
09:41:16.821 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:41:16.821 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:41:16.832 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:41:17.102 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [984e054d-d265-4d6a-b32c-edb06ed115cc] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:41:17.132 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [984e054d-d265-4d6a-b32c-edb06ed115cc] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:41:17.247 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:32:14.198 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:32:14.241 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:32:14.576 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:32:14.576 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@25f20087[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:32:14.576 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751506876399_127.0.0.1_1071
12:32:14.581 [nacos-grpc-client-executor-2060] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751506876399_127.0.0.1_1071]Ignore complete event,isRunning:false,isAbandon=false
12:32:14.583 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@bd819a1[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2061]
12:33:24.711 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:33:25.462 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5c8643cc-da67-477d-9909-5838d5a8f479_config-0
12:33:25.538 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
12:33:25.570 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
12:33:25.581 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
12:33:25.594 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
12:33:25.608 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
12:33:25.620 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
12:33:25.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c8643cc-da67-477d-9909-5838d5a8f479_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:33:25.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c8643cc-da67-477d-9909-5838d5a8f479_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002d19a396b40
12:33:25.625 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c8643cc-da67-477d-9909-5838d5a8f479_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002d19a396d60
12:33:25.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c8643cc-da67-477d-9909-5838d5a8f479_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:33:25.627 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c8643cc-da67-477d-9909-5838d5a8f479_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:33:25.647 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c8643cc-da67-477d-9909-5838d5a8f479_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:33:26.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c8643cc-da67-477d-9909-5838d5a8f479_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751517206619_127.0.0.1_11348
12:33:26.969 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c8643cc-da67-477d-9909-5838d5a8f479_config-0] Notify connected event to listeners.
12:33:26.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c8643cc-da67-477d-9909-5838d5a8f479_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:33:26.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c8643cc-da67-477d-9909-5838d5a8f479_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002d19a510ad8
12:33:27.181 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:33:30.085 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
12:33:30.086 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:33:30.087 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:33:30.307 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:33:32.187 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:33:35.292 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9ec6f46b-fa84-4a28-94b5-6f5add6aacd7
12:33:35.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ec6f46b-fa84-4a28-94b5-6f5add6aacd7] RpcClient init label, labels = {module=naming, source=sdk}
12:33:35.296 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ec6f46b-fa84-4a28-94b5-6f5add6aacd7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:33:35.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ec6f46b-fa84-4a28-94b5-6f5add6aacd7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:33:35.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ec6f46b-fa84-4a28-94b5-6f5add6aacd7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:33:35.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ec6f46b-fa84-4a28-94b5-6f5add6aacd7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:33:35.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ec6f46b-fa84-4a28-94b5-6f5add6aacd7] Success to connect to server [localhost:8848] on start up, connectionId = 1751517215310_127.0.0.1_11407
12:33:35.424 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ec6f46b-fa84-4a28-94b5-6f5add6aacd7] Notify connected event to listeners.
12:33:35.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ec6f46b-fa84-4a28-94b5-6f5add6aacd7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:33:35.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ec6f46b-fa84-4a28-94b5-6f5add6aacd7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002d19a510ad8
12:33:35.497 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
12:33:35.541 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
12:33:35.709 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 11.82 seconds (JVM running for 13.131)
12:33:35.724 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
12:33:35.725 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
12:33:35.751 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
12:33:35.968 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:33:36.036 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ec6f46b-fa84-4a28-94b5-6f5add6aacd7] Receive server push request, request = NotifySubscriberRequest, requestId = 163
12:33:36.070 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ec6f46b-fa84-4a28-94b5-6f5add6aacd7] Ack server push request, request = NotifySubscriberRequest, requestId = 163
13:33:06.065 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:33:06.071 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:33:06.403 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:33:06.403 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@148b8469[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:33:06.404 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751517215310_127.0.0.1_11407
13:33:06.406 [nacos-grpc-client-executor-728] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751517215310_127.0.0.1_11407]Ignore complete event,isRunning:false,isAbandon=false
13:33:06.409 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@547fb3d9[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 729]
14:04:09.207 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:04:10.071 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 63ca4ec3-8196-4dea-a72f-77a6fbcb05ef_config-0
14:04:10.157 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
14:04:10.192 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
14:04:10.192 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
14:04:10.217 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
14:04:10.235 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:04:10.256 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
14:04:10.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ca4ec3-8196-4dea-a72f-77a6fbcb05ef_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:04:10.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ca4ec3-8196-4dea-a72f-77a6fbcb05ef_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000247ac3c6b40
14:04:10.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ca4ec3-8196-4dea-a72f-77a6fbcb05ef_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000247ac3c6d60
14:04:10.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ca4ec3-8196-4dea-a72f-77a6fbcb05ef_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:04:10.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ca4ec3-8196-4dea-a72f-77a6fbcb05ef_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:04:10.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ca4ec3-8196-4dea-a72f-77a6fbcb05ef_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:11.700 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ca4ec3-8196-4dea-a72f-77a6fbcb05ef_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751522651428_127.0.0.1_7962
14:04:11.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ca4ec3-8196-4dea-a72f-77a6fbcb05ef_config-0] Notify connected event to listeners.
14:04:11.700 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ca4ec3-8196-4dea-a72f-77a6fbcb05ef_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:04:11.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63ca4ec3-8196-4dea-a72f-77a6fbcb05ef_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000247ac500668
14:04:11.843 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:04:15.161 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
14:04:15.162 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:04:15.162 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:04:15.362 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:04:17.422 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:04:20.559 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cb3c0308-b84b-43c4-a3b7-d25f628358be
14:04:20.559 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb3c0308-b84b-43c4-a3b7-d25f628358be] RpcClient init label, labels = {module=naming, source=sdk}
14:04:20.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb3c0308-b84b-43c4-a3b7-d25f628358be] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:04:20.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb3c0308-b84b-43c4-a3b7-d25f628358be] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:04:20.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb3c0308-b84b-43c4-a3b7-d25f628358be] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:04:20.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb3c0308-b84b-43c4-a3b7-d25f628358be] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:20.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb3c0308-b84b-43c4-a3b7-d25f628358be] Success to connect to server [localhost:8848] on start up, connectionId = 1751522660576_127.0.0.1_8052
14:04:20.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb3c0308-b84b-43c4-a3b7-d25f628358be] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:04:20.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb3c0308-b84b-43c4-a3b7-d25f628358be] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000247ac500668
14:04:20.697 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb3c0308-b84b-43c4-a3b7-d25f628358be] Notify connected event to listeners.
14:04:20.760 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
14:04:20.795 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
14:04:20.973 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 12.901 seconds (JVM running for 14.392)
14:04:20.990 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
14:04:20.991 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
14:04:20.994 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
14:04:21.340 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb3c0308-b84b-43c4-a3b7-d25f628358be] Receive server push request, request = NotifySubscriberRequest, requestId = 172
14:04:21.377 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb3c0308-b84b-43c4-a3b7-d25f628358be] Ack server push request, request = NotifySubscriberRequest, requestId = 172
14:04:21.387 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:14:22.162 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:14:22.167 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:14:22.501 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:14:22.501 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1e1c6f09[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:14:22.502 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751522660576_127.0.0.1_8052
16:14:22.505 [nacos-grpc-client-executor-1572] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751522660576_127.0.0.1_8052]Ignore complete event,isRunning:false,isAbandon=false
16:14:22.508 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6da81a9b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1573]
16:51:24.448 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:51:26.627 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7cc063b1-500e-4065-ada6-d4842e30b9d9_config-0
16:51:26.812 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 94 ms to scan 1 urls, producing 3 keys and 6 values 
16:51:26.897 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 4 keys and 9 values 
16:51:26.939 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 10 values 
16:51:26.963 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 5 values 
16:51:26.990 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 1 keys and 7 values 
16:51:27.014 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
16:51:27.023 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cc063b1-500e-4065-ada6-d4842e30b9d9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:51:27.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cc063b1-500e-4065-ada6-d4842e30b9d9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000228633b0200
16:51:27.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cc063b1-500e-4065-ada6-d4842e30b9d9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000228633b0420
16:51:27.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cc063b1-500e-4065-ada6-d4842e30b9d9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:51:27.029 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cc063b1-500e-4065-ada6-d4842e30b9d9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:51:27.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cc063b1-500e-4065-ada6-d4842e30b9d9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:51:29.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cc063b1-500e-4065-ada6-d4842e30b9d9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751532688940_127.0.0.1_3857
16:51:29.310 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cc063b1-500e-4065-ada6-d4842e30b9d9_config-0] Notify connected event to listeners.
16:51:29.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cc063b1-500e-4065-ada6-d4842e30b9d9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:51:29.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7cc063b1-500e-4065-ada6-d4842e30b9d9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000228634e88c8
16:51:29.523 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:51:35.264 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
16:51:35.265 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:51:35.266 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:51:35.614 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:51:38.377 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:51:44.323 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of edad4ad8-7053-4416-a59e-462803d00314
16:51:44.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edad4ad8-7053-4416-a59e-462803d00314] RpcClient init label, labels = {module=naming, source=sdk}
16:51:44.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edad4ad8-7053-4416-a59e-462803d00314] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:51:44.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edad4ad8-7053-4416-a59e-462803d00314] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:51:44.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edad4ad8-7053-4416-a59e-462803d00314] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:51:44.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edad4ad8-7053-4416-a59e-462803d00314] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:51:44.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edad4ad8-7053-4416-a59e-462803d00314] Success to connect to server [localhost:8848] on start up, connectionId = 1751532704347_127.0.0.1_3907
16:51:44.474 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edad4ad8-7053-4416-a59e-462803d00314] Notify connected event to listeners.
16:51:44.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edad4ad8-7053-4416-a59e-462803d00314] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:51:44.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edad4ad8-7053-4416-a59e-462803d00314] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000228634e88c8
16:51:44.548 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
16:51:44.601 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
16:51:44.880 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 22.933 seconds (JVM running for 27.2)
16:51:44.910 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
16:51:44.913 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
16:51:44.928 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
16:51:45.052 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edad4ad8-7053-4416-a59e-462803d00314] Receive server push request, request = NotifySubscriberRequest, requestId = 284
16:51:45.078 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [edad4ad8-7053-4416-a59e-462803d00314] Ack server push request, request = NotifySubscriberRequest, requestId = 284
20:46:23.820 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:46:23.823 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:46:24.161 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:46:24.161 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@a999f78[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:46:24.161 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751532704347_127.0.0.1_3907
20:46:24.164 [nacos-grpc-client-executor-2817] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751532704347_127.0.0.1_3907]Ignore complete event,isRunning:false,isAbandon=false
20:46:24.167 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@794bf1f2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2818]
