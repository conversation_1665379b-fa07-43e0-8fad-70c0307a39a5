package com.heju.system.file.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.file.domain.dto.SysFileInfoDto;
import com.heju.system.file.domain.po.SysFileInfoPo;
import com.heju.system.file.domain.query.SysFileInfoQuery;
import org.apache.ibatis.annotations.*;

import java.io.Serializable;
import java.util.List;

/**
 * 文件信息管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysFileInfoMapper extends BaseMapper<SysFileInfoQuery, SysFileInfoDto, SysFileInfoPo> {

    //文件管理列表
        @Select("<script>" +
                "SELECT DISTINCT " +
                "a.* ,b.`name` classifyName,c.`name` positionName," +
                "GROUP_CONCAT(DISTINCT d.operate_type) operate_types," +
                "GROUP_CONCAT(DISTINCT e.is_view) AS is_views," +
                "GROUP_CONCAT(DISTINCT e.is_download) AS is_downloads," +
                "f.nick_name createName " +
                "from sys_file_info a " +
                "LEFT JOIN sys_file_classify b ON a.classify_id = b.id " +
                "LEFT JOIN sys_user f ON f.id= a.create_by " +
                "LEFT JOIN sys_file_position c ON a.position_id = c.id " +
                "LEFT JOIN sys_file_role_merge d ON d.file_id=a.id " +
                "LEFT JOIN sys_file_borrow_record e on e.file_id=a.id " +
                "WHERE " +
                "a.del_flag = 0 " +
                "AND a.position_id IS NOT NULL " +
                "AND (a.create_by=#{query.createBy} " +
                "   OR d.role_id IN " +
                "   <foreach collection=\"query.roleIds\" item=\"roleId\" open=\"(\" separator=\",\" close=\")\"> " +
                "       #{roleId} " +
                "   </foreach>" +
                " OR (e.borrow_user_id=#{query.createBy} AND NOW() BETWEEN e.start_time AND e.end_time)" +
                ") " +
                "<if test='query.positionId != null and query.positionId != \"\"'> " +
                "AND a.position_id = #{query.positionId} " +
                "</if>" +
                "<if test='query.classifyId != null and query.classifyId != \"\"'> " +
                "AND a.classify_id = #{query.classifyId} " +
                "</if>" +
                "<if test='query.name != null and query.name.trim() != \"\"'> " +
                "AND a.name LIKE CONCAT('%', #{query.name}, '%') " +
                "</if>" +
                "GROUP BY a.`id`" +
                "ORDER BY a.create_time DESC " +
                "</script>" )
    List<SysFileInfoDto> selectByQuery(@Param("query") SysFileInfoQuery query);

        //根据ids查询回收站列表
        @Select("<script>" +
                "SELECT DISTINCT " +
                "    a.id, " +
                "    a.code, " +
                "    a.name, " +
                "    a.classify_id, " +
                "    a.position_id, " +
                "    a.size, " +
                "    a.url, " +
                "    a.sort, " +
                "    a.status, " +
                "    a.remark, " +
                "    a.create_by, " +
                "    a.create_time, " +
                "    a.update_by, " +
                "    a.update_time, " +
                "    a.del_flag, " +
                "    a.tenant_id, " +
                "    b.`name` AS classifyName, " +
                "    c.`name` AS positionName, " +
                "    d.`nick_name` AS createName " +
                "FROM sys_file_info a " +
                "LEFT JOIN sys_file_classify b ON a.classify_id = b.id " +
                "LEFT JOIN sys_file_position c ON a.position_id = c.id " +
                "LEFT JOIN sys_user d ON a.create_by = d.id " +
                "<where>" +
                "  a.del_flag = 1 " +  // 新增的查询条件：只查已删除的文件
                "  AND a.id IN " +
                "  <foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
                "    #{id} " +
                "  </foreach>" +
                "</where>" +
                "ORDER BY a.create_time DESC " +
                "</script>")
        List<SysFileInfoDto> selectDeleteByIdList(@Param("ids") List<Long> ids);

    //查详情
    SysFileInfoDto selectInfoById(Serializable id);


    //admin 查询数据
    List<SysFileInfoDto> selectAdminList(@Param("query") SysFileInfoQuery query);


    @Select("SELECT DISTINCT couint(*) " +
            "FROM sys_file_info " +
            "WHERE " +
            "  <if test=\"query.name != null and query.name != ''\">" +
            "    name LIKE CONCAT('%', #{query.name}, '%') AND" +
            "  </if>" +
            "  <if test=\"query.position_id != null\">" +
            "    position_id = #{query.position_id} AND" +
            "  </if>" +
            "  <if test=\"query.classify_id != null\">" +
            "    classify_id = #{query.classify_id} AND" +
            "  </if>" +
            " (create_by = #{query.create_by} " +
            "   <if test=\"query.fileIDs != null and query.fileIDs.size() > 0\">" +
            "     OR id IN " +
            "     <foreach collection=\"query.fileIDs\" item=\"id\" open=\"(\" separator=\",\" close=\")\">" +
            "       #{id}" +
            "     </foreach>" +
            "   </if>" +
            "  )")
    long countList(@Param("query") SysFileInfoQuery query);

    //已删除文件查询
//    @Select("select * from sys_file_info where del_flag = 1")
    @Select("<script>" +
            "SELECT DISTINCT " +
            "    a.id, " +
            "    a.code, " +
            "    a.name, " +
            "    a.classify_id, " +
            "    a.position_id, " +
            "    a.size, " +
            "    a.url, " +
            "    a.sort, " +
            "    a.delete_by, " +
            "    a.delete_time, " +
            "    a.status, " +
            "    a.remark, " +
            "    a.create_by, " +
            "    a.create_time, " +
            "    a.update_by, " +
            "    a.update_time, " +
            "    a.del_flag, " +
            "    a.tenant_id, " +
            "    b.`name` AS classifyName, " +
            "    c.`name` AS positionName, " +
            "    d.`nick_name`AS createName " +
            "FROM sys_file_info a " +
            "LEFT JOIN sys_file_classify b ON a.classify_id = b.id " +
            "LEFT JOIN sys_file_position c ON a.position_id = c.id " +
            "LEFT JOIN sys_user d ON a.create_by = d.id " +
            "<where>" +
            "  a.del_flag = 1 " +  // 注意：0表示正常，1表示删除
            "  <if test=\"query.name != null and query.name != ''\">" +
            "    AND a.name LIKE CONCAT('%', #{query.name}, '%') " +
            "  </if>" +
            "  <if test=\"query.classifyId != null and query.classifyId != ''\">" +
            "    AND a.classify_id = #{query.classifyId} " +
            "  </if>" +
            "  <if test=\"query.positionId != null and query.positionId != ''\">" +
            "    AND a.position_id = #{query.positionId} " +
            "  </if>" +
            "  <if test=\"query.createBy != null and query.createBy != ''\">" +
            "    AND a.create_by = #{query.createBy} " +
            "  </if>" +
            "</where>" +
            "ORDER BY a.sort ASC, a.create_time DESC " +  // 添加排序
            "</script>")
    List<SysFileInfoDto> selectByDelFlag(@Param("query") SysFileInfoQuery query);


   //删除文件
    @Update("update sys_file_info SET del_flag = 1,update_time = NOW(),status=1 where id=#{id}")
    int deletefile(@Param("id") Long id);


    //批量删除文件
  /*  @Update("<script>"+
            "UPDATE sys_file_info SET del_flag = 1,update_time = NOW(),status=1"+
            "WHERE id IN "+
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>"+
            "#{id}"+
            "</foreach>"+
            "</script>")
    int softDeleteByIds(@Param("ids") List<Long> ids);*/


    //删除回收站中的已超过30天的需要删除信息
    @Delete("delete from sys_file_info where del_flag = 1 and update_time < DATE_SUB(NOW(), INTERVAL 30 DAY))")
    int delectfileinfo();

    //回收站批量恢复
    @Update("<script>" +
            "update sys_file_info set del_flag=0 where `id` in " +
            " <foreach collection='fileIds' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>" +
            "</script>")
    int replyByIds(@Param("fileIds") List<Long> fileIds);

    //回收站批量删除
    @Delete("<script>" +
            "delete from sys_file_info where `id` in " +
            " <foreach collection='idList' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>" +
            "</script>")
    int deleteByIds(@Param("idList") List<Long> idList);

    //暂存管理批量删除
    @Update("<script>" +
            "update sys_file_info set del_flag=1 where `id` in " +
            " <foreach collection='idList' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>" +
            "</script>")
    int deleteStorageByIds(@Param("idList") List<Long> idList);

    // 查询 回收站中已超过30天的文件
    List<SysFileInfoDto> selectExpiredFiles();
}
