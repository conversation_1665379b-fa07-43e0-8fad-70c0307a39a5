package com.heju.system.api.model.base;

import com.heju.common.core.constant.basic.TenantConstants;
import com.heju.system.api.model.Source;
import com.heju.system.api.organize.domain.dto.SysEnterpriseDto;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 默认用户信息
 *
 * <AUTHOR>
 */
@Data
public class BaseLoginUser<User> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 用户唯一标识 */
    protected String token;

    /** 企业账号Id */
    protected Long enterpriseId;

    /** 企业账号 */
    protected String enterpriseName;

    /** 用户名Id */
    protected Long userId;

    /** 用户名 */
    protected String userName;

    /** 用户昵称 */
    protected String nickName;

    /** 租户标识 */
    protected String isLessor;

    /** 用户标识 */
    protected String userType;

    /** 主数据源 */
    protected String sourceName;

    /** 登录时间 */
    protected Long loginTime;

    /** 登录IP地址 */
    protected String ipaddr;

    /** 源策略组 */
    protected Source source;

    /** 企业信息 */
    protected SysEnterpriseDto enterprise;

    /** 用户信息 */
    protected User user;

    /** 账户类型 */
    protected TenantConstants.AccountType accountType;

}
