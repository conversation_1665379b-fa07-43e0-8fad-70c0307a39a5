package com.heju.system.file.service.impl;

import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.file.domain.dto.SysFileBorrowRecordDto;
import com.heju.system.file.domain.query.SysFileBorrowRecordQuery;
import com.heju.system.file.manager.ISysFileBorrowRecordManager;
import com.heju.system.file.service.ISysFileBorrowRecordService;
import com.heju.system.organize.manager.ISysUserManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件借阅记录管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysFileBorrowRecordServiceImpl extends BaseServiceImpl<SysFileBorrowRecordQuery, SysFileBorrowRecordDto, ISysFileBorrowRecordManager> implements ISysFileBorrowRecordService {

    @Autowired
    private ISysFileBorrowRecordManager manager;

    @Autowired
    private ISysUserManager userManager;

    /**
     * 查询文件借阅记录对象列表 | 数据权限
     *
     * @param fileBorrowRecord 文件借阅记录对象
     * @return 文件借阅记录对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysFileBorrowRecordMapper"})
    public List<SysFileBorrowRecordDto> selectListScope(SysFileBorrowRecordQuery fileBorrowRecord) {
        List<SysFileBorrowRecordDto> borrowRecordDtos = baseManager.selectList(fileBorrowRecord);
        if (borrowRecordDtos == null || borrowRecordDtos.isEmpty()) {
            return Collections.emptyList();
        }
        Set<Long> borrowUsers = borrowRecordDtos.stream().map(SysFileBorrowRecordDto::getBorrowUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<SysUserDto> sysUserDtos = userManager.selectListByIds(borrowUsers);
        Map<Long, String> userMap = sysUserDtos.stream().collect(Collectors.toMap(
                SysUserDto::getId,
                SysUserDto::getNickName,
                (existing, replacement) -> existing
        ));
        for (SysFileBorrowRecordDto dto : borrowRecordDtos) {
            if (dto.getBorrowUserId() != null ) {
                dto.setNickName(userMap.get(dto.getBorrowUserId()));
            }
        }
        return borrowRecordDtos;
    }

    /**
     * 借阅记录列表
     * @param fileBorrowRecord
     * @return
     */
    @Override
    public List<SysFileBorrowRecordDto> overTimeList(SysFileBorrowRecordQuery fileBorrowRecord) {
        List<SysFileBorrowRecordDto> borrowRecordDtos = manager.selectOverTimeList(fileBorrowRecord);
        if (borrowRecordDtos == null || borrowRecordDtos.isEmpty()) {
            return Collections.emptyList();
        }
        Set<Long> borrowUsers = borrowRecordDtos.stream().map(SysFileBorrowRecordDto::getBorrowUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<SysUserDto> sysUserDtos = userManager.selectListByIds(borrowUsers);
        Map<Long, String> userMap = sysUserDtos.stream().collect(Collectors.toMap(
                SysUserDto::getId,
                SysUserDto::getNickName,
                (existing, replacement) -> existing
        ));
        for (SysFileBorrowRecordDto dto : borrowRecordDtos) {
            if (dto.getBorrowUserId() != null ) {
                dto.setNickName(userMap.get(dto.getBorrowUserId()));
            }
        }
        return borrowRecordDtos;
    }

    /**
     * 批量操作(新增、修改)
     */
    @Override
    public int batchOperation(List<SysFileBorrowRecordDto> fileBorrowRecords) {
        if (fileBorrowRecords == null || fileBorrowRecords.isEmpty()) {
            throw new IllegalArgumentException("批量操作文件借阅记录不能为空");
        }
        //分成两个list
        List<SysFileBorrowRecordDto> toInsert = new ArrayList<>();
        List<SysFileBorrowRecordDto> toUpdate = new ArrayList<>();

        // 使用 Set 来保存已添加的 (fileId, borrowUserId) 组合
        Set<Long> borrowUserSet = new HashSet<>();
        for (SysFileBorrowRecordDto record : fileBorrowRecords) {
            if (record.getId() == null) {
                // 校验必填字段
                if (record.getEndTime().before(record.getStartTime()) || record.getEndTime().equals(record.getStartTime())) {
                    throw new IllegalArgumentException("请正确填写开始、结束时间顺序");
                }
                if (record.getFileId() == null || record.getBorrowUserId() == null) {
                    throw new IllegalArgumentException("新增记录中 fileId 和 borrowUserId 不能为空");
                }

                Long borrowUserId = record.getBorrowUserId();
                if (borrowUserSet.contains(borrowUserId)) {
                    throw new IllegalArgumentException("不允许插入重复的借阅记录");
                } else {
                    borrowUserSet.add(borrowUserId);
                    toInsert.add(record);
                }
            } else {
                toUpdate.add(record);
            }
        }
        //受影响行
        int count = 0;
        //批量插入
        if (!toInsert.isEmpty()) {
            count += manager.insertBatch(toInsert);
        }
        //批量更新
        if (!toUpdate.isEmpty()) {
            count += manager.updateBatch(toUpdate);
        }
        return count;
    }
}
