package com.heju.system.file.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.file.domain.dto.SysFileRecordDto;
import com.heju.system.file.domain.po.SysFileRecordPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-05T14:37:37+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysFileRecordConverterImpl implements SysFileRecordConverter {

    @Override
    public SysFileRecordDto mapperDto(SysFileRecordPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFileRecordDto sysFileRecordDto = new SysFileRecordDto();

        sysFileRecordDto.setId( arg0.getId() );
        sysFileRecordDto.setSourceName( arg0.getSourceName() );
        sysFileRecordDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFileRecordDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFileRecordDto.setName( arg0.getName() );
        sysFileRecordDto.setStatus( arg0.getStatus() );
        sysFileRecordDto.setSort( arg0.getSort() );
        sysFileRecordDto.setRemark( arg0.getRemark() );
        sysFileRecordDto.setCreateBy( arg0.getCreateBy() );
        sysFileRecordDto.setCreateTime( arg0.getCreateTime() );
        sysFileRecordDto.setUpdateBy( arg0.getUpdateBy() );
        sysFileRecordDto.setUpdateTime( arg0.getUpdateTime() );
        sysFileRecordDto.setDelFlag( arg0.getDelFlag() );
        sysFileRecordDto.setCreateName( arg0.getCreateName() );
        sysFileRecordDto.setUpdateName( arg0.getUpdateName() );
        sysFileRecordDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysFileRecordDto.setFileId( arg0.getFileId() );
        sysFileRecordDto.setOperateType( arg0.getOperateType() );

        return sysFileRecordDto;
    }

    @Override
    public List<SysFileRecordDto> mapperDto(Collection<SysFileRecordPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFileRecordDto> list = new ArrayList<SysFileRecordDto>( arg0.size() );
        for ( SysFileRecordPo sysFileRecordPo : arg0 ) {
            list.add( mapperDto( sysFileRecordPo ) );
        }

        return list;
    }

    @Override
    public Page<SysFileRecordDto> mapperPageDto(Collection<SysFileRecordPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFileRecordDto> page = new Page<SysFileRecordDto>();
        for ( SysFileRecordPo sysFileRecordPo : arg0 ) {
            page.add( mapperDto( sysFileRecordPo ) );
        }

        return page;
    }

    @Override
    public SysFileRecordPo mapperPo(SysFileRecordDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFileRecordPo sysFileRecordPo = new SysFileRecordPo();

        sysFileRecordPo.setId( arg0.getId() );
        sysFileRecordPo.setSourceName( arg0.getSourceName() );
        sysFileRecordPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFileRecordPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFileRecordPo.setName( arg0.getName() );
        sysFileRecordPo.setStatus( arg0.getStatus() );
        sysFileRecordPo.setSort( arg0.getSort() );
        sysFileRecordPo.setRemark( arg0.getRemark() );
        sysFileRecordPo.setCreateBy( arg0.getCreateBy() );
        sysFileRecordPo.setCreateTime( arg0.getCreateTime() );
        sysFileRecordPo.setUpdateBy( arg0.getUpdateBy() );
        sysFileRecordPo.setUpdateTime( arg0.getUpdateTime() );
        sysFileRecordPo.setDelFlag( arg0.getDelFlag() );
        sysFileRecordPo.setCreateName( arg0.getCreateName() );
        sysFileRecordPo.setUpdateName( arg0.getUpdateName() );
        sysFileRecordPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysFileRecordPo.setFileId( arg0.getFileId() );
        sysFileRecordPo.setOperateType( arg0.getOperateType() );

        return sysFileRecordPo;
    }

    @Override
    public List<SysFileRecordPo> mapperPo(Collection<SysFileRecordDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFileRecordPo> list = new ArrayList<SysFileRecordPo>( arg0.size() );
        for ( SysFileRecordDto sysFileRecordDto : arg0 ) {
            list.add( mapperPo( sysFileRecordDto ) );
        }

        return list;
    }

    @Override
    public Page<SysFileRecordPo> mapperPagePo(Collection<SysFileRecordDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFileRecordPo> page = new Page<SysFileRecordPo>();
        for ( SysFileRecordDto sysFileRecordDto : arg0 ) {
            page.add( mapperPo( sysFileRecordDto ) );
        }

        return page;
    }
}
