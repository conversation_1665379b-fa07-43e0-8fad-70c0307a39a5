package com.heju.system.file.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.file.domain.dto.SysFileRecordDto;
import com.heju.system.file.domain.model.SysFileRecordConverter;
import com.heju.system.file.domain.po.SysFileRecordPo;
import com.heju.system.file.domain.query.SysFileRecordQuery;
import com.heju.system.file.manager.ISysFileRecordManager;
import com.heju.system.file.mapper.SysFileRecordMapper;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件操作记录管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysFileRecordManager extends BaseManagerImpl<SysFileRecordQuery, SysFileRecordDto, SysFileRecordPo, SysFileRecordMapper, SysFileRecordConverter> implements ISysFileRecordManager {

    /**
     * 文件管理操作列表
     * @param fileId
     * @return
     */
    @Override
    public List<SysFileRecordDto> selectByFileId(Serializable fileId) {
        LambdaQueryWrapper<SysFileRecordPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysFileRecordPo::getFileId, fileId);
        List<SysFileRecordPo> sysFileRecordPos = baseMapper.selectList(wrapper);
        return sysFileRecordPos.stream()
                        .map(po -> subMerge(mapperDto(po)))
                        .collect(Collectors.toList());
    }
}
