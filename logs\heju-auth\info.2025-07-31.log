09:43:50.367 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:43:51.142 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of eb0a004e-c9d7-4cf2-98c8-3bc722755173_config-0
09:43:51.218 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 41 ms to scan 1 urls, producing 3 keys and 6 values 
09:43:51.250 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:43:51.265 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 3 keys and 10 values 
09:43:51.278 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:43:51.288 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 7 values 
09:43:51.297 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:43:51.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0a004e-c9d7-4cf2-98c8-3bc722755173_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:43:51.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0a004e-c9d7-4cf2-98c8-3bc722755173_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000146313b4fb8
09:43:51.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0a004e-c9d7-4cf2-98c8-3bc722755173_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000146313b51d8
09:43:51.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0a004e-c9d7-4cf2-98c8-3bc722755173_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:43:51.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0a004e-c9d7-4cf2-98c8-3bc722755173_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:43:51.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0a004e-c9d7-4cf2-98c8-3bc722755173_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:43:52.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0a004e-c9d7-4cf2-98c8-3bc722755173_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753926232224_127.0.0.1_5981
09:43:52.466 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0a004e-c9d7-4cf2-98c8-3bc722755173_config-0] Notify connected event to listeners.
09:43:52.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0a004e-c9d7-4cf2-98c8-3bc722755173_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:43:52.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0a004e-c9d7-4cf2-98c8-3bc722755173_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000146314ed418
09:43:52.731 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:43:56.478 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:43:56.478 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:43:56.481 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:43:56.919 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:44:01.301 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:44:10.985 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6847a748-d322-4905-aaff-1d8fca268628
09:44:10.986 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] RpcClient init label, labels = {module=naming, source=sdk}
09:44:10.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:44:10.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:44:10.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:44:10.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:11.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] Success to connect to server [localhost:8848] on start up, connectionId = 1753926251063_127.0.0.1_6090
09:44:11.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:11.360 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] Notify connected event to listeners.
09:44:11.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000146314ed418
09:44:11.540 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:44:11.706 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:44:13.567 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 23.88 seconds (JVM running for 26.324)
09:44:13.601 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:44:13.601 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:44:13.606 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:44:14.700 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:44:14.709 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:44:47.720 [http-nio-9200-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:44:56.347 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:44:56.349 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:44:59.440 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:44:59.442 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6847a748-d322-4905-aaff-1d8fca268628] Ack server push request, request = NotifySubscriberRequest, requestId = 12
11:35:32.137 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:35:32.144 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:35:32.477 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:35:32.477 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@65723452[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:35:32.478 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753926251063_127.0.0.1_6090
11:35:32.480 [nacos-grpc-client-executor-1392] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753926251063_127.0.0.1_6090]Ignore complete event,isRunning:false,isAbandon=false
11:35:32.486 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4f037b0b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1393]
11:35:49.684 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:35:50.204 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6fe709c9-244c-46ad-9bb9-c057d0913216_config-0
11:35:50.258 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
11:35:50.283 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
11:35:50.291 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:35:50.301 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
11:35:50.313 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
11:35:50.325 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
11:35:50.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fe709c9-244c-46ad-9bb9-c057d0913216_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:35:50.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fe709c9-244c-46ad-9bb9-c057d0913216_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001d0023b7d80
11:35:50.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fe709c9-244c-46ad-9bb9-c057d0913216_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001d0023b8000
11:35:50.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fe709c9-244c-46ad-9bb9-c057d0913216_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:35:50.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fe709c9-244c-46ad-9bb9-c057d0913216_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:35:50.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fe709c9-244c-46ad-9bb9-c057d0913216_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:35:51.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fe709c9-244c-46ad-9bb9-c057d0913216_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753932950936_127.0.0.1_4689
11:35:51.126 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fe709c9-244c-46ad-9bb9-c057d0913216_config-0] Notify connected event to listeners.
11:35:51.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fe709c9-244c-46ad-9bb9-c057d0913216_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:35:51.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fe709c9-244c-46ad-9bb9-c057d0913216_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001d0024f0228
11:35:51.242 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:35:53.006 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
11:35:53.007 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:35:53.007 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:35:53.114 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:35:54.230 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:35:55.294 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d3d44db3-1e85-4f72-ba32-ef516e872637
11:35:55.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] RpcClient init label, labels = {module=naming, source=sdk}
11:35:55.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:35:55.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:35:55.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:35:55.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:35:55.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Success to connect to server [localhost:8848] on start up, connectionId = 1753932955309_127.0.0.1_4704
11:35:55.422 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Notify connected event to listeners.
11:35:55.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:35:55.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001d0024f0228
11:35:55.485 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
11:35:55.511 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
11:35:55.644 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 6.455 seconds (JVM running for 20.917)
11:35:55.656 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
11:35:55.657 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
11:35:55.660 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
11:35:55.956 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Receive server push request, request = NotifySubscriberRequest, requestId = 15
11:35:55.969 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Ack server push request, request = NotifySubscriberRequest, requestId = 15
11:41:02.874 [http-nio-9200-exec-4] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:41:09.908 [nacos-grpc-client-executor-71] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Receive server push request, request = NotifySubscriberRequest, requestId = 22
11:41:09.909 [nacos-grpc-client-executor-71] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Ack server push request, request = NotifySubscriberRequest, requestId = 22
11:41:11.441 [nacos-grpc-client-executor-74] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Receive server push request, request = NotifySubscriberRequest, requestId = 23
11:41:11.442 [nacos-grpc-client-executor-74] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Ack server push request, request = NotifySubscriberRequest, requestId = 23
14:14:04.135 [nacos-grpc-client-executor-2013] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Receive server push request, request = NotifySubscriberRequest, requestId = 25
14:14:04.153 [nacos-grpc-client-executor-2013] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Ack server push request, request = NotifySubscriberRequest, requestId = 25
14:14:24.978 [nacos-grpc-client-executor-2017] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Receive server push request, request = NotifySubscriberRequest, requestId = 28
14:14:24.993 [nacos-grpc-client-executor-2017] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Ack server push request, request = NotifySubscriberRequest, requestId = 28
14:36:10.446 [nacos-grpc-client-executor-2277] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Receive server push request, request = NotifySubscriberRequest, requestId = 32
14:36:10.465 [nacos-grpc-client-executor-2277] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Ack server push request, request = NotifySubscriberRequest, requestId = 32
14:36:26.827 [nacos-grpc-client-executor-2281] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Receive server push request, request = NotifySubscriberRequest, requestId = 36
14:36:26.844 [nacos-grpc-client-executor-2281] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3d44db3-1e85-4f72-ba32-ef516e872637] Ack server push request, request = NotifySubscriberRequest, requestId = 36
14:59:26.688 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:59:26.692 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:59:27.028 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:59:27.029 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@530b6152[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:59:27.029 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753932955309_127.0.0.1_4704
14:59:27.032 [nacos-grpc-client-executor-2560] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753932955309_127.0.0.1_4704]Ignore complete event,isRunning:false,isAbandon=false
14:59:27.038 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@20b276b5[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2561]
15:07:06.301 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:07:06.883 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6080f501-767d-4723-ac3c-21c8e3484f28_config-0
15:07:06.944 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
15:07:06.967 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
15:07:06.976 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
15:07:06.987 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
15:07:07.001 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
15:07:07.016 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
15:07:07.019 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6080f501-767d-4723-ac3c-21c8e3484f28_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:07:07.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6080f501-767d-4723-ac3c-21c8e3484f28_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000225ac3ca138
15:07:07.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6080f501-767d-4723-ac3c-21c8e3484f28_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000225ac3ca358
15:07:07.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6080f501-767d-4723-ac3c-21c8e3484f28_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:07:07.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6080f501-767d-4723-ac3c-21c8e3484f28_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:07:07.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6080f501-767d-4723-ac3c-21c8e3484f28_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:07:08.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6080f501-767d-4723-ac3c-21c8e3484f28_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753945627872_127.0.0.1_9207
15:07:08.105 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6080f501-767d-4723-ac3c-21c8e3484f28_config-0] Notify connected event to listeners.
15:07:08.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6080f501-767d-4723-ac3c-21c8e3484f28_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:08.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6080f501-767d-4723-ac3c-21c8e3484f28_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000225ac5044f0
15:07:08.277 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:07:13.005 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
15:07:13.007 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:07:13.008 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:07:13.635 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:07:17.739 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:07:21.033 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 00529c34-14dd-4962-a63d-6184d2a6c152
15:07:21.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00529c34-14dd-4962-a63d-6184d2a6c152] RpcClient init label, labels = {module=naming, source=sdk}
15:07:21.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00529c34-14dd-4962-a63d-6184d2a6c152] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:07:21.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00529c34-14dd-4962-a63d-6184d2a6c152] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:07:21.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00529c34-14dd-4962-a63d-6184d2a6c152] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:07:21.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00529c34-14dd-4962-a63d-6184d2a6c152] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:07:21.180 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00529c34-14dd-4962-a63d-6184d2a6c152] Success to connect to server [localhost:8848] on start up, connectionId = 1753945641053_127.0.0.1_9277
15:07:21.180 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00529c34-14dd-4962-a63d-6184d2a6c152] Notify connected event to listeners.
15:07:21.180 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00529c34-14dd-4962-a63d-6184d2a6c152] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:21.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00529c34-14dd-4962-a63d-6184d2a6c152] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000225ac5044f0
15:07:21.272 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
15:07:21.314 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
15:07:21.596 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 15.896 seconds (JVM running for 16.795)
15:07:21.620 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
15:07:21.621 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
15:07:21.628 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
15:07:21.753 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00529c34-14dd-4962-a63d-6184d2a6c152] Receive server push request, request = NotifySubscriberRequest, requestId = 40
15:07:21.783 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00529c34-14dd-4962-a63d-6184d2a6c152] Ack server push request, request = NotifySubscriberRequest, requestId = 40
15:07:21.963 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:46:46.016 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:46:46.022 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:46:46.359 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:46:46.359 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4c3680bb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:46:46.359 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753945641053_127.0.0.1_9277
20:46:46.361 [nacos-grpc-client-executor-4078] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753945641053_127.0.0.1_9277]Ignore complete event,isRunning:false,isAbandon=false
20:46:46.367 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@47b0d582[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 4079]
