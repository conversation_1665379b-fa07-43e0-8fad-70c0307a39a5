#!/bin/sh

# 复制项目的文件到对应docker路径，便于一键生成镜像。
usage() {
	echo "Usage: sh copy.sh"
	exit 1
}


# copy sql
echo "begin copy sql "
cp ../sql/quartz.sql ./mysql/db
cp ../sql/heju_1.sql ./mysql/db
cp ../sql/heju_2.sql ./mysql/db
cp ../sql/xy_config.sql ./mysql/db

# copy html
echo "begin copy html "
cp -r ../heju-ui/main/dist/** ./nginx/html/main
cp -r ../heju-ui/administrator/dist/** ./nginx/html/administrator


# copy jar
echo "begin copy heju-gateway "
cp ../heju-gateway/target/heju-gateway.jar ./heju/gateway/jar

echo "begin copy heju-auth "
cp ../heju-auth/target/heju-auth.jar ./heju/auth/jar

echo "begin copy heju-visual "
cp ../heju-visual/heju-monitor/target/heju-visual-monitor.jar  ./heju/visual/monitor/jar

echo "begin copy heju-modules-system "
cp ../heju-modules/heju-system/target/heju-modules-system.jar ./heju/modules/system/jar

echo "begin copy heju-modules-tenant "
cp ../heju-modules/heju-tenant/target/heju-modules-tenant.jar ./heju/modules/tenant/jar

echo "begin copy heju-modules-file "
cp ../heju-modules/heju-file/target/heju-modules-file.jar ./heju/modules/file/jar

echo "begin copy heju-modules-job "
cp ../heju-modules/heju-job/target/heju-modules-job.jar ./heju/modules/job/jar

echo "begin copy heju-modules-gen "
cp ../heju-modules/heju-gen/target/heju-modules-gen.jar ./heju/modules/gen/jar