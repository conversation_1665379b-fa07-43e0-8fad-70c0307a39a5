package com.heju.system.file.controller;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

import java.time.Year;


public class FileCode {
    public static String generateSummaryString(String string) {
        // 固定输入字符串

        // 获取当前年份
        int currentYear = Year.now().getValue();

        // 生成6位随机数
//        Random random = new Random();
//        int randomNumber = 100000 + random.nextInt(900000);
        String uuid = java.util.UUID.randomUUID().toString().replaceAll("-", ""); // 去掉横线
        String randomNumber = uuid.substring(0, 6); // 截取前6位

        // 获取中文字符的前三个拼音首字母（大写）
        String initials = extractChineseInitials(string);

        // 拼接最终结果
        return  initials + "-" + currentYear + "-" + randomNumber;
    }
    private static String extractChineseInitials(String input) {
        if (input == null || input.isEmpty()) {
            return "";
        }
        // 提取中文字符
        StringBuilder chineseChars = new StringBuilder();
        for (char c : input.toCharArray()) {
            chineseChars.append(c);
        }
        // 取前三个字符
        String firstThree = chineseChars.length() >= 3 ?
                chineseChars.substring(0, 3) :
                chineseChars.toString();
        // 转换为拼音首字母
        return convertToInitials(firstThree);
    }

    private static String convertToInitials(String chinese) {
        if (chinese == null || chinese.isEmpty()) {
            return "";
        }
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.UPPERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        StringBuilder initials = new StringBuilder();
        for (char c : chinese.toCharArray()) {
            try {
                String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                if (pinyinArray != null && pinyinArray.length > 0) {
                    initials.append(pinyinArray[0].charAt(0)); // 取拼音首字母
                }
            } catch (BadHanyuPinyinOutputFormatCombination e) {
                // 忽略非中文字符
            }
        }
        return initials.toString();
    }
}
