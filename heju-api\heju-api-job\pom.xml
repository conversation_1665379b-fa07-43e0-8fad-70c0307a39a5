<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.heju</groupId>
        <artifactId>heju-api</artifactId>
        <version>2.5.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>heju-api-job</artifactId>

    <description>
        heju-api-job定时任务接口模块
    </description>

    <dependencies>

        <!-- heju Common Core -->
        <dependency>
            <groupId>com.heju</groupId>
            <artifactId>heju-common-core</artifactId>
        </dependency>

        <!-- Quartz -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.mchange</groupId>
                    <artifactId>c3p0</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

</project>