09:24:00.150 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:24:02.224 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0
09:24:02.416 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 89 ms to scan 1 urls, producing 3 keys and 6 values 
09:24:02.496 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 4 keys and 9 values 
09:24:02.514 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 3 keys and 10 values 
09:24:02.545 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 1 keys and 5 values 
09:24:02.582 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 29 ms to scan 1 urls, producing 1 keys and 7 values 
09:24:02.606 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
09:24:02.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:24:02.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000014901392ad8
09:24:02.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000014901392cf8
09:24:02.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:24:02.625 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:24:02.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:04.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:05.087 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:05.137 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:05.137 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:05.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000149014a46e8
09:24:05.628 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:05.939 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:06.323 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:06.790 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:07.236 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:24:07.405 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:08.067 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:08.799 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:09.624 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:10.576 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:11.624 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:13.419 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:15.095 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:17.786 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:18.469 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:24:18.470 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:24:18.470 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:24:18.843 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:24:19.484 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:21.158 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:22.382 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:24:22.982 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:24.779 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:26.526 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f61e6fbc-9982-442a-8424-9d8467dd9897
09:24:26.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] RpcClient init label, labels = {module=naming, source=sdk}
09:24:26.532 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:26.532 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:26.532 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:26.532 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:26.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:26.615 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:26.615 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:26.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:26.628 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:26.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000149014a46e8
09:24:26.754 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:26.968 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:26.968 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:24:27.295 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:27.714 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:27.958 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:24:27.958 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@25bbca43[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:24:27.958 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f61e6fbc-9982-442a-8424-9d8467dd9897] Client is shutdown, stop reconnect to server
09:24:27.958 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@74cd82f1[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:24:27.962 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 122945ae-2a7b-4b9e-8b46-18a8689a9963
09:24:27.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122945ae-2a7b-4b9e-8b46-18a8689a9963] RpcClient init label, labels = {module=naming, source=sdk}
09:24:27.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122945ae-2a7b-4b9e-8b46-18a8689a9963] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:27.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122945ae-2a7b-4b9e-8b46-18a8689a9963] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:27.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122945ae-2a7b-4b9e-8b46-18a8689a9963] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:27.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122945ae-2a7b-4b9e-8b46-18a8689a9963] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:27.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122945ae-2a7b-4b9e-8b46-18a8689a9963] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:27.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122945ae-2a7b-4b9e-8b46-18a8689a9963] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:28.014 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122945ae-2a7b-4b9e-8b46-18a8689a9963] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:28.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122945ae-2a7b-4b9e-8b46-18a8689a9963] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:28.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122945ae-2a7b-4b9e-8b46-18a8689a9963] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000149014a46e8
09:24:28.150 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122945ae-2a7b-4b9e-8b46-18a8689a9963] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:28.403 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122945ae-2a7b-4b9e-8b46-18a8689a9963] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:28.534 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9300"]
09:24:28.534 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:24:28.537 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5f3cb94-cae3-46b4-a87a-a92732bfdd6d_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:28.623 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9300"]
09:24:28.675 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9300"]
09:24:28.800 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122945ae-2a7b-4b9e-8b46-18a8689a9963] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:25:18.182 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:25:19.083 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b0367d65-025f-480e-bc0f-515a1509c538_config-0
09:25:19.167 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
09:25:19.200 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:25:19.214 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
09:25:19.229 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:25:19.243 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:25:19.262 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
09:25:19.268 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:25:19.269 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000025baf396d88
09:25:19.270 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000025baf396fa8
09:25:19.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:25:19.273 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:25:19.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:25:20.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751592320453_127.0.0.1_9577
09:25:20.705 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Notify connected event to listeners.
09:25:20.705 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:25:20.706 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025baf510668
09:25:20.858 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:25:24.958 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:25:24.960 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:25:24.960 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:25:25.207 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:25:27.714 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:25:31.102 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 49056315-f936-4366-a839-8cc51291af53
09:25:31.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] RpcClient init label, labels = {module=naming, source=sdk}
09:25:31.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:25:31.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:25:31.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:25:31.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:25:31.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Success to connect to server [localhost:8848] on start up, connectionId = 1751592331124_127.0.0.1_9829
09:25:31.248 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Notify connected event to listeners.
09:25:31.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:25:31.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025baf510668
09:25:31.321 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:25:31.364 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:25:31.545 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 14.417 seconds (JVM running for 15.84)
09:25:31.562 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:25:31.563 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:25:31.568 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:25:31.862 [RMI TCP Connection(17)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:25:31.887 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:25:31.913 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Ack server push request, request = NotifySubscriberRequest, requestId = 4
11:41:38.293 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Server healthy check fail, currentConnection = 1751592331124_127.0.0.1_9829
11:41:38.950 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Server healthy check fail, currentConnection = 1751592320453_127.0.0.1_9577
11:41:47.665 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:41:47.696 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:41:47.902 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Success to connect a server [localhost:8848], connectionId = 1751600507790_127.0.0.1_13713
11:41:47.903 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751592320453_127.0.0.1_9577
11:41:47.903 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751592320453_127.0.0.1_9577
11:41:47.913 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Notify disconnected event to listeners
11:41:47.927 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Notify connected event to listeners.
11:41:47.954 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Success to connect a server [localhost:8848], connectionId = 1751600507803_127.0.0.1_13715
11:41:47.955 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Abandon prev connection, server is localhost:8848, connectionId is 1751592331124_127.0.0.1_9829
11:41:47.955 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751592331124_127.0.0.1_9829
11:41:47.956 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Notify disconnected event to listeners
11:41:47.932 [nacos-grpc-client-executor-1653] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751592320453_127.0.0.1_9577]Ignore complete event,isRunning:true,isAbandon=true
11:41:47.981 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Notify connected event to listeners.
11:41:51.113 [nacos-grpc-client-executor-1649] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Receive server push request, request = NotifySubscriberRequest, requestId = 26
11:41:51.134 [nacos-grpc-client-executor-1649] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Ack server push request, request = NotifySubscriberRequest, requestId = 26
13:04:36.980 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Server healthy check fail, currentConnection = 1751600507790_127.0.0.1_13713
13:04:41.183 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:04:53.966 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Server healthy check fail, currentConnection = 1751600507803_127.0.0.1_13715
13:04:54.080 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:04:54.272 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Success to connect a server [localhost:8848], connectionId = 1751605494088_127.0.0.1_6543
13:04:54.272 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Abandon prev connection, server is localhost:8848, connectionId is 1751600507803_127.0.0.1_13715
13:04:54.281 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751600507803_127.0.0.1_13715
13:04:54.281 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Notify disconnected event to listeners
13:04:54.281 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Notify connected event to listeners.
13:04:54.356 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Success to connect a server [localhost:8848], connectionId = 1751605494213_127.0.0.1_6552
13:04:54.356 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751600507790_127.0.0.1_13713
13:04:54.356 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751600507790_127.0.0.1_13713
13:04:54.357 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Notify disconnected event to listeners
13:04:54.357 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0367d65-025f-480e-bc0f-515a1509c538_config-0] Notify connected event to listeners.
13:04:59.622 [nacos-grpc-client-executor-2648] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Receive server push request, request = NotifySubscriberRequest, requestId = 75
13:04:59.855 [nacos-grpc-client-executor-2648] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49056315-f936-4366-a839-8cc51291af53] Ack server push request, request = NotifySubscriberRequest, requestId = 75
15:09:03.612 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:09:03.620 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:09:03.960 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:09:03.963 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3d534ec5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:09:03.964 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751605494088_127.0.0.1_6543
15:09:03.968 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2d8ac772[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 4136]
15:09:03.970 [nacos-grpc-client-executor-4136] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751605494088_127.0.0.1_6543]Ignore complete event,isRunning:false,isAbandon=false
