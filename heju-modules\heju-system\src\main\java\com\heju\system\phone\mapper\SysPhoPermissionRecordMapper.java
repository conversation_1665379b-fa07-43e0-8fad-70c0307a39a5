package com.heju.system.phone.mapper;

import com.heju.system.phone.domain.query.SysPhoPermissionRecordQuery;
import com.heju.system.phone.domain.dto.SysPhoPermissionRecordDto;
import com.heju.system.phone.domain.po.SysPhoPermissionRecordPo;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 手机号授权管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysPhoPermissionRecordMapper extends BaseMapper<SysPhoPermissionRecordQuery, SysPhoPermissionRecordDto, SysPhoPermissionRecordPo> {



    List<SysPhoPermissionRecordDto> selectRecordForTimes(@Param("query") SysPhoPermissionRecordQuery query);
}
