package com.heju.system.file.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.file.domain.dto.SysFileBorrowRecordDto;
import com.heju.system.file.domain.model.SysFileBorrowRecordConverter;
import com.heju.system.file.domain.po.SysFileBorrowRecordPo;
import com.heju.system.file.domain.query.SysFileBorrowRecordQuery;
import com.heju.system.file.manager.ISysFileBorrowRecordManager;
import com.heju.system.file.mapper.SysFileBorrowRecordMapper;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 文件借阅记录管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysFileBorrowRecordManager extends BaseManagerImpl<SysFileBorrowRecordQuery, SysFileBorrowRecordDto, SysFileBorrowRecordPo, SysFileBorrowRecordMapper, SysFileBorrowRecordConverter> implements ISysFileBorrowRecordManager {

    /**
     * 查列表
     * @param query
     * @return
     */
    public List<SysFileBorrowRecordDto> selectList(SysFileBorrowRecordQuery query) {
        //精确到秒级
        LocalDateTime now = LocalDateTime.now();
        List<SysFileBorrowRecordPo> poList = baseMapper.selectList(new LambdaQueryWrapper<SysFileBorrowRecordPo>(query)
                        .ge(SysFileBorrowRecordPo::getEndTime,now));
        return subMerge(mapperDto(poList));
    }

    /**
     * 查借阅记录列表
     * @param query 数据查询对象
     * @return
     */
    @Override
    public List<SysFileBorrowRecordDto> selectOverTimeList(SysFileBorrowRecordQuery query) {
        //精确到秒级
        LocalDateTime now = LocalDateTime.now();
        List<SysFileBorrowRecordPo> poList = baseMapper.selectList(new LambdaQueryWrapper<SysFileBorrowRecordPo>(query)
                .le(SysFileBorrowRecordPo::getEndTime,now));
        return subMerge(mapperDto(poList));
    }

}
