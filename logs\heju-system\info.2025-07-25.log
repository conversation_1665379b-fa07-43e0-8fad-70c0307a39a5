09:09:00.573 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:09:01.655 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 64553c4e-b463-4ff0-baad-269108ecbce1_config-0
09:09:01.777 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 58 ms to scan 1 urls, producing 3 keys and 6 values 
09:09:01.847 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:09:01.855 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 5 ms to scan 1 urls, producing 3 keys and 10 values 
09:09:01.865 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:09:01.877 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:09:01.885 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
09:09:01.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64553c4e-b463-4ff0-baad-269108ecbce1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:09:01.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64553c4e-b463-4ff0-baad-269108ecbce1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000026b673cf410
09:09:01.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64553c4e-b463-4ff0-baad-269108ecbce1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000026b673cf630
09:09:01.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64553c4e-b463-4ff0-baad-269108ecbce1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:09:01.892 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64553c4e-b463-4ff0-baad-269108ecbce1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:09:01.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64553c4e-b463-4ff0-baad-269108ecbce1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:09:03.067 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64553c4e-b463-4ff0-baad-269108ecbce1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753405742826_127.0.0.1_6349
09:09:03.068 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64553c4e-b463-4ff0-baad-269108ecbce1_config-0] Notify connected event to listeners.
09:09:03.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64553c4e-b463-4ff0-baad-269108ecbce1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:09:03.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64553c4e-b463-4ff0-baad-269108ecbce1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026b67508fb0
09:09:03.259 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:09:09.761 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:09:09.763 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:09:09.763 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:09:10.110 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:09:11.398 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:09:11.399 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:09:11.399 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:09:22.022 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:09:26.311 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 929bd55f-e4e4-43d4-913f-50a407b8b7a3
09:09:26.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [929bd55f-e4e4-43d4-913f-50a407b8b7a3] RpcClient init label, labels = {module=naming, source=sdk}
09:09:26.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [929bd55f-e4e4-43d4-913f-50a407b8b7a3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:09:26.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [929bd55f-e4e4-43d4-913f-50a407b8b7a3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:09:26.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [929bd55f-e4e4-43d4-913f-50a407b8b7a3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:09:26.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [929bd55f-e4e4-43d4-913f-50a407b8b7a3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:09:26.452 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [929bd55f-e4e4-43d4-913f-50a407b8b7a3] Success to connect to server [localhost:8848] on start up, connectionId = 1753405766329_127.0.0.1_6530
09:09:26.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [929bd55f-e4e4-43d4-913f-50a407b8b7a3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:09:26.453 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [929bd55f-e4e4-43d4-913f-50a407b8b7a3] Notify connected event to listeners.
09:09:26.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [929bd55f-e4e4-43d4-913f-50a407b8b7a3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026b67508fb0
09:09:26.520 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:09:26.557 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:09:26.716 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 27.171 seconds (JVM running for 28.615)
09:09:26.735 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:09:26.735 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:09:26.735 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:09:26.928 [RMI TCP Connection(13)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:09:27.043 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [929bd55f-e4e4-43d4-913f-50a407b8b7a3] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:09:27.059 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [929bd55f-e4e4-43d4-913f-50a407b8b7a3] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:11:10.827 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [929bd55f-e4e4-43d4-913f-50a407b8b7a3] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:11:10.827 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [929bd55f-e4e4-43d4-913f-50a407b8b7a3] Ack server push request, request = NotifySubscriberRequest, requestId = 14
09:11:11.436 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:11:11.436 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:11:11.652 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:11:11.654 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
09:30:36.267 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:30:36.274 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:30:36.623 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:30:36.623 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7b6ebab9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:30:36.625 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753405766329_127.0.0.1_6530
09:30:36.629 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2f74608[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 272]
09:30:36.794 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:30:36.806 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
09:30:36.816 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
09:30:36.816 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
09:30:36.820 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
09:30:36.820 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:30:36.821 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:30:36.821 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:31:12.965 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:31:14.262 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b10630e8-cfe8-4458-b8e2-77d6ec3eb061_config-0
09:31:14.352 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
09:31:14.403 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
09:31:14.417 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:31:14.429 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:31:14.440 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:31:14.451 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:31:14.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b10630e8-cfe8-4458-b8e2-77d6ec3eb061_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:31:14.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b10630e8-cfe8-4458-b8e2-77d6ec3eb061_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000020a813b6d38
09:31:14.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b10630e8-cfe8-4458-b8e2-77d6ec3eb061_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000020a813b6f58
09:31:14.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b10630e8-cfe8-4458-b8e2-77d6ec3eb061_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:31:14.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b10630e8-cfe8-4458-b8e2-77d6ec3eb061_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:31:14.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b10630e8-cfe8-4458-b8e2-77d6ec3eb061_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:31:15.551 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b10630e8-cfe8-4458-b8e2-77d6ec3eb061_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753407075297_127.0.0.1_9585
09:31:15.553 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b10630e8-cfe8-4458-b8e2-77d6ec3eb061_config-0] Notify connected event to listeners.
09:31:15.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b10630e8-cfe8-4458-b8e2-77d6ec3eb061_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:31:15.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b10630e8-cfe8-4458-b8e2-77d6ec3eb061_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000020a814f0ad8
09:31:15.800 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:31:19.858 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:31:19.859 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:31:19.859 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:31:20.031 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:31:20.818 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:31:20.824 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:31:20.824 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:31:28.793 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:31:31.772 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2bada8a4-6e46-48d7-bf4d-5a34d635705c
09:31:31.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bada8a4-6e46-48d7-bf4d-5a34d635705c] RpcClient init label, labels = {module=naming, source=sdk}
09:31:31.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bada8a4-6e46-48d7-bf4d-5a34d635705c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:31:31.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bada8a4-6e46-48d7-bf4d-5a34d635705c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:31:31.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bada8a4-6e46-48d7-bf4d-5a34d635705c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:31:31.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bada8a4-6e46-48d7-bf4d-5a34d635705c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:31:31.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bada8a4-6e46-48d7-bf4d-5a34d635705c] Success to connect to server [localhost:8848] on start up, connectionId = 1753407091790_127.0.0.1_9603
09:31:31.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bada8a4-6e46-48d7-bf4d-5a34d635705c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:31:31.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bada8a4-6e46-48d7-bf4d-5a34d635705c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000020a814f0ad8
09:31:31.915 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bada8a4-6e46-48d7-bf4d-5a34d635705c] Notify connected event to listeners.
09:31:32.006 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:31:32.522 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bada8a4-6e46-48d7-bf4d-5a34d635705c] Receive server push request, request = NotifySubscriberRequest, requestId = 19
09:31:32.524 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bada8a4-6e46-48d7-bf4d-5a34d635705c] Ack server push request, request = NotifySubscriberRequest, requestId = 19
09:31:32.666 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:31:32.667 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@8004b2d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:31:32.667 [main] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753407091790_127.0.0.1_9603
09:31:32.671 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753407091790_127.0.0.1_9603]Ignore complete event,isRunning:false,isAbandon=false
09:31:32.674 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@11caa21d[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 6]
09:31:32.701 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5eaecd0f-28b2-4364-a04d-68fad94114f0
09:31:32.701 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5eaecd0f-28b2-4364-a04d-68fad94114f0] RpcClient init label, labels = {module=naming, source=sdk}
09:31:32.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5eaecd0f-28b2-4364-a04d-68fad94114f0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:31:32.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5eaecd0f-28b2-4364-a04d-68fad94114f0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:31:32.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5eaecd0f-28b2-4364-a04d-68fad94114f0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:31:32.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5eaecd0f-28b2-4364-a04d-68fad94114f0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:31:32.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5eaecd0f-28b2-4364-a04d-68fad94114f0] Success to connect to server [localhost:8848] on start up, connectionId = 1753407092728_127.0.0.1_9604
09:31:32.908 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5eaecd0f-28b2-4364-a04d-68fad94114f0] Notify connected event to listeners.
09:31:32.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5eaecd0f-28b2-4364-a04d-68fad94114f0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:31:32.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5eaecd0f-28b2-4364-a04d-68fad94114f0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000020a814f0ad8
09:31:32.988 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:31:32.992 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:31:33.002 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:31:33.002 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:31:33.003 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9600"]
09:31:33.003 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:31:33.023 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9600"]
09:31:33.030 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9600"]
09:33:56.155 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:33:56.935 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6683651c-07dd-4aa6-b9c6-f063f2add0fa_config-0
09:33:57.024 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 
09:33:57.079 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:33:57.094 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:33:57.102 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:33:57.127 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 1 keys and 7 values 
09:33:57.135 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:33:57.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6683651c-07dd-4aa6-b9c6-f063f2add0fa_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:33:57.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6683651c-07dd-4aa6-b9c6-f063f2add0fa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d2593cdd70
09:33:57.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6683651c-07dd-4aa6-b9c6-f063f2add0fa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001d2593cdf90
09:33:57.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6683651c-07dd-4aa6-b9c6-f063f2add0fa_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:33:57.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6683651c-07dd-4aa6-b9c6-f063f2add0fa_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:33:57.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6683651c-07dd-4aa6-b9c6-f063f2add0fa_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:33:58.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6683651c-07dd-4aa6-b9c6-f063f2add0fa_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753407237941_127.0.0.1_9805
09:33:58.173 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6683651c-07dd-4aa6-b9c6-f063f2add0fa_config-0] Notify connected event to listeners.
09:33:58.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6683651c-07dd-4aa6-b9c6-f063f2add0fa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:33:58.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6683651c-07dd-4aa6-b9c6-f063f2add0fa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d259507d88
09:33:58.363 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:34:02.534 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:34:02.534 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:34:02.534 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:34:02.725 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:34:03.598 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:34:03.598 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:34:03.598 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:34:11.619 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:34:14.650 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 35933cde-7ea4-459f-a4da-a09f7d7f7e1e
09:34:14.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35933cde-7ea4-459f-a4da-a09f7d7f7e1e] RpcClient init label, labels = {module=naming, source=sdk}
09:34:14.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35933cde-7ea4-459f-a4da-a09f7d7f7e1e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:34:14.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35933cde-7ea4-459f-a4da-a09f7d7f7e1e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:34:14.655 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35933cde-7ea4-459f-a4da-a09f7d7f7e1e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:34:14.655 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35933cde-7ea4-459f-a4da-a09f7d7f7e1e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:34:14.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35933cde-7ea4-459f-a4da-a09f7d7f7e1e] Success to connect to server [localhost:8848] on start up, connectionId = 1753407254657_127.0.0.1_9825
09:34:14.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35933cde-7ea4-459f-a4da-a09f7d7f7e1e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:34:14.791 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35933cde-7ea4-459f-a4da-a09f7d7f7e1e] Notify connected event to listeners.
09:34:14.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35933cde-7ea4-459f-a4da-a09f7d7f7e1e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d259507d88
09:34:14.860 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:34:14.901 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:34:15.041 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.474 seconds (JVM running for 20.435)
09:34:15.059 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:34:15.059 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:34:15.059 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:34:15.234 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:34:15.357 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35933cde-7ea4-459f-a4da-a09f7d7f7e1e] Receive server push request, request = NotifySubscriberRequest, requestId = 22
09:34:15.370 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35933cde-7ea4-459f-a4da-a09f7d7f7e1e] Ack server push request, request = NotifySubscriberRequest, requestId = 22
09:35:10.725 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:35:10.725 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:56:44.292 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:56:44.304 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:56:44.650 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:56:44.650 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5a8c856d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:56:44.652 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753407254657_127.0.0.1_9825
09:56:44.654 [nacos-grpc-client-executor-276] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753407254657_127.0.0.1_9825]Ignore complete event,isRunning:false,isAbandon=false
09:56:44.658 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@64bd250d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 277]
09:56:44.822 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:56:44.826 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
09:56:44.833 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
09:56:44.833 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:56:44.834 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:56:44.834 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:56:48.373 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:56:49.212 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 01f74625-bf0b-45f9-932a-ff5d29906f2d_config-0
09:56:49.285 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
09:56:49.329 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:56:49.339 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:56:49.350 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:56:49.361 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:56:49.374 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:56:49.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01f74625-bf0b-45f9-932a-ff5d29906f2d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:56:49.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01f74625-bf0b-45f9-932a-ff5d29906f2d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001ced539ed38
09:56:49.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01f74625-bf0b-45f9-932a-ff5d29906f2d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001ced539ef58
09:56:49.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01f74625-bf0b-45f9-932a-ff5d29906f2d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:56:49.382 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01f74625-bf0b-45f9-932a-ff5d29906f2d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:56:49.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01f74625-bf0b-45f9-932a-ff5d29906f2d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:56:50.631 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01f74625-bf0b-45f9-932a-ff5d29906f2d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753408610398_127.0.0.1_11746
09:56:50.632 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01f74625-bf0b-45f9-932a-ff5d29906f2d_config-0] Notify connected event to listeners.
09:56:50.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01f74625-bf0b-45f9-932a-ff5d29906f2d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:56:50.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [01f74625-bf0b-45f9-932a-ff5d29906f2d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001ced5518fb0
09:56:50.749 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:56:54.573 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:56:54.574 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:56:54.574 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:56:54.796 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:56:55.879 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:56:55.882 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:56:55.882 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:57:04.200 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:57:07.352 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1b1b2c4c-1fda-4af4-9d30-7f7b8cb10349
09:57:07.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b1b2c4c-1fda-4af4-9d30-7f7b8cb10349] RpcClient init label, labels = {module=naming, source=sdk}
09:57:07.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b1b2c4c-1fda-4af4-9d30-7f7b8cb10349] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:57:07.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b1b2c4c-1fda-4af4-9d30-7f7b8cb10349] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:57:07.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b1b2c4c-1fda-4af4-9d30-7f7b8cb10349] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:57:07.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b1b2c4c-1fda-4af4-9d30-7f7b8cb10349] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:57:07.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b1b2c4c-1fda-4af4-9d30-7f7b8cb10349] Success to connect to server [localhost:8848] on start up, connectionId = 1753408627370_127.0.0.1_11772
09:57:07.486 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b1b2c4c-1fda-4af4-9d30-7f7b8cb10349] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:57:07.486 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b1b2c4c-1fda-4af4-9d30-7f7b8cb10349] Notify connected event to listeners.
09:57:07.486 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b1b2c4c-1fda-4af4-9d30-7f7b8cb10349] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001ced5518fb0
09:57:07.580 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:57:07.620 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:57:07.766 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.01 seconds (JVM running for 21.188)
09:57:07.790 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:57:07.791 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:57:07.792 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:57:08.104 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b1b2c4c-1fda-4af4-9d30-7f7b8cb10349] Receive server push request, request = NotifySubscriberRequest, requestId = 29
09:57:08.123 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b1b2c4c-1fda-4af4-9d30-7f7b8cb10349] Ack server push request, request = NotifySubscriberRequest, requestId = 29
09:57:08.356 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:57:18.927 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:57:18.927 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:22:44.955 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:22:44.955 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:22:45.288 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:22:45.294 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3dca7277[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:22:45.294 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753408627370_127.0.0.1_11772
11:22:45.296 [nacos-grpc-client-executor-1036] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753408627370_127.0.0.1_11772]Ignore complete event,isRunning:false,isAbandon=false
11:22:45.298 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7af7280e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1037]
11:22:45.468 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:22:45.471 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:22:45.481 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:22:45.487 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:22:45.487 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:22:45.487 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:22:51.092 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:22:51.895 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b0b928f0-ba9e-4c91-9446-83baa2009cc5_config-0
11:22:51.970 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
11:22:52.010 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
11:22:52.020 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:22:52.031 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
11:22:52.040 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
11:22:52.052 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
11:22:52.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b928f0-ba9e-4c91-9446-83baa2009cc5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:22:52.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b928f0-ba9e-4c91-9446-83baa2009cc5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001e78139ed38
11:22:52.057 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b928f0-ba9e-4c91-9446-83baa2009cc5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001e78139ef58
11:22:52.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b928f0-ba9e-4c91-9446-83baa2009cc5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:22:52.059 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b928f0-ba9e-4c91-9446-83baa2009cc5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:22:52.070 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b928f0-ba9e-4c91-9446-83baa2009cc5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:22:53.040 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b928f0-ba9e-4c91-9446-83baa2009cc5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753413772817_127.0.0.1_4180
11:22:53.042 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b928f0-ba9e-4c91-9446-83baa2009cc5_config-0] Notify connected event to listeners.
11:22:53.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b928f0-ba9e-4c91-9446-83baa2009cc5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:22:53.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0b928f0-ba9e-4c91-9446-83baa2009cc5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e781518668
11:22:53.248 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:22:56.943 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:22:56.944 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:22:56.944 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:22:57.145 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:22:57.940 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:22:57.943 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:22:57.943 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:23:05.728 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:23:08.663 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 299f5ce8-c29b-436c-9033-33daf876c703
11:23:08.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [299f5ce8-c29b-436c-9033-33daf876c703] RpcClient init label, labels = {module=naming, source=sdk}
11:23:08.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [299f5ce8-c29b-436c-9033-33daf876c703] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:23:08.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [299f5ce8-c29b-436c-9033-33daf876c703] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:23:08.666 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [299f5ce8-c29b-436c-9033-33daf876c703] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:23:08.666 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [299f5ce8-c29b-436c-9033-33daf876c703] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:23:08.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [299f5ce8-c29b-436c-9033-33daf876c703] Success to connect to server [localhost:8848] on start up, connectionId = 1753413788675_127.0.0.1_4196
11:23:08.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [299f5ce8-c29b-436c-9033-33daf876c703] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:23:08.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [299f5ce8-c29b-436c-9033-33daf876c703] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e781518668
11:23:08.790 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [299f5ce8-c29b-436c-9033-33daf876c703] Notify connected event to listeners.
11:23:08.861 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:23:08.903 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:23:09.038 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.591 seconds (JVM running for 19.618)
11:23:09.053 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:23:09.053 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:23:09.053 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:23:09.209 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:23:09.423 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [299f5ce8-c29b-436c-9033-33daf876c703] Receive server push request, request = NotifySubscriberRequest, requestId = 36
11:23:09.441 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [299f5ce8-c29b-436c-9033-33daf876c703] Ack server push request, request = NotifySubscriberRequest, requestId = 36
11:23:16.472 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:23:16.473 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:35:17.679 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:35:17.687 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:35:17.997 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:35:17.997 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@179ffe06[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:35:17.997 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753413788675_127.0.0.1_4196
11:35:17.997 [nacos-grpc-client-executor-144] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753413788675_127.0.0.1_4196]Ignore complete event,isRunning:false,isAbandon=false
11:35:18.012 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5ec6b936[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 145]
11:35:18.178 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:35:18.178 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:35:18.199 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:35:18.201 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:35:18.203 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:35:18.203 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:35:24.168 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:35:25.027 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0dd4c59e-e5af-45a4-8deb-05671a3f5840_config-0
11:35:25.097 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 
11:35:25.139 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
11:35:25.148 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
11:35:25.158 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
11:35:25.168 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
11:35:25.181 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
11:35:25.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd4c59e-e5af-45a4-8deb-05671a3f5840_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:35:25.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd4c59e-e5af-45a4-8deb-05671a3f5840_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f58139eaf8
11:35:25.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd4c59e-e5af-45a4-8deb-05671a3f5840_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001f58139ed18
11:35:25.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd4c59e-e5af-45a4-8deb-05671a3f5840_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:35:25.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd4c59e-e5af-45a4-8deb-05671a3f5840_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:35:25.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd4c59e-e5af-45a4-8deb-05671a3f5840_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:35:26.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd4c59e-e5af-45a4-8deb-05671a3f5840_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753414525962_127.0.0.1_5077
11:35:26.191 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd4c59e-e5af-45a4-8deb-05671a3f5840_config-0] Notify connected event to listeners.
11:35:26.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd4c59e-e5af-45a4-8deb-05671a3f5840_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:35:26.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0dd4c59e-e5af-45a4-8deb-05671a3f5840_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f581518668
11:35:26.379 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:35:30.108 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:35:30.108 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:35:30.109 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:35:30.302 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:35:31.064 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:35:31.066 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:35:31.066 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:35:38.677 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:35:41.696 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 133dbb46-2db7-476d-a505-c12d5db86afc
11:35:41.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133dbb46-2db7-476d-a505-c12d5db86afc] RpcClient init label, labels = {module=naming, source=sdk}
11:35:41.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133dbb46-2db7-476d-a505-c12d5db86afc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:35:41.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133dbb46-2db7-476d-a505-c12d5db86afc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:35:41.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133dbb46-2db7-476d-a505-c12d5db86afc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:35:41.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133dbb46-2db7-476d-a505-c12d5db86afc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:35:41.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133dbb46-2db7-476d-a505-c12d5db86afc] Success to connect to server [localhost:8848] on start up, connectionId = 1753414541709_127.0.0.1_5089
11:35:41.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133dbb46-2db7-476d-a505-c12d5db86afc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:35:41.822 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133dbb46-2db7-476d-a505-c12d5db86afc] Notify connected event to listeners.
11:35:41.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133dbb46-2db7-476d-a505-c12d5db86afc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f581518668
11:35:41.908 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:35:41.951 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:35:42.093 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.549 seconds (JVM running for 19.77)
11:35:42.109 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:35:42.109 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:35:42.110 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:35:42.261 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:35:42.420 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133dbb46-2db7-476d-a505-c12d5db86afc] Receive server push request, request = NotifySubscriberRequest, requestId = 42
11:35:42.434 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [133dbb46-2db7-476d-a505-c12d5db86afc] Ack server push request, request = NotifySubscriberRequest, requestId = 42
11:35:55.642 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:35:55.643 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:38:37.591 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:38:37.604 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:38:37.929 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:38:37.929 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3632cf46[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:38:37.929 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753414541709_127.0.0.1_5089
11:38:37.929 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753414541709_127.0.0.1_5089]Ignore complete event,isRunning:false,isAbandon=false
11:38:37.940 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@25b2cce7[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 40]
11:38:38.114 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:38:38.123 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:38:38.134 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:38:38.134 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:38:38.137 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:38:38.137 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:38:43.508 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:38:44.345 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6da14b04-3242-4a00-a0d8-df3828a7d02a_config-0
11:38:44.415 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 
11:38:44.457 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
11:38:44.470 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
11:38:44.482 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
11:38:44.493 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
11:38:44.500 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
11:38:44.508 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6da14b04-3242-4a00-a0d8-df3828a7d02a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:38:44.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6da14b04-3242-4a00-a0d8-df3828a7d02a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002462e39ed38
11:38:44.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6da14b04-3242-4a00-a0d8-df3828a7d02a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002462e39ef58
11:38:44.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6da14b04-3242-4a00-a0d8-df3828a7d02a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:38:44.510 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6da14b04-3242-4a00-a0d8-df3828a7d02a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:38:44.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6da14b04-3242-4a00-a0d8-df3828a7d02a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:38:45.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6da14b04-3242-4a00-a0d8-df3828a7d02a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753414725275_127.0.0.1_5527
11:38:45.501 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6da14b04-3242-4a00-a0d8-df3828a7d02a_config-0] Notify connected event to listeners.
11:38:45.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6da14b04-3242-4a00-a0d8-df3828a7d02a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:38:45.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6da14b04-3242-4a00-a0d8-df3828a7d02a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002462e518668
11:38:45.632 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:38:50.322 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:38:50.331 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:38:50.331 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:38:50.691 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:38:51.987 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:38:51.987 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:38:51.987 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:39:07.411 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:39:10.845 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bf7a9049-b43b-4205-b09e-ccf4cbf80f19
11:39:10.845 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf7a9049-b43b-4205-b09e-ccf4cbf80f19] RpcClient init label, labels = {module=naming, source=sdk}
11:39:10.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf7a9049-b43b-4205-b09e-ccf4cbf80f19] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:39:10.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf7a9049-b43b-4205-b09e-ccf4cbf80f19] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:39:10.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf7a9049-b43b-4205-b09e-ccf4cbf80f19] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:39:10.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf7a9049-b43b-4205-b09e-ccf4cbf80f19] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:39:10.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf7a9049-b43b-4205-b09e-ccf4cbf80f19] Success to connect to server [localhost:8848] on start up, connectionId = 1753414750856_127.0.0.1_5603
11:39:10.970 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf7a9049-b43b-4205-b09e-ccf4cbf80f19] Notify connected event to listeners.
11:39:10.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf7a9049-b43b-4205-b09e-ccf4cbf80f19] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:39:10.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf7a9049-b43b-4205-b09e-ccf4cbf80f19] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002462e518668
11:39:11.019 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:39:11.056 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:39:11.216 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 28.358 seconds (JVM running for 29.483)
11:39:11.232 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:39:11.232 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:39:11.232 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:39:11.460 [RMI TCP Connection(17)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:39:11.575 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf7a9049-b43b-4205-b09e-ccf4cbf80f19] Receive server push request, request = NotifySubscriberRequest, requestId = 50
11:39:11.599 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf7a9049-b43b-4205-b09e-ccf4cbf80f19] Ack server push request, request = NotifySubscriberRequest, requestId = 50
11:39:22.309 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:39:22.309 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:39:27.473 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:39:27.485 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:39:27.835 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:39:27.835 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3cfc454d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:39:27.835 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753414750856_127.0.0.1_5603
11:39:27.838 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753414750856_127.0.0.1_5603]Ignore complete event,isRunning:false,isAbandon=false
11:39:27.840 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@27bcf3de[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 13]
11:39:28.027 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:39:28.033 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:39:28.047 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:39:28.047 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:39:28.048 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:39:28.048 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:39:33.341 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:39:34.177 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0f26dc86-b047-44a9-90f6-c1920880b74f_config-0
11:39:34.254 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 3 keys and 6 values 
11:39:34.313 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
11:39:34.313 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
11:39:34.332 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
11:39:34.345 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
11:39:34.358 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
11:39:34.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f26dc86-b047-44a9-90f6-c1920880b74f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:39:34.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f26dc86-b047-44a9-90f6-c1920880b74f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000187e239e480
11:39:34.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f26dc86-b047-44a9-90f6-c1920880b74f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000187e239e6a0
11:39:34.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f26dc86-b047-44a9-90f6-c1920880b74f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:39:34.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f26dc86-b047-44a9-90f6-c1920880b74f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:39:34.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f26dc86-b047-44a9-90f6-c1920880b74f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:39:35.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f26dc86-b047-44a9-90f6-c1920880b74f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753414775179_127.0.0.1_5671
11:39:35.418 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f26dc86-b047-44a9-90f6-c1920880b74f_config-0] Notify connected event to listeners.
11:39:35.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f26dc86-b047-44a9-90f6-c1920880b74f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:39:35.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f26dc86-b047-44a9-90f6-c1920880b74f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000187e2518228
11:39:35.621 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:39:39.670 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:39:39.670 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:39:39.670 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:39:39.876 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:39:40.747 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:39:40.747 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:39:40.747 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:39:48.767 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:39:52.071 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b1e2327e-9af1-4860-971b-931bf7a446d3
11:39:52.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1e2327e-9af1-4860-971b-931bf7a446d3] RpcClient init label, labels = {module=naming, source=sdk}
11:39:52.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1e2327e-9af1-4860-971b-931bf7a446d3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:39:52.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1e2327e-9af1-4860-971b-931bf7a446d3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:39:52.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1e2327e-9af1-4860-971b-931bf7a446d3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:39:52.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1e2327e-9af1-4860-971b-931bf7a446d3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:39:52.195 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1e2327e-9af1-4860-971b-931bf7a446d3] Success to connect to server [localhost:8848] on start up, connectionId = 1753414792083_127.0.0.1_5723
11:39:52.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1e2327e-9af1-4860-971b-931bf7a446d3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:39:52.196 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1e2327e-9af1-4860-971b-931bf7a446d3] Notify connected event to listeners.
11:39:52.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1e2327e-9af1-4860-971b-931bf7a446d3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000187e2518228
11:39:52.273 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:39:52.329 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:39:52.466 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.694 seconds (JVM running for 20.738)
11:39:52.487 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:39:52.487 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:39:52.487 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:39:52.708 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:39:52.826 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1e2327e-9af1-4860-971b-931bf7a446d3] Receive server push request, request = NotifySubscriberRequest, requestId = 56
11:39:52.842 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1e2327e-9af1-4860-971b-931bf7a446d3] Ack server push request, request = NotifySubscriberRequest, requestId = 56
11:40:08.429 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:40:08.430 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:46:04.479 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:46:04.483 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:46:04.827 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:46:04.827 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@315ffd1d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:46:04.827 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753414792083_127.0.0.1_5723
11:46:04.831 [nacos-grpc-client-executor-79] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753414792083_127.0.0.1_5723]Ignore complete event,isRunning:false,isAbandon=false
11:46:04.831 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5bf8d225[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 80]
11:46:04.996 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:46:05.001 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:46:05.012 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:46:05.012 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:46:05.012 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:46:05.015 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:46:10.747 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:46:11.501 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 867ee83f-00fc-4967-a62e-3b51c69c4042_config-0
11:46:11.571 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 3 keys and 6 values 
11:46:11.613 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
11:46:11.623 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
11:46:11.638 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
11:46:11.650 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
11:46:11.658 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
11:46:11.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [867ee83f-00fc-4967-a62e-3b51c69c4042_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:46:11.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [867ee83f-00fc-4967-a62e-3b51c69c4042_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d20139e480
11:46:11.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [867ee83f-00fc-4967-a62e-3b51c69c4042_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001d20139e6a0
11:46:11.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [867ee83f-00fc-4967-a62e-3b51c69c4042_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:46:11.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [867ee83f-00fc-4967-a62e-3b51c69c4042_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:46:11.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [867ee83f-00fc-4967-a62e-3b51c69c4042_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:46:12.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [867ee83f-00fc-4967-a62e-3b51c69c4042_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753415172428_127.0.0.1_6290
11:46:12.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [867ee83f-00fc-4967-a62e-3b51c69c4042_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:46:12.646 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [867ee83f-00fc-4967-a62e-3b51c69c4042_config-0] Notify connected event to listeners.
11:46:12.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [867ee83f-00fc-4967-a62e-3b51c69c4042_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d201518228
11:46:12.857 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:46:16.641 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:46:16.643 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:46:16.643 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:46:16.872 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:46:18.274 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:46:18.276 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:46:18.277 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:46:26.889 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:46:30.138 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fcf1c2f8-3455-441f-beb7-c460c6fce76b
11:46:30.138 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcf1c2f8-3455-441f-beb7-c460c6fce76b] RpcClient init label, labels = {module=naming, source=sdk}
11:46:30.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcf1c2f8-3455-441f-beb7-c460c6fce76b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:46:30.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcf1c2f8-3455-441f-beb7-c460c6fce76b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:46:30.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcf1c2f8-3455-441f-beb7-c460c6fce76b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:46:30.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcf1c2f8-3455-441f-beb7-c460c6fce76b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:46:30.268 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcf1c2f8-3455-441f-beb7-c460c6fce76b] Success to connect to server [localhost:8848] on start up, connectionId = 1753415190149_127.0.0.1_6325
11:46:30.269 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcf1c2f8-3455-441f-beb7-c460c6fce76b] Notify connected event to listeners.
11:46:30.269 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcf1c2f8-3455-441f-beb7-c460c6fce76b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:46:30.270 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcf1c2f8-3455-441f-beb7-c460c6fce76b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d201518228
11:46:30.364 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:46:30.411 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:46:30.556 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.53 seconds (JVM running for 21.976)
11:46:30.574 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:46:30.575 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:46:30.575 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:46:30.910 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcf1c2f8-3455-441f-beb7-c460c6fce76b] Receive server push request, request = NotifySubscriberRequest, requestId = 64
11:46:30.927 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fcf1c2f8-3455-441f-beb7-c460c6fce76b] Ack server push request, request = NotifySubscriberRequest, requestId = 64
11:46:31.148 [RMI TCP Connection(11)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:46:40.357 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:46:40.358 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:50:25.685 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:50:25.685 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:50:26.005 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:50:26.005 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@40fa4fd0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:50:26.005 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753415190149_127.0.0.1_6325
11:50:26.005 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753415190149_127.0.0.1_6325]Ignore complete event,isRunning:false,isAbandon=false
11:50:26.005 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@14d6e00a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 14]
11:50:26.148 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:50:26.148 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:50:26.148 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:50:26.148 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:50:26.148 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:50:26.148 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:50:30.207 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:50:30.735 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f25552c7-ae4f-498f-b75d-2e4c9921e059_config-0
11:50:30.789 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 
11:50:30.817 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
11:50:30.823 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:50:30.830 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:50:30.837 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
11:50:30.843 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
11:50:30.845 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f25552c7-ae4f-498f-b75d-2e4c9921e059_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:50:30.845 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f25552c7-ae4f-498f-b75d-2e4c9921e059_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000253173cdd70
11:50:30.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f25552c7-ae4f-498f-b75d-2e4c9921e059_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000253173cdf90
11:50:30.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f25552c7-ae4f-498f-b75d-2e4c9921e059_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:50:30.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f25552c7-ae4f-498f-b75d-2e4c9921e059_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:50:30.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f25552c7-ae4f-498f-b75d-2e4c9921e059_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:50:31.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f25552c7-ae4f-498f-b75d-2e4c9921e059_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753415431325_127.0.0.1_6667
11:50:31.514 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f25552c7-ae4f-498f-b75d-2e4c9921e059_config-0] Notify connected event to listeners.
11:50:31.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f25552c7-ae4f-498f-b75d-2e4c9921e059_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:50:31.516 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f25552c7-ae4f-498f-b75d-2e4c9921e059_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025317508228
11:50:31.597 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:50:33.935 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:50:33.936 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:50:33.936 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:50:34.046 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:50:34.746 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:50:34.747 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:50:34.747 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:50:39.857 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:50:41.959 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3726ad0a-b5a0-4058-b9eb-0ec576bcea95
11:50:41.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3726ad0a-b5a0-4058-b9eb-0ec576bcea95] RpcClient init label, labels = {module=naming, source=sdk}
11:50:41.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3726ad0a-b5a0-4058-b9eb-0ec576bcea95] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:50:41.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3726ad0a-b5a0-4058-b9eb-0ec576bcea95] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:50:41.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3726ad0a-b5a0-4058-b9eb-0ec576bcea95] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:50:41.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3726ad0a-b5a0-4058-b9eb-0ec576bcea95] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:50:42.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3726ad0a-b5a0-4058-b9eb-0ec576bcea95] Success to connect to server [localhost:8848] on start up, connectionId = 1753415441970_127.0.0.1_6684
11:50:42.092 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3726ad0a-b5a0-4058-b9eb-0ec576bcea95] Notify connected event to listeners.
11:50:42.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3726ad0a-b5a0-4058-b9eb-0ec576bcea95] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:50:42.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3726ad0a-b5a0-4058-b9eb-0ec576bcea95] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025317508228
11:50:42.131 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:50:42.152 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:50:42.244 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.511 seconds (JVM running for 13.447)
11:50:42.256 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:50:42.256 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:50:42.257 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:50:42.374 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:50:42.700 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3726ad0a-b5a0-4058-b9eb-0ec576bcea95] Receive server push request, request = NotifySubscriberRequest, requestId = 70
11:50:42.714 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3726ad0a-b5a0-4058-b9eb-0ec576bcea95] Ack server push request, request = NotifySubscriberRequest, requestId = 70
11:50:48.867 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:50:48.867 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:58:44.118 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:58:44.118 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:58:44.476 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:58:44.476 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5456fe7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:58:44.476 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753415441970_127.0.0.1_6684
11:58:44.476 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@dce9df6[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 62]
11:58:44.621 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:58:44.623 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:58:44.627 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:58:44.627 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:58:44.627 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:58:44.627 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:58:48.523 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:58:49.044 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e58bad2c-5110-4149-8a4f-580be8823ed1_config-0
11:58:49.090 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 6 values 
11:58:49.124 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 4 keys and 9 values 
11:58:49.133 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:58:49.137 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
11:58:49.143 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
11:58:49.145 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
11:58:49.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e58bad2c-5110-4149-8a4f-580be8823ed1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:58:49.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e58bad2c-5110-4149-8a4f-580be8823ed1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000027ad03bdd70
11:58:49.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e58bad2c-5110-4149-8a4f-580be8823ed1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000027ad03bdf90
11:58:49.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e58bad2c-5110-4149-8a4f-580be8823ed1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:58:49.156 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e58bad2c-5110-4149-8a4f-580be8823ed1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:58:49.156 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e58bad2c-5110-4149-8a4f-580be8823ed1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:58:49.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e58bad2c-5110-4149-8a4f-580be8823ed1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753415929623_127.0.0.1_7204
11:58:49.804 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e58bad2c-5110-4149-8a4f-580be8823ed1_config-0] Notify connected event to listeners.
11:58:49.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e58bad2c-5110-4149-8a4f-580be8823ed1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:58:49.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e58bad2c-5110-4149-8a4f-580be8823ed1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000027ad04f7cb0
11:58:49.875 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:58:52.109 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:58:52.109 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:58:52.109 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:58:52.221 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:58:52.627 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:58:52.627 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:58:52.627 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:58:57.709 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:58:59.801 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 59af4c41-88df-4a56-9c30-dd49bd83ef06
11:58:59.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59af4c41-88df-4a56-9c30-dd49bd83ef06] RpcClient init label, labels = {module=naming, source=sdk}
11:58:59.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59af4c41-88df-4a56-9c30-dd49bd83ef06] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:58:59.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59af4c41-88df-4a56-9c30-dd49bd83ef06] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:58:59.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59af4c41-88df-4a56-9c30-dd49bd83ef06] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:58:59.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59af4c41-88df-4a56-9c30-dd49bd83ef06] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:58:59.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59af4c41-88df-4a56-9c30-dd49bd83ef06] Success to connect to server [localhost:8848] on start up, connectionId = 1753415939810_127.0.0.1_7225
11:58:59.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59af4c41-88df-4a56-9c30-dd49bd83ef06] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:58:59.918 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59af4c41-88df-4a56-9c30-dd49bd83ef06] Notify connected event to listeners.
11:58:59.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59af4c41-88df-4a56-9c30-dd49bd83ef06] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000027ad04f7cb0
11:58:59.957 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:58:59.977 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:59:00.064 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 11.972 seconds (JVM running for 12.765)
11:59:00.074 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:59:00.075 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:59:00.075 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:59:00.377 [RMI TCP Connection(8)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:59:00.460 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59af4c41-88df-4a56-9c30-dd49bd83ef06] Receive server push request, request = NotifySubscriberRequest, requestId = 77
11:59:00.469 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59af4c41-88df-4a56-9c30-dd49bd83ef06] Ack server push request, request = NotifySubscriberRequest, requestId = 77
11:59:08.323 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:59:08.323 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:00:57.631 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:00:57.633 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:00:57.960 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:00:57.960 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@56d59d69[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:00:57.960 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753415939810_127.0.0.1_7225
12:00:57.960 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753415939810_127.0.0.1_7225]Ignore complete event,isRunning:false,isAbandon=false
12:00:57.960 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@66af91d[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 22]
12:00:58.101 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:00:58.103 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:00:58.109 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:00:58.109 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:00:58.109 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:00:58.109 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:01:02.085 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:01:02.614 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6a844765-7ef6-47d4-886a-a44d151a9b45_config-0
12:01:02.664 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 
12:01:02.691 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
12:01:02.697 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
12:01:02.704 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
12:01:02.711 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
12:01:02.717 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
12:01:02.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a844765-7ef6-47d4-886a-a44d151a9b45_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:01:02.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a844765-7ef6-47d4-886a-a44d151a9b45_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002871a3bfd00
12:01:02.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a844765-7ef6-47d4-886a-a44d151a9b45_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002871a3c0000
12:01:02.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a844765-7ef6-47d4-886a-a44d151a9b45_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:01:02.722 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a844765-7ef6-47d4-886a-a44d151a9b45_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:01:02.727 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a844765-7ef6-47d4-886a-a44d151a9b45_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:01:03.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a844765-7ef6-47d4-886a-a44d151a9b45_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753416063211_127.0.0.1_7383
12:01:03.392 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a844765-7ef6-47d4-886a-a44d151a9b45_config-0] Notify connected event to listeners.
12:01:03.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a844765-7ef6-47d4-886a-a44d151a9b45_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:01:03.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a844765-7ef6-47d4-886a-a44d151a9b45_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002871a4fa0a0
12:01:03.471 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:01:06.178 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:01:06.179 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:01:06.179 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:01:06.295 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:01:06.915 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:01:06.916 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:01:06.916 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:01:11.951 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:01:13.995 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7f26a0c6-e11e-49c6-aeab-1bb9d410ce8a
12:01:13.995 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f26a0c6-e11e-49c6-aeab-1bb9d410ce8a] RpcClient init label, labels = {module=naming, source=sdk}
12:01:13.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f26a0c6-e11e-49c6-aeab-1bb9d410ce8a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:01:13.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f26a0c6-e11e-49c6-aeab-1bb9d410ce8a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:01:13.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f26a0c6-e11e-49c6-aeab-1bb9d410ce8a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:01:13.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f26a0c6-e11e-49c6-aeab-1bb9d410ce8a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:01:14.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f26a0c6-e11e-49c6-aeab-1bb9d410ce8a] Success to connect to server [localhost:8848] on start up, connectionId = 1753416074004_127.0.0.1_7402
12:01:14.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f26a0c6-e11e-49c6-aeab-1bb9d410ce8a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:01:14.125 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f26a0c6-e11e-49c6-aeab-1bb9d410ce8a] Notify connected event to listeners.
12:01:14.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f26a0c6-e11e-49c6-aeab-1bb9d410ce8a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002871a4fa0a0
12:01:14.164 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:01:14.184 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:01:14.320 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.719 seconds (JVM running for 13.575)
12:01:14.330 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:01:14.330 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:01:14.331 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:01:14.716 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f26a0c6-e11e-49c6-aeab-1bb9d410ce8a] Receive server push request, request = NotifySubscriberRequest, requestId = 84
12:01:14.728 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f26a0c6-e11e-49c6-aeab-1bb9d410ce8a] Ack server push request, request = NotifySubscriberRequest, requestId = 84
12:01:14.752 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:01:18.451 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:01:18.451 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:03:10.945 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:03:10.951 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:03:11.289 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:03:11.289 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@d01fc66[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:03:11.289 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753416074004_127.0.0.1_7402
12:03:11.289 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753416074004_127.0.0.1_7402]Ignore complete event,isRunning:false,isAbandon=false
12:03:11.295 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@bd043c3[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 38]
12:03:11.413 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:03:11.423 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:03:11.425 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:03:11.425 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:03:11.425 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:03:11.425 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:03:15.309 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:03:15.860 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a1d03de0-0ba6-4072-bbae-caec90bbb8cb_config-0
12:03:15.914 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
12:03:15.940 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
12:03:15.946 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
12:03:15.952 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
12:03:15.958 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
12:03:15.966 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
12:03:15.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1d03de0-0ba6-4072-bbae-caec90bbb8cb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:03:15.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1d03de0-0ba6-4072-bbae-caec90bbb8cb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023f013be480
12:03:15.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1d03de0-0ba6-4072-bbae-caec90bbb8cb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000023f013be6a0
12:03:15.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1d03de0-0ba6-4072-bbae-caec90bbb8cb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:03:15.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1d03de0-0ba6-4072-bbae-caec90bbb8cb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:03:15.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1d03de0-0ba6-4072-bbae-caec90bbb8cb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:03:16.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1d03de0-0ba6-4072-bbae-caec90bbb8cb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753416196471_127.0.0.1_7675
12:03:16.652 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1d03de0-0ba6-4072-bbae-caec90bbb8cb_config-0] Notify connected event to listeners.
12:03:16.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1d03de0-0ba6-4072-bbae-caec90bbb8cb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:03:16.653 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1d03de0-0ba6-4072-bbae-caec90bbb8cb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023f014f8228
12:03:16.723 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:03:19.279 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:03:19.279 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:03:19.279 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:03:19.396 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:03:19.888 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:03:19.889 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:03:19.889 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:03:25.326 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:03:27.547 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c299fc8d-0962-4bf6-af06-18da796c1fba
12:03:27.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c299fc8d-0962-4bf6-af06-18da796c1fba] RpcClient init label, labels = {module=naming, source=sdk}
12:03:27.548 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c299fc8d-0962-4bf6-af06-18da796c1fba] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:03:27.548 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c299fc8d-0962-4bf6-af06-18da796c1fba] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:03:27.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c299fc8d-0962-4bf6-af06-18da796c1fba] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:03:27.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c299fc8d-0962-4bf6-af06-18da796c1fba] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:03:27.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c299fc8d-0962-4bf6-af06-18da796c1fba] Success to connect to server [localhost:8848] on start up, connectionId = 1753416207557_127.0.0.1_7698
12:03:27.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c299fc8d-0962-4bf6-af06-18da796c1fba] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:03:27.675 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c299fc8d-0962-4bf6-af06-18da796c1fba] Notify connected event to listeners.
12:03:27.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c299fc8d-0962-4bf6-af06-18da796c1fba] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023f014f8228
12:03:27.720 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:03:27.744 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:03:27.841 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.001 seconds (JVM running for 13.802)
12:03:27.854 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:03:27.854 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:03:27.854 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:03:28.022 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:03:28.218 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c299fc8d-0962-4bf6-af06-18da796c1fba] Receive server push request, request = NotifySubscriberRequest, requestId = 90
12:03:28.236 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c299fc8d-0962-4bf6-af06-18da796c1fba] Ack server push request, request = NotifySubscriberRequest, requestId = 90
12:03:35.532 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:03:35.532 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:09:25.144 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:09:25.160 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:09:25.499 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:09:25.499 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4d1d654a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:09:25.499 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753416207557_127.0.0.1_7698
13:09:25.499 [nacos-grpc-client-executor-757] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753416207557_127.0.0.1_7698]Ignore complete event,isRunning:false,isAbandon=false
13:09:25.499 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6d3c3dea[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 758]
13:09:25.630 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:09:25.630 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:09:25.643 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:09:25.643 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:09:25.643 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:09:25.643 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:09:51.580 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:09:52.131 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 328cba53-3602-4445-b11e-04c0d94bf5c6_config-0
13:09:52.202 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
13:09:52.235 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
13:09:52.249 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
13:09:52.257 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
13:09:52.267 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
13:09:52.275 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
13:09:52.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [328cba53-3602-4445-b11e-04c0d94bf5c6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:09:52.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [328cba53-3602-4445-b11e-04c0d94bf5c6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001ebd13b6480
13:09:52.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [328cba53-3602-4445-b11e-04c0d94bf5c6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001ebd13b66a0
13:09:52.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [328cba53-3602-4445-b11e-04c0d94bf5c6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:09:52.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [328cba53-3602-4445-b11e-04c0d94bf5c6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:09:52.287 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [328cba53-3602-4445-b11e-04c0d94bf5c6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:09:52.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [328cba53-3602-4445-b11e-04c0d94bf5c6_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753420192795_127.0.0.1_11573
13:09:52.983 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [328cba53-3602-4445-b11e-04c0d94bf5c6_config-0] Notify connected event to listeners.
13:09:52.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [328cba53-3602-4445-b11e-04c0d94bf5c6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:09:52.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [328cba53-3602-4445-b11e-04c0d94bf5c6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ebd14f0228
13:09:53.086 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:09:55.590 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:09:55.590 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:09:55.590 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:09:55.698 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:09:56.209 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:09:56.209 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:09:56.209 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:10:01.224 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:10:03.317 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4a52d007-7031-4f0e-ac97-c4849c7d2e71
13:10:03.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a52d007-7031-4f0e-ac97-c4849c7d2e71] RpcClient init label, labels = {module=naming, source=sdk}
13:10:03.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a52d007-7031-4f0e-ac97-c4849c7d2e71] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:10:03.319 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a52d007-7031-4f0e-ac97-c4849c7d2e71] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:10:03.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a52d007-7031-4f0e-ac97-c4849c7d2e71] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:10:03.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a52d007-7031-4f0e-ac97-c4849c7d2e71] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:10:03.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a52d007-7031-4f0e-ac97-c4849c7d2e71] Success to connect to server [localhost:8848] on start up, connectionId = 1753420203323_127.0.0.1_11590
13:10:03.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a52d007-7031-4f0e-ac97-c4849c7d2e71] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:10:03.440 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a52d007-7031-4f0e-ac97-c4849c7d2e71] Notify connected event to listeners.
13:10:03.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a52d007-7031-4f0e-ac97-c4849c7d2e71] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ebd14f0228
13:10:03.475 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:10:03.501 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:10:03.594 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.472 seconds (JVM running for 14.286)
13:10:03.605 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:10:03.605 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:10:03.605 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:10:04.051 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a52d007-7031-4f0e-ac97-c4849c7d2e71] Receive server push request, request = NotifySubscriberRequest, requestId = 98
13:10:04.067 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a52d007-7031-4f0e-ac97-c4849c7d2e71] Ack server push request, request = NotifySubscriberRequest, requestId = 98
13:10:10.557 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:10:11.664 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:10:11.664 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:26:36.990 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:26:36.995 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:26:37.326 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:26:37.326 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4085940a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:26:37.327 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753420203323_127.0.0.1_11590
14:26:37.327 [nacos-grpc-client-executor-925] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753420203323_127.0.0.1_11590]Ignore complete event,isRunning:false,isAbandon=false
14:26:37.331 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7a5ddf9e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 926]
14:26:37.505 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:26:37.509 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:26:37.522 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:26:37.522 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:26:37.524 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:26:37.524 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:26:42.150 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:26:42.686 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d20152f8-28f6-4a18-9fba-3e4721a3e534_config-0
14:26:42.738 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
14:26:42.763 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
14:26:42.768 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
14:26:42.774 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
14:26:42.780 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
14:26:42.786 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
14:26:42.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d20152f8-28f6-4a18-9fba-3e4721a3e534_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:26:42.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d20152f8-28f6-4a18-9fba-3e4721a3e534_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002030c3cdd70
14:26:42.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d20152f8-28f6-4a18-9fba-3e4721a3e534_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002030c3cdf90
14:26:42.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d20152f8-28f6-4a18-9fba-3e4721a3e534_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:26:42.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d20152f8-28f6-4a18-9fba-3e4721a3e534_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:26:42.797 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d20152f8-28f6-4a18-9fba-3e4721a3e534_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:26:43.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d20152f8-28f6-4a18-9fba-3e4721a3e534_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753424803285_127.0.0.1_3951
14:26:43.475 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d20152f8-28f6-4a18-9fba-3e4721a3e534_config-0] Notify connected event to listeners.
14:26:43.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d20152f8-28f6-4a18-9fba-3e4721a3e534_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:26:43.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d20152f8-28f6-4a18-9fba-3e4721a3e534_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002030c507b88
14:26:43.557 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:26:45.953 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:26:45.953 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:26:45.953 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:26:46.068 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:26:46.541 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:26:46.542 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:26:46.542 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:26:51.716 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:26:53.763 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e5b63412-095b-4184-9e82-f09f89a2914b
14:26:53.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5b63412-095b-4184-9e82-f09f89a2914b] RpcClient init label, labels = {module=naming, source=sdk}
14:26:53.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5b63412-095b-4184-9e82-f09f89a2914b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:26:53.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5b63412-095b-4184-9e82-f09f89a2914b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:26:53.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5b63412-095b-4184-9e82-f09f89a2914b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:26:53.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5b63412-095b-4184-9e82-f09f89a2914b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:26:53.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5b63412-095b-4184-9e82-f09f89a2914b] Success to connect to server [localhost:8848] on start up, connectionId = 1753424813773_127.0.0.1_3964
14:26:53.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5b63412-095b-4184-9e82-f09f89a2914b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:26:53.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5b63412-095b-4184-9e82-f09f89a2914b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002030c507b88
14:26:53.888 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5b63412-095b-4184-9e82-f09f89a2914b] Notify connected event to listeners.
14:26:53.925 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:26:53.945 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:26:54.033 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.412 seconds (JVM running for 13.259)
14:26:54.045 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:26:54.045 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:26:54.046 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:26:54.293 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:26:54.490 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5b63412-095b-4184-9e82-f09f89a2914b] Receive server push request, request = NotifySubscriberRequest, requestId = 105
14:26:54.506 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e5b63412-095b-4184-9e82-f09f89a2914b] Ack server push request, request = NotifySubscriberRequest, requestId = 105
14:27:14.644 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:27:14.644 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:30:03.576 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:30:03.576 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:30:03.908 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:30:03.908 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@45a94850[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:30:03.908 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753424813773_127.0.0.1_3964
14:30:03.911 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753424813773_127.0.0.1_3964]Ignore complete event,isRunning:false,isAbandon=false
14:30:03.913 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3f091580[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 51]
14:30:04.041 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:30:04.041 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:30:04.054 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:30:04.054 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:30:04.056 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:30:04.056 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:30:08.572 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:30:09.100 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 142a5a7d-b967-41e2-a54f-539a6d99f2f3_config-0
14:30:09.152 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
14:30:09.178 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
14:30:09.185 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
14:30:09.192 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
14:30:09.198 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
14:30:09.205 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
14:30:09.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142a5a7d-b967-41e2-a54f-539a6d99f2f3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:30:09.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142a5a7d-b967-41e2-a54f-539a6d99f2f3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002b1613bdaf0
14:30:09.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142a5a7d-b967-41e2-a54f-539a6d99f2f3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002b1613bdd10
14:30:09.208 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142a5a7d-b967-41e2-a54f-539a6d99f2f3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:30:09.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142a5a7d-b967-41e2-a54f-539a6d99f2f3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:30:09.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142a5a7d-b967-41e2-a54f-539a6d99f2f3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:30:09.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142a5a7d-b967-41e2-a54f-539a6d99f2f3_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753425009707_127.0.0.1_4239
14:30:09.902 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142a5a7d-b967-41e2-a54f-539a6d99f2f3_config-0] Notify connected event to listeners.
14:30:09.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142a5a7d-b967-41e2-a54f-539a6d99f2f3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:30:09.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142a5a7d-b967-41e2-a54f-539a6d99f2f3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002b1614f7cb0
14:30:09.966 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:30:12.298 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:30:12.298 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:30:12.298 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:30:12.410 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:30:12.889 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:30:12.889 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:30:12.889 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:30:20.824 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:30:23.210 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9640178c-98ea-4394-bd8b-93edbb6c3091
14:30:23.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9640178c-98ea-4394-bd8b-93edbb6c3091] RpcClient init label, labels = {module=naming, source=sdk}
14:30:23.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9640178c-98ea-4394-bd8b-93edbb6c3091] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:30:23.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9640178c-98ea-4394-bd8b-93edbb6c3091] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:30:23.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9640178c-98ea-4394-bd8b-93edbb6c3091] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:30:23.213 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9640178c-98ea-4394-bd8b-93edbb6c3091] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:30:23.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9640178c-98ea-4394-bd8b-93edbb6c3091] Success to connect to server [localhost:8848] on start up, connectionId = 1753425023220_127.0.0.1_4276
14:30:23.330 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9640178c-98ea-4394-bd8b-93edbb6c3091] Notify connected event to listeners.
14:30:23.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9640178c-98ea-4394-bd8b-93edbb6c3091] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:30:23.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9640178c-98ea-4394-bd8b-93edbb6c3091] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002b1614f7cb0
14:30:23.367 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:30:23.388 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:30:23.471 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.367 seconds (JVM running for 16.186)
14:30:23.482 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:30:23.482 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:30:23.482 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:30:23.860 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9640178c-98ea-4394-bd8b-93edbb6c3091] Receive server push request, request = NotifySubscriberRequest, requestId = 111
14:30:23.875 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9640178c-98ea-4394-bd8b-93edbb6c3091] Ack server push request, request = NotifySubscriberRequest, requestId = 111
14:30:23.974 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:30:33.418 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:30:33.418 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:31:11.488 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:31:11.488 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:31:11.826 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:31:11.826 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@24d985d3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:31:11.826 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753425023220_127.0.0.1_4276
14:31:11.826 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753425023220_127.0.0.1_4276]Ignore complete event,isRunning:false,isAbandon=false
14:31:11.826 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1b111f5f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 22]
14:31:11.969 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:31:11.971 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:31:11.976 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:31:11.976 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:31:11.977 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:31:11.977 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:31:16.450 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:31:16.983 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fbc077c4-fb19-472b-b1bf-0d29f74d81c0_config-0
14:31:17.036 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
14:31:17.065 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
14:31:17.072 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
14:31:17.079 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
14:31:17.085 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
14:31:17.092 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
14:31:17.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbc077c4-fb19-472b-b1bf-0d29f74d81c0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:31:17.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbc077c4-fb19-472b-b1bf-0d29f74d81c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002333339eaf8
14:31:17.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbc077c4-fb19-472b-b1bf-0d29f74d81c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002333339ed18
14:31:17.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbc077c4-fb19-472b-b1bf-0d29f74d81c0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:31:17.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbc077c4-fb19-472b-b1bf-0d29f74d81c0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:31:17.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbc077c4-fb19-472b-b1bf-0d29f74d81c0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:17.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbc077c4-fb19-472b-b1bf-0d29f74d81c0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753425077605_127.0.0.1_4386
14:31:17.807 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbc077c4-fb19-472b-b1bf-0d29f74d81c0_config-0] Notify connected event to listeners.
14:31:17.807 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbc077c4-fb19-472b-b1bf-0d29f74d81c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:17.807 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fbc077c4-fb19-472b-b1bf-0d29f74d81c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023333518ad8
14:31:17.888 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:31:20.469 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:31:20.470 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:31:20.470 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:31:20.585 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:31:21.068 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:31:21.069 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:31:21.070 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:31:26.818 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:31:30.603 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 64325b87-ab7e-462b-9a9d-5b3ee3b639b1
14:31:30.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64325b87-ab7e-462b-9a9d-5b3ee3b639b1] RpcClient init label, labels = {module=naming, source=sdk}
14:31:30.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64325b87-ab7e-462b-9a9d-5b3ee3b639b1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:31:30.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64325b87-ab7e-462b-9a9d-5b3ee3b639b1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:31:30.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64325b87-ab7e-462b-9a9d-5b3ee3b639b1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:31:30.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64325b87-ab7e-462b-9a9d-5b3ee3b639b1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:30.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64325b87-ab7e-462b-9a9d-5b3ee3b639b1] Success to connect to server [localhost:8848] on start up, connectionId = 1753425090615_127.0.0.1_4413
14:31:30.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64325b87-ab7e-462b-9a9d-5b3ee3b639b1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:30.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64325b87-ab7e-462b-9a9d-5b3ee3b639b1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023333518ad8
14:31:30.740 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64325b87-ab7e-462b-9a9d-5b3ee3b639b1] Notify connected event to listeners.
14:31:30.791 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:31:30.830 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:31:30.992 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.023 seconds (JVM running for 15.939)
14:31:31.008 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:31:31.008 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:31:31.008 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:31:31.115 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:31:31.279 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64325b87-ab7e-462b-9a9d-5b3ee3b639b1] Receive server push request, request = NotifySubscriberRequest, requestId = 121
14:31:31.299 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [64325b87-ab7e-462b-9a9d-5b3ee3b639b1] Ack server push request, request = NotifySubscriberRequest, requestId = 121
14:31:35.678 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:31:35.678 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:48:44.380 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:48:44.380 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:48:44.731 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:48:44.731 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@d49d7f1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:48:44.731 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753425090615_127.0.0.1_4413
14:48:44.736 [nacos-grpc-client-executor-219] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753425090615_127.0.0.1_4413]Ignore complete event,isRunning:false,isAbandon=false
14:48:44.736 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@49aac345[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 220]
14:48:44.902 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:48:44.905 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:48:44.906 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:48:44.906 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:48:44.906 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:48:44.906 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:48:47.674 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:48:48.225 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d849ce5d-3245-4f6d-961b-e25d06eaf34b_config-0
14:48:48.283 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 3 keys and 6 values 
14:48:48.314 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
14:48:48.321 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
14:48:48.329 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
14:48:48.336 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
14:48:48.345 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
14:48:48.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d849ce5d-3245-4f6d-961b-e25d06eaf34b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:48:48.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d849ce5d-3245-4f6d-961b-e25d06eaf34b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001bf013bdd70
14:48:48.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d849ce5d-3245-4f6d-961b-e25d06eaf34b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001bf013bdf90
14:48:48.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d849ce5d-3245-4f6d-961b-e25d06eaf34b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:48:48.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d849ce5d-3245-4f6d-961b-e25d06eaf34b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:48:48.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d849ce5d-3245-4f6d-961b-e25d06eaf34b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:48:49.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d849ce5d-3245-4f6d-961b-e25d06eaf34b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753426128843_127.0.0.1_5630
14:48:49.028 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d849ce5d-3245-4f6d-961b-e25d06eaf34b_config-0] Notify connected event to listeners.
14:48:49.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d849ce5d-3245-4f6d-961b-e25d06eaf34b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:48:49.029 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d849ce5d-3245-4f6d-961b-e25d06eaf34b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001bf014f7b88
14:48:49.102 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:48:51.536 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:48:51.536 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:48:51.537 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:48:51.641 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:48:52.401 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:48:52.403 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:48:52.403 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:48:57.473 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:48:59.936 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c0e43e18-12d7-4541-bbf5-42e54491c46b
14:48:59.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e43e18-12d7-4541-bbf5-42e54491c46b] RpcClient init label, labels = {module=naming, source=sdk}
14:48:59.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e43e18-12d7-4541-bbf5-42e54491c46b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:48:59.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e43e18-12d7-4541-bbf5-42e54491c46b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:48:59.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e43e18-12d7-4541-bbf5-42e54491c46b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:48:59.942 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e43e18-12d7-4541-bbf5-42e54491c46b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:49:00.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e43e18-12d7-4541-bbf5-42e54491c46b] Success to connect to server [localhost:8848] on start up, connectionId = 1753426139954_127.0.0.1_5643
14:49:00.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e43e18-12d7-4541-bbf5-42e54491c46b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:49:00.072 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e43e18-12d7-4541-bbf5-42e54491c46b] Notify connected event to listeners.
14:49:00.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e43e18-12d7-4541-bbf5-42e54491c46b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001bf014f7b88
14:49:00.132 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:49:00.172 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:49:00.398 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.207 seconds (JVM running for 14.239)
14:49:00.416 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:49:00.417 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:49:00.418 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:49:00.635 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e43e18-12d7-4541-bbf5-42e54491c46b] Receive server push request, request = NotifySubscriberRequest, requestId = 126
14:49:00.653 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0e43e18-12d7-4541-bbf5-42e54491c46b] Ack server push request, request = NotifySubscriberRequest, requestId = 126
14:49:00.773 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:49:21.849 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:49:21.864 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:37:40.224 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:37:40.230 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:37:40.553 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:37:40.553 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@63840f95[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:37:40.553 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753426139954_127.0.0.1_5643
15:37:40.553 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@614ab12e[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 544]
15:37:40.716 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:37:40.718 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:37:40.725 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:37:40.725 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:37:40.725 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:37:40.725 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:37:45.992 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:37:46.657 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c86d47f2-ea75-4a7b-8e1e-d57d671101ee_config-0
15:37:46.721 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
15:37:46.761 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 4 keys and 9 values 
15:37:46.777 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
15:37:46.777 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
15:37:46.793 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 1 ms to scan 1 urls, producing 1 keys and 7 values 
15:37:46.809 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
15:37:46.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c86d47f2-ea75-4a7b-8e1e-d57d671101ee_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:37:46.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c86d47f2-ea75-4a7b-8e1e-d57d671101ee_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000014d2a39ed38
15:37:46.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c86d47f2-ea75-4a7b-8e1e-d57d671101ee_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000014d2a39ef58
15:37:46.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c86d47f2-ea75-4a7b-8e1e-d57d671101ee_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:37:46.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c86d47f2-ea75-4a7b-8e1e-d57d671101ee_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:37:46.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c86d47f2-ea75-4a7b-8e1e-d57d671101ee_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:37:48.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c86d47f2-ea75-4a7b-8e1e-d57d671101ee_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753429067768_127.0.0.1_9650
15:37:48.011 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c86d47f2-ea75-4a7b-8e1e-d57d671101ee_config-0] Notify connected event to listeners.
15:37:48.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c86d47f2-ea75-4a7b-8e1e-d57d671101ee_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:37:48.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c86d47f2-ea75-4a7b-8e1e-d57d671101ee_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000014d2a518ad8
15:37:48.148 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:37:51.835 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:37:51.835 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:37:51.835 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:37:52.005 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:37:52.937 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:37:52.937 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:37:52.937 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:38:04.001 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:38:08.194 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5c5631f8-077d-40b1-bfe0-f040400c6821
15:38:08.195 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c5631f8-077d-40b1-bfe0-f040400c6821] RpcClient init label, labels = {module=naming, source=sdk}
15:38:08.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c5631f8-077d-40b1-bfe0-f040400c6821] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:38:08.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c5631f8-077d-40b1-bfe0-f040400c6821] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:38:08.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c5631f8-077d-40b1-bfe0-f040400c6821] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:38:08.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c5631f8-077d-40b1-bfe0-f040400c6821] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:38:08.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c5631f8-077d-40b1-bfe0-f040400c6821] Success to connect to server [localhost:8848] on start up, connectionId = 1753429088207_127.0.0.1_9707
15:38:08.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c5631f8-077d-40b1-bfe0-f040400c6821] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:38:08.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c5631f8-077d-40b1-bfe0-f040400c6821] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000014d2a518ad8
15:38:08.321 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c5631f8-077d-40b1-bfe0-f040400c6821] Notify connected event to listeners.
15:38:08.370 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:38:08.418 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:38:08.641 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.186 seconds (JVM running for 24.235)
15:38:08.678 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:38:08.679 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:38:08.680 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:38:08.804 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:38:08.920 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c5631f8-077d-40b1-bfe0-f040400c6821] Receive server push request, request = NotifySubscriberRequest, requestId = 134
15:38:08.920 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c5631f8-077d-40b1-bfe0-f040400c6821] Ack server push request, request = NotifySubscriberRequest, requestId = 134
15:38:13.685 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:38:13.685 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:02:06.772 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:02:06.779 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:02:07.138 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:02:07.140 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@66c907de[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:02:07.140 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753429088207_127.0.0.1_9707
19:02:07.142 [nacos-grpc-client-executor-2452] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753429088207_127.0.0.1_9707]Ignore complete event,isRunning:false,isAbandon=false
19:02:07.146 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7002fb4b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2453]
19:02:07.332 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:02:07.338 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:02:07.354 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:02:07.354 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:02:07.356 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:02:07.358 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
