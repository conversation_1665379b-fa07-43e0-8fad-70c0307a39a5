10:04:04.882 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:04:05.756 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 94b3d10e-f68f-404c-b67d-e56ada046387_config-0
10:04:05.843 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 33 ms to scan 1 urls, producing 3 keys and 6 values 
10:04:05.877 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 4 keys and 9 values 
10:04:05.895 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 3 keys and 10 values 
10:04:05.900 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 1 keys and 5 values 
10:04:05.913 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 7 values 
10:04:05.929 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
10:04:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94b3d10e-f68f-404c-b67d-e56ada046387_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:04:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94b3d10e-f68f-404c-b67d-e56ada046387_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000127e33b38c8
10:04:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94b3d10e-f68f-404c-b67d-e56ada046387_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000127e33b3ae8
10:04:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94b3d10e-f68f-404c-b67d-e56ada046387_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:04:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94b3d10e-f68f-404c-b67d-e56ada046387_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:04:05.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94b3d10e-f68f-404c-b67d-e56ada046387_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:07.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94b3d10e-f68f-404c-b67d-e56ada046387_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752372247326_127.0.0.1_8167
10:04:07.601 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94b3d10e-f68f-404c-b67d-e56ada046387_config-0] Notify connected event to listeners.
10:04:07.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94b3d10e-f68f-404c-b67d-e56ada046387_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:07.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94b3d10e-f68f-404c-b67d-e56ada046387_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000127e34ef720
10:04:08.034 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:04:10.539 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
10:04:10.540 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:04:10.540 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:04:10.686 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:04:12.232 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:04:14.823 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 59beb568-eefd-4ec2-bedc-66362ea7c864
10:04:14.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59beb568-eefd-4ec2-bedc-66362ea7c864] RpcClient init label, labels = {module=naming, source=sdk}
10:04:14.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59beb568-eefd-4ec2-bedc-66362ea7c864] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:04:14.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59beb568-eefd-4ec2-bedc-66362ea7c864] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:04:14.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59beb568-eefd-4ec2-bedc-66362ea7c864] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:04:14.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59beb568-eefd-4ec2-bedc-66362ea7c864] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:14.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59beb568-eefd-4ec2-bedc-66362ea7c864] Success to connect to server [localhost:8848] on start up, connectionId = 1752372254838_127.0.0.1_8183
10:04:14.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59beb568-eefd-4ec2-bedc-66362ea7c864] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:14.958 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59beb568-eefd-4ec2-bedc-66362ea7c864] Notify connected event to listeners.
10:04:14.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59beb568-eefd-4ec2-bedc-66362ea7c864] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000127e34ef720
10:04:15.007 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
10:04:15.042 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
10:04:15.169 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 11.06 seconds (JVM running for 16.323)
10:04:15.182 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
10:04:15.183 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
10:04:15.186 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
10:04:15.566 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59beb568-eefd-4ec2-bedc-66362ea7c864] Receive server push request, request = NotifySubscriberRequest, requestId = 4
10:04:15.582 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59beb568-eefd-4ec2-bedc-66362ea7c864] Ack server push request, request = NotifySubscriberRequest, requestId = 4
19:20:01.578 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:20:01.589 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:20:01.923 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:20:01.923 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@757ee8e6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:20:01.923 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752372254838_127.0.0.1_8183
19:20:01.928 [nacos-grpc-client-executor-6677] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752372254838_127.0.0.1_8183]Ignore complete event,isRunning:false,isAbandon=false
19:20:01.930 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2c8a19d4[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6678]
19:21:56.341 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:21:58.112 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 47ad8896-1383-4c21-8525-ec45696ebf3b_config-0
19:21:58.309 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 94 ms to scan 1 urls, producing 3 keys and 6 values 
19:21:58.417 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 59 ms to scan 1 urls, producing 4 keys and 9 values 
19:21:58.445 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 10 values 
19:21:58.473 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 1 keys and 5 values 
19:21:58.505 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 1 keys and 7 values 
19:21:58.534 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 2 keys and 8 values 
19:21:58.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:21:58.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000025c22396440
19:21:58.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000025c22396660
19:21:58.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:21:58.550 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:21:58.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:00.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:00.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:00.812 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
19:22:00.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:22:00.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025c224e61b0
19:22:01.107 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:01.338 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:01.917 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:02.385 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:03.168 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:03.175 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:22:03.816 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:04.563 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:05.399 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:07.923 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:09.308 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:10.775 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:12.158 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:12.317 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
19:22:12.320 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:22:12.321 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:22:13.020 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:22:14.121 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:15.680 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:17.366 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:18.337 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:22:19.398 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:21.288 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:22:23.580 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Success to connect a server [localhost:8848], connectionId = 1752405743394_127.0.0.1_2830
19:22:23.581 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47ad8896-1383-4c21-8525-ec45696ebf3b_config-0] Notify connected event to listeners.
19:22:26.352 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1178f7ab-18ab-41ae-a64b-2443d65a5e52
19:22:26.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1178f7ab-18ab-41ae-a64b-2443d65a5e52] RpcClient init label, labels = {module=naming, source=sdk}
19:22:26.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1178f7ab-18ab-41ae-a64b-2443d65a5e52] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:22:26.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1178f7ab-18ab-41ae-a64b-2443d65a5e52] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:22:26.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1178f7ab-18ab-41ae-a64b-2443d65a5e52] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:22:26.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1178f7ab-18ab-41ae-a64b-2443d65a5e52] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:26.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1178f7ab-18ab-41ae-a64b-2443d65a5e52] Success to connect to server [localhost:8848] on start up, connectionId = 1752405746376_127.0.0.1_2844
19:22:26.514 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1178f7ab-18ab-41ae-a64b-2443d65a5e52] Notify connected event to listeners.
19:22:26.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1178f7ab-18ab-41ae-a64b-2443d65a5e52] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:22:26.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1178f7ab-18ab-41ae-a64b-2443d65a5e52] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025c224e61b0
19:22:26.689 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
19:22:26.775 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
19:22:27.179 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 32.504 seconds (JVM running for 35.358)
19:22:27.209 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
19:22:27.212 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
19:22:27.215 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
19:22:27.476 [RMI TCP Connection(27)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:22:27.807 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1178f7ab-18ab-41ae-a64b-2443d65a5e52] Receive server push request, request = NotifySubscriberRequest, requestId = 1
19:22:27.808 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1178f7ab-18ab-41ae-a64b-2443d65a5e52] Ack server push request, request = NotifySubscriberRequest, requestId = 1
19:34:22.257 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:34:22.261 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:34:22.604 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:34:22.605 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@54e12b42[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:34:22.605 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752405746376_127.0.0.1_2844
19:34:22.606 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@96feb90[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 158]
