package com.heju.system.file.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.validate.V_A;
import com.heju.common.core.web.validate.V_E;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.system.file.domain.dto.SysFileRecordDto;
import com.heju.system.file.domain.query.SysFileRecordQuery;
import com.heju.system.file.service.ISysFileRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 文件操作记录管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/record")
public class SysFileRecordController extends BaseController<SysFileRecordQuery, SysFileRecordDto, ISysFileRecordService> {

    @Autowired ISysFileRecordService sysFileRecordService;

    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "文件操作记录" ;
    }

    /**
     * 查询文件操作记录列表
     */
    @Override
    @GetMapping("/list")
    //    @RequiresPermissions(Auth.SYS_FILE_RECORD_LIST)
    public AjaxResult list(SysFileRecordQuery fileRecord) {
        return super.list(fileRecord);
    }


    /**
     * 查询文件操作记录详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.SYS_FILE_RECORD_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }

    /**
     * 文件操作记录新增
     */
    @Override
    @PostMapping
    @RequiresPermissions(Auth.SYS_FILE_RECORD_ADD)
    @Log(title = "文件操作记录管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated({V_A.class}) @RequestBody SysFileRecordDto fileRecord) {
        return super.add(fileRecord);
    }

    /**
     * 查看文件历史操作记录
     */
    @GetMapping(value = "/file/{fileId}")
    public AjaxResult fileRecord(@PathVariable Serializable fileId) {
        startPage();
        List<SysFileRecordDto> list = sysFileRecordService.selectByFileId(fileId);
        return getDataTable(list);
    }

    /**
     * 文件操作记录修改
     */
    @Override
    @PutMapping
    @RequiresPermissions(Auth.SYS_FILE_RECORD_EDIT)
    @Log(title = "文件操作记录管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated({V_E.class}) @RequestBody SysFileRecordDto fileRecord) {
        return super.edit(fileRecord);
    }

    /**
     * 文件操作记录修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.SYS_FILE_RECORD_EDIT, Auth.SYS_FILE_RECORD_ES}, logical = Logical.OR)
    @Log(title = "文件操作记录管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody SysFileRecordDto fileRecord) {
        return super.editStatus(fileRecord);
    }

    /**
     * 文件操作记录批量删除
     */
    @Override
    @DeleteMapping("/batch/{idList}")
    @RequiresPermissions(Auth.SYS_FILE_RECORD_DEL)
    @Log(title = "文件操作记录管理", businessType = BusinessType.DELETE)
    public AjaxResult batchRemove(@PathVariable List<Long> idList) {
        return super.batchRemove(idList);
    }

    /**
     * 获取文件操作记录选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

}
