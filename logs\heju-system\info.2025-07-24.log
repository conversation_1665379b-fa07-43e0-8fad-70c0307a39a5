09:12:07.891 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:12:09.087 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 595da1ed-e524-4ec3-9455-840edd766cf4_config-0
09:12:09.204 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 52 ms to scan 1 urls, producing 3 keys and 6 values 
09:12:09.254 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:12:09.263 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:12:09.276 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:12:09.286 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:12:09.295 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
09:12:09.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595da1ed-e524-4ec3-9455-840edd766cf4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:12:09.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595da1ed-e524-4ec3-9455-840edd766cf4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001b90b3b7b00
09:12:09.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595da1ed-e524-4ec3-9455-840edd766cf4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001b90b3b7d20
09:12:09.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595da1ed-e524-4ec3-9455-840edd766cf4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:12:09.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595da1ed-e524-4ec3-9455-840edd766cf4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:12:09.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595da1ed-e524-4ec3-9455-840edd766cf4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:10.833 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595da1ed-e524-4ec3-9455-840edd766cf4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753319530454_127.0.0.1_12497
09:12:10.833 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595da1ed-e524-4ec3-9455-840edd766cf4_config-0] Notify connected event to listeners.
09:12:10.833 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595da1ed-e524-4ec3-9455-840edd766cf4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:10.834 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595da1ed-e524-4ec3-9455-840edd766cf4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001b90b4f1450
09:12:11.080 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:12:17.915 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:12:17.916 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:12:17.917 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:12:18.263 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:12:20.011 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:12:20.013 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:12:20.014 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:12:31.474 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:12:35.249 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 67d234f1-7a0b-402d-ae85-30c08e5e17d6
09:12:35.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] RpcClient init label, labels = {module=naming, source=sdk}
09:12:35.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:12:35.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:12:35.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:12:35.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:35.390 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] Success to connect to server [localhost:8848] on start up, connectionId = 1753319555264_127.0.0.1_12730
09:12:35.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:35.391 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] Notify connected event to listeners.
09:12:35.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001b90b4f1450
09:12:35.470 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:12:35.517 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:12:35.682 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 28.623 seconds (JVM running for 30.831)
09:12:35.700 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:12:35.701 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:12:35.701 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:12:36.006 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:12:36.023 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:16:16.423 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:16:20.122 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:16:20.123 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:16:21.287 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:16:21.288 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:16:22.111 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:16:22.111 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
10:37:18.402 [nacos-grpc-client-executor-1029] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] Receive server push request, request = NotifySubscriberRequest, requestId = 15
10:37:18.404 [nacos-grpc-client-executor-1029] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [67d234f1-7a0b-402d-ae85-30c08e5e17d6] Ack server push request, request = NotifySubscriberRequest, requestId = 15
11:49:17.441 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:49:17.447 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:49:17.783 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:49:17.783 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5646a393[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:49:17.783 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753319555264_127.0.0.1_12730
11:49:17.785 [nacos-grpc-client-executor-1915] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753319555264_127.0.0.1_12730]Ignore complete event,isRunning:false,isAbandon=false
11:49:17.795 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@46874d8a[Running, pool size = 9, active threads = 0, queued tasks = 0, completed tasks = 1916]
11:49:17.992 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:49:17.999 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
11:49:18.009 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
11:49:18.010 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:49:18.011 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:49:18.011 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:49:18.014 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:49:18.014 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:49:25.537 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:49:26.313 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d5360608-32b6-45ff-b3a6-2bb72e89ca6a_config-0
11:49:26.383 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
11:49:26.415 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 2 ms to scan 1 urls, producing 4 keys and 9 values 
11:49:26.428 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:49:26.446 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
11:49:26.457 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
11:49:26.463 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
11:49:26.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5360608-32b6-45ff-b3a6-2bb72e89ca6a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:49:26.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5360608-32b6-45ff-b3a6-2bb72e89ca6a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001c11c39f1c0
11:49:26.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5360608-32b6-45ff-b3a6-2bb72e89ca6a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001c11c39f3e0
11:49:26.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5360608-32b6-45ff-b3a6-2bb72e89ca6a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:49:26.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5360608-32b6-45ff-b3a6-2bb72e89ca6a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:49:26.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5360608-32b6-45ff-b3a6-2bb72e89ca6a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:49:27.519 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5360608-32b6-45ff-b3a6-2bb72e89ca6a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753328967297_127.0.0.1_4695
11:49:27.519 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5360608-32b6-45ff-b3a6-2bb72e89ca6a_config-0] Notify connected event to listeners.
11:49:27.519 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5360608-32b6-45ff-b3a6-2bb72e89ca6a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:49:27.523 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5360608-32b6-45ff-b3a6-2bb72e89ca6a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001c11c518d48
11:49:27.711 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:49:31.805 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:49:31.806 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:49:31.806 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:49:31.988 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:49:32.843 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:49:32.845 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:49:32.846 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:49:41.247 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:49:44.551 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2a106ea8-47f5-4f34-8447-9bda7dd30f72
11:49:44.551 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a106ea8-47f5-4f34-8447-9bda7dd30f72] RpcClient init label, labels = {module=naming, source=sdk}
11:49:44.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a106ea8-47f5-4f34-8447-9bda7dd30f72] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:49:44.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a106ea8-47f5-4f34-8447-9bda7dd30f72] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:49:44.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a106ea8-47f5-4f34-8447-9bda7dd30f72] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:49:44.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a106ea8-47f5-4f34-8447-9bda7dd30f72] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:49:44.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a106ea8-47f5-4f34-8447-9bda7dd30f72] Success to connect to server [localhost:8848] on start up, connectionId = 1753328984566_127.0.0.1_4726
11:49:44.693 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a106ea8-47f5-4f34-8447-9bda7dd30f72] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:49:44.694 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a106ea8-47f5-4f34-8447-9bda7dd30f72] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001c11c518d48
11:49:44.694 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a106ea8-47f5-4f34-8447-9bda7dd30f72] Notify connected event to listeners.
11:49:44.757 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:49:44.798 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:49:44.948 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.055 seconds (JVM running for 21.159)
11:49:44.983 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:49:44.984 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:49:44.985 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:49:45.252 [RMI TCP Connection(10)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:49:45.336 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a106ea8-47f5-4f34-8447-9bda7dd30f72] Receive server push request, request = NotifySubscriberRequest, requestId = 25
11:49:45.353 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a106ea8-47f5-4f34-8447-9bda7dd30f72] Ack server push request, request = NotifySubscriberRequest, requestId = 25
11:51:26.958 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:51:26.963 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:51:27.288 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:51:27.288 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4b5f58ed[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:51:27.290 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753328984566_127.0.0.1_4726
11:51:27.294 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753328984566_127.0.0.1_4726]Ignore complete event,isRunning:false,isAbandon=false
11:51:27.299 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1952f7d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 31]
11:51:27.481 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:51:27.486 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:51:27.503 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:51:27.504 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:51:32.638 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:51:33.507 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 24c9111b-d458-4dc9-9445-7e763b08194f_config-0
11:51:33.582 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
11:51:33.626 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
11:51:33.637 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
11:51:33.647 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
11:51:33.658 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
11:51:33.671 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
11:51:33.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c9111b-d458-4dc9-9445-7e763b08194f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:51:33.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c9111b-d458-4dc9-9445-7e763b08194f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000216b839e480
11:51:33.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c9111b-d458-4dc9-9445-7e763b08194f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000216b839e6a0
11:51:33.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c9111b-d458-4dc9-9445-7e763b08194f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:51:33.677 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c9111b-d458-4dc9-9445-7e763b08194f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:51:33.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c9111b-d458-4dc9-9445-7e763b08194f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:51:34.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c9111b-d458-4dc9-9445-7e763b08194f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753329094435_127.0.0.1_4985
11:51:34.674 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c9111b-d458-4dc9-9445-7e763b08194f_config-0] Notify connected event to listeners.
11:51:34.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c9111b-d458-4dc9-9445-7e763b08194f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:51:34.676 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c9111b-d458-4dc9-9445-7e763b08194f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000216b8518228
11:51:34.878 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:51:39.032 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:51:39.032 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:51:39.032 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:51:39.242 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:51:39.924 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:51:39.927 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:51:39.928 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:51:48.942 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:51:52.349 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 55251aaa-43b3-4640-9fdb-7b31e53d369f
11:51:52.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55251aaa-43b3-4640-9fdb-7b31e53d369f] RpcClient init label, labels = {module=naming, source=sdk}
11:51:52.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55251aaa-43b3-4640-9fdb-7b31e53d369f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:51:52.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55251aaa-43b3-4640-9fdb-7b31e53d369f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:51:52.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55251aaa-43b3-4640-9fdb-7b31e53d369f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:51:52.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55251aaa-43b3-4640-9fdb-7b31e53d369f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:51:52.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55251aaa-43b3-4640-9fdb-7b31e53d369f] Success to connect to server [localhost:8848] on start up, connectionId = 1753329112362_127.0.0.1_5085
11:51:52.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55251aaa-43b3-4640-9fdb-7b31e53d369f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:51:52.489 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55251aaa-43b3-4640-9fdb-7b31e53d369f] Notify connected event to listeners.
11:51:52.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55251aaa-43b3-4640-9fdb-7b31e53d369f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000216b8518228
11:51:52.542 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:51:52.573 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:51:52.714 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.759 seconds (JVM running for 21.888)
11:51:52.731 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:51:52.732 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:51:52.733 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:51:53.082 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55251aaa-43b3-4640-9fdb-7b31e53d369f] Receive server push request, request = NotifySubscriberRequest, requestId = 33
11:51:53.105 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55251aaa-43b3-4640-9fdb-7b31e53d369f] Ack server push request, request = NotifySubscriberRequest, requestId = 33
11:51:53.168 [RMI TCP Connection(17)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:52:25.450 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:52:25.450 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:28:55.367 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:28:55.370 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:28:55.700 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:28:55.700 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@214f80fa[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:28:55.700 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753329112362_127.0.0.1_5085
13:28:55.700 [nacos-grpc-client-executor-1174] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753329112362_127.0.0.1_5085]Ignore complete event,isRunning:false,isAbandon=false
13:28:55.700 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5f0f9e4e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1175]
13:28:55.841 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:28:55.841 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:28:55.850 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:28:55.851 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:28:55.852 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:28:55.852 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:29:23.724 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:29:24.340 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 57d86f63-ead6-44da-bba6-148ab83c9e88_config-0
13:29:24.403 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 
13:29:24.432 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
13:29:24.439 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
13:29:24.450 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
13:29:24.458 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
13:29:24.468 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
13:29:24.471 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57d86f63-ead6-44da-bba6-148ab83c9e88_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:29:24.471 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57d86f63-ead6-44da-bba6-148ab83c9e88_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001c6013b6af8
13:29:24.471 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57d86f63-ead6-44da-bba6-148ab83c9e88_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001c6013b6d18
13:29:24.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57d86f63-ead6-44da-bba6-148ab83c9e88_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:29:24.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57d86f63-ead6-44da-bba6-148ab83c9e88_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:29:24.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57d86f63-ead6-44da-bba6-148ab83c9e88_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:29:25.347 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57d86f63-ead6-44da-bba6-148ab83c9e88_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753334965135_127.0.0.1_13864
13:29:25.348 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57d86f63-ead6-44da-bba6-148ab83c9e88_config-0] Notify connected event to listeners.
13:29:25.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57d86f63-ead6-44da-bba6-148ab83c9e88_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:29:25.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57d86f63-ead6-44da-bba6-148ab83c9e88_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c6014f0ad8
13:29:25.486 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:29:28.383 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:29:28.384 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:29:28.388 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:29:28.657 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:29:29.789 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:29:29.791 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:29:29.791 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:29:35.883 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:29:38.500 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 986d47f8-2880-46c1-8ebb-20f965e6448e
13:29:38.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [986d47f8-2880-46c1-8ebb-20f965e6448e] RpcClient init label, labels = {module=naming, source=sdk}
13:29:38.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [986d47f8-2880-46c1-8ebb-20f965e6448e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:29:38.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [986d47f8-2880-46c1-8ebb-20f965e6448e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:29:38.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [986d47f8-2880-46c1-8ebb-20f965e6448e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:29:38.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [986d47f8-2880-46c1-8ebb-20f965e6448e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:29:38.630 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [986d47f8-2880-46c1-8ebb-20f965e6448e] Success to connect to server [localhost:8848] on start up, connectionId = 1753334978504_127.0.0.1_13882
13:29:38.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [986d47f8-2880-46c1-8ebb-20f965e6448e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:29:38.632 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [986d47f8-2880-46c1-8ebb-20f965e6448e] Notify connected event to listeners.
13:29:38.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [986d47f8-2880-46c1-8ebb-20f965e6448e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c6014f0ad8
13:29:38.696 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:29:38.716 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:29:38.816 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.544 seconds (JVM running for 17.531)
13:29:38.835 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:29:38.835 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:29:38.836 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:29:39.245 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [986d47f8-2880-46c1-8ebb-20f965e6448e] Receive server push request, request = NotifySubscriberRequest, requestId = 43
13:29:39.249 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [986d47f8-2880-46c1-8ebb-20f965e6448e] Ack server push request, request = NotifySubscriberRequest, requestId = 43
13:29:42.050 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:31:39.724 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:31:39.727 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:31:40.033 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:31:40.033 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7fe57bbf[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:31:40.033 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753334978504_127.0.0.1_13882
13:31:40.033 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753334978504_127.0.0.1_13882]Ignore complete event,isRunning:false,isAbandon=false
13:31:40.038 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@12f2018a[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 35]
13:31:40.167 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:31:40.167 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:31:40.167 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:31:40.167 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:31:44.811 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:31:45.438 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c13021c2-e567-497b-af22-bec6783a2db7_config-0
13:31:45.483 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 6 values 
13:31:45.516 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
13:31:45.523 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
13:31:45.530 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
13:31:45.537 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
13:31:45.545 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
13:31:45.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c13021c2-e567-497b-af22-bec6783a2db7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:31:45.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c13021c2-e567-497b-af22-bec6783a2db7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f1de39e480
13:31:45.548 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c13021c2-e567-497b-af22-bec6783a2db7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001f1de39e6a0
13:31:45.548 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c13021c2-e567-497b-af22-bec6783a2db7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:31:45.548 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c13021c2-e567-497b-af22-bec6783a2db7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:31:45.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c13021c2-e567-497b-af22-bec6783a2db7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:31:46.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c13021c2-e567-497b-af22-bec6783a2db7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753335106076_127.0.0.1_14216
13:31:46.263 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c13021c2-e567-497b-af22-bec6783a2db7_config-0] Notify connected event to listeners.
13:31:46.263 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c13021c2-e567-497b-af22-bec6783a2db7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:31:46.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c13021c2-e567-497b-af22-bec6783a2db7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f1de518228
13:31:46.345 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:31:48.806 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:31:48.807 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:31:48.807 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:31:48.927 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:31:49.499 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:31:49.500 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:31:49.501 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:31:55.367 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:31:58.177 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2a5b03a0-7c30-481c-a73d-ec5a732954b5
13:31:58.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a5b03a0-7c30-481c-a73d-ec5a732954b5] RpcClient init label, labels = {module=naming, source=sdk}
13:31:58.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a5b03a0-7c30-481c-a73d-ec5a732954b5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:31:58.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a5b03a0-7c30-481c-a73d-ec5a732954b5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:31:58.180 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a5b03a0-7c30-481c-a73d-ec5a732954b5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:31:58.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a5b03a0-7c30-481c-a73d-ec5a732954b5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:31:58.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a5b03a0-7c30-481c-a73d-ec5a732954b5] Success to connect to server [localhost:8848] on start up, connectionId = 1753335118190_127.0.0.1_14236
13:31:58.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a5b03a0-7c30-481c-a73d-ec5a732954b5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:31:58.299 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a5b03a0-7c30-481c-a73d-ec5a732954b5] Notify connected event to listeners.
13:31:58.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a5b03a0-7c30-481c-a73d-ec5a732954b5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f1de518228
13:31:58.342 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:31:58.368 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:31:58.486 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 14.269 seconds (JVM running for 15.368)
13:31:58.502 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:31:58.502 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:31:58.503 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:31:58.804 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:31:58.841 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a5b03a0-7c30-481c-a73d-ec5a732954b5] Receive server push request, request = NotifySubscriberRequest, requestId = 51
13:31:58.855 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a5b03a0-7c30-481c-a73d-ec5a732954b5] Ack server push request, request = NotifySubscriberRequest, requestId = 51
13:32:59.033 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:32:59.033 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:37:01.271 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:37:01.275 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:37:01.602 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:37:01.602 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@571735d4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:37:01.602 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753335118190_127.0.0.1_14236
13:37:01.603 [nacos-grpc-client-executor-74] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753335118190_127.0.0.1_14236]Ignore complete event,isRunning:false,isAbandon=false
13:37:01.603 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5b0e12fd[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 75]
13:37:01.767 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:37:01.770 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:37:01.778 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:37:01.779 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:37:01.780 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:37:01.780 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:37:06.133 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:37:06.698 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 04e99ec7-2b83-464f-9233-bcc718f3ef33_config-0
13:37:06.749 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 6 values 
13:37:06.779 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
13:37:06.785 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
13:37:06.791 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
13:37:06.796 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
13:37:06.805 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
13:37:06.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04e99ec7-2b83-464f-9233-bcc718f3ef33_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:37:06.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04e99ec7-2b83-464f-9233-bcc718f3ef33_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000181813cdd00
13:37:06.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04e99ec7-2b83-464f-9233-bcc718f3ef33_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000181813cdf20
13:37:06.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04e99ec7-2b83-464f-9233-bcc718f3ef33_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:37:06.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04e99ec7-2b83-464f-9233-bcc718f3ef33_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:37:06.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04e99ec7-2b83-464f-9233-bcc718f3ef33_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:37:07.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04e99ec7-2b83-464f-9233-bcc718f3ef33_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753335427334_127.0.0.1_14785
13:37:07.518 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04e99ec7-2b83-464f-9233-bcc718f3ef33_config-0] Notify connected event to listeners.
13:37:07.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04e99ec7-2b83-464f-9233-bcc718f3ef33_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:37:07.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04e99ec7-2b83-464f-9233-bcc718f3ef33_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018181507b88
13:37:07.600 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:37:10.070 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:37:10.071 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:37:10.071 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:37:10.192 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:37:10.899 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:37:10.899 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:37:10.900 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:37:16.027 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:37:18.163 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e2d983c6-9b9e-4233-869e-0abfc5a0f19f
13:37:18.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2d983c6-9b9e-4233-869e-0abfc5a0f19f] RpcClient init label, labels = {module=naming, source=sdk}
13:37:18.164 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2d983c6-9b9e-4233-869e-0abfc5a0f19f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:37:18.164 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2d983c6-9b9e-4233-869e-0abfc5a0f19f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:37:18.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2d983c6-9b9e-4233-869e-0abfc5a0f19f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:37:18.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2d983c6-9b9e-4233-869e-0abfc5a0f19f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:37:18.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2d983c6-9b9e-4233-869e-0abfc5a0f19f] Success to connect to server [localhost:8848] on start up, connectionId = 1753335438172_127.0.0.1_14803
13:37:18.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2d983c6-9b9e-4233-869e-0abfc5a0f19f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:37:18.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2d983c6-9b9e-4233-869e-0abfc5a0f19f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018181507b88
13:37:18.290 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2d983c6-9b9e-4233-869e-0abfc5a0f19f] Notify connected event to listeners.
13:37:18.330 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:37:18.350 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:37:18.441 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.815 seconds (JVM running for 13.701)
13:37:18.452 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:37:18.452 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:37:18.453 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:37:18.737 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:37:18.899 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2d983c6-9b9e-4233-869e-0abfc5a0f19f] Receive server push request, request = NotifySubscriberRequest, requestId = 60
13:37:18.916 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2d983c6-9b9e-4233-869e-0abfc5a0f19f] Ack server push request, request = NotifySubscriberRequest, requestId = 60
13:37:27.694 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:37:27.695 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:39:52.439 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04e99ec7-2b83-464f-9233-bcc718f3ef33_config-0] Receive server push request, request = ConfigChangeNotifyRequest, requestId = 62
13:39:52.440 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [04e99ec7-2b83-464f-9233-bcc718f3ef33_config-0] Ack server push request, request = ConfigChangeNotifyRequest, requestId = 62
13:40:16.956 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:40:16.964 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:40:17.282 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:40:17.283 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2a56672[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:40:17.283 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753335438172_127.0.0.1_14803
13:40:17.285 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753335438172_127.0.0.1_14803]Ignore complete event,isRunning:false,isAbandon=false
13:40:17.286 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4757ec84[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 48]
13:40:17.425 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:40:17.427 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:40:17.431 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:40:17.431 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:40:17.431 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:40:17.431 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:40:20.027 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:40:20.580 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f589b74b-7d20-449a-b9fd-6caca5847798_config-0
13:40:20.629 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
13:40:20.656 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
13:40:20.662 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
13:40:20.669 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
13:40:20.675 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
13:40:20.682 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
13:40:20.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f589b74b-7d20-449a-b9fd-6caca5847798_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:40:20.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f589b74b-7d20-449a-b9fd-6caca5847798_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000198663bdd70
13:40:20.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f589b74b-7d20-449a-b9fd-6caca5847798_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000198663bdf90
13:40:20.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f589b74b-7d20-449a-b9fd-6caca5847798_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:40:20.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f589b74b-7d20-449a-b9fd-6caca5847798_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:40:20.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f589b74b-7d20-449a-b9fd-6caca5847798_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:40:21.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f589b74b-7d20-449a-b9fd-6caca5847798_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753335621168_127.0.0.1_1199
13:40:21.351 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f589b74b-7d20-449a-b9fd-6caca5847798_config-0] Notify connected event to listeners.
13:40:21.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f589b74b-7d20-449a-b9fd-6caca5847798_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:40:21.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f589b74b-7d20-449a-b9fd-6caca5847798_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000198664f7cb0
13:40:21.430 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:40:23.879 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:40:23.880 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:40:23.880 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:40:23.996 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:40:24.501 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:40:24.502 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:40:24.502 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:40:29.585 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:40:31.720 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d1dd9c1d-f2a5-4412-94f7-b1609dc5bd1c
13:40:31.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1dd9c1d-f2a5-4412-94f7-b1609dc5bd1c] RpcClient init label, labels = {module=naming, source=sdk}
13:40:31.722 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1dd9c1d-f2a5-4412-94f7-b1609dc5bd1c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:40:31.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1dd9c1d-f2a5-4412-94f7-b1609dc5bd1c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:40:31.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1dd9c1d-f2a5-4412-94f7-b1609dc5bd1c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:40:31.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1dd9c1d-f2a5-4412-94f7-b1609dc5bd1c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:40:31.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1dd9c1d-f2a5-4412-94f7-b1609dc5bd1c] Success to connect to server [localhost:8848] on start up, connectionId = 1753335631731_127.0.0.1_1214
13:40:31.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1dd9c1d-f2a5-4412-94f7-b1609dc5bd1c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:40:31.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1dd9c1d-f2a5-4412-94f7-b1609dc5bd1c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000198664f7cb0
13:40:31.843 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1dd9c1d-f2a5-4412-94f7-b1609dc5bd1c] Notify connected event to listeners.
13:40:31.881 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:40:31.902 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:40:31.987 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.464 seconds (JVM running for 13.42)
13:40:31.997 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:40:31.997 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:40:31.999 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:40:32.384 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1dd9c1d-f2a5-4412-94f7-b1609dc5bd1c] Receive server push request, request = NotifySubscriberRequest, requestId = 68
13:40:32.397 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1dd9c1d-f2a5-4412-94f7-b1609dc5bd1c] Ack server push request, request = NotifySubscriberRequest, requestId = 68
13:40:32.460 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:40:40.797 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:40:40.797 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:41:02.137 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:41:02.141 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:41:02.467 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:41:02.467 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1efec1a9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:41:02.467 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753335631731_127.0.0.1_1214
13:41:02.467 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753335631731_127.0.0.1_1214]Ignore complete event,isRunning:false,isAbandon=false
13:41:02.467 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@87fef29[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 18]
13:41:02.607 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:41:02.609 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:41:02.613 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:41:02.613 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:41:02.614 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:41:02.615 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:41:35.208 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:41:37.239 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 59e1d61c-ecfa-4fb2-94ea-2ccb7019f26d_config-0
13:41:37.480 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 115 ms to scan 1 urls, producing 3 keys and 6 values 
13:41:37.595 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 4 keys and 9 values 
13:41:37.616 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
13:41:37.653 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 1 keys and 5 values 
13:41:37.678 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 7 values 
13:41:37.699 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
13:41:37.705 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59e1d61c-ecfa-4fb2-94ea-2ccb7019f26d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:41:37.706 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59e1d61c-ecfa-4fb2-94ea-2ccb7019f26d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001c60c3b6af8
13:41:37.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59e1d61c-ecfa-4fb2-94ea-2ccb7019f26d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001c60c3b6d18
13:41:37.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59e1d61c-ecfa-4fb2-94ea-2ccb7019f26d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:41:37.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59e1d61c-ecfa-4fb2-94ea-2ccb7019f26d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:41:37.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59e1d61c-ecfa-4fb2-94ea-2ccb7019f26d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:41:40.546 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59e1d61c-ecfa-4fb2-94ea-2ccb7019f26d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753335700075_127.0.0.1_1419
13:41:40.547 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59e1d61c-ecfa-4fb2-94ea-2ccb7019f26d_config-0] Notify connected event to listeners.
13:41:40.548 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59e1d61c-ecfa-4fb2-94ea-2ccb7019f26d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:41:40.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59e1d61c-ecfa-4fb2-94ea-2ccb7019f26d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c60c4f0ad8
13:41:41.068 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:41:52.311 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:41:52.314 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:41:52.314 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:41:52.880 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:41:54.916 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:41:54.919 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:41:54.920 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:42:07.980 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:42:11.244 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9dd6f027-9b18-4c8f-8621-cf63f2995d53
13:42:11.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dd6f027-9b18-4c8f-8621-cf63f2995d53] RpcClient init label, labels = {module=naming, source=sdk}
13:42:11.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dd6f027-9b18-4c8f-8621-cf63f2995d53] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:42:11.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dd6f027-9b18-4c8f-8621-cf63f2995d53] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:42:11.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dd6f027-9b18-4c8f-8621-cf63f2995d53] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:42:11.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dd6f027-9b18-4c8f-8621-cf63f2995d53] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:42:11.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dd6f027-9b18-4c8f-8621-cf63f2995d53] Success to connect to server [localhost:8848] on start up, connectionId = 1753335731251_127.0.0.1_1512
13:42:11.364 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dd6f027-9b18-4c8f-8621-cf63f2995d53] Notify connected event to listeners.
13:42:11.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dd6f027-9b18-4c8f-8621-cf63f2995d53] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:42:11.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dd6f027-9b18-4c8f-8621-cf63f2995d53] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c60c4f0ad8
13:42:11.413 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:42:11.440 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:42:11.536 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 37.31 seconds (JVM running for 38.873)
13:42:11.549 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:42:11.550 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:42:11.550 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:42:11.958 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dd6f027-9b18-4c8f-8621-cf63f2995d53] Receive server push request, request = NotifySubscriberRequest, requestId = 6
13:42:11.974 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dd6f027-9b18-4c8f-8621-cf63f2995d53] Ack server push request, request = NotifySubscriberRequest, requestId = 6
13:42:37.599 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:42:38.688 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:42:38.688 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:16:12.426 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:16:12.430 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:16:12.775 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:16:12.775 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@65f9647c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:16:12.775 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753335731251_127.0.0.1_1512
14:16:12.777 [nacos-grpc-client-executor-420] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753335731251_127.0.0.1_1512]Ignore complete event,isRunning:false,isAbandon=false
14:16:12.779 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@34f4a915[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 421]
14:16:12.922 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:16:12.923 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:16:12.927 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:16:12.927 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:16:12.928 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:16:12.928 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:16:41.729 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:16:42.281 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 06afe547-3867-4989-a016-6034a044772a_config-0
14:16:42.330 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
14:16:42.359 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
14:16:42.366 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
14:16:42.372 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
14:16:42.380 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
14:16:42.390 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:16:42.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06afe547-3867-4989-a016-6034a044772a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:16:42.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06afe547-3867-4989-a016-6034a044772a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002518139ed38
14:16:42.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06afe547-3867-4989-a016-6034a044772a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002518139ef58
14:16:42.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06afe547-3867-4989-a016-6034a044772a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:16:42.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06afe547-3867-4989-a016-6034a044772a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:16:42.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06afe547-3867-4989-a016-6034a044772a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:16:43.085 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06afe547-3867-4989-a016-6034a044772a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753337802907_127.0.0.1_5045
14:16:43.085 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06afe547-3867-4989-a016-6034a044772a_config-0] Notify connected event to listeners.
14:16:43.085 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06afe547-3867-4989-a016-6034a044772a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:16:43.085 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06afe547-3867-4989-a016-6034a044772a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025181518fb0
14:16:43.164 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:16:45.568 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:16:45.568 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:16:45.568 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:16:45.676 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:16:46.170 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:16:46.171 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:16:46.172 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:16:51.318 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:16:53.579 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a286e347-e732-4154-95d8-f5e9964ad880
14:16:53.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a286e347-e732-4154-95d8-f5e9964ad880] RpcClient init label, labels = {module=naming, source=sdk}
14:16:53.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a286e347-e732-4154-95d8-f5e9964ad880] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:16:53.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a286e347-e732-4154-95d8-f5e9964ad880] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:16:53.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a286e347-e732-4154-95d8-f5e9964ad880] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:16:53.583 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a286e347-e732-4154-95d8-f5e9964ad880] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:16:53.705 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a286e347-e732-4154-95d8-f5e9964ad880] Success to connect to server [localhost:8848] on start up, connectionId = 1753337813590_127.0.0.1_5060
14:16:53.705 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a286e347-e732-4154-95d8-f5e9964ad880] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:16:53.705 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a286e347-e732-4154-95d8-f5e9964ad880] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025181518fb0
14:16:53.705 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a286e347-e732-4154-95d8-f5e9964ad880] Notify connected event to listeners.
14:16:53.746 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:16:53.769 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:16:53.856 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.653 seconds (JVM running for 13.611)
14:16:53.867 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:16:53.867 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:16:53.867 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:16:54.215 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:16:54.260 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a286e347-e732-4154-95d8-f5e9964ad880] Receive server push request, request = NotifySubscriberRequest, requestId = 13
14:16:54.278 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a286e347-e732-4154-95d8-f5e9964ad880] Ack server push request, request = NotifySubscriberRequest, requestId = 13
14:16:58.789 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:16:58.789 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:17:52.422 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:17:52.426 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:17:52.762 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:17:52.762 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2eb38db1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:17:52.762 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753337813590_127.0.0.1_5060
14:17:52.764 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753337813590_127.0.0.1_5060]Ignore complete event,isRunning:false,isAbandon=false
14:17:52.765 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 24]
14:17:52.897 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:17:52.897 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:17:52.897 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:17:52.897 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:17:52.897 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:17:52.897 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:17:57.025 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:17:57.567 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3c37325f-a927-4933-8e05-557af9f518b3_config-0
14:17:57.620 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
14:17:57.648 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
14:17:57.654 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
14:17:57.662 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
14:17:57.669 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
14:17:57.675 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
14:17:57.678 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:17:57.678 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001e9013ceaf8
14:17:57.678 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001e9013ced18
14:17:57.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:17:57.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:17:57.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:17:58.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753337878174_127.0.0.1_5209
14:17:58.363 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] Notify connected event to listeners.
14:17:58.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:17:58.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e901508ad8
14:17:58.447 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:18:00.962 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:18:00.962 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:18:00.962 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:18:01.079 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:18:01.805 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:18:01.806 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:18:01.806 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:18:07.449 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:18:10.525 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3159ee3c-2102-44e3-9ef8-dd420bb47fc4
14:18:10.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] RpcClient init label, labels = {module=naming, source=sdk}
14:18:10.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:18:10.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:18:10.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:18:10.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:18:10.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Success to connect to server [localhost:8848] on start up, connectionId = 1753337890538_127.0.0.1_5235
14:18:10.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:18:10.661 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Notify connected event to listeners.
14:18:10.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e901508ad8
14:18:10.710 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:18:10.739 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:18:10.868 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 14.327 seconds (JVM running for 15.157)
14:18:10.883 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:18:10.883 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:18:10.884 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:18:11.150 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:18:11.277 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Receive server push request, request = NotifySubscriberRequest, requestId = 16
14:18:11.293 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Ack server push request, request = NotifySubscriberRequest, requestId = 16
14:18:16.969 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:18:16.969 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:02:01.399 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Server healthy check fail, currentConnection = 1753337890538_127.0.0.1_5235
16:02:01.401 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:02:01.536 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Success to connect a server [localhost:8848], connectionId = 1753344121424_127.0.0.1_8363
16:02:01.537 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Abandon prev connection, server is localhost:8848, connectionId is 1753337890538_127.0.0.1_5235
16:02:01.538 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753337890538_127.0.0.1_5235
16:02:01.541 [nacos-grpc-client-executor-1215] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753337890538_127.0.0.1_5235]Ignore complete event,isRunning:false,isAbandon=true
16:02:01.546 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Notify disconnected event to listeners
16:02:01.548 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Server check success, currentServer is localhost:8848 
16:02:01.548 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Notify connected event to listeners.
16:02:04.909 [nacos-grpc-client-executor-1221] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Receive server push request, request = NotifySubscriberRequest, requestId = 19
16:02:04.909 [nacos-grpc-client-executor-1221] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Ack server push request, request = NotifySubscriberRequest, requestId = 19
16:23:55.278 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] Server healthy check fail, currentConnection = 1753337878174_127.0.0.1_5209
16:23:55.278 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:23:55.288 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3159ee3c-2102-44e3-9ef8-dd420bb47fc4] Server check success, currentServer is localhost:8848 
16:23:55.416 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] Success to connect a server [localhost:8848], connectionId = 1753345435297_127.0.0.1_13228
16:23:55.417 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1753337878174_127.0.0.1_5209
16:23:55.417 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753337878174_127.0.0.1_5209
16:23:55.418 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] Notify disconnected event to listeners
16:23:55.420 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c37325f-a927-4933-8e05-557af9f518b3_config-0] Notify connected event to listeners.
16:33:13.909 [http-nio-9600-exec-10] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:33:13.909 [http-nio-9600-exec-10] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
20:42:17.769 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:42:17.775 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:42:18.110 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:42:18.111 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7c619460[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:42:18.113 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753344121424_127.0.0.1_8363
20:42:18.116 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1fb57d0b[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 4696]
20:42:18.293 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:42:18.298 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
20:42:18.306 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
20:42:18.306 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:42:18.307 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:42:18.307 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:42:18.308 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:42:18.308 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
