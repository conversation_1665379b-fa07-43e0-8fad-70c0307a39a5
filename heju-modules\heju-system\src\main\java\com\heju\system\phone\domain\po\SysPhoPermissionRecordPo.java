package com.heju.system.phone.domain.po;

import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.heju.common.core.web.tenant.base.TBaseEntity;
import com.heju.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

import static com.heju.common.core.constant.basic.EntityConstants.*;

/**
 * 手机号授权 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_pho_permission_record", excludeProperty = { STATUS, UPDATE_BY, SORT, DEL_FLAG, UPDATE_TIME, REMARK, NAME })
public class SysPhoPermissionRecordPo extends TBaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 授权用户id */
    @Excel(name = "授权用户id")
    protected Long permissionerId;

    /** 开始时间 */
    @Excel(name = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected LocalDateTime startTime;

    /** 结束时间 */
    @Excel(name = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected LocalDateTime endTime;

    /** 授权次数 */
    @Excel(name = "授权次数")
    protected Integer times;

    /** 已看次数 */
    @Excel(name = "已看次数")
    protected Integer viewedTimes;

    /** 剩余次数 */
    @Excel(name = "剩余次数")
    protected Integer remainingTimes;

    /** 授权手机号 */
    @Excel(name = "授权手机号")
    protected String phoneNumber;

}
